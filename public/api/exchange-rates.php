<?php
/**
 * Простой API для получения курсов валют
 * Объединяет курс воны от ЦБ РФ и курс доллара из rates.json
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
error_reporting(0);
ini_set('display_errors', 0);

// Пути к файлам
$ratesFile = __DIR__ . '/../rates.json';

/**
 * Получение курса воны от ЦБ РФ
 */
function getCBRKRWRate() {
    $url = 'https://www.cbr-xml-daily.ru/daily_json.js';

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 200 && $response) {
        $data = json_decode($response, true);
        if ($data && isset($data['Valute']['KRW'])) {
            $krw = $data['Valute']['KRW'];
            $nominal = $krw['Nominal'];
            $value = $krw['Value'];
            return $value / $nominal; // Курс за 1 вону
        }
    }

    return 0.075; // Fallback курс
}



try {
    // Получаем курс воны от ЦБ
    $krwRate = getCBRKRWRate();

    // Получаем курсы доллара из rates.json
    $usdRates = [];
    if (file_exists($ratesFile)) {
        $ratesData = json_decode(file_get_contents($ratesFile), true);
        if ($ratesData) {
            // Извлекаем все доступные курсы доллара
            foreach (['usdtrub', 'usda7a5', 'usdta7a5'] as $key) {
                if (isset($ratesData[$key])) {
                    $usdRates[$key] = [
                        'sell' => floatval($ratesData[$key]['sell']),
                        'buy' => floatval($ratesData[$key]['buy'])
                    ];
                }
            }
        }
    }

    // Если нет курсов доллара, используем fallback
    if (empty($usdRates)) {
        $usdRates = ['usdtrub' => ['sell' => 95, 'buy' => 94]];
    }

    // Возвращаем результат
    echo json_encode([
        'success' => true,
        'KRW_to_RUB' => $krwRate,
        'USD_rates' => $usdRates,
        'updated' => date('Y-m-d H:i:s')
    ]);

} catch (Exception $e) {
    // Fallback в случае ошибки
    echo json_encode([
        'success' => true,
        'KRW_to_RUB' => 0.075,
        'USD_rates' => ['usdtrub' => ['sell' => 95, 'buy' => 94]],
        'updated' => date('Y-m-d H:i:s'),
        'error' => $e->getMessage()
    ]);
}
?>
