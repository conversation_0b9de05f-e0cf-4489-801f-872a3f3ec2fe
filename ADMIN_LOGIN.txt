===============================================
🔐 СИСТЕМА АВТОРИЗАЦИИ SHMS AUTO - ГОТОВА!
===============================================

⚡ БЫСТРАЯ НАСТРОЙКА:

1. Создание администратора:
   node create-admin.js
   (введите логин, имя и пароль)

2. Запуск сервера:
   npm run local

3. Вход в админку:
   http://localhost:3000/admin

🛠️ УПРАВЛЕНИЕ АДМИНИСТРАТОРАМИ:
   node create-admin.js   - создать администратора
   node list-admins.js    - показать список
   node delete-admin.js   - удалить/деактивировать

🛡️ БЕЗОПАСНОСТЬ:
   ✅ Хеширование паролей (bcrypt)
   ✅ Rate limiting (5 попыток/15 мин)
   ✅ IP блокировка
   ✅ CSRF защита
   ✅ Логирование действий
   ✅ Восстановление паролей
   ✅ Защищенные сессии

📚 ДОКУМЕНТАЦИЯ:
   - QUICK_SETUP.md - быстрая настройка
   - ADMIN_AUTH_SETUP.md - подробная документация

⚠️ ВАЖНО:
   - Смените пароль после первого входа
   - Настройте email для восстановления паролей
   - Включите HTTPS в продакшене

===============================================
