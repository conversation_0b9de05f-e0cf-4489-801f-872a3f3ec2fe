<?php
// Скрипт для анализа структуры данных, получаемых из API Encar
ini_set('memory_limit', '256M');
ini_set('display_errors', 1);
error_reporting(E_ALL);

// API настройки
$api_host = 'https://autobase-wade.auto-parser.ru';
$api_username = 'admin';
$api_password = 'n2Q8ewyLft9qgPmim5ng';

// Используем текущую дату
$date = date('Y-m-d');
$file_type = 'active_offer';

// Строим URL
$url = "{$api_host}/encar/{$date}/{$file_type}.csv";

echo "Анализ структуры данных API Encar<br>";
echo "URL: {$url}<br>";
echo "Дата: {$date}<br><br>";

// Проверка наличия необходимых расширений PHP
if (!extension_loaded('curl')) {
    echo "ОШИБКА: Расширение cURL не установлено!<br>";
    exit(1);
}

// Инициализируем сессию cURL
$ch = curl_init();

// Устанавливаем параметры cURL
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
curl_setopt($ch, CURLOPT_USERPWD, "{$api_username}:{$api_password}");
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_CAINFO, __DIR__ . '/cacert.pem');

// Ограничиваем размер данных - получаем только начало файла
curl_setopt($ch, CURLOPT_RANGE, '0-20480'); // Получаем только первые 20KB

// Выполняем запрос
$response = curl_exec($ch);
$status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

// Проверяем ошибки cURL
if (curl_errno($ch)) {
    echo "ОШИБКА cURL: " . curl_error($ch) . "<br><br>";
    curl_close($ch);
    exit(1);
}

// Закрываем сессию cURL
curl_close($ch);

if ($status_code < 200 || $status_code >= 300) {
    echo "Ошибка получения данных. Код статуса: {$status_code}<br>";
    exit(1);
}

// Парсим CSV данные
$lines = explode("\n", $response);
if (count($lines) < 2) {
    echo "Недостаточно данных для анализа<br>";
    exit(1);
}

$headers = explode('|', $lines[0]);
$headers = array_map('trim', $headers);

echo "<h2>Структура данных</h2>";
echo "<p>Найдено " . count($headers) . " полей в заголовке CSV.</p>";

// Анализируем первую запись
$first_row = null;
if (isset($lines[1]) && !empty(trim($lines[1]))) {
    $values = explode('|', $lines[1]);
    $first_row = [];
    
    foreach ($headers as $i => $header) {
        if (isset($values[$i])) {
            $first_row[$header] = trim($values[$i]);
        } else {
            $first_row[$header] = '';
        }
    }
}

// Ищем поля с возможными изображениями
$image_fields = [];
$url_pattern = '/^https?:\/\//i';

if ($first_row) {
    echo "<h3>Поля, которые могут содержать URL изображений:</h3>";
    echo "<ul>";
    
    foreach ($first_row as $key => $value) {
        if (preg_match($url_pattern, $value) || 
            stripos($key, 'image') !== false || 
            stripos($key, 'photo') !== false || 
            stripos($key, 'pic') !== false || 
            stripos($key, 'img') !== false) {
            
            echo "<li><strong>{$key}</strong>: {$value}</li>";
            $image_fields[] = $key;
        }
    }
    
    if (empty($image_fields)) {
        echo "<li>Не найдено полей, похожих на URL изображений</li>";
    }
    
    echo "</ul>";
}

// Выводим все поля и их значения из первой записи
echo "<h3>Все поля и их значения (первая запись):</h3>";
echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
echo "<tr><th>Поле</th><th>Значение</th></tr>";

if ($first_row) {
    foreach ($first_row as $key => $value) {
        $is_image = in_array($key, $image_fields);
        echo "<tr " . ($is_image ? "style='background-color: #ffeeee;'" : "") . ">";
        echo "<td>{$key}</td>";
        echo "<td>" . htmlspecialchars($value) . "</td>";
        echo "</tr>";
    }
}

echo "</table>";

// Форма для тестирования отображения изображения
if (!empty($image_fields)) {
    echo "<h3>Тест отображения изображения:</h3>";
    echo "<form method='get' action='encar-proxy-improved.php'>";
    echo "<input type='hidden' name='type' value='image'>";
    echo "<select name='url' style='width: 300px;'>";
    
    foreach ($image_fields as $field) {
        if (isset($first_row[$field]) && !empty($first_row[$field])) {
            echo "<option value='" . htmlspecialchars($first_row[$field]) . "'>{$field}</option>";
        }
    }
    
    echo "</select>";
    echo "<input type='submit' value='Показать изображение'>";
    echo "</form>";
    
    echo "<p>Или просмотреть все изображения:</p>";
    
    foreach ($image_fields as $field) {
        if (isset($first_row[$field]) && !empty($first_row[$field])) {
            echo "<h4>Поле: {$field}</h4>";
            echo "<img src='encar-proxy-improved.php?type=image&url=" . urlencode($first_row[$field]) . "' style='max-width: 300px; border: 1px solid #ddd; padding: 5px;'>";
        }
    }
}
?> 