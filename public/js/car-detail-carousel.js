// Автоматический слайдер + ручное управление + полноэкранный просмотр
document.addEventListener('DOMContentLoaded', function () {    // Image carousel    const slider = document.getElementById('carSlider');    const prevBtn = document.getElementById('sliderPrev');    const nextBtn = document.getElementById('sliderNext');    const slides = document.querySelectorAll('.car-detail__main-img');    const modal = document.getElementById('imgModal');    const modalImg = document.getElementById('imgModalImg');    const closeModal = document.getElementById('imgModalClose');        
    let currentSlide = 0;    const slideCount = slides.length;        // Initialize slider
    if (slideCount > 0) {
        slides[0].classList.add('active');
        
        // Add click events to slides to open modal
        slides.forEach((slide, index) => {
            slide.addEventListener('click', function() {
                modal.style.display = 'flex';
                modalImg.src = this.src;
            });
        });
        
        // Close modal on click
        closeModal.addEventListener('click', function() {
            modal.style.display = 'none';
        });
        
        // Also close on outside click
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
        
        // Previous slide
        prevBtn.addEventListener('click', function() {
            goToSlide((currentSlide - 1 + slideCount) % slideCount);
        });
        
        // Next slide
        nextBtn.addEventListener('click', function() {
            goToSlide((currentSlide + 1) % slideCount);
        });
        
        // Add keyboard support
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                goToSlide((currentSlide - 1 + slideCount) % slideCount);
            } else if (e.key === 'ArrowRight') {
                goToSlide((currentSlide + 1) % slideCount);
            } else if (e.key === 'Escape' && modal.style.display === 'flex') {
                modal.style.display = 'none';
            }
        });
    }
    
    function goToSlide(n) {
        slides[currentSlide].classList.remove('active');
        currentSlide = n;
        slides[currentSlide].classList.add('active');
    }
    
    // Tab navigation
    const tabs = document.querySelectorAll('.car-detail__tab');
    const sections = {
        'desc': document.getElementById('desc'),
        'specs': document.getElementById('specs'),
        'best': document.getElementById('best')
    };
    
    // Smooth scroll function
    function smoothScroll(target, duration) {
        const targetPosition = target.getBoundingClientRect().top + window.pageYOffset - 100;
        const startPosition = window.pageYOffset;
        const distance = targetPosition - startPosition;
        let startTime = null;
        
        function animation(currentTime) {
            if (startTime === null) startTime = currentTime;
            const timeElapsed = currentTime - startTime;
            const scrollY = ease(timeElapsed, startPosition, distance, duration);
            window.scrollTo(0, scrollY);
            if (timeElapsed < duration) requestAnimationFrame(animation);
        }
        
        // Easing function
        function ease(t, b, c, d) {
            t /= d / 2;
            if (t < 1) return c / 2 * t * t + b;
            t--;
            return -c / 2 * (t * (t - 2) - 1) + b;
        }
        
        requestAnimationFrame(animation);
    }
    
    // Handle tab clicks
    tabs.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all tabs
            tabs.forEach(t => t.classList.remove('active'));
            
            // Add active class to clicked tab
            this.classList.add('active');
            
            // Get the target section
            const targetId = this.getAttribute('href').substring(1);
            const targetSection = sections[targetId];
            
            if (targetSection) {
                smoothScroll(targetSection, 500);
            }
        });
    });
    
    // Scroll event for highlighting active tab
    window.addEventListener('scroll', function() {
        const scrollPosition = window.scrollY + 150; // Add offset for header
        
        // Find which section is in view
        let currentSection = '';
        
        Object.entries(sections).forEach(([id, section]) => {
            if (section) {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.offsetHeight;
                
                if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                    currentSection = id;
                }
            }
        });
        
        // Update active tab
        if (currentSection) {
            tabs.forEach(tab => {
                const tabTarget = tab.getAttribute('href').substring(1);
                if (tabTarget === currentSection) {
                    tab.classList.add('active');
                } else {
                    tab.classList.remove('active');
                }
            });
        }
    });
    
    // Mobile menu
    const burgerBtn = document.getElementById('burgerBtn');
    if (burgerBtn) {
        burgerBtn.addEventListener('click', function() {
            this.classList.toggle('open');
            document.querySelector('.header__nav')?.classList.toggle('open');
        });
    }
}); 