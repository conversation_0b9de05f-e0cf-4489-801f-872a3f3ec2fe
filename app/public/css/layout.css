/* ===== LAYOUT.CSS - ГЛОБАЛЬНОЕ ЦЕНТРИРОВАНИЕ И КОНТЕЙНЕРЫ ===== */

/* Базовые стили для центрирования страниц */
.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  box-sizing: border-box;
}

/* Контейнер для секций с правильными отступами */
.section-container {
  padding: 60px 20px;
  margin: 0 auto;
  max-width: 1200px;
  box-sizing: border-box;
}

/* Уменьшенные отступы для мобильных устройств */
@media (max-width: 768px) {
  .page-container {
    padding: 0 15px;
  }
  
  .section-container {
    padding: 40px 15px;
  }
}

/* Очень маленькие экраны */
@media (max-width: 480px) {
  .page-container {
    padding: 0 10px;
  }
  
  .section-container {
    padding: 30px 10px;
  }
}

/* Специальные контейнеры для разных типов контента */
.hero-container {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
}

.brands-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 60px 20px;
}

.stock-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 60px 20px;
}

.benefits-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 60px 20px;
}

.categories-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 60px 20px;
}

.reviews-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 60px 20px;
}

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 60px 20px;
}

.search-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 60px 20px;
}

/* Адаптивные стили для контейнеров */
@media (max-width: 768px) {
  .brands-container,
  .stock-container,
  .benefits-container,
  .categories-container,
  .reviews-container,
  .contact-container,
  .search-container {
    padding: 40px 15px;
  }
}

@media (max-width: 480px) {
  .brands-container,
  .stock-container,
  .benefits-container,
  .categories-container,
  .reviews-container,
  .contact-container,
  .search-container {
    padding: 30px 10px;
  }
}
