<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Configuration
$auth = 'Basic ' . base64_encode('admin:n2Q8ewyLft9qgPmim5ng');
$host = 'https://autobase-wade.auto-parser.ru';

// Check if this is just a health check
if (isset($_GET['check'])) {
    echo json_encode(['status' => 'ok', 'message' => 'Proxy is operational']);
    exit;
}

// Get parameters
$fileType = isset($_GET['fileType']) ? $_GET['fileType'] : 'active_offer';
$date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
$brand = isset($_GET['brand']) ? $_GET['brand'] : '';
$model = isset($_GET['model']) ? $_GET['model'] : '';
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 100; // Limit results to prevent memory issues

// Validate file type
if (!in_array($fileType, ['active_offer', 'removed_offer'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid file type']);
    exit;
}

$url = "{$host}/encar/{$date}/{$fileType}.csv";

// Initialize cURL session
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Authorization: ' . $auth]);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

// Execute the request
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

// Check if request was successful
if ($httpCode !== 200 || empty($response)) {
    http_response_code(502);
    echo json_encode([
        'error' => 'Failed to fetch data from Encar API',
        'httpCode' => $httpCode
    ]);
    exit;
}

// Process CSV data
$tempFile = tempnam(sys_get_temp_dir(), 'encar');
file_put_contents($tempFile, $response);

$cars = [];
$count = 0;

if (($handle = fopen($tempFile, "r")) !== FALSE) {
    // Get header row
    $header = fgetcsv($handle, 0, "|");
    
    // Process data rows
    while (($data = fgetcsv($handle, 0, "|")) !== FALSE) {
        // Create associative array from header and data
        $car = array_combine($header, $data);
        
        // Filter by brand if specified
        if (!empty($brand) && stripos($car['mark'] ?? '', $brand) === false) {
            continue;
        }
        
        // Filter by model if specified
        if (!empty($model) && stripos($car['model'] ?? '', $model) === false) {
            continue;
        }
        
        // Clean and transform data
        $car = cleanCarData($car);
        
        $cars[] = $car;
        $count++;
        
        // Apply limit to prevent memory issues
        if ($count >= $limit) {
            break;
        }
    }
    fclose($handle);
}

// Remove temporary file
unlink($tempFile);

// Return JSON response
echo json_encode($cars);
exit;

/**
 * Clean and transform car data
 */
function cleanCarData($car) {
    // Map CSV fields to our expected format
    $result = [
        'mark' => $car['mark'] ?? '',
        'brand' => $car['mark'] ?? '',
        'model' => $car['model'] ?? '',
        'year' => $car['year'] ?? '',
        'price' => $car['price'] ?? '',
        'km_age' => $car['odometer'] ?? '0',
        'mileage' => $car['odometer'] ?? '0',
        'color' => $car['color'] ?? '',
        'engine_type' => formatEngine($car),
        'engine_volume' => $car['engine_volume'] ?? '',
        'power' => $car['power'] ?? '',
        'transmission_type' => $car['transmission_type'] ?? '',
        'body_type' => $car['body_type'] ?? '',
        'address' => $car['address'] ?? '',
        'seller' => $car['seller_name'] ?? '',
        'seller_type' => $car['seller_type'] ?? '',
        'url' => $car['url'] ?? '',
        'description' => $car['description'] ?? '',
    ];
    
    // Handle images
    if (isset($car['images']) && !empty($car['images'])) {
        try {
            // Images might be stored as JSON array
            $images = json_decode($car['images'], true);
            if (is_array($images)) {
                $result['images'] = json_encode($images);
            } else {
                // Or comma-separated list
                $imageArray = explode(',', $car['images']);
                $result['images'] = json_encode($imageArray);
            }
        } catch (Exception $e) {
            $result['images'] = json_encode([]);
        }
    }
    
    return $result;
}

/**
 * Format engine details
 */
function formatEngine($car) {
    $parts = [];
    
    if (!empty($car['engine_volume'])) {
        $parts[] = $car['engine_volume'] . 'л';
    }
    
    if (!empty($car['engine_type'])) {
        $parts[] = $car['engine_type'];
    }
    
    if (!empty($car['cylinders'])) {
        $parts[] = $car['cylinders'] . ' цил.';
    }
    
    return implode(' ', $parts);
} 