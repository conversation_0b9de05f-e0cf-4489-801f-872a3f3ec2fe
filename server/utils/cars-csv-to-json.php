<?php
ini_set('memory_limit', '256M');
set_time_limit(120); // 2 минуты максимум
// cars-csv-to-json.php

// === Конфиг ===
$username = 'admin';
$password = 'n2Q8ewyLft9qgPmim5ng';
$dataFilePath = __DIR__ . '/public/data/cars.json';
$cacheDir = __DIR__ . '/cache/';

// Создаем директорию для кеша, если её нет
if (!is_dir($cacheDir)) {
    mkdir($cacheDir, 0777, true);
}

// Берём вчерашнюю дату
$date = date('Y-m-d', strtotime('-1 day'));
$url = "https://autobase-wade.auto-parser.ru/encar/{$date}/active_offer.csv";
$tmpFile = $cacheDir . 'active_offer.csv';

echo "Скачиваем первые 1000 строк CSV через curl...\n";

// Скачиваем через curl только первые 1000 строк
$cmd = sprintf(
    'curl -L -X GET "%s" -H "authorization: Basic %s" --max-time 60 --retry 3 --retry-delay 5 | head -n 1000 > %s',
    $url,
    base64_encode("$username:$password"),
    $tmpFile
);
exec($cmd, $output, $returnVar);

if ($returnVar !== 0) {
    die("Не удалось скачать CSV\n");
}

echo "Парсим CSV...\n";

// Читаем CSV построчно
$handle = fopen($tmpFile, 'r');
if (!$handle) die("Не удалось открыть CSV\n");

$headers = [];
$cars = [];
$limit = 30; // Возвращаем лимит к 30 машинам
$lineNum = 0;

// Список важных полей для сохранения
$importantFields = [
    'inner_id', 'mark', 'model', 'year', 'price', 'km_age', 'images',
    'color', 'transmission', 'fuel_type', 'engine_volume', 'power',
    'body_type', 'doors', 'wheel', 'vin', 'description', 'location'
];

while (($line = fgets($handle)) !== false) {
    $row = explode('|', $line);
    if ($lineNum === 0) {
        $headers = $row;
        $lineNum++;
        continue;
    }
    
    $car = [];
    foreach ($headers as $j => $header) {
        $value = $row[$j] ?? '';
        
        // Сохраняем только важные поля
        if (in_array($header, $importantFields)) {
            // Форматируем значения
            switch ($header) {
                case 'price':
                    $value = (int)$value;
                    break;
                case 'km_age':
                    $value = (int)$value;
                    break;
                case 'year':
                    $value = (int)$value;
                    break;
                case 'images':
                    // Преобразуем строку изображений в массив
                    $value = array_filter(explode(',', $value));
                    break;
                case 'engine_volume':
                    $value = (float)$value;
                    break;
                case 'power':
                    $value = (int)$value;
                    break;
            }
            $car[$header] = $value;
        }
    }
    
    // Добавляем дополнительные поля
    $car['created_at'] = date('Y-m-d H:i:s');
    $car['source'] = 'encar';
    
    // Валидация данных
    if (!empty($car['mark']) && !empty($car['model']) && !empty($car['price'])) {
        // Форматируем описание
        if (!empty($car['description'])) {
            $car['description'] = trim($car['description']);
            // Ограничиваем длину описания
            if (mb_strlen($car['description']) > 1000) {
                $car['description'] = mb_substr($car['description'], 0, 997) . '...';
            }
        }
        
        $cars[] = $car;
        if (count($cars) >= $limit) break;
    }
    
    $lineNum++;
}

fclose($handle);
unlink($tmpFile);

echo "Сохраняем в JSON...\n";

// === Сохраняем в JSON ===
if (!is_dir(dirname($dataFilePath))) {
    mkdir(dirname($dataFilePath), 0777, true);
}

// Добавляем метаданные
$output = [
    'meta' => [
        'total_cars' => count($cars),
        'generated_at' => date('Y-m-d H:i:s'),
        'source_date' => $date
    ],
    'cars' => $cars
];

file_put_contents($dataFilePath, json_encode($output, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
echo "Готово! Сохранено " . count($cars) . " авто в $dataFilePath\n"; 