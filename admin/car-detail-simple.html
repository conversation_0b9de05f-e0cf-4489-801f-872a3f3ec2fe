<!DOCTYPE html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Простое создание детальной страницы | SHMS Admin</title>
    <link rel="stylesheet" href="css/styles.css" />
    <link rel="stylesheet" href="css/simple-admin.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
  </head>
  <body>
    <!-- Mobile Header -->
    <header class="admin-mobile-header">
      <button
        class="admin-burger"
        id="adminBurgerBtn"
        aria-label="Открыть меню"
      >
        <span></span>
        <span></span>
        <span></span>
      </button>
      <a href="index.html" class="admin-mobile-header__logo"> SHMS Admin </a>
    </header>

    <!-- Пустой раздел для отступа -->
    <div class="admin-mobile-header-spacer"></div>

    <!-- Mobile Menu -->
    <nav class="admin-dropdown-menu" id="adminDropdownMenu">
      <a href="index.html">Автомобили в наличии</a>
      <a href="featured.html">Автомобили на главной</a>
      <a href="car-detail-simple.html" class="active">Детальные страницы</a>
      <a href="reviews.html">Отзывы</a>
    </nav>

    <!-- Hero / Title Section -->
    <section class="admin-hero-stock">
      <div class="admin-hero-header-group">
        <div class="admin-header__left">
          <a
            href="#"
            class="admin-header__back-link"
            onclick="window.history.back(); return false;"
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19 12H5M5 12L12 19M5 12L12 5"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            Назад
          </a>
        </div>
        <div class="admin-header__center-group">
          <nav class="admin-header__nav">
            <a href="index.html" class="admin-header__logo-link">SHMS Admin</a>
            <div class="admin-header__nav-links">
              <a href="index.html" class="admin-header__nav-link"
                >Автомобили в наличии</a
              >
              <a href="featured.html" class="admin-header__nav-link"
                >Автомобили на главной</a
              >
              <a
                href="car-detail-simple.html"
                class="admin-header__nav-link active"
                >Детальные страницы</a
              >
              <a href="reviews.html" class="admin-header__nav-link">Отзывы</a>
            </div>
          </nav>
        </div>
      </div>
    </section>

    <div class="admin-container">
      <!-- Main Content -->
      <div class="simple-editor">
        <!-- Existing Pages Management -->
        <div class="admin-section">
          <div class="section-header">
            <h2>
              <i class="fas fa-list"></i> Управление существующими страницами
            </h2>
            <p class="section-description">
              Просматривайте, редактируйте и управляйте уже созданными
              детальными страницами
            </p>
          </div>

          <div class="existing-pages-controls">
            <button id="refresh-pages-btn" class="btn btn-secondary">
              <i class="fas fa-sync-alt"></i> Обновить список
            </button>
            <div class="pages-filter">
              <select id="status-filter" class="filter-select">
                <option value="">Все статусы</option>
                <option value="draft">Черновики</option>
                <option value="published">Опубликованные</option>
              </select>
            </div>
          </div>

          <div class="existing-pages-list" id="existing-pages-list">
            <div class="loading-state" id="pages-loading">
              <i class="fas fa-spinner fa-spin"></i> Загрузка страниц...
            </div>
            <div class="empty-state" id="pages-empty" style="display: none">
              <i class="fas fa-file-alt"></i>
              <p>Детальные страницы не найдены</p>
            </div>
            <!-- Existing pages will be loaded here -->
          </div>
        </div>

        <!-- Car Selection -->
        <div class="admin-section">
          <div class="section-header">
            <h2><i class="fas fa-car"></i> Выбор автомобиля</h2>
            <p class="section-description">
              Выберите автомобиль для создания детальной страницы
            </p>
          </div>

          <div class="car-selector">
            <select id="car-select" class="car-select-dropdown">
              <option value="">-- Выберите автомобиль --</option>
            </select>
            <button id="load-car-btn" class="btn btn-primary">
              <i class="fas fa-download"></i> Загрузить данные
            </button>
          </div>

          <!-- Selected Car Preview -->
          <div
            id="selected-car-preview"
            class="selected-car-preview"
            style="display: none"
          >
            <div class="car-preview-card">
              <div class="car-preview-image">
                <img id="preview-image" src="" alt="Автомобиль" />
              </div>
              <div class="car-preview-info">
                <h3 id="preview-title">Название автомобиля</h3>
                <p id="preview-subtitle">Подзаголовок</p>
                <div class="car-preview-specs">
                  <span id="preview-power">Мощность</span>
                  <span id="preview-mileage">Пробег</span>
                  <span id="preview-price">Цена</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Detail Form -->
        <div
          class="admin-section"
          id="detail-form-section"
          style="display: none"
        >
          <div class="section-header">
            <h2><i class="fas fa-edit"></i> Детальная информация</h2>
            <p class="section-description">
              Заполните дополнительную информацию для детальной страницы
            </p>
          </div>

          <form id="simple-detail-form" class="simple-form">
            <input type="hidden" id="car-id" name="car_id" />
            <input type="hidden" id="detail-id" name="detail_id" />

            <!-- Basic Info -->
            <div class="form-section">
              <h3><i class="fas fa-info-circle"></i> Основная информация</h3>

              <div class="form-row">
                <div class="form-group">
                  <label for="full-title">Полное название</label>
                  <input
                    type="text"
                    id="full-title"
                    name="full_title"
                    placeholder="2024 Audi RS6 Avant Performance"
                  />
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="price">Цена ($)</label>
                  <input
                    type="text"
                    id="price"
                    name="price"
                    placeholder="307,286"
                  />
                </div>
                <div class="form-group">
                  <label for="moscow-price">Цена в Москве ($)</label>
                  <input
                    type="text"
                    id="moscow-price"
                    name="moscow_price"
                    placeholder="320,000"
                  />
                </div>
              </div>
            </div>

            <!-- Description -->
            <div class="form-section">
              <h3><i class="fas fa-align-left"></i> Описание</h3>
              <div class="form-group">
                <label for="description">Описание автомобиля</label>
                <small style="color: #666; display: block; margin-bottom: 5px">
                  Используйте **текст** для выделения жирным шрифтом
                </small>
                <textarea
                  id="description"
                  name="description"
                  rows="6"
                  placeholder="Опишите особенности и преимущества автомобиля... Используйте **жирный текст** для выделения важных моментов."
                ></textarea>
              </div>
            </div>

            <!-- Specifications -->
            <div class="form-section">
              <h3><i class="fas fa-cogs"></i> Технические характеристики</h3>
              <div class="form-group">
                <label for="specs"
                  >Характеристики (каждая с новой строки)</label
                >
                <small style="color: #666; display: block; margin-bottom: 5px">
                  Для цветов используйте формат: Цвет интерьера: Черный #000000,
                  Красный #FF0000
                </small>
                <textarea
                  id="specs"
                  name="specs"
                  rows="8"
                  placeholder="Привод: AWD&#10;Двигатель: 4л / V8 twin-turbo&#10;Мощность: 585 л.с.&#10;Трансмиссия: 8-скоростная АКПП&#10;Цвет экстерьера: Sebring Black Crystal Effect&#10;Цвета интерьера: Черный #000000, Красный #FF0000"
                ></textarea>
              </div>
            </div>

            <!-- Best Features -->
            <div class="form-section">
              <h3><i class="fas fa-star"></i> Лучшее в автомобиле</h3>

              <div class="form-row">
                <div class="form-group">
                  <label for="main-features"
                    >Основные опции (каждая с новой строки)</label
                  >
                  <small
                    style="color: #666; display: block; margin-bottom: 5px"
                  >
                    Используйте **текст** для выделения жирным шрифтом
                  </small>
                  <textarea
                    id="main-features"
                    name="main_features"
                    rows="6"
                    placeholder="**4.0-литровый V8**, 630 л.с.&#10;Полный привод quattro с активными дифференциалами&#10;Разгон 0-100 км/ч - 3.4 секунды"
                  ></textarea>
                </div>

                <div class="form-group">
                  <label for="additional-features"
                    >Дополнительные опции (каждая с новой строки)</label
                  >
                  <small
                    style="color: #666; display: block; margin-bottom: 5px"
                  >
                    Используйте **текст** для выделения жирным шрифтом
                  </small>
                  <textarea
                    id="additional-features"
                    name="additional_features"
                    rows="6"
                    placeholder="Керамические тормоза (опция)&#10;Динамическая подвеска RS&#10;Матрица LED-фары с лазерной технологией&#10;Спортивная выхлопная система RS"
                  ></textarea>
                </div>
              </div>
            </div>
          </form>
        </div>

        <!-- Image Gallery -->
        <div class="admin-section" id="gallery-section" style="display: none">
          <div class="section-header">
            <h2><i class="fas fa-images"></i> Галерея изображений</h2>
            <p class="section-description">
              Добавьте фотографии автомобиля (рекомендуется 5 изображений)
            </p>
            <div
              style="
                background: #e3f2fd;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
                border-left: 4px solid #2196f3;
              "
            >
              <strong>📸 Порядок фотографий:</strong><br />
              • <strong>1-е фото</strong> = Главное изображение (большое фото
              вверху)<br />
              • <strong>2-5 фото</strong> = Галерея внизу + карусель<br />
              • Перетаскивайте фото для изменения порядка
            </div>
          </div>

          <div class="image-upload-area">
            <div class="upload-zone" id="upload-zone">
              <div class="upload-icon">
                <i class="fas fa-cloud-upload-alt"></i>
              </div>
              <div class="upload-text">
                <h3>Перетащите изображения сюда</h3>
                <p>
                  или
                  <button type="button" class="upload-btn">
                    выберите файлы
                  </button>
                </p>
                <small
                  >Поддерживаются: JPG, PNG, WebP (макс. 10MB каждое)</small
                >
              </div>
              <input
                type="file"
                id="image-input"
                multiple
                accept="image/*"
                style="display: none"
              />
            </div>

            <div class="uploaded-images" id="uploaded-images">
              <!-- Uploaded images will appear here -->
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="admin-section">
          <div class="action-buttons">
            <button
              type="button"
              id="preview-btn"
              class="btn btn-secondary"
              disabled
            >
              <i class="fas fa-eye"></i> Предпросмотр
            </button>
            <button
              type="button"
              id="save-draft-btn"
              class="btn btn-info"
              disabled
            >
              <i class="fas fa-save"></i> Сохранить черновик
            </button>
            <button
              type="button"
              id="publish-btn"
              class="btn btn-success"
              disabled
            >
              <i class="fas fa-globe"></i> Создать страницу
            </button>
            <button
              type="button"
              id="generate-html-btn"
              class="btn btn-warning"
              disabled
              style="display: none"
            >
              <i class="fas fa-code"></i> Создать HTML
            </button>
          </div>
        </div>
      </div>

      <!-- Preview Modal -->
      <div id="preview-modal" class="modal-overlay" style="display: none">
        <div class="modal-content">
          <div class="modal-header">
            <h3>Предпросмотр детальной страницы</h3>
            <button class="modal-close" id="close-preview">&times;</button>
          </div>
          <div class="modal-body">
            <iframe id="preview-frame" src="" frameborder="0"></iframe>
          </div>
        </div>
      </div>
    </div>

    <!-- Notifications -->
    <div id="notification" class="notification">
      <div class="notification-content">
        <div class="notification-icon">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="notification-message">
          <div class="notification-title"></div>
          <div class="notification-text"></div>
        </div>
        <button class="notification-close">&times;</button>
      </div>
    </div>

    <script src="js/simple-car-detail.js"></script>

    <!-- Mobile Menu JavaScript -->
    <script>
      // Mobile menu functionality
      document.addEventListener("DOMContentLoaded", function () {
        const burgerBtn = document.getElementById("adminBurgerBtn");
        const dropdownMenu = document.getElementById("adminDropdownMenu");

        if (burgerBtn && dropdownMenu) {
          burgerBtn.addEventListener("click", function () {
            burgerBtn.classList.toggle("open");
            dropdownMenu.classList.toggle("active");
          });

          // Close menu when clicking outside
          document.addEventListener("click", function (e) {
            if (
              !burgerBtn.contains(e.target) &&
              !dropdownMenu.contains(e.target)
            ) {
              burgerBtn.classList.remove("open");
              dropdownMenu.classList.remove("active");
            }
          });
        }
      });
    </script>
  </body>
</html>
