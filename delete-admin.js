const sqlite3 = require("sqlite3").verbose();
const path = require("path");
const readline = require("readline");

// Создаем интерфейс для ввода данных
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

console.log("🗑️  Удаление администратора SHMS Auto");
console.log("===================================\n");

// Путь к базе данных
const dbPath = path.join(__dirname, "data/stock.db");

// Подключаемся к базе данных
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error("❌ Ошибка подключения к базе данных:", err.message);
    process.exit(1);
  }
  console.log("✅ Подключение к базе данных установлено\n");
});

// Функция для ввода данных
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

// Функция получения списка администраторов
function getAdmins() {
  return new Promise((resolve, reject) => {
    const sql = `
            SELECT id, username, email, full_name, is_active, created_at, last_login
            FROM admins 
            ORDER BY created_at DESC
        `;

    db.all(sql, [], (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows || []);
      }
    });
  });
}

// Функция поиска администратора
function findAdmin(identifier) {
  return new Promise((resolve, reject) => {
    const sql = `
            SELECT id, username, email, full_name, is_active
            FROM admins
            WHERE username = ? OR id = ?
        `;

    db.get(sql, [identifier, identifier], (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

// Функция удаления администратора
function deleteAdmin(id) {
  return new Promise((resolve, reject) => {
    const sql = "DELETE FROM admins WHERE id = ?";

    db.run(sql, [id], function (err) {
      if (err) {
        reject(err);
      } else {
        resolve(this.changes);
      }
    });
  });
}

// Функция деактивации администратора
function deactivateAdmin(id) {
  return new Promise((resolve, reject) => {
    const sql = "UPDATE admins SET is_active = 0 WHERE id = ?";

    db.run(sql, [id], function (err) {
      if (err) {
        reject(err);
      } else {
        resolve(this.changes);
      }
    });
  });
}

// Функция активации администратора
function activateAdmin(id) {
  return new Promise((resolve, reject) => {
    const sql = "UPDATE admins SET is_active = 1 WHERE id = ?";

    db.run(sql, [id], function (err) {
      if (err) {
        reject(err);
      } else {
        resolve(this.changes);
      }
    });
  });
}

// Основная функция
async function main() {
  try {
    // Получаем список администраторов
    const admins = await getAdmins();

    if (admins.length === 0) {
      console.log("❌ В системе нет администраторов");
      return;
    }

    console.log("📋 Список администраторов:\n");
    admins.forEach((admin, index) => {
      const status = admin.is_active ? "🟢 Активен" : "🔴 Неактивен";
      const lastLogin = admin.last_login
        ? new Date(admin.last_login).toLocaleString("ru-RU")
        : "Никогда";

      console.log(`${index + 1}. ID: ${admin.id}`);
      console.log(`   Логин: ${admin.username}`);
      if (admin.full_name) {
        console.log(`   Имя: ${admin.full_name}`);
      }
      console.log(`   Статус: ${status}`);
      console.log(`   Последний вход: ${lastLogin}`);
      console.log("");
    });

    // Выбираем действие
    console.log("Выберите действие:");
    console.log("1. Удалить администратора (полное удаление)");
    console.log("2. Деактивировать администратора (отключить доступ)");
    console.log("3. Активировать администратора (включить доступ)");
    console.log("4. Отмена\n");

    const action = await askQuestion("Введите номер действия (1-4): ");

    if (action === "4" || !action) {
      console.log("Операция отменена");
      return;
    }

    if (!["1", "2", "3"].includes(action)) {
      console.log("❌ Неверный выбор");
      return;
    }

    // Выбираем администратора
    const identifier = await askQuestion(
      "\nВведите ID или логин администратора: "
    );

    if (!identifier) {
      console.log("❌ Не указан администратор");
      return;
    }

    // Ищем администратора
    const admin = await findAdmin(identifier);

    if (!admin) {
      console.log("❌ Администратор не найден");
      return;
    }

    // Показываем информацию о найденном администраторе
    console.log("\n📋 Найден администратор:");
    console.log(`   ID: ${admin.id}`);
    console.log(`   Логин: ${admin.username}`);
    if (admin.full_name) {
      console.log(`   Имя: ${admin.full_name}`);
    }
    console.log(
      `   Статус: ${admin.is_active ? "🟢 Активен" : "🔴 Неактивен"}`
    );

    // Проверяем, не последний ли это активный администратор
    if (action === "1" || (action === "2" && admin.is_active)) {
      const activeAdmins = admins.filter(
        (a) => a.is_active && a.id !== admin.id
      );
      if (activeAdmins.length === 0) {
        console.log("\n⚠️  ВНИМАНИЕ: Это последний активный администратор!");
        console.log(
          "Удаление или деактивация приведет к потере доступа к админке!"
        );

        const confirm = await askQuestion(
          '\nВы уверены? Введите "ДА" для подтверждения: '
        );
        if (confirm !== "ДА") {
          console.log("Операция отменена");
          return;
        }
      }
    }

    // Подтверждение действия
    let actionText;
    switch (action) {
      case "1":
        actionText = "УДАЛИТЬ";
        break;
      case "2":
        actionText = "ДЕАКТИВИРОВАТЬ";
        break;
      case "3":
        actionText = "АКТИВИРОВАТЬ";
        break;
    }

    const confirm = await askQuestion(
      `\nВы уверены, что хотите ${actionText} администратора "${admin.username}"? (y/N): `
    );

    if (confirm.toLowerCase() !== "y" && confirm.toLowerCase() !== "yes") {
      console.log("Операция отменена");
      return;
    }

    // Выполняем действие
    console.log("\n🔄 Выполнение операции...");

    let result;
    switch (action) {
      case "1":
        result = await deleteAdmin(admin.id);
        if (result > 0) {
          console.log(`✅ Администратор "${admin.username}" успешно удален`);
        } else {
          console.log("❌ Ошибка при удалении администратора");
        }
        break;

      case "2":
        if (!admin.is_active) {
          console.log("❌ Администратор уже неактивен");
          return;
        }
        result = await deactivateAdmin(admin.id);
        if (result > 0) {
          console.log(`✅ Администратор "${admin.username}" деактивирован`);
        } else {
          console.log("❌ Ошибка при деактивации администратора");
        }
        break;

      case "3":
        if (admin.is_active) {
          console.log("❌ Администратор уже активен");
          return;
        }
        result = await activateAdmin(admin.id);
        if (result > 0) {
          console.log(`✅ Администратор "${admin.username}" активирован`);
        } else {
          console.log("❌ Ошибка при активации администратора");
        }
        break;
    }
  } catch (error) {
    console.error("\n❌ Ошибка:", error.message);
  } finally {
    rl.close();
    db.close();
  }
}

// Обработка сигналов завершения
process.on("SIGINT", () => {
  console.log("\n\nОтменено пользователем.");
  rl.close();
  db.close();
  process.exit(0);
});

// Запуск
main();
