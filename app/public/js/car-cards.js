// JavaScript для динамической загрузки карточек автомобилей с API
document.addEventListener("DOMContentLoaded", function () {
  // Находим контейнер для карточек
  const cardsContainer = document.querySelector(".stock-cards");

  if (!cardsContainer) {
    console.error("Контейнер для карточек не найден");
    return;
  }

  // Устанавливаем сообщение о загрузке
  cardsContainer.innerHTML =
    '<div class="loading">Загрузка автомобилей...</div>';

  // Функция создания карточки автомобиля
  function createCarCard(car) {
    const detailPageLink = car.detailPage
      ? `onclick="window.location.href='${car.detailPage}'" style="cursor: pointer;"`
      : "";

    return `
      <div class="car-card" ${detailPageLink}>
        <div class="car-card__image-container">
          <img src="${
            car.image || car.imageUrl || "/assets/img/cars/default-car.jpg"
          }" alt="${car.title || car.name}" class="car-card__image">
        </div>
        <div class="car-card__info">
          <h3 class="car-card__title">${
            car.title || car.name || "Автомобиль"
          }</h3>
          <div class="car-card__price">${car.price || "По запросу"}</div>
          <div class="car-card__specs">${
            car.specs || car.description || ""
          }</div>
        </div>
      </div>
    `;
  }

  // Функция для загрузки информации о детальных страницах
  async function loadDetailPages() {
    try {
      const response = await fetch("/api/published");
      if (!response.ok) {
        console.warn("Не удалось загрузить детальные страницы");
        return [];
      }
      const pages = await response.json();
      console.log("Loaded detail pages:", pages);
      return pages;
    } catch (error) {
      console.error("Ошибка при загрузке детальных страниц:", error);
      return [];
    }
  }

  // URL для загрузки данных (замените на ваш реальный API эндпоинт)
  const apiUrl = "/api/cars";

  // Загружаем данные с сервера
  async function loadCarsWithDetails() {
    try {
      // Загружаем автомобили
      const carsResponse = await fetch(apiUrl);
      if (!carsResponse.ok) {
        throw new Error("Ошибка при загрузке данных");
      }
      const cars = await carsResponse.json();

      // Загружаем детальные страницы
      const detailPages = await loadDetailPages();

      // Добавляем ссылки на детальные страницы к карточкам автомобилей
      const carsWithDetails = cars.map((car) => {
        const detailPage = detailPages.find((page) => page.car_id === car.id);
        return {
          ...car,
          detailPage: detailPage ? detailPage.filePath : null,
        };
      });

      // Очищаем контейнер от сообщения загрузки
      cardsContainer.innerHTML = "";

      // Если есть данные, создаем карточки
      if (carsWithDetails && carsWithDetails.length > 0) {
        // Создаем HTML для всех карточек
        const cardsHTML = carsWithDetails
          .map((car) => createCarCard(car))
          .join("");
        cardsContainer.innerHTML = cardsHTML;
      } else {
        // Если нет автомобилей
        cardsContainer.innerHTML =
          '<div class="no-cars">Нет доступных автомобилей в наличии</div>';
      }

      // Применяем стили к контейнеру
      cardsContainer.style.display = "grid";
      cardsContainer.style.gridTemplateColumns =
        "repeat(auto-fill, minmax(300px, 1fr))";
      cardsContainer.style.gap = "30px";
      cardsContainer.style.padding = "20px 0";
    } catch (error) {
      console.error("Ошибка при загрузке карточек автомобилей:", error);
      cardsContainer.innerHTML =
        '<div class="error">Не удалось загрузить данные об автомобилях. Пожалуйста, попробуйте позже.</div>';
    }
  }

  // Запускаем загрузку
  loadCarsWithDetails();
});
