<?php
// Упрощенная версия скрипта для создания заглушек без использования GD

// Указываем путь для сохранения файлов
$placeholder_path = 'Img/placeholder-car.jpg';
$logo_path = 'PNG/default-logo.png';

// Проверяем и создаем директории, если они не существуют
if (!is_dir('Img')) {
    mkdir('Img', 0755, true);
}
if (!is_dir('PNG')) {
    mkdir('PNG', 0755, true);
}

// Простой способ создания файлов без использования GD
function createEmptyFile($path, $content = '') {
    // Открываем файл для записи
    $fp = fopen($path, 'w');
    // Записываем содержимое, если оно есть
    if (!empty($content)) {
        fwrite($fp, $content);
    }
    // Закрываем файл
    fclose($fp);
    
    return file_exists($path);
}

// Готовый байт-код простого серого изображения в формате jpg
$jpg_data = base64_decode('/9j/4AAQSkZJRgABAQEASABIAAD/2wBDAAMCAgICAgMCAgIDAwMDBAYEBAQEBAgGBgUGCQgKCgkICQkKDA8MCgsOCwkJDRENDg8QEBEQCgwSExIQEw8QEBD/2wBDAQMDAwQDBAgEBAgQCwkLEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBD/wAARCAAyAGQDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAn/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AJVAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//Z');

// Готовый байт-код простого серого изображения в формате png
$png_data = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAIAAAD/gAIDAAAAA3NCSVQICAjb4U/gAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAFUlEQVRoge3BAQEAAACCIP+vbkhAAQAA7werGXICOwGUiAAAAABJRU5ErkJggg==');

// Создаем заглушки
$placeholder_created = createEmptyFile($placeholder_path, $jpg_data);
$logo_created = createEmptyFile($logo_path, $png_data);

// Выводим результат
header('Content-Type: text/html; charset=utf-8');
echo '<h1>Создание файлов-заглушек</h1>';

if ($placeholder_created) {
    echo "<p style='color:green'>✅ Файл-заглушка для автомобиля создан: {$placeholder_path}</p>";
} else {
    echo "<p style='color:red'>❌ Ошибка создания файла-заглушки для автомобиля: {$placeholder_path}</p>";
}

if ($logo_created) {
    echo "<p style='color:green'>✅ Файл-заглушка для логотипа создан: {$logo_path}</p>";
} else {
    echo "<p style='color:red'>❌ Ошибка создания файла-заглушки для логотипа: {$logo_path}</p>";
}

// Отображаем созданные изображения (если они существуют)
echo '<h2>Предпросмотр созданных изображений:</h2>';
echo "<div style='margin-top: 20px;'>";

if (file_exists($placeholder_path)) {
    echo "<div style='margin-bottom: 20px;'>";
    echo "<h3>Заглушка автомобиля:</h3>";
    echo "<img src='{$placeholder_path}' style='max-width: 300px; border: 1px solid #ccc;'>";
    echo "</div>";
} else {
    echo "<p style='color:red'>Файл заглушки автомобиля не найден.</p>";
}

if (file_exists($logo_path)) {
    echo "<div>";
    echo "<h3>Заглушка логотипа:</h3>";
    echo "<img src='{$logo_path}' style='max-width: 100px; border: 1px solid #ccc;'>";
    echo "</div>";
} else {
    echo "<p style='color:red'>Файл заглушки логотипа не найден.</p>";
}

echo "</div>";

// Дополнительно создадим простую HTML страницу с заглушками
$html_content = <<<HTML
<!DOCTYPE html>
<html>
<head>
    <title>Заглушки изображений</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .image-container { margin: 20px 0; }
        img { border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>Заглушки изображений</h1>
    
    <div class="image-container">
        <h2>Заглушка автомобиля:</h2>
        <img src="Img/placeholder-car.jpg" alt="Placeholder Car">
    </div>
    
    <div class="image-container">
        <h2>Заглушка логотипа:</h2>
        <img src="PNG/default-logo.png" alt="Default Logo">
    </div>
    
    <p>
        <a href="test.html">Перейти к тестовой странице</a> | 
        <a href="order.html">Перейти к странице заказа</a>
    </p>
</body>
</html>
HTML;

// Сохраняем HTML страницу
file_put_contents('placeholders.html', $html_content);

// Инструкции
echo '<h2>Следующие шаги:</h2>';
echo '<ol>';
echo '<li>Откройте страницу заглушек: <a href="placeholders.html" target="_blank">placeholders.html</a></li>';
echo '<li>Откройте тестовую страницу: <a href="test.html" target="_blank">test.html</a> для проверки всех компонентов</li>';
echo '<li>Перейдите к странице заказа: <a href="order.html" target="_blank">order.html</a></li>';
echo '</ol>';
?> 