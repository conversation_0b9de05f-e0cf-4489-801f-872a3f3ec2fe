/* ====== Общие стили ====== */
*,
*::before,
*::after {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif;
  background: #f8f8f8;
  color: #111;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* ====== Header ====== */
.header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 32px 0 0 0;
  background: #fafafa;
  position: relative;
}

.header__back {
  font-size: 1.5rem;
  color: #111;
  text-decoration: none;
  margin-left: 60px;
  font-weight: 500;
  transition: color 0.2s;
}
.header__back:hover { color: #888; }

.header__center {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: -60px; /* compensate for back button */
}

.header__logo {
  height: 40px;
  margin-bottom: 8px;
}

.header__nav {
  display: flex;
  gap: 40px;
  margin-top: 8px;
}

.header__nav-link {
  font-size: 1.25rem;
  color: #111;
  text-decoration: none;
  font-weight: 700;
  transition: color 0.2s;
  position: relative;
}

.header__nav-link--active,
.header__nav-link:hover {
  color: #222;
}

.header__nav-link--active::after {
  content: '';
  display: block;
  margin: 0 auto;
  margin-top: 4px;
  width: 60%;
  height: 3px;
  background: #111;
  border-radius: 2px;
}

/* ====== Hero Section ====== */
.contacts-hero {
  text-align: center;
  margin-top: 60px;
  margin-bottom: 32px;
}

.contacts-hero__title {
  font-size: 4rem;
  font-weight: 800;
  margin: 0 0 16px 0;
  letter-spacing: 1px;
}

.contacts-hero__subtitle {
  font-size: 1.5rem;
  color: #222;
  margin: 0;
}

/* ====== Contacts Info ====== */
.contacts-info {
  margin: 48px auto 0 auto;
  max-width: 1200px;
  padding: 0 24px;
}

.contacts-info__heading {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 48px;
}

.contacts-info__grid {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
  justify-content: center;
}

.contacts-info__card {
  background: #eaeef0;
  color: #333;
  border-radius: 16px;
  padding: 32px 36px;
  min-width: 300px;
  max-width: 340px;
  font-size: 1.2rem;
  box-shadow: 0 8px 24px rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
  cursor: pointer;
}

.contacts-info__card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 36px rgba(0,0,0,0.12);
  background: #f1f4f6;
}

.contacts-info__card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background: linear-gradient(to bottom, #4d6a77, #8ca6b4);
}

.contacts-info__card strong {
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 16px;
  display: block;
  color: #333333;
}

.contacts-info__card--address, 
.contacts-info__card--email,
.contacts-info__card--phone {
  position: relative;
}

#office-address {
  line-height: 1.5;
}

.copy-tooltip {
  position: absolute;
  left: 50%;
  bottom: 16px;
  transform: translateX(-50%);
  background: #4d6a77;
  color: #fff;
  padding: 6px 16px;
  border-radius: 8px;
  font-size: 0.9rem;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0,0,0,0.10);
}

.contacts-info__card:hover .copy-tooltip {
  opacity: 0;
}

.contacts-info__phone {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 12px;
  display: block;
  color: #4d6a77;
}

.contacts-info__email {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 12px;
  display: block;
  color: #4d6a77;
}

.contacts-info__desc {
  font-size: 1rem;
  color: #555;
  margin-top: 6px;
  opacity: 0.9;
}

.contacts-info__card--address {
  cursor: pointer;
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contacts-info__card--address:hover {
  box-shadow: 0 16px 48px rgba(0,0,0,0.2);
  transform: translateY(-8px);
}

/* Social Media Section */
.social-media {
  margin: 60px auto 40px auto;
  max-width: 1200px;
  padding: 0 24px;
  text-align: center;
}

.social-media__title {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 32px;
}

.social-media__grid {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.social-media__card {
  background: #f1f4f6;
  border-radius: 16px;
  width: 240px;
  padding: 28px 20px;
  box-shadow: 0 8px 24px rgba(0,0,0,0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-decoration: none;
  color: #111;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid #dce4e8;
}

.social-media__card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.08);
  background: #ffffff;
}

.social-media__icon {
  font-size: 2.5rem;
  margin-bottom: 16px;
  color: #4d6a77;
}

.social-media__name {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 8px;
}

.social-media__handle {
  font-size: 0.95rem;
  color: #4d6a77;
}

/* ====== Map Section ====== */
.contacts-map {
  margin: 48px auto 0 auto;
  max-width: 1200px;
  padding: 0 24px;
}

.contacts-map__iframe {
  width: 100%;
  height: 350px;
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.07);
  background: #eaeaea;
}

/* ====== Footer ====== */
/* СТИЛИ ФУТЕРА УДАЛЕНЫ. Используйте styles.css для футера. */

/* ====== Адаптивность ====== */
@media (max-width: 900px) {
  .header__nav { gap: 20px; }
  .contacts-info__grid { flex-direction: column; align-items: center; }
  .footer__container { flex-direction: column; align-items: center; gap: 32px; }
  .footer__nav { gap: 24px; }
  .contacts-info__heading { font-size: 2.2rem; }
  .social-media__title { font-size: 2rem; }
}

@media (max-width: 600px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    padding: 16px 0 0 0;
  }
  .header__back { margin-left: 16px; font-size: 1.1rem; }
  .header__center { margin-left: 0; }
  .header__logo { height: 32px; }
  .header__nav { flex-direction: column; gap: 8px; align-items: center; }
  /* Hide navigation links except logo on mobile */
  .header__nav-link:not(.header__logo-link) {
    display: none !important;
  }
  .contacts-hero__title { font-size: 2.2rem; }
  .contacts-hero__subtitle { font-size: 1rem; }
  .contacts-info__heading { font-size: 1.8rem; }
  .social-media__title { font-size: 1.8rem; }
  .contacts-info__card { padding: 24px 20px; border-radius: 12px; }
  .contacts-info__card strong { font-size: 1.2rem; margin-bottom: 12px; }
  .contacts-info__phone { font-size: 1.3rem; }
  .contacts-info__email { font-size: 1.1rem; }
  .copy-tooltip { font-size: 0.8rem; padding: 5px 12px; border-radius: 4px; }
  /* Футер стили перенесены в footer.css */
}

@media (max-width: 700px) {
  .footer__requisites {
    text-align: center;
    padding: 15px 0;
  }
  
  .footer__requisites-title {
    font-size: 15px;
    margin-bottom: 8px;
  }
}

@media (min-width: 601px) {
  .footer__info {
    display: grid;
    grid-template-columns: 1fr auto;
    grid-template-areas: "left right";
    align-items: flex-start;
    width: 100%;
    padding-bottom: 2rem;
    gap: 0 2.5rem;
  }
  .footer__left {
    grid-area: left;
  }
  .footer__right {
    grid-area: right;
    max-width: 420px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
  .footer__nav {
    justify-content: flex-end;
    margin-bottom: 1.5rem;
  }
  .footer__requisites {
    width: 100%;
  }
}

/* === Header (из car-detail.css) === */
.hero-header-group {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 110px;
  padding: 0 2vw;
  background: #fff;
  box-shadow: 0 1px 6px rgba(0,0,0,0.05);
  position: relative;
}

.header__center-group {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2.5rem;
}

.header__logo-link {
  display: flex;
  align-items: center;
  margin-right: 2.5rem;
}

.header__logo-img {
  height: 95px;
  width: auto;
  display: block;
  background: #fff;
  vertical-align: middle;
  align-self: center;
}

.header__nav {
  display: flex;
  align-items: center;
  gap: 3.5rem;
}

.header__back-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #333;
  text-decoration: none;
  font-size: 1rem;
  transition: all 0.3s ease;
  padding: 0 12px;
  border-radius: 4px;
  height: 48px;
  line-height: 48px;
  position: static;
  background: none;
  z-index: 2;
}

.header__back-link:hover {
  color: #000;
  background: rgba(0, 0, 0, 0.05);
  transform: translateX(-2px);
}

.header__nav-link {
  font-weight: 700;
  font-size: 1.1rem;
  color: #111;
  transition: color 0.2s;
  line-height: 95px;
  display: flex;
  align-items: center;
}

.header__nav-link.active {
  color: #000;
  text-decoration: none;
  font-weight: 700;
}

.header__nav-link:hover {
  color: #6F9AAB;
}

.hero-stock {
  background: #fff;
  position: relative;
  padding-bottom: 0;
}

/* === Mobile Header === */
.mobile-header {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: #fff;
  z-index: 1000;
  padding: 0 20px;
  box-shadow: 0 1px 6px rgba(0,0,0,0.05);
}

@media (max-width: 768px) {
  .mobile-header {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .mobile-header__logo {
    position: static;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: auto;
    text-align: center;
  }
}

/* Пустой раздел для отступа */
.mobile-header-spacer {
  display: none;
  height: 80px;
  width: 100%;
}

@media (max-width: 768px) {
  .burger {
    position: fixed;
    z-index: 2001;
    display: flex;
  }

  .mobile-header-spacer {
    display: block;
    height: 80px;
  }
  
  .hero-header-group {
    display: none;
  }
  
  .contacts-info__card {
    min-width: 90%;
    max-width: 100%;
    padding: 24px;
  }
  
  .social-media__grid {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
  }
  
  .social-media__card {
    width: 140px;
    padding: 20px 10px;
  }
  
  .social-media__icon {
    font-size: 2rem;
    margin-bottom: 12px;
  }
  
  .social-media__name {
    font-size: 1rem;
    margin-bottom: 6px;
  }
  
  .social-media__handle {
    font-size: 0.8rem;
  }
}

/* Стили для бургер-меню */
.burger {
  display: block;
  position: fixed;
  top: 24px;
  left: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: none;
  border: none;
  z-index: 2000;
  cursor: pointer;
  gap: 6px;
}

.burger span {
  display: block;
  width: 32px;
  height: 4px;
  background: #000;
  border-radius: 2px;
  transition: transform 0.3s cubic-bezier(.4,1.3,.6,1), opacity 0.25s;
}

.burger.open span:nth-child(1) {
  transform: translateY(10px) rotate(45deg);
}
.burger.open span:nth-child(2) {
  opacity: 0;
}
.burger.open span:nth-child(3) {
  transform: translateY(-10px) rotate(-45deg);
}

.dropdown-menu {
  position: fixed;
  top: 64px;
  left: 16px;
  min-width: 260px;
  background: #111;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.25);
  padding: 32px 36px 24px 36px;
  z-index: 1500;
  display: none;
  flex-direction: column;
  gap: 18px;
  opacity: 0;
  pointer-events: none;
  transform: translateY(-20px) scale(0.98);
  transition: opacity 0.28s cubic-bezier(.4,1.3,.6,1), transform 0.28s cubic-bezier(.4,1.3,.6,1);
}

.dropdown-menu.active {
  display: flex;
  opacity: 1;
  pointer-events: auto;
  transform: translateY(0) scale(1);
}

.dropdown-menu a {
  color: #fff;
  font-size: 1.15rem;
  text-decoration: none;
  transition: color 0.18s;
  font-family: inherit;
}

.dropdown-menu a:hover {
  color: #b2c9d6;
}

/* Скрываем бургер и мобильное меню на десктопе */
@media (min-width: 769px) {
  .mobile-header,
  .mobile-header-spacer,
  .burger,
  .dropdown-menu {
    display: none !important;
  }
} 