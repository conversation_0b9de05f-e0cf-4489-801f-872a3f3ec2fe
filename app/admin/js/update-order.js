const db = require('./database.js');

// Получить все избранные автомобили
db.all(`
    SELECT f.id, f.car_id, c.title
    FROM featured_cars f 
    JOIN stock_cars c ON f.car_id = c.id 
    ORDER BY f.display_order ASC, f.id ASC
`, [], (err, featured) => {
    if (err) {
        console.error('Ошибка при получении избранных автомобилей:', err);
        process.exit(1);
    }
    
    console.log(`Найдено ${featured.length} избранных автомобилей`);
    
    // Обновляем порядок отображения
    let processed = 0;
    
    featured.forEach((car, index) => {
        const newOrder = index + 1;
        console.log(`Обновление порядка для ${car.title} (ID=${car.id}): ${newOrder}`);
        
        db.run('UPDATE featured_cars SET display_order = ? WHERE id = ?', 
            [newOrder, car.id], function(err) {
            if (err) {
                console.error(`Ошибка при обновлении порядка для автомобиля ${car.title}:`, err);
            } else {
                console.log(`Автомобиль ${car.title} теперь имеет порядок ${newOrder}`);
            }
            
            processed++;
            if (processed === featured.length) {
                console.log('Порядок отображения для всех автомобилей обновлен');
                process.exit(0);
            }
        });
    });
}); 