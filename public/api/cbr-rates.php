<?php
/**
 * API для получения курсов валют от ЦБ РФ
 * Получает курс корейской воны к рублю
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Отключаем вывод ошибок
error_reporting(0);
ini_set('display_errors', 0);

// Путь к кэшу
$cacheDir = __DIR__ . '/../../data/cache/';
$cacheFile = $cacheDir . 'cbr_rates.json';

// Создаем директорию кэша если её нет
if (!is_dir($cacheDir)) {
    mkdir($cacheDir, 0777, true);
}

/**
 * Получение курсов от ЦБ РФ
 */
function getCBRRates() {
    $url = 'https://www.cbr-xml-daily.ru/daily_json.js';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200 || !$response) {
        return false;
    }
    
    $data = json_decode($response, true);
    if (!$data || !isset($data['Valute'])) {
        return false;
    }
    
    return $data;
}

/**
 * Получение курса корейской воны
 */
function getKRWRate($cbrData) {
    // Корейская вона в данных ЦБ
    if (isset($cbrData['Valute']['KRW'])) {
        $krw = $cbrData['Valute']['KRW'];
        // ЦБ дает курс за номинал (обычно 1000 вон)
        $nominal = $krw['Nominal'];
        $value = $krw['Value'];
        
        // Возвращаем курс за 1 вону
        return $value / $nominal;
    }
    
    return false;
}

/**
 * Проверка актуальности кэша
 */
function isCacheValid($cacheFile) {
    if (!file_exists($cacheFile)) {
        return false;
    }
    
    $cacheTime = filemtime($cacheFile);
    $currentTime = time();
    
    // Кэш действителен 1 час
    return ($currentTime - $cacheTime) < 3600;
}

try {
    // Проверяем кэш
    if (isCacheValid($cacheFile)) {
        $cachedData = json_decode(file_get_contents($cacheFile), true);
        if ($cachedData && isset($cachedData['KRW_to_RUB'])) {
            echo json_encode([
                'success' => true,
                'source' => 'cache',
                'KRW_to_RUB' => $cachedData['KRW_to_RUB'],
                'updated' => $cachedData['updated'],
                'cache_time' => date('Y-m-d H:i:s', filemtime($cacheFile))
            ]);
            exit;
        }
    }
    
    // Получаем свежие данные от ЦБ
    $cbrData = getCBRRates();
    if (!$cbrData) {
        // Если не удалось получить данные, пробуем использовать старый кэш
        if (file_exists($cacheFile)) {
            $cachedData = json_decode(file_get_contents($cacheFile), true);
            if ($cachedData && isset($cachedData['KRW_to_RUB'])) {
                echo json_encode([
                    'success' => true,
                    'source' => 'old_cache',
                    'KRW_to_RUB' => $cachedData['KRW_to_RUB'],
                    'updated' => $cachedData['updated'],
                    'warning' => 'Using old cache data due to CBR API unavailability'
                ]);
                exit;
            }
        }
        
        throw new Exception('Failed to get CBR data and no cache available');
    }
    
    // Получаем курс воны
    $krwRate = getKRWRate($cbrData);
    if (!$krwRate) {
        throw new Exception('KRW rate not found in CBR data');
    }
    
    // Подготавливаем данные для кэша
    $responseData = [
        'success' => true,
        'source' => 'cbr',
        'KRW_to_RUB' => $krwRate,
        'updated' => $cbrData['Date'],
        'timestamp' => $cbrData['Timestamp']
    ];
    
    // Сохраняем в кэш
    file_put_contents($cacheFile, json_encode($responseData));
    
    // Возвращаем результат
    echo json_encode($responseData);
    
} catch (Exception $e) {
    // В случае ошибки возвращаем фиксированный курс
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'fallback' => true,
        'KRW_to_RUB' => 0.075, // Примерный курс как fallback
        'message' => 'Using fallback rate due to error'
    ]);
}
?>
