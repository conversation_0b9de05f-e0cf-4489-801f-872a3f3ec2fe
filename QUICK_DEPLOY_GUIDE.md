# ⚡ БЫСТРОЕ РАЗВЕРТЫВАНИЕ АВТООБНОВЛЕНИЯ ENCAR

## 🎯 ЗА 5 МИНУТ

### 1. Загрузите файлы на сервер
```bash
# Скопируйте эти файлы в /var/www/shms-auto/:
scripts/auto-update-encar.sh
scripts/setup-cron.sh
```

### 2. Установите права и запустите настройку
```bash
cd /var/www/shms-auto
chmod +x scripts/*.sh
./scripts/setup-cron.sh install
```

### 3. Протестируйте
```bash
./scripts/setup-cron.sh test
```

**Готово!** Система будет автоматически обновлять данные каждый день в 10:00.

---

## 📋 ЧТО ДЕЛАЕТ СИСТЕМА

### Автоматически каждый день:
1. **Создает резервную копию** текущих CSV файлов
2. **Загружает новые данные** с вашего API сервера
3. **Сохраняет файл** как `encar_active_YYYY-MM-DD.csv`
4. **Перезапускает сайт** через PM2
5. **Проверяет работоспособность** всех компонентов
6. **Отправляет уведомления** о результате

### Файлы и директории:
```
/var/www/shms-auto/
├── scripts/auto-update-encar.sh    # Основной скрипт
├── public/api/encar_data/          # CSV файлы
├── logs/encar-update.log           # Логи
└── backups/csv/                    # Резервные копии
```

---

## 🔧 КОМАНДЫ УПРАВЛЕНИЯ

```bash
# Проверить статус
./scripts/setup-cron.sh status

# Запустить обновление вручную
./scripts/auto-update-encar.sh

# Посмотреть логи
tail -f logs/encar-update.log

# Удалить автообновление
./scripts/setup-cron.sh remove
```

---

## ⚠️ ВАЖНЫЕ МОМЕНТЫ

### Требования:
- ✅ **PHP** установлен и работает
- ✅ **PM2** настроен для сайта (имя: `shms-auto`)
- ✅ **Cron** служба активна
- ✅ **Интернет** доступ к API серверу

### Настройки по умолчанию:
- **Время обновления**: 10:00 каждый день
- **Хранение бэкапов**: 7 дней
- **Таймаут загрузки**: 30 минут
- **Email уведомления**: <EMAIL>

### Изменение настроек:
Отредактируйте файл `scripts/auto-update-encar.sh`:
```bash
nano scripts/auto-update-encar.sh

# Найдите секцию КОНФИГУРАЦИЯ и измените:
CRON_TIME="0 10 * * *"              # Время запуска
ADMIN_EMAIL="<EMAIL>"        # Email для уведомлений
KEEP_BACKUPS=7                      # Дни хранения бэкапов
```

---

## 🚨 РЕШЕНИЕ ПРОБЛЕМ

### Если что-то не работает:

#### 1. Проверьте логи:
```bash
tail -20 logs/encar-update.log
tail -20 logs/encar-error.log
```

#### 2. Проверьте cron:
```bash
crontab -l | grep auto-update
systemctl status cron
```

#### 3. Проверьте PM2:
```bash
pm2 status
pm2 logs shms-auto
```

#### 4. Тестируйте API:
```bash
curl -u "admin:n2Q8ewyLft9qgPmim5ng" \
  "https://autobase-wade.auto-parser.ru/encar/$(date '+%Y-%m-%d')/active_offer.csv" \
  | head -5
```

#### 5. Ручной запуск PHP:
```bash
php public/api/auto-setup.php update
```

---

## 📞 ПОДДЕРЖКА

**Если нужна помощь:**
1. Сохраните вывод команды `./scripts/setup-cron.sh status`
2. Приложите последние 20 строк из `logs/encar-update.log`
3. Опишите проблему и отправьте на <EMAIL>

---

## ✅ ПРОВЕРКА ГОТОВНОСТИ

После установки убедитесь:

- [ ] Команда `crontab -l` показывает задачу с `auto-update-encar.sh`
- [ ] Команда `./scripts/setup-cron.sh status` показывает "OK"
- [ ] Файл `logs/encar-update.log` создается при тестировании
- [ ] PM2 показывает приложение `shms-auto` в статусе "online"
- [ ] В `public/api/encar_data/` появляются файлы `encar_active_*.csv`

**Если все пункты выполнены - система готова к работе!**

---

## 🎉 РЕЗУЛЬТАТ

После настройки:
- ✅ **Данные автомобилей обновляются ежедневно**
- ✅ **Сайт автоматически перезапускается**
- ✅ **Создаются резервные копии**
- ✅ **Ведется подробное логирование**
- ✅ **Отправляются уведомления о статусе**

**Ваш сайт теперь всегда будет иметь актуальные данные автомобилей!**
