/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
html,
body,
.hero {
  transform: none !important;
  filter: none !important;
  perspective: none !important;
  will-change: auto !important;
  font-family: "Inter", sans-serif !important;
  background: #fff;
  color: #111;
  scroll-behavior: smooth;
}
html,
body {
  margin: 0;
  padding: 0;
}
img {
  max-width: 100%;
  display: block;
}
a {
  color: inherit;
  text-decoration: none;
}

/* Header */
.header {
  display: flex;
  align-items: center;
  height: 80px;
  background: transparent;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
}
.header__menu {
  /* position: fixed; */
  top: 28px;
  left: 32px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.header__burger {
  font-size: 2.5rem;
  color: #fff;
  cursor: pointer;
  background: none;
  border: none;
  z-index: 1001;
  transition: opacity 0.2s;
  position: fixed;
  top: 28px;
  left: 32px;
}
.header__logo {
  font-size: 4rem;
  font-family: "Orbitron", "Segoe UI", <PERSON><PERSON>, sans-serif;
  color: #fff;
  letter-spacing: 0.2em;
  font-weight: 700;
}

/* Hero Section */
.hero {
  margin: 0;
  padding: 0;
  position: relative;
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.hero__bg {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.hero__overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 2;
  top: 0;
  left: 0;
}
.hero__logo-block {
  position: absolute;
  top: -20px !important;
  left: 0;
  width: 100vw;
  height: 0;
  display: block;
  transform: none;
  z-index: 3;
  padding-top: 0;
}
.hero__logo {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: auto;
  max-width: 90vw;
  height: auto;
  max-height: 200px;
  object-fit: contain;
  margin: 0;
}
.hero__content {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  z-index: 3;
  margin: 0;
  width: auto;
}
.hero__title {
  font-size: 5rem;
  font-family: "Orbitron", "Segoe UI", Arial, sans-serif;
  letter-spacing: 0.2em;
  margin-bottom: 2rem;
}
.hero__subtitle {
  font-size: 2rem;
  margin-bottom: 2rem;
  font-weight: 300;
}
.hero__btn {
  position: absolute;
  left: 24px;
  bottom: 5vh;
  border: 2px solid #fff;
  background: transparent;
  color: #fff;
  padding: 0.8em 2em;
  font-size: 1.1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.18s cubic-bezier(0.4, 1.3, 0.6, 1), box-shadow 0.18s;
  z-index: 3;
}
.hero__btn:hover {
  background: transparent;
  color: #fff;
  transform: scale(1.07);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
}

@media (max-width: 700px) {
  .hero__logo-block {
    top: 40px !important;
  }
  .hero__btn {
    left: 12px;
    bottom: 12px;
    padding: 0.6em 1.5em;
    font-size: 0.9rem;
  }
  .car-card {
    padding: 16px;
    margin: 0 0 24px 0;
    width: 100%;
    max-width: 100%;
    min-height: auto;
  }

  .car-card:hover {
    transform: none !important;
  }

  .car-card__image {
    height: 220px;
  }
}

@media (max-width: 500px) {
  .hero__logo-block {
    top: 15px !important;
  }
  .hero__btn {
    left: 8px;
    bottom: 8px;
    padding: 0.5em 1.2em;
    font-size: 0.8rem;
  }
}

@media (max-width: 900px) {
  .hero__logo {
    max-width: 70vw;
    max-height: 120px;
  }
}

@media (max-width: 600px) {
  .hero {
    min-height: 350px;
    height: 60vh;
  }
  .hero__content {
    margin-top: 5vh;
  }
  .hero__logo {
    max-width: 90vw;
    max-height: 80px;
  }
  .car-card__info {
    flex-direction: row !important;
    align-items: flex-end !important;
    justify-content: space-between !important;
    gap: 12px !important;
    margin-top: 10px !important;
  }
  .car-card__info-list {
    min-width: 0 !important;
    width: 60% !important;
    font-size: 0.98rem !important;
  }
  .car-card__price-block {
    min-width: 0 !important;
    width: 50% !important;
    margin-left: 0 !important;
    align-items: flex-end !important;
    text-align: right !important;
    margin-top: 0 !important;
    margin-bottom: 12px !important;
  }
  .benefits__list {
    gap: 1rem !important;
  }
  .benefit {
    padding: 1.2rem 0.7rem !important;
    min-width: 0 !important;
    max-width: 95vw !important;
    font-size: 0.98rem !important;
  }
  .benefit__title {
    font-size: 1rem !important;
  }
  .benefit__desc {
    font-size: 0.93rem !important;
  }
  .benefit__icon {
    margin-bottom: 0.7rem !important;
  }
  .hero__bg--mobile {
    object-fit: contain !important;
    object-position: center center !important;
    background: #000 !important;
    width: 100vw !important;
    height: 60vh !important;
    max-height: 100vw !important;
    display: block !important;
  }
}

/* Brands */
.brands {
  background: #fff;
  padding: 3rem 0 2rem 0;
  display: flex;
  justify-content: center;
}
.brands__list {
  display: flex;
  flex-wrap: wrap;
  gap: 2.5rem 4rem;
  justify-content: center;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}
.brands__list img {
  opacity: 0.7;
  filter: grayscale(1);
  height: 40px;
  object-fit: contain;
  transition: opacity 0.2s;
}
.brands__list img:hover {
  opacity: 1;
  filter: none;
}

/* Brands Grid (фикс для мобильных) */
.brands__grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 3.5rem 4.5rem;
  justify-items: center;
  align-items: center;
  max-width: 1000px;
  margin: 0 auto;
  padding: 2.5rem 1rem 2rem 1rem;
  box-sizing: border-box;
  opacity: 0;
  transform: translateX(80vw) rotateY(-90deg) scale(0.9);
  transition: opacity 1.7s cubic-bezier(0.4, 1.3, 0.6, 1),
    transform 1.7s cubic-bezier(0.4, 1.3, 0.6, 1);
  animation: brandsAppear 1.7s cubic-bezier(0.4, 1.3, 0.6, 1) forwards;
}

@keyframes brandsAppear {
  0% {
    opacity: 0;
    transform: translateX(80vw) rotateY(-90deg) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateX(0) rotateY(0) scale(1);
  }
}

.brands__grid.visible {
  opacity: 1;
  transform: none;
  animation: brandsAppear 1.7s cubic-bezier(0.4, 1.3, 0.6, 1) forwards;
}

.brand img {
  max-width: 170px;
  max-height: 70px;
  width: 100%;
  height: auto;
  object-fit: contain;
  opacity: 0.6;
  filter: grayscale(1) brightness(1.1);
  transition: transform 0.35s cubic-bezier(0.4, 1.3, 0.6, 1), box-shadow 0.35s,
    opacity 0.25s, filter 0.35s;
  will-change: transform, filter, opacity;
}

.brand img:hover {
  opacity: 1;
  filter: grayscale(0) drop-shadow(0 4px 24px rgba(80, 80, 80, 0.1));
  transform: scale(1.13) translateY(-6px) rotate(-2deg);
  z-index: 2;
}

@media (max-width: 900px) {
  .brands__grid {
    gap: 1.5rem 1.5rem;
    padding: 1.2rem 0.5rem 1.2rem 0.5rem;
  }
  .brand img {
    max-width: 90px;
    max-height: 38px;
  }
}
@media (max-width: 600px) {
  .brands__grid {
    gap: 0.7rem 0.7rem;
    padding: 0.5rem 0.1rem 0.5rem 0.1rem;
    width: 100vw;
    max-width: 100vw;
  }
  .brand img {
    max-width: 54px;
    max-height: 32px;
  }
}

/* Stock Section */
.stock {
  padding: 3rem 2vw 2rem 2vw;
  background: #fff;
}
.stock__title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 2.5rem;
}
.stock__cards {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  justify-content: center;
  align-items: stretch;
}
.stock__card {
  background: #fff !important;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  padding: 2rem 1.5rem 1.5rem 1.5rem;
  max-width: 370px;
  min-width: 300px;
  flex: 1 1 340px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  transition: box-shadow 0.2s;
}
.stock__card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}
.stock__card h3 {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}
.stock__card-info {
  color: #6c757d;
  font-size: 0.95rem;
  margin-bottom: 1rem;
}
.stock__card img {
  border-radius: 10px;
  margin-bottom: 1rem;
  width: 100%;
  height: 180px;
  object-fit: fill;
}
.stock__card-specs {
  display: flex;
  gap: 2rem;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 700;
}
.stock__card-list {
  list-style: none;
  font-size: 0.98rem;
  margin-bottom: 1.2rem;
  color: #222;
}
.stock__card-price {
  font-size: 1.2rem;
  font-weight: 700;
  color: #111;
  margin-top: auto;
  display: flex;
  flex-direction: column;
  gap: 0.2em;
}
.dot {
  display: inline-block;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  vertical-align: middle;
  border: 2px solid #fff;
  margin-left: -3px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}
.dot:hover {
  transform: scale(1.2);
  z-index: 2;
}
.car-card__info-list .dot:first-child {
  margin-left: 0;
}
.dot--orange {
  background: #eb5d3d;
}
.dot--red {
  background: #b92626;
}
.dot--black {
  background: #1e1f1e;
}

/* Benefits */
.benefits {
  background: #f7fafd;
  padding: 3rem 2vw 2rem 2vw;
  text-align: center;
}
.benefits__title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
}
.benefits__list {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: center;
  align-items: stretch;
}
.benefit {
  background: #6f9aab;
  color: #ffffff;
  border-radius: 10px;
  width: 320px;
  height: 220px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 1.5rem;
  box-sizing: border-box;
  transition: box-shadow 0.2s;
}
.benefit--dark {
  background: #444e52;
}
.benefit__title {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}
.benefit__desc {
  font-size: 0.98rem;
}
.benefit__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}
.benefit__icon img {
  width: 22px;
  height: 24px;
  object-fit: contain;
  display: block;
}
.benefit__icon--bitcoin {
  width: 32px;
  height: 32px;
  object-fit: contain;
  display: block;
}

/* Categories */
.categories {
  background: #fff;
  padding: 2rem 2vw 2rem 2vw;
}
.categories__list {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: center;
}
.category {
  border: none !important;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.07);
  padding: 1rem 0.5rem;
  min-width: 160px;
  max-width: 200px;
  flex: 1 1 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.category__title {
  font-size: 1.1rem;
  font-weight: 700;
  margin: 1rem 0 0.5rem 0;
}
.category__btn {
  background: #f2f4f7;
  color: #222;
  border: none;
  border-radius: 6px;
  padding: 0.5em 1.2em;
  font-size: 1rem;
  margin-top: 0.5rem;
  cursor: pointer;
  transition: background 0.2s;
}
.category__btn:hover {
  background: #b2c9d6;
}
.category img {
  max-width: 90%;
  max-height: 110px;
  width: auto;
  height: auto;
  margin-bottom: 0.5rem;
  object-fit: contain;
  transition: max-width 0.2s, max-height 0.2s;
}

@media (max-width: 700px) {
  .category img {
    max-width: 80%;
    max-height: 70px;
  }
}

/* Стили для мобильных устройств - горизонтальное отображение категорий */
@media (max-width: 600px) {
  .categories__list {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    gap: 10px;
    padding: 10px 0;
    justify-content: flex-start;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    margin: 0 -10px;
    padding: 0 10px 15px;
  }

  .categories__list::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  .category {
    flex: 0 0 auto;
    width: 30%;
    min-width: 110px;
    max-width: 140px;
    margin-right: 5px;
    scroll-snap-align: center;
    padding: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .category img {
    max-width: 100%;
    height: 90px;
    object-fit: cover;
    margin-bottom: 5px;
    border-radius: 8px;
  }

  .category__title {
    font-size: 0.9rem;
    margin: 5px 0;
    text-align: center;
  }
}

/* Reviews */
.reviews {
  background: #fff;
  padding: 3rem 2vw 2rem 2vw;
  text-align: center;
}
.reviews__title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}
.reviews__btn-group {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}
.reviews__btn {
  background: #111;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.7em 2em;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  cursor: pointer;
  transition: background 0.2s;
}
.reviews__btn:hover {
  background: #b2c9d6;
  color: #111;
}
.reviews__btn--secondary {
  margin-right: 0;
}
.reviews__list {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: center;
}
.review {
  position: relative;
  background-size: cover;
  background-position: center;
  border-radius: 18px;
  overflow: hidden;
  min-width: 280px;
  max-width: 350px;
  flex: 1 1 300px;
  height: 400px;
  display: flex;
  align-items: flex-end;
  box-shadow: 0 6px 32px rgba(0, 0, 0, 0.13);
  margin-bottom: 0;
}
.review__gradient {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background: linear-gradient(
    0deg,
    rgba(20, 20, 20, 0.97) 0%,
    rgba(20, 20, 20, 0.82) 45%,
    rgba(20, 20, 20, 0.28) 85%,
    rgba(20, 20, 20, 0.05) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 1.2rem 1.2rem 1.5rem 1.2rem;
  height: 100%;
}
.review__date {
  position: absolute;
  left: 1.2rem;
  top: 1.2rem;
  background: rgba(0, 0, 0, 0.55);
  color: #fff;
  font-size: 0.98rem;
  font-weight: 400;
  border-radius: 7px;
  padding: 0.18em 0.9em;
  z-index: 2;
  letter-spacing: 0.01em;
}
.review__arrow {
  position: absolute;
  right: 1.2rem;
  top: 1.2rem;
  background: none;
  color: inherit;
  border: none;
  border-radius: 0;
  width: auto;
  height: auto;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
  z-index: 2;
  box-shadow: none;
}
.review__arrow:hover {
  background: none;
  color: #fff;
}
.review__arrow svg rect {
  transition: fill 0.22s;
}
.review__arrow svg path {
  transition: stroke 0.22s, transform 0.22s;
}
.review__arrow:hover svg rect {
  fill: #fff;
}
.review__arrow:hover svg path {
  stroke: #111;
}
.review__arrow:hover svg path:last-child {
  transform: translateX(6px);
}
.review__content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end;
  width: 100%;
}
.review__title {
  font-size: 1.25rem;
  font-weight: 800;
  color: #fff;
  margin-bottom: 0.5rem;
  line-height: 1.2;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.18);
}
.review__desc {
  font-size: 1.05rem;
  color: #fff;
  font-weight: 400;
  line-height: 1.35;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.13);
}

/* Contact */
.contact {
  background: #fff;
  padding: 3rem 2vw 2rem 2vw;
  text-align: center;
}
.contact__title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}
.contact__btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem auto;
  background: none;
  color: inherit;
  border: none;
  border-radius: 0;
  width: auto;
  height: auto;
  padding: 0;
  cursor: pointer;
  transition: none;
  box-shadow: none;
}
.contact__btn:hover {
  background: none;
  color: inherit;
}
.contact__desc {
  color: #6c757d;
  font-size: 1rem;
  margin-bottom: 1.5rem;
}
.contact__divider {
  width: 80px;
  height: 4px;
  background: #b2c9d6;
  border-radius: 2px;
  margin: 0 auto;
}
.contact__btn svg rect {
  transition: fill 0.22s;
}
.contact__btn svg path {
  transition: stroke 0.22s, transform 0.22s;
}
.contact__btn:hover svg rect {
  fill: #fff;
}
.contact__btn:hover svg path {
  stroke: #111;
}
.contact__btn:hover svg path:last-child {
  transform: translateX(6px);
}

/* === Десктопные стили футера с order.css === */
/* Футер теперь находится в отдельном файле footer.css */

/* Responsive */
@media (max-width: 1100px) {
  .stock__cards,
  .reviews__list,
  .benefits__list,
  .categories__list {
    flex-direction: column;
    align-items: center;
  }
}
@media (max-width: 700px) {
  .header__logo {
    font-size: 2.2rem;
  }
  .hero__title {
    font-size: 2.5rem;
  }
  .hero__subtitle {
    font-size: 1.1rem;
  }
  .hero__content {
    margin-left: 2vw;
    margin-top: 10vh;
  }
  .brands__list {
    gap: 1.2rem 2rem;
  }
  .stock__card,
  .review,
  .category,
  .benefit {
    min-width: 90vw;
    max-width: 98vw;
    padding: 1rem;
  }
  .review {
    flex: 1 1 90vw;
  }
  .car-card {
    padding: 14px 8px 32px 8px;
    min-width: 0;
    max-width: 100%;
    width: 100%;
    margin: 0 0 18px 0;
    box-sizing: border-box;
  }
  .car-card__image img {
    height: 200px;
    border-radius: 14px;
    width: 100%;
    object-fit: fill;
    margin-bottom: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  }
  .car-card__info {
    flex-direction: column;
    align-items: stretch;
    gap: 0;
    margin-top: 10px;
  }
  .car-card__info-list {
    width: 100%;
    min-width: 0;
    text-align: left;
    word-break: break-word;
  }
  .car-card__price-block {
    min-width: 0;
    margin-left: 0;
    width: 100%;
    align-items: flex-end;
    margin-top: 12px;
    display: flex;
    flex-direction: column;
    overflow-wrap: break-word;
    text-align: right;
  }
  .car-card__price,
  .car-card__in-stock {
    text-align: right;
    width: 100%;
    word-break: break-word;
  }

  .car-card__in-stock {
    margin-bottom: 8px !important;
    text-align: center !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;
  }
  .header__menu {
    top: 16px;
    left: 12px;
  }
  .header__burger {
    font-size: 2rem;
  }
  .benefit {
    width: 95vw;
    min-width: 0;
    max-width: 98vw;
    height: auto;
    padding: 1.2rem 0.7rem;
  }
  .benefits__list {
    gap: 1rem;
  }
  .categories__list {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: center;
    gap: 0.7rem;
    padding: 0;
    margin: 0;
  }
  .category {
    width: 30vw;
    min-width: 90px;
    max-width: 32vw;
  }
}
@media (max-width: 500px) {
  .hero {
    min-height: 350px;
    height: 60vh;
  }
  .hero__content {
    margin-top: 5vh;
  }
  .footer__logo {
    font-size: 1.5rem;
  }
}

.car-card {
  background: #fff !important;
  border: 1px solid #dbe0e6;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  padding: 24px 24px 20px 24px;
  width: 500px;
  min-height: 460px;
  display: flex;
  flex-direction: column;
  font-family: "Segoe UI", Arial, sans-serif;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.car-card:hover {
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}

.car-card__header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 14px;
  background-color: #fff !important;
}

.car-card__title {
  font-size: 1.55rem;
  font-weight: 700;
  margin-bottom: 4px;
  line-height: 1.2;
  color: #222;
}

.car-card__subtitle {
  color: #6c757d;
  font-size: 1rem;
  margin-bottom: 0;
  line-height: 1.3;
}

.car-card__effect {
  font-weight: 700;
  color: #6e828a;
}

.car-card__image {
  position: relative;
  width: 100%;
  height: 260px;
  margin-bottom: 18px;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.car-card__image img {
  width: 100%;
  height: 100%;
  object-fit: fill;
  transition: none;
}

.car-card__image::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30px;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.1) 100%
  );
  z-index: 1;
  opacity: 0.5;
  pointer-events: none;
}

.car-card__specs-row {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 32px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 15px;
  margin-bottom: 18px;
}

.car-card__spec {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.car-card__spec-divider {
  width: 1px;
  height: 36px;
  background: #e5e7eb;
  margin: 0 16px;
  border-radius: 2px;
}

.car-card__spec-main {
  font-size: 1.35rem;
  font-weight: 700;
  display: block;
  color: #111;
}

.car-card__spec-label {
  font-size: 0.9rem;
  color: #6c757d;
  margin-top: 4px;
}

.car-card__info {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 16px;
  margin-top: 5px;
}

.car-card__info-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-size: 0.95rem;
  min-width: 260px;
  min-height: 120px;
  line-height: 1.5;
}

.car-card__info-list b {
  font-weight: 750;
  color: #222;
  margin-left: 4px;
}

.car-card__price-block {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 140px;
  margin-left: 16px;
}

.car-card__price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111;
  margin-bottom: 5px;
  white-space: nowrap;
  letter-spacing: -0.01em;
}

.car-card__in-stock {
  font-size: 0.95rem;
  color: #6c757d;
  white-space: nowrap;
}

/* Dropdown menu */
.dropdown-menu {
  position: fixed;
  top: 64px;
  left: 16px;
  min-width: 260px;
  background: #111;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
  padding: 32px 36px 24px 36px;
  z-index: 1500;
  display: none;
  flex-direction: column;
  gap: 18px;
  opacity: 0;
  pointer-events: none;
  transform: translateY(-20px) scale(0.98);
  transition: opacity 0.28s cubic-bezier(0.4, 1.3, 0.6, 1),
    transform 0.28s cubic-bezier(0.4, 1.3, 0.6, 1);
}
.dropdown-menu.active {
  display: flex;
  opacity: 1;
  pointer-events: auto;
  transform: translateY(0) scale(1);
}
.dropdown-menu a {
  color: #fff;
  font-size: 1.15rem;
  text-decoration: none;
  transition: color 0.18s;
  font-family: inherit;
}
.dropdown-menu a:hover {
  color: #b2c9d6;
}

/* Burger button */
.burger {
  position: fixed;
  top: 24px;
  left: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: none;
  border: none;
  z-index: 2000;
  cursor: pointer;
  gap: 6px;
}
.burger span {
  display: block;
  width: 32px;
  height: 4px;
  background: #fff;
  border-radius: 2px;
  transition: transform 0.3s cubic-bezier(0.4, 1.3, 0.6, 1), opacity 0.25s;
}

/* Burger button animation */
.burger.open span:nth-child(1) {
  transform: translateY(10px) rotate(45deg);
}
.burger.open span:nth-child(2) {
  opacity: 0;
}
.burger.open span:nth-child(3) {
  transform: translateY(-10px) rotate(-45deg);
}

.burger.dark span {
  background-color: #000;
}

.burger.dark {
  color: #000;
}

.benefit--dark .benefit__icon {
  color: #fff;
  font-size: 2.2rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  margin-bottom: 1rem;
}

@font-face {
  font-family: "Inter";
  src: url("/assets/fonts/Inter-VariableFont_opsz,wght.ttf") format("truetype");
  font-weight: 100 900;
  font-style: normal;
}

@font-face {
  font-family: "Inter";
  src: url("/assets/fonts/Inter-Italic-VariableFont_opsz,wght.ttf")
    format("truetype");
  font-weight: 100 900;
  font-style: italic;
}

h1,
h2,
.stock__title,
.car-card__title,
.reviews__title {
  font-weight: 600 !important;
}
.car-card__effect,
.benefit__title,
.category__title,
.footer__nav a {
  font-weight: 500 !important;
}

/* Search */
.search {
  background: #fff;
  padding: 2.5rem 2vw 2.5rem 2vw;
  display: flex;
  justify-content: center;
  align-items: center;
}

.search__form {
  position: relative;
  display: flex;
  max-width: 768px;
  width: 100%;
  background: #f7fafd;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  min-height: 45px;
}

.search__input {
  flex: 1 1 auto;
  border: none;
  outline: none;
  padding: 0.9rem 2.8em 0.9rem 1.6em;
  font-size: 1.1rem;
  background: transparent;
  color: #222;
  min-height: 45px;
  box-sizing: border-box;
}

.search__input::placeholder {
  color: #6c757d;
  opacity: 1;
}

.search__btn {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: #222;
  color: #ffffff;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  box-sizing: border-box;
}

.search__btn:hover {
  background: #444;
  color: #fff;
  transform: translateY(-50%) scale(1.05);
}

/* Улучшенная мобильная адаптация для формы поиска */
@media (max-width: 768px) {
  .search__form {
    max-width: 95vw;
    min-height: 56px;
  }
  .search {
    padding: 1.5rem 1vw;
  }
  .search__input {
    font-size: 1.1rem;
    padding: 1.4em 3em 1.4em 1.4em;
    min-height: 56px;
  }
  .search__btn {
    width: 38px;
    height: 38px;
    padding: 9px;
  }
  .search__btn svg {
    width: 22px;
    height: 22px;
  }
}

@media (max-width: 600px) {
  .search__form {
    max-width: 98vw;
    min-height: 58px;
  }
  .search {
    padding: 1.2rem 1vw 1.2rem 1vw;
  }
  .search__input {
    font-size: 1.1rem;
    padding: 1.5em 3.2em 1.5em 1.5em;
    min-height: 58px;
  }
  .search__btn {
    width: 40px;
    height: 40px;
    padding: 10px;
  }
  .search__btn svg {
    width: 24px;
    height: 24px;
  }
}

/* Дополнительная адаптация для маленьких экранов */
@media (max-width: 480px) {
  .search__form {
    max-width: 100vw;
    min-height: 60px;
    margin: 0 auto;
  }
  .search {
    padding: 1rem 0.5vw;
  }
  .search__input {
    font-size: 1rem;
    padding: 1.6em 3.4em 1.6em 1.6em;
    min-height: 60px;
  }
  .search__btn {
    width: 42px;
    height: 42px;
    padding: 11px;
  }
  .search__btn svg {
    width: 26px;
    height: 26px;
  }
}

/* Адаптация для очень маленьких экранов */
@media (max-width: 360px) {
  .search__form {
    min-height: 62px;
  }
  .search__input {
    font-size: 0.95rem;
    padding: 1.7em 3.6em 1.7em 1.7em;
    min-height: 62px;
  }
  .search__btn {
    width: 44px;
    height: 44px;
    padding: 12px;
  }
  .search__btn svg {
    width: 28px;
    height: 28px;
  }
}

/* Navigation Buttons Section */
.search-container {
  width: 100%;
  max-width: 1100px;
  display: flex;
  justify-content: center;
}

.navigation-buttons-wrapper {
  display: flex;
  gap: 20px;
  width: 100%;
  max-width: 600px;
  justify-content: center;
  align-items: center;
}

.nav-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  border-radius: 50px;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  min-height: 60px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.nav-btn__content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.nav-btn__text {
  position: relative;
  z-index: 2;
}

.nav-btn--stock {
  background: #6f9aab;
  color: #fff;
  border: 2px solid #6f9aab;
}

.nav-btn--stock:hover {
  background: #5a8394;
  border-color: #5a8394;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(111, 154, 171, 0.3);
}

.nav-btn--order {
  background: transparent;
  color: #333;
  border: 2px solid #333;
}

.nav-btn--order:hover {
  background: #333;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(51, 51, 51, 0.3);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .navigation-buttons-wrapper {
    flex-direction: column;
    gap: 15px;
    max-width: 400px;
  }

  .nav-btn {
    padding: 18px 32px;
    font-size: 1.1rem;
    min-height: 65px;
  }
}

@media (max-width: 600px) {
  .navigation-buttons-wrapper {
    gap: 12px;
    max-width: 350px;
  }

  .nav-btn {
    padding: 16px 28px;
    font-size: 1.05rem;
    min-height: 60px;
  }
}

@media (max-width: 480px) {
  .navigation-buttons-wrapper {
    gap: 10px;
    max-width: 320px;
  }

  .nav-btn {
    padding: 14px 24px;
    font-size: 1rem;
    min-height: 55px;
    border-radius: 40px;
  }
}

/* Удаляем медиа-запросы для бургер-меню */
.order-card-title--small {
  font-size: 1.1rem;
  font-weight: 600;
  color: #222;
  margin: 0.2em 0 0.5em 0;
  letter-spacing: 0.01em;
  line-height: 1.2;
  text-align: left;
}

.no-cars {
  text-align: center;
  padding: 3rem 1rem;
  color: #666;
  font-size: 1.2rem;
  background-color: #f8f8f8;
  border-radius: 8px;
  width: 100%;
}

.error {
  text-align: center;
  padding: 3rem 1rem;
  color: #721c24;
  font-size: 1.2rem;
  background-color: #f8d7da;
  border-radius: 8px;
  width: 100%;
}

/* Поддержка старого форматирования карточек автомобилей */
.car-card__info-title {
  font-weight: 700;
  font-size: 1.1rem;
  margin-bottom: 8px;
  color: #111;
}

.car-card__colors {
  display: inline-flex;
  align-items: center;
  gap: 2px;
  margin-left: 5px;
}

.car-card__buttons {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.car-card__btn {
  padding: 10px 15px;
  border: none;
  border-radius: 6px;
  font-weight: 700;
  cursor: pointer;
  flex: 1;
  transition: all 0.2s;
}

.car-card__btn--primary {
  background-color: #222;
  color: #fff;
}

.car-card__btn--primary:hover {
  background-color: #444;
}

.car-card__btn--secondary {
  background-color: #f0f0f0;
  color: #222;
}

.car-card__btn--secondary:hover {
  background-color: #e0e0e0;
}

/* Точки цветов */
.dot {
  display: inline-block;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  vertical-align: middle;
  border: 2px solid #fff;
  margin-left: -3px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.dot:hover {
  transform: scale(1.2);
  z-index: 2;
}

.car-card__info-list .dot:first-child {
  margin-left: 0;
}

/* Поддержка старых классов для совместимости */
.dot--orange {
  background: #eb5d3d;
}
.dot--red {
  background: #b92626;
}
.dot--black {
  background: #1e1f1e;
}
.dot--white {
  background: #ffffff;
  border: 1px solid #ddd;
}
.dot--blue {
  background: #0066cc;
}
.dot--brown {
  background: #8b4513;
}
.dot--beige {
  background: #f5f5dc;
}
.dot--gray {
  background: #808080;
}

/* Контейнер для цветных точек */
.color-dots-container {
  display: inline-flex;
  align-items: center;
  gap: 2px;
  margin-left: 4px;
}

/* Подписи под цветами */
.color-dots-names {
  display: block;
  font-size: 0.8rem;
  color: #666;
  margin-top: 2px;
}

@media (max-width: 1024px) and (min-width: 701px) {
  .car-card {
    width: 470px;
    min-height: 420px;
  }

  .car-card__image {
    height: 260px;
  }
}

.modal {
  display: none;
  position: fixed;
  z-index: 3000;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.45);
  justify-content: center;
  align-items: center;
}
.modal.active {
  display: flex;
}
.modal__content {
  background: #fff;
  padding: 2rem 1.5rem;
  border-radius: 12px;
  max-width: 350px;
  width: 90vw;
  position: relative;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
}
.modal__close {
  position: absolute;
  right: 1rem;
  top: 1rem;
  font-size: 2rem;
  cursor: pointer;
  color: #888;
}
.modal__content h3 {
  margin-bottom: 1rem;
}
#reviewForm input,
#reviewForm textarea {
  width: 100%;
  margin-bottom: 1rem;
  padding: 0.7em;
  border-radius: 6px;
  border: 1px solid #ccc;
  font-size: 1rem;
}
#reviewForm button {
  width: 100%;
  padding: 0.7em;
  background: #111;
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background 0.2s;
}
#reviewForm button:hover {
  background: #b2c9d6;
  color: #111;
}
.modal__success {
  color: green;
  text-align: center;
  margin-top: 1rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-size: 0.97rem;
  margin-bottom: 1rem;
  gap: 0.5em;
}
#reviewForm input[type="checkbox"],
.feedback__form input[type="checkbox"] {
  width: 1.1em;
  height: 1.1em;
}

.cookie-modal {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(30, 30, 30, 0.45);
  z-index: 5000;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.3s;
}
.cookie-modal__content {
  background: #fff;
  color: #222;
  border-radius: 14px;
  padding: 2.2em 1.5em 1.5em 1.5em;
  max-width: 95vw;
  width: 350px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
  text-align: center;
}
.cookie-modal__btn {
  background: #b2c9d6;
  color: #111;
  border: none;
  border-radius: 6px;
  padding: 0.7em 2em;
  font-size: 1.1rem;
  cursor: pointer;
  margin-top: 1.2em;
  transition: background 0.2s;
}
.cookie-modal__btn:hover {
  background: #222;
  color: #fff;
}
.cookie-modal[hidden] {
  display: none !important;
}

.footer__requisites-title {
  font-weight: 700;
  color: #fff;
  font-size: 1.08rem;
  margin-bottom: 0.4em;
  letter-spacing: 0.01em;
}
.footer__requisites-info {
  line-height: 1.5;
  font-size: 0.98rem;
}
@media (max-width: 700px) {
  .footer__requisites {
    padding: 0.8rem 0.7rem;
    font-size: 0.95rem;
    max-width: 98vw;
    margin-top: 1.2rem;
  }
  .footer__requisites-title {
    font-size: 1rem;
  }
}

@media (min-aspect-ratio: 16/9) {
  .video-bg {
    height: 100%;
  }
}

@media (max-aspect-ratio: 16/9) {
  .video-bg {
    width: 100%;
  }
}

/* Hero видео контейнер */
.hero-video-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.video-bg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  object-fit: cover;
  object-position: center;
  /* Safari/macOS specific fixes */
  -webkit-transform: translate(-50%, -50%);
  -webkit-object-fit: cover;
  -webkit-object-position: center;
}

@media (min-aspect-ratio: 16/9) {
  .video-bg {
    width: 100%;
    height: auto;
  }
}

@media (max-aspect-ratio: 16/9) {
  .video-bg {
    width: auto;
    height: 100%;
  }
}

/* Safari/macOS specific video fixes */
@media not all and (min-resolution: 0.001dpcm) {
  @supports (-webkit-appearance: none) {
    .video-bg {
      /* Safari-specific video positioning */
      -webkit-transform: translate(-50%, -50%) scale(1.01);
      transform: translate(-50%, -50%) scale(1.01);
      -webkit-object-fit: cover;
      object-fit: cover;
      -webkit-object-position: center center;
      object-position: center center;
    }

    .hero__bg--widescreen {
      /* Specific fixes for widescreen video on Safari */
      -webkit-transform: translate(-50%, -50%) scale(1.02);
      transform: translate(-50%, -50%) scale(1.02);
      min-width: 102%;
      min-height: 102%;
    }
  }
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
  .video-bg {
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    -webkit-object-fit: cover;
    object-fit: cover;
    width: 100vw;
    height: 100vh;
    min-width: 100vw;
    min-height: 100vh;
  }
}

/* Video Section */
.video-section {
  padding: 4rem 0;
}

.content-video-container {
  max-width: 800px;
  margin: 0 auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
}

.content-video-container video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Featured Cars Section */
.featured-cars {
  padding: 4rem 0;
  background-color: #f8f9fa;
}

.section-title {
  text-align: center;
  margin-bottom: 2rem;
  font-size: 2rem;
  font-weight: 700;
  color: #333;
}

.cars-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

.car-card {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.car-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.car-image {
  height: 200px;
  overflow: hidden;
}

.car-image img {
  width: 100%;
  height: 100%;
  object-fit: fill;
  transition: none;
}

.car-info {
  padding: 1.5rem;
}

.car-info h3 {
  margin: 0 0 0.5rem;
  font-size: 1.25rem;
  font-weight: 700;
}

.car-year,
.car-price {
  margin: 0.5rem 0;
  color: #666;
}

.car-price {
  font-weight: 700;
  color: #333;
  font-size: 1.2rem;
}

.car-button {
  display: inline-block;
  margin-top: 1rem;
  padding: 0.5rem 1.5rem;
  background-color: #2c3e50;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.car-button:hover {
  background-color: #1a252f;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* Обновленные стили для секций с центрированием */
.brands,
.stock,
.benefits,
.search,
.categories,
.reviews,
.contact {
  padding: 0;
}

.brands-container,
.stock-container,
.benefits-container,
.search-container,
.categories-container,
.reviews-container,
.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 60px 20px;
}

@media (max-width: 768px) {
  .brands-container,
  .stock-container,
  .benefits-container,
  .search-container,
  .categories-container,
  .reviews-container,
  .contact-container {
    padding: 40px 15px;
  }
}

@media (max-width: 480px) {
  .brands-container,
  .stock-container,
  .benefits-container,
  .search-container,
  .categories-container,
  .reviews-container,
  .contact-container {
    padding: 30px 10px;
  }
}

/* Media Queries */
@media (max-width: 768px) {
  .cars-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .content-video-container {
    max-width: 100%;
  }
}

@media (max-width: 576px) {
  .cars-grid {
    grid-template-columns: 1fr;
  }

  .section-title {
    font-size: 1.75rem;
  }
}

/* ====== Car Class Modal ====== */
.car-class-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.35);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.2s;
}
.car-class-modal[style*="display:none"] {
  display: none !important;
}
.car-class-modal__content {
  background: #fff;
  border-radius: 18px;
  padding: 2.5rem 2.5rem 2rem 2.5rem;
  min-width: 320px;
  max-width: 95vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
  position: relative;
}
.car-class-modal__close {
  position: absolute;
  top: 18px;
  right: 22px;
  background: none;
  border: none;
  font-size: 2.2rem;
  color: #888;
  cursor: pointer;
  transition: color 0.2s;
}
.car-class-modal__close:hover {
  color: #222;
}
#carClassModalTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.2rem;
  text-align: center;
}
#carClassModalList {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  justify-content: center;
}
.car-class-modal__item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fafbfc;
  border-radius: 12px;
  padding: 1rem 1.2rem;
  min-width: 180px;
  max-width: 220px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  text-align: center;
}
.car-class-modal__item-img {
  width: 120px;
  height: 80px;
  object-fit: contain;
  margin-bottom: 0.7rem;
  border-radius: 8px;
  background: #fff;
}
.car-class-modal__item-link {
  font-size: 1.08rem;
  font-weight: 500;
  color: #222;
  text-decoration: none;
  margin-bottom: 0.2rem;
  transition: color 0.18s;
}
.car-class-modal__item-link:hover {
  color: #6f9aab;
}
@media (max-width: 900px) {
  .car-class-modal__content {
    min-width: 0;
    width: 98vw;
    padding: 1.2rem 0.5rem;
  }
  #carClassModalList {
    gap: 0.7rem;
  }
}

.hero__bg--mobile {
  display: none !important;
}
.hero__bg--desktop {
  display: block !important;
}

@media (max-width: 600px) {
  .hero__bg--desktop {
    display: none !important;
  }
  .hero__bg--mobile {
    display: block !important;
  }
}

@media (min-width: 1200px) {
  .video-bg,
  .hero__bg {
    width: 100% !important;
    height: 100% !important;
    min-width: 0 !important;
    min-height: 0 !important;
    max-width: 100% !important;
    max-height: 100% !important;
    object-fit: cover !important;
    object-position: center center !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    margin: 0 !important;
    z-index: 1;
    background: #000;
    transition: none;
    transform: none !important;
  }
}

.category__image-container {
  background: #fff !important;
}
