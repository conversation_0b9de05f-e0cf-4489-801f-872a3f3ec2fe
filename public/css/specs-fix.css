/* Специальные стили для исправления таблицы характеристик */
body {
  margin: 0 !important;
  padding: 0 !important;
  background: #fff !important;
}

/* Общий контейнер */
.car-detail {
  width: 100% !important;
  max-width: 1140px !important;
  margin: 0 auto !important;
  position: relative !important;
  padding: 0 20px !important;
  box-sizing: border-box !important;
}

/* Общие стили для всех основных секций */
.car-detail__main-section {
  width: 100% !important;
  max-width: 1000px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  padding: 0 !important;
  box-sizing: border-box !important;
  overflow: visible !important;
  clear: both !important;
  position: relative !important;
}

/* Общие стили для заголовков секций */
.car-detail__section-title {
  font-size: 1.7rem !important;
  font-weight: 800 !important;
  margin-bottom: 32px !important;
}

/* Стили для центрирования и уменьшения карусели */
.car-detail__gallery {
  display: flex !important;
  justify-content: center !important;
  margin: 32px auto 0 !important;
  width: 100% !important;
  max-width: 1000px !important;
  padding: 0 !important;
}

.slider {
  position: relative !important;
  width: 100% !important;
  height: 500px !important;
  background-color: #f9f9f9 !important;
  border-radius: 6px !important;
  overflow: hidden !important;
  margin: 0 auto !important;
}

.car-detail__main-img {
  width: 100% !important;
  height: 100% !important;
  object-fit: fill !important;
  border-radius: 0 !important;
  background: #fff !important;
  display: none !important;
  margin: 0 auto !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
}

.car-detail__main-img.active {
  display: block !important;
}

/* Заголовок и кнопка */
.car-detail__headline-row {
  width: 100% !important;
  max-width: 1000px !important;
  margin: 48px auto 0 !important;
  padding: 0 !important;
  display: flex !important;
  align-items: flex-start !important;
  justify-content: space-between !important;
}

/* Секция характеристик */
.car-detail__specs-section {
  width: 100% !important;
  max-width: 1000px !important;
  margin: 64px auto 0 !important;
  padding: 0 !important;
  box-sizing: border-box !important;
  overflow: visible !important;
  clear: both !important;
  float: none !important;
  position: relative !important;
  left: 0 !important;
  display: block !important;
  top: 0 !important;
  transform: none !important;
  transition: none !important;
}

.car-detail__specs-section * {
  box-sizing: border-box !important;
}

.car-detail__specs-title {
  font-size: 1.7rem !important;
  font-weight: 800 !important;
  margin-bottom: 32px !important;
}

.car-detail__specs-content {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  overflow: visible !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;
}

.specs-table-container {
  width: 100% !important;
  margin-bottom: 30px !important;
}

.car-detail__specs-table {
  width: 100% !important;
  max-width: 100% !important;
  border-collapse: collapse !important;
  margin-bottom: 32px !important;
  table-layout: fixed !important;
  border-spacing: 0 !important;
}

.car-detail__specs-table td {
  padding: 12px 0 !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: normal !important;
  vertical-align: top !important;
  border-bottom: 1px solid #eee !important;
}

.car-detail__specs-table tr:last-child td {
  border-bottom: none !important;
}

.car-detail__specs-table td:first-child {
  width: 40% !important;
  padding-right: 20px !important;
  color: #666 !important;
  font-weight: 400 !important;
}

.car-detail__specs-table td:last-child {
  text-align: right !important;
  width: 60% !important;
  word-wrap: break-word !important;
  word-break: normal !important;
  padding-left: 20px !important;
  max-width: 60% !important;
  color: #000 !important;
  font-weight: 600 !important;
}

/* Унифицированные стили для всех фото в таблице характеристик */
.car-detail__specs-gallery {
  max-width: 100% !important;
  width: 100% !important;
  overflow: visible !important;
  display: flex !important;
  gap: 20px !important;
  justify-content: space-between !important;
  margin-top: 24px !important;
}

.car-detail__specs-img {
  width: calc(50% - 10px) !important;
  height: 250px !important;
  object-fit: fill !important;
  border-radius: 6px !important;
  display: block !important;
}

/* Секция "лучшее в авто" */
.car-detail__best-section {
  width: 100% !important;
  max-width: 1000px !important;
  margin: 64px auto 0 !important;
  padding: 0 !important;
  box-sizing: border-box !important;
  overflow: visible !important;
  clear: both !important;
  float: none !important;
  position: relative !important;
  left: 0 !important;
  display: block !important;
  top: 0 !important;
  transform: none !important;
  transition: none !important;
}

.car-detail__best-section * {
  box-sizing: border-box !important;
}

.car-detail__best-title {
  font-size: 1.7rem !important;
  font-weight: 800 !important;
  margin-bottom: 32px !important;
}

.car-detail__best-columns {
  display: flex !important;
  gap: 48px !important;
  margin-top: 32px !important;
  width: 100% !important;
  box-sizing: border-box !important;
  overflow: visible !important;
}

.car-detail__best-column {
  flex: 1 !important;
  position: relative !important;
  width: calc(50% - 24px) !important;
  box-sizing: border-box !important;
}

.car-detail__best-column:first-child:after {
  content: "" !important;
  position: absolute !important;
  right: -24px !important;
  top: 0 !important;
  height: 100% !important;
  width: 1px !important;
  background-color: #ddd !important;
}

.car-detail__best-column h4 {
  font-size: 1.1rem !important;
  font-weight: 500 !important;
  margin-bottom: 20px !important;
  color: #111 !important;
}

.car-detail__best-column ul {
  list-style: disc inside !important;
  padding-left: 5px !important;
  font-size: 1rem !important;
  color: #333 !important;
  line-height: 1.7 !important;
  margin: 0 !important;
}

/* Секция "Похожие предложения" */
.car-detail__similar-section {
  width: 100% !important;
  max-width: 1000px !important;
  margin: 64px auto 0 !important;
  padding: 0 !important;
}

.car-detail__similar-title {
  font-size: 1.7rem !important;
  font-weight: 800 !important;
  margin-bottom: 32px !important;
}

/* Унифицированные стили для изображений в секции "Похожие предложения" */
.car-detail__similar-card {
  width: calc(33.333% - 16px) !important;
  margin-bottom: 24px !important;
}

.car-detail__similar-img {
  width: 100% !important;
  height: 220px !important;
  object-fit: fill !important;
  border-radius: 6px !important;
  margin-bottom: 12px !important;
}

/* Секция описания */
.car-detail__desc-row {
  width: 100% !important;
  max-width: 1000px !important;
  margin: 64px auto 0 !important;
  padding: 0 !important;
  display: flex !important;
  align-items: flex-start !important;
  gap: 48px !important;
}

.car-detail__desc-title {
  font-size: 1.7rem !important;
  font-weight: 800 !important;
  margin-bottom: 32px !important;
}

/* Унифицированные стили для изображений в секции описания */
.car-detail__desc-gallery {
  display: flex !important;
  gap: 18px !important;
  margin-top: 24px !important;
  justify-content: space-between !important;
}

.car-detail__desc-img {
  width: calc(50% - 9px) !important;
  height: 250px !important;
  object-fit: fill !important;
  border-radius: 6px !important;
  display: block !important;
}

/* Медиа-запросы для адаптивности */
@media only screen and (max-width: 1200px) {
  .car-detail,
  .car-detail__gallery,
  .car-detail__headline-row,
  .car-detail__desc-row,
  .car-detail__specs-section,
  .car-detail__best-section,
  .car-detail__similar-section {
    width: 95% !important;
    max-width: 95% !important;
    margin-left: auto !important;
    margin-right: auto !important;
    padding: 0 15px !important;
  }

  .slider {
    height: 400px !important;
  }

  .car-detail__specs-gallery,
  .car-detail__desc-gallery {
    flex-direction: column !important;
    gap: 15px !important;
    margin-top: 20px !important;
  }

  .car-detail__specs-img,
  .car-detail__desc-img {
    width: 100% !important;
    height: 250px !important;
  }

  .car-detail__best-columns {
    flex-direction: column !important;
    gap: 32px !important;
  }

  .car-detail__best-column {
    width: 100% !important;
  }

  .car-detail__best-column:first-child:after {
    display: none !important;
  }

  .car-detail__similar-card {
    width: calc(50% - 12px) !important;
  }
}

@media only screen and (max-width: 768px) {
  .car-detail,
  .car-detail__gallery,
  .car-detail__headline-row,
  .car-detail__desc-row,
  .car-detail__specs-section,
  .car-detail__best-section,
  .car-detail__similar-section {
    max-width: 100% !important;
    width: 100% !important;
    margin-top: 40px !important;
    padding: 0 15px !important;
  }

  .slider {
    max-width: 100% !important;
    height: 350px !important;
  }

  .car-detail__specs-img,
  .car-detail__desc-img,
  .car-detail__similar-img {
    height: 230px !important;
  }

  .car-detail__specs-table td {
    padding: 10px 0 !important;
  }

  .car-detail__specs-table td:first-child {
    width: 45% !important;
    padding-right: 10px !important;
  }

  .car-detail__specs-table td:last-child {
    width: 55% !important;
    padding-left: 10px !important;
    max-width: 55% !important;
  }

  .car-detail__best-title,
  .car-detail__specs-title,
  .car-detail__similar-title,
  .car-detail__desc-title {
    font-size: 1.5rem !important;
    margin-bottom: 24px !important;
  }

  .car-detail__best-columns {
    gap: 32px !important;
  }

  .car-detail__best-column h4 {
    font-size: 1rem !important;
    margin-bottom: 15px !important;
  }

  .car-detail__headline-row {
    flex-direction: column !important;
  }

  .car-detail__desc-row {
    flex-direction: column !important;
    gap: 30px !important;
  }

  .car-detail__similar-card {
    width: 100% !important;
  }
}
