// Самый простой бургер-меню скрипт, который гарантированно работает
document.addEventListener('DOMContentLoaded', function() {
  console.log('Simple burger script loaded!');
  
  // Находим элементы
  const burger = document.getElementById('burgerBtn');
  const menu = document.getElementById('dropdownMenu');
  const body = document.body;
  const videoContainer = document.querySelector('.hero-video-container');
  const mobileHeader = document.querySelector('.mobile-header');
  
  // Проверяем, что они найдены
  if (!burger || !menu) {
    console.error('Burger button or menu not found!');
    return;
  }
  
  console.log('Burger and menu found, ready to work');
  
  // Функция для управления цветом бургера в зависимости от прокрутки
  function updateBurgerColor() {
    if (!videoContainer) {
      console.error('Video container not found!');
      return;
    }
    
    const videoContainerRect = videoContainer.getBoundingClientRect();
    const videoContainerBottom = videoContainerRect.bottom;
    
    // Если бургер находится в пределах видео-контейнера, то он белый
    // Иначе - черный
    if (videoContainerBottom > 0) {
      burger.classList.remove('dark');
      // Принудительно делаем полоски бургера белыми
      burger.querySelectorAll('span').forEach(span => {
        span.style.backgroundColor = '#fff !important';
        span.setAttribute('style', 'background-color: #fff !important');
      });
      // Убираем белый фон у хедера
      if (mobileHeader) {
        mobileHeader.classList.remove('scrolled');
      }
      console.log('Burger is white (in video area)');
    } else {
      burger.classList.add('dark');
      // Принудительно делаем полоски бургера черными
      burger.querySelectorAll('span').forEach(span => {
        span.style.backgroundColor = '#000 !important';
        span.setAttribute('style', 'background-color: #000 !important');
      });
      // Добавляем белый фон хедеру после прокрутки видео
      if (mobileHeader) {
        mobileHeader.classList.add('scrolled');
      }
      console.log('Burger is black (below video area)');
    }
  }
  
  // Вызываем функцию при загрузке страницы
  setTimeout(updateBurgerColor, 100); // Небольшая задержка для уверенности
  
  // Добавляем обработчик при прокрутке
  window.addEventListener('scroll', updateBurgerColor);
  
  // Принудительно вызываем обновление цвета при изменении размера окна
  window.addEventListener('resize', updateBurgerColor);
  
  // Добавляем обработчик клика на бургер
  burger.addEventListener('click', function(event) {
    event.stopPropagation(); // Предотвращаем всплытие клика
    console.log('Burger clicked!');
    
    // Переключаем класс для меню
    if (menu.classList.contains('active')) {
      console.log('Closing menu');
      menu.classList.remove('active');
      burger.classList.remove('open');
      menu.style.display = 'none';
      
      // Обновляем цвет после закрытия меню
      updateBurgerColor();
    } else {
      console.log('Opening menu');
      menu.classList.add('active');
      burger.classList.add('open');
      menu.style.display = 'flex';
    }
  });
  
  // Закрываем меню при клике вне его
  document.addEventListener('click', function(event) {
    if (menu.classList.contains('active') && 
        !menu.contains(event.target) && 
        event.target !== burger) {
      console.log('Clicked outside, closing menu');
      menu.classList.remove('active');
      burger.classList.remove('open');
      menu.style.display = 'none';
      
      // Обновляем цвет после закрытия меню
      updateBurgerColor();
    }
  });
  
  // Закрываем меню при клике на пункты меню
  const menuLinks = menu.querySelectorAll('a');
  menuLinks.forEach(link => {
    link.addEventListener('click', function() {
      console.log('Menu link clicked, closing menu');
      menu.classList.remove('active');
      burger.classList.remove('open');
      menu.style.display = 'none';
      
      // Обновляем цвет после закрытия меню
      updateBurgerColor();
    });
  });
  
  console.log('Simple burger script initialized');
}); 