<?php
// Скрипт для автоматической настройки обновления данных Encar на хостинге Reg.ru
// Запустите этот скрипт один раз после размещения сайта на хостинге

// Увеличиваем лимиты для обработки больших файлов
ini_set('max_execution_time', 1800); // 30 минут
ini_set('memory_limit', '1024M');    // 1 ГБ памяти
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Простой способ запустить скачивание: просто добавьте ?download=1 к URL
if (isset($_GET['download']) && $_GET['download'] == '1') {
    header('Content-Type: text/plain; charset=utf-8');
    echo "Запуск процесса скачивания данных Encar...\n\n";
    $result = downloadAndProcessEncarData();
    echo "Статус: " . ($result['success'] ? "Успешно" : "Ошибка") . "\n";
    if (isset($result['message'])) {
        echo "Сообщение: " . $result['message'] . "\n";
    }
    if (isset($result['file_size'])) {
        echo "Размер файла: " . $result['file_size'] . "\n";
    }
    if (isset($result['output']) && is_array($result['output'])) {
        echo "\nЖурнал операций:\n" . implode("\n", $result['output']) . "\n";
    }
    exit;
}

// Проверяем наличие директорий и создаем их если нужно
$dataDir = __DIR__ . '/encar_data/';
$cacheDir = __DIR__ . '/cache/';

if (!is_dir($dataDir)) {
    mkdir($dataDir, 0777, true);
}

if (!is_dir($cacheDir)) {
    mkdir($cacheDir, 0777, true);
}

// Проверяем доступность API
function testApiConnection() {
    $auth = 'Basic ' . base64_encode('admin:n2Q8ewyLft9qgPmim5ng');
    $host = 'https://autobase-wade.auto-parser.ru';
    $date = date('Y-m-d');
    $url = "{$host}/encar/{$date}/active_offer.csv";
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            'Authorization: ' . $auth
        ],
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => 0,
        CURLOPT_TIMEOUT => 10
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'success' => ($httpCode == 200),
        'code' => $httpCode,
        'size' => $response ? strlen($response) : 0
    ];
}

// Функция для скачивания и обработки данных Encar
function downloadAndProcessEncarData() {
    global $dataDir, $cacheDir;
    $logOutput = [];
    $startTime = microtime(true);
    $auth = 'Basic ' . base64_encode('admin:n2Q8ewyLft9qgPmim5ng');
    $host = 'https://autobase-wade.auto-parser.ru';
    $date = date('Y-m-d');
    $url = "{$host}/encar/{$date}/active_offer.csv";
    
    // Очистка старых данных
    $logOutput[] = "Удаление старых данных...";
    echo "🗑️  Удаление старых данных...\n";
    $oldFiles = glob($dataDir . '*.csv');
    foreach ($oldFiles as $file) {
        unlink($file);
    }

    // Очистка кэша
    $logOutput[] = "Очистка кэша...";
    echo "🧹 Очистка кэша...\n";
    $cacheFiles = glob($cacheDir . '*.json');
    foreach ($cacheFiles as $file) {
        unlink($file);
    }

    // Скачивание новых данных
    $logOutput[] = "Скачивание новых данных Encar...";
    echo "📡 Скачивание новых данных Encar...\n";
    $targetFile = $dataDir . 'encar_' . $date . '.csv';
    
    // Потоковая загрузка большого файла
    $fp = fopen($targetFile, 'w');
    if (!$fp) {
        return [
            'success' => false,
            'message' => "Не удалось создать файл: $targetFile",
            'output' => $logOutput
        ];
    }
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_HTTPHEADER => [
            'Authorization: ' . $auth
        ],
        CURLOPT_FILE => $fp,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => 0,
        CURLOPT_TIMEOUT => 1800 // 30 минут
    ]);
    
    $success = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $fileSize = curl_getinfo($ch, CURLINFO_SIZE_DOWNLOAD);
    
    curl_close($ch);
    fclose($fp);
    
    if (!$success || $httpCode != 200) {
        return [
            'success' => false,
            'message' => "Ошибка скачивания: " . ($error ?: "HTTP код $httpCode"),
            'output' => $logOutput
        ];
    }
    
    $logOutput[] = "Файл успешно скачан: " . round($fileSize / 1024 / 1024, 2) . " МБ";
    echo "✅ Файл успешно скачан: " . round($fileSize / 1024 / 1024, 2) . " МБ\n";

    // Создаем сводку данных
    $logOutput[] = "Создание сводки данных...";
    echo "📊 Создание сводки данных...\n";
    
    // Открываем файл для чтения
    $handle = fopen($targetFile, "r");
    if (!$handle) {
        return [
            'success' => false,
            'message' => "Не удалось открыть файл: $targetFile",
            'output' => $logOutput
        ];
    }
    
    // Читаем заголовок
    $headers = fgetcsv($handle, 0, "|");
    if (!$headers) {
        fclose($handle);
        return [
            'success' => false,
            'message' => "Не удалось прочитать заголовки CSV",
            'output' => $logOutput
        ];
    }
    
    // Собираем статистику
    $totalRows = 0;
    $brands = [];
    $models = [];
    
    // Ограничиваем чтение первыми 10000 строками для статистики
    $maxStatsRows = 10000;
    
    while (($data = fgetcsv($handle, 0, "|")) !== FALSE && $totalRows < $maxStatsRows) {
        if (count($data) != count($headers)) continue;
        
        $totalRows++;
        
        // Собираем статистику по маркам и моделям
        $row = array_combine($headers, $data);
        if (isset($row['mark'])) {
            $brand = $row['mark'];
            if (!isset($brands[$brand])) {
                $brands[$brand] = 0;
            }
            $brands[$brand]++;
        }
        
        if (isset($row['model'])) {
            $model = $row['model'];
            if (!isset($models[$model])) {
                $models[$model] = 0;
            }
            $models[$model]++;
        }
    }
    
    // Сохраняем общую информацию о статистике
    $statsFile = $dataDir . 'encar_stats_' . $date . '.json';
    $stats = [
        'date' => $date,
        'total_rows' => $totalRows,
        'top_brands' => array_slice($brands, 0, 20, true),
        'top_models' => array_slice($models, 0, 20, true),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    file_put_contents($statsFile, json_encode($stats, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    fclose($handle);
    
    $endTime = microtime(true);
    $executionTime = round($endTime - $startTime, 2);
    $logOutput[] = "Процесс обновления завершен за $executionTime секунд";
    echo "✅ Статистика сохранена!\n";
    echo "⏱️  Процесс обновления завершен за $executionTime секунд\n";
    
    return [
        'success' => true,
        'file_size' => round($fileSize / 1024 / 1024, 2) . " МБ",
        'rows_processed' => $totalRows,
        'execution_time' => $executionTime,
        'output' => $logOutput
    ];
}

// Автоматическое определение типа хостинга и создание cron
function detectHostingAndSetupCron() {
    $regRuCpanelDetected = false;
    $cpanelDetected = false;
    $results = [];
    
    // Проверяем наличие cPanel API
    if (file_exists('/usr/local/cpanel/version')) {
        $cpanelDetected = true;
        $results[] = 'Обнаружен хостинг на базе cPanel';
    }
    
    // Проверяем по доменам в виртуальных хостах
    if (function_exists('apache_get_modules') && in_array('mod_vhost_alias', apache_get_modules())) {
        $results[] = 'Обнаружен Apache с mod_vhost_alias (возможно REG.RU)';
        $regRuCpanelDetected = true;
    }
    
    // Проверяем общие пути REG.RU
    if (file_exists('/etc/regru')) {
        $regRuCpanelDetected = true;
        $results[] = 'Обнаружены файлы конфигурации REG.RU';
    }
    
    // Результаты определения хостинга
    return [
        'isRegRu' => $regRuCpanelDetected,
        'isCpanel' => $cpanelDetected,
        'details' => $results
    ];
}

// Функция для запуска скрипта обновления немедленно
function runUpdateNow() {
    try {
        // Вместо запуска внешнего скрипта, выполняем функцию обновления прямо здесь
        return downloadAndProcessEncarData();
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => "Ошибка: " . $e->getMessage(),
            'output' => ["Возникло исключение при обновлении данных"]
        ];
    }
}

// Создаем файл .htaccess для защиты CSV файлов
function secureDataDirectory() {
    $htaccessContent = <<<EOT
# Защищаем файлы CSV от прямого доступа
<FilesMatch "\.(csv)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Защищаем директорию от индексации
Options -Indexes
EOT;

    $dataDir = __DIR__ . '/encar_data/';
    $htaccessFile = $dataDir . '.htaccess';
    
    if (file_put_contents($htaccessFile, $htaccessContent)) {
        return true;
    }
    
    return false;
}

// Получить cron-команду для хостинга
function getCronCommand() {
    $scriptPath = __DIR__ . "/auto-setup.php";
    $phpPath = PHP_BINARY;
    
    // Обычно на хостингах PHP доступен в стандартном пути
    if (empty($phpPath) || !file_exists($phpPath)) {
        $phpPath = '/usr/bin/php';
    }
    
    return "$phpPath $scriptPath update";
}

// Генерация инструкций для REG.RU
function getRegRuInstructions() {
    $cronCommand = getCronCommand();
    
    return [
        'Войдите в панель управления REG.RU',
        'Перейдите в раздел "Хостинг" -> выберите нужный домен',
        'Выберите "Планировщик задач" или "Cron" в меню слева',
        'Создайте новое задание:',
        '- Периодичность выполнения: Ежедневно в 10:00',
        '- Команда: ' . $cronCommand,
        '- Email для отчётов: введите ваш email',
        'Нажмите "Добавить" или "Сохранить"'
    ];
}

// Проверяем командную строку для запуска обновления
if (isset($argv[1]) && $argv[1] == 'update') {
    echo "🚀 ЗАПУСК ОБНОВЛЕНИЯ ДАННЫХ ENCAR\n";
    echo "=====================================\n";
    echo "⏰ Время начала: " . date('Y-m-d H:i:s') . "\n";
    echo "📡 Подключение к API Encar...\n";
    echo "📥 Начинаем загрузку данных...\n\n";

    $result = runUpdateNow();

    echo "\n=====================================\n";
    echo "📊 РЕЗУЛЬТАТ ОБНОВЛЕНИЯ\n";
    echo "=====================================\n";
    echo "Статус: " . ($result['success'] ? "✅ Успешно" : "❌ Ошибка") . "\n";

    if (isset($result['message'])) {
        echo "Сообщение: " . $result['message'] . "\n";
    }

    if (isset($result['file_size'])) {
        echo "📁 Размер файла: " . $result['file_size'] . "\n";
    }

    if (isset($result['rows_processed'])) {
        echo "📋 Обработано строк: " . $result['rows_processed'] . "\n";
    }

    if (isset($result['execution_time'])) {
        echo "⏱️  Время выполнения: " . $result['execution_time'] . " сек\n";
    }

    if (isset($result['output']) && is_array($result['output'])) {
        echo "\n📝 ДЕТАЛЬНЫЙ ЛОГ:\n";
        echo "=====================================\n";
        foreach ($result['output'] as $line) {
            echo "• " . $line . "\n";
        }
    }

    echo "\n🎉 Обновление завершено!\n";
    exit;
}

// HTML вывод
header('Content-Type: text/html; charset=utf-8');

// Запускаем действия по запросу
$setupComplete = false;
$setupResult = null;
$updateResult = null;
$hostingInfo = null;
$apiTest = null;
$secureResult = false;

if (isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'detect_hosting':
            $hostingInfo = detectHostingAndSetupCron();
            break;
            
        case 'run_update':
            $updateResult = runUpdateNow();
            break;
            
        case 'test_api':
            $apiTest = testApiConnection();
            break;
            
        case 'secure':
            $secureResult = secureDataDirectory();
            break;
            
        case 'setup_all':
            // Полная автоматическая настройка
            $hostingInfo = detectHostingAndSetupCron();
            $apiTest = testApiConnection();
            $secureResult = secureDataDirectory();
            
            if ($apiTest['success']) {
                $updateResult = runUpdateNow();
                $setupComplete = $updateResult['success'];
            }
            break;
    }
}

?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Автоматическая настройка обновления Encar API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .card {
            background: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        .warning {
            color: orange;
        }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        button, .button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px 0;
            display: inline-block;
            text-decoration: none;
        }
        button:hover, .button:hover {
            background: #2980b9;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #3498db;
        }
        .steps {
            background: #fffde7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        ol li {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>Автоматическая настройка обновления данных Encar</h1>
    
    <?php if ($setupComplete): ?>
    <div class="card">
        <h2 class="success">Настройка успешно завершена!</h2>
        <p>Система обновления данных Encar API настроена и готова к работе.</p>
        <p>Что было сделано:</p>
        <ul>
            <li>Создана структура директорий для хранения данных</li>
            <li>Проверено соединение с API Encar</li>
            <li>Выполнено первичное скачивание данных</li>
            <li>Настроена защита данных от внешнего доступа</li>
        </ul>
        <p>Теперь вам осталось только настроить запуск скрипта по расписанию.</p>
    </div>
    <?php endif; ?>
    
    <div class="card">
        <h2>Быстрая настройка</h2>
        <p>Нажмите на кнопку ниже для автоматической настройки всех компонентов:</p>
        <form method="post">
            <input type="hidden" name="action" value="setup_all">
            <button type="submit">Выполнить полную настройку</button>
        </form>
    </div>
    
    <div class="card">
        <h2>Проверка соединения с API</h2>
        <p>Проверьте доступность API Encar:</p>
        <form method="post">
            <input type="hidden" name="action" value="test_api">
            <button type="submit">Проверить соединение</button>
        </form>
        
        <?php if ($apiTest): ?>
        <div class="result">
            <?php if ($apiTest['success']): ?>
            <p class="success">Соединение с API установлено успешно!</p>
            <p>Размер полученных данных: <?php echo round($apiTest['size'] / 1024, 2); ?> КБ</p>
            <?php else: ?>
            <p class="error">Ошибка соединения с API. Код HTTP: <?php echo $apiTest['code']; ?></p>
            <p>Проверьте доступность сервера API и правильность данных авторизации.</p>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
    
    <div class="card">
        <h2>Определение типа хостинга</h2>
        <p>Определите тип вашего хостинга для получения инструкций:</p>
        <form method="post">
            <input type="hidden" name="action" value="detect_hosting">
            <button type="submit">Определить хостинг</button>
        </form>
        
        <?php if ($hostingInfo): ?>
        <div class="result">
            <?php if (!empty($hostingInfo['details'])): ?>
                <h3>Результаты определения:</h3>
                <ul>
                    <?php foreach($hostingInfo['details'] as $detail): ?>
                    <li><?php echo $detail; ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
            
            <?php if ($hostingInfo['isRegRu']): ?>
            <h3>Инструкции для REG.RU:</h3>
            <ol>
                <?php foreach(getRegRuInstructions() as $step): ?>
                <li><?php echo $step; ?></li>
                <?php endforeach; ?>
            </ol>
            <?php else: ?>
            <p>Похоже, вы используете другой хостинг. Вот универсальные инструкции:</p>
            <p>Команда для планировщика задач: <code><?php echo getCronCommand(); ?></code></p>
            <p>Рекомендуемое расписание: ежедневно в 10:00</p>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
    
    <div class="card">
        <h2>Запуск обновления данных</h2>
        <p>Запустите процесс обновления данных прямо сейчас:</p>
        <form method="post">
            <input type="hidden" name="action" value="run_update">
            <button type="submit">Запустить обновление</button>
        </form>
        
        <?php if ($updateResult): ?>
        <div class="result">
            <?php if ($updateResult['success']): ?>
            <p class="success">Обновление данных успешно выполнено!</p>
            <?php else: ?>
            <p class="error">Ошибка при обновлении данных.</p>
            <?php endif; ?>
            <pre><?php echo $updateResult['output']; ?></pre>
        </div>
        <?php endif; ?>
    </div>
    
    <div class="card">
        <h2>Защита данных</h2>
        <p>Настройте защиту данных от прямого доступа:</p>
        <form method="post">
            <input type="hidden" name="action" value="secure">
            <button type="submit">Настроить защиту</button>
        </form>
        
        <?php if ($secureResult !== null): ?>
        <div class="result">
            <?php if ($secureResult): ?>
            <p class="success">Защита данных успешно настроена.</p>
            <?php else: ?>
            <p class="error">Ошибка при настройке защиты данных.</p>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
    
    <div class="card">
        <h2>Информация для ручной настройки</h2>
        <p>Если автоматическая настройка не подходит, используйте эти данные для ручной настройки на хостинге:</p>
        
        <h3>Команда для планировщика задач:</h3>
        <pre><?php echo getCronCommand(); ?></pre>
        
        <h3>Расписание:</h3>
        <p>Рекомендуется запускать скрипт ежедневно в 10:00</p>
        
        <h3>Общие шаги для REG.RU:</h3>
        <ol>
            <?php foreach(getRegRuInstructions() as $step): ?>
            <li><?php echo $step; ?></li>
            <?php endforeach; ?>
        </ol>
 