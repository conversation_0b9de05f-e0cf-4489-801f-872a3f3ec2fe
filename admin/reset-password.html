<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Сброс пароля | SHMS Auto</title>
    <link rel="stylesheet" href="css/login.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="icon" href="../public/favicon.ico" type="image/x-icon">
</head>
<body>
    <div class="login-container">
        <div class="login-background">
            <div class="login-overlay"></div>
        </div>
        
        <div class="login-content">
            <div class="login-card">
                <div class="login-header">
                    <div class="login-logo">
                        <i class="fas fa-lock"></i>
                        <h1>SHMS Auto</h1>
                    </div>
                    <h2>Сброс пароля</h2>
                    <p>Введите новый пароль для вашей учетной записи</p>
                </div>

                <form id="resetPasswordForm" class="login-form">
                    <input type="hidden" id="token" name="token">
                    
                    <div class="form-group">
                        <label for="newPassword">
                            <i class="fas fa-lock"></i>
                            Новый пароль
                        </label>
                        <div class="password-input">
                            <input 
                                type="password" 
                                id="newPassword" 
                                name="newPassword" 
                                required 
                                autocomplete="new-password"
                                placeholder="Введите новый пароль"
                            >
                            <button type="button" class="password-toggle" id="passwordToggle1">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="field-error" id="newPassword-error"></div>
                        <div class="password-requirements">
                            <small>
                                Пароль должен содержать минимум 8 символов, включая заглавные и строчные буквы, цифры и специальные символы
                            </small>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword">
                            <i class="fas fa-lock"></i>
                            Подтвердите пароль
                        </label>
                        <div class="password-input">
                            <input 
                                type="password" 
                                id="confirmPassword" 
                                name="confirmPassword" 
                                required 
                                autocomplete="new-password"
                                placeholder="Повторите новый пароль"
                            >
                            <button type="button" class="password-toggle" id="passwordToggle2">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="field-error" id="confirmPassword-error"></div>
                    </div>

                    <button type="submit" class="login-button" id="submitButton">
                        <span class="button-text">Сбросить пароль</span>
                        <span class="button-loader" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                    </button>

                    <div class="form-message" id="formMessage"></div>
                </form>

                <div class="login-footer">
                    <p>
                        <a href="login.html" class="forgot-password">
                            <i class="fas fa-arrow-left"></i>
                            Вернуться к входу
                        </a>
                    </p>
                    <div class="security-info">
                        <small>
                            <i class="fas fa-info-circle"></i>
                            После сброса пароля вы сможете войти с новыми учетными данными
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Уведомления -->
    <div id="notification" class="notification">
        <div class="notification-content">
            <div class="notification-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="notification-message">
                <div class="notification-title"></div>
                <div class="notification-text"></div>
            </div>
            <button class="notification-close">&times;</button>
        </div>
    </div>

    <script>
        // Система сброса пароля
        class ResetPasswordSystem {
            constructor() {
                this.form = document.getElementById('resetPasswordForm');
                this.tokenInput = document.getElementById('token');
                this.newPasswordInput = document.getElementById('newPassword');
                this.confirmPasswordInput = document.getElementById('confirmPassword');
                this.submitButton = document.getElementById('submitButton');
                this.formMessage = document.getElementById('formMessage');
                this.notification = document.getElementById('notification');
                
                this.init();
            }

            init() {
                this.extractTokenFromUrl();
                this.bindEvents();
                this.newPasswordInput.focus();
            }

            extractTokenFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                const token = urlParams.get('token');
                
                if (!token) {
                    this.showMessage('Недействительная ссылка для сброса пароля', 'error');
                    this.submitButton.disabled = true;
                    return;
                }
                
                this.tokenInput.value = token;
            }

            bindEvents() {
                this.form.addEventListener('submit', (e) => this.handleSubmit(e));
                
                // Валидация в реальном времени
                this.newPasswordInput.addEventListener('input', () => this.validatePassword());
                this.confirmPasswordInput.addEventListener('input', () => this.validatePasswordMatch());
                
                // Показать/скрыть пароли
                document.getElementById('passwordToggle1').addEventListener('click', () => 
                    this.togglePassword('newPassword', 'passwordToggle1'));
                document.getElementById('passwordToggle2').addEventListener('click', () => 
                    this.togglePassword('confirmPassword', 'passwordToggle2'));
                
                const notificationClose = this.notification.querySelector('.notification-close');
                if (notificationClose) {
                    notificationClose.addEventListener('click', () => this.hideNotification());
                }
            }

            async handleSubmit(e) {
                e.preventDefault();
                
                this.clearErrors();
                
                if (!this.validateForm()) {
                    return;
                }
                
                this.setLoading(true);
                
                try {
                    const formData = new FormData(this.form);
                    const requestData = {
                        token: formData.get('token'),
                        newPassword: formData.get('newPassword'),
                        confirmPassword: formData.get('confirmPassword')
                    };
                    
                    const response = await fetch('/api/auth/reset-password', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        credentials: 'include',
                        body: JSON.stringify(requestData)
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        this.showMessage(data.message, 'success');
                        this.form.reset();
                        
                        // Перенаправляем на страницу входа через 3 секунды
                        setTimeout(() => {
                            window.location.href = 'login.html';
                        }, 3000);
                        
                    } else {
                        if (data.details && Array.isArray(data.details)) {
                            // Показываем детальные ошибки валидации пароля
                            this.showFieldError('newPassword', data.details.join(', '));
                        } else {
                            this.showMessage(data.error || 'Произошла ошибка при сбросе пароля', 'error');
                        }
                    }
                    
                } catch (error) {
                    console.error('Ошибка при сбросе пароля:', error);
                    this.showMessage('Ошибка соединения с сервером. Попробуйте позже.', 'error');
                } finally {
                    this.setLoading(false);
                }
            }

            validateForm() {
                let isValid = true;
                
                if (!this.validatePassword()) {
                    isValid = false;
                }
                
                if (!this.validatePasswordMatch()) {
                    isValid = false;
                }
                
                return isValid;
            }

            validatePassword() {
                const password = this.newPasswordInput.value;
                this.clearFieldError('newPassword');
                
                if (!password) {
                    this.showFieldError('newPassword', 'Пароль обязателен для заполнения');
                    return false;
                }
                
                const errors = [];
                
                if (password.length < 8) {
                    errors.push('минимум 8 символов');
                }
                
                if (!/[a-z]/.test(password)) {
                    errors.push('строчные буквы');
                }
                
                if (!/[A-Z]/.test(password)) {
                    errors.push('заглавные буквы');
                }
                
                if (!/\d/.test(password)) {
                    errors.push('цифры');
                }
                
                if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
                    errors.push('специальные символы');
                }
                
                if (errors.length > 0) {
                    this.showFieldError('newPassword', `Пароль должен содержать: ${errors.join(', ')}`);
                    return false;
                }
                
                return true;
            }

            validatePasswordMatch() {
                const password = this.newPasswordInput.value;
                const confirmPassword = this.confirmPasswordInput.value;
                
                this.clearFieldError('confirmPassword');
                
                if (!confirmPassword) {
                    this.showFieldError('confirmPassword', 'Подтверждение пароля обязательно');
                    return false;
                }
                
                if (password !== confirmPassword) {
                    this.showFieldError('confirmPassword', 'Пароли не совпадают');
                    return false;
                }
                
                return true;
            }

            togglePassword(inputId, toggleId) {
                const input = document.getElementById(inputId);
                const toggle = document.getElementById(toggleId);
                const icon = toggle.querySelector('i');
                
                const type = input.type === 'password' ? 'text' : 'password';
                input.type = type;
                icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
            }

            showFieldError(fieldName, message) {
                const errorElement = document.getElementById(`${fieldName}-error`);
                const fieldElement = document.getElementById(fieldName);
                
                if (errorElement) {
                    errorElement.textContent = message;
                }
                
                if (fieldElement) {
                    fieldElement.style.borderColor = 'var(--danger)';
                }
            }

            clearFieldError(fieldName) {
                const errorElement = document.getElementById(`${fieldName}-error`);
                const fieldElement = document.getElementById(fieldName);
                
                if (errorElement) {
                    errorElement.textContent = '';
                }
                
                if (fieldElement) {
                    fieldElement.style.borderColor = '';
                }
            }

            clearErrors() {
                this.clearFieldError('newPassword');
                this.clearFieldError('confirmPassword');
                this.hideMessage();
            }

            showMessage(message, type = 'error') {
                this.formMessage.textContent = message;
                this.formMessage.className = `form-message ${type}`;
                this.formMessage.style.display = 'block';
            }

            hideMessage() {
                this.formMessage.style.display = 'none';
            }

            hideNotification() {
                this.notification.classList.remove('show');
            }

            setLoading(loading) {
                const buttonText = this.submitButton.querySelector('.button-text');
                const buttonLoader = this.submitButton.querySelector('.button-loader');
                
                if (loading) {
                    buttonText.style.display = 'none';
                    buttonLoader.style.display = 'inline-block';
                    this.submitButton.disabled = true;
                } else {
                    buttonText.style.display = 'inline-block';
                    buttonLoader.style.display = 'none';
                    this.submitButton.disabled = false;
                }
            }
        }

        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', () => {
            new ResetPasswordSystem();
        });
    </script>

    <style>
        .password-requirements {
            margin-top: 0.5rem;
        }
        
        .password-requirements small {
            color: var(--text-light);
            font-size: 0.8rem;
            line-height: 1.4;
        }
    </style>
</body>
</html>
