===============================================
🟠 ИСПРАВЛЕНИЕ КНОПКИ "СНЯТЬ С ПУБЛИКАЦИИ"
===============================================

❌ ПРОБЛЕМА:
   Кнопка "Снять с публикации" была белая на белом фоне - не видна

✅ ИСПРАВЛЕНО:

🎨 Добавлены переменные CSS для warning цветов:
   --warning: #f39c12 (оранжевый)
   --warning-hover: #e67e22 (темнее оранжевый)
   --warning-gradient: linear-gradient(135deg, #f39c12, #e67e22)

🔧 Файлы изменены:
   - admin/css/styles.css (строки 20-26)
   - app/admin/css/styles.css (строки 20-26)

📍 Кнопка использует класс: .page-action-btn.unpublish

===============================================

⚡ РЕЗУЛЬТАТ:

✅ Кнопка "Снять с публикации" теперь:
   - Оранжевая с белым текстом (#f39c12)
   - Хорошо видна на любом фоне
   - Темнеет при наведении (#e67e22)
   - Поднимается при hover эффекте
   - Имеет иконку глаза с перечеркиванием

🎯 Цветовая схема кнопок:
   - 🟢 Опубликовать: зеленый (--success)
   - 🟠 Снять с публикации: оранжевый (--warning)
   - 🔴 Удалить: красный (--danger)
   - 🔵 Обновить HTML: синий (--secondary)

===============================================

💡 ДОПОЛНИТЕЛЬНО:

Использованы стандартные warning цвета:
   - Основной: #f39c12 (Material Design Orange)
   - Hover: #e67e22 (более темный оранжевый)
   - Градиент: от светлого к темному

Эффекты:
   - Плавные переходы (var(--transition))
   - Подъем на 1px при наведении
   - Соответствует общему стилю админки

===============================================

🔍 РАСПОЛОЖЕНИЕ КНОПКИ:

Кнопка появляется в списке существующих страниц:
   - Только для опубликованных страниц
   - В разделе "Существующие страницы"
   - Рядом с кнопками "Обновить HTML" и "Удалить"

Функция: unpublishPage(pageId)
Действие: Переводит страницу в статус "draft"

===============================================
