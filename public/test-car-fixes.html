<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Тест исправлений поиска автомобилей</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 10px 10px 0;
            transition: background 0.3s;
        }
        
        .test-button:hover {
            background: #2980b9;
        }
        
        .test-button.success {
            background: #27ae60;
        }
        
        .test-button.error {
            background: #e74c3c;
        }
        
        .results {
            margin-top: 20px;
            padding: 16px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        
        .results.success {
            background: #d5f4e6;
            border: 1px solid #27ae60;
            color: #1e8449;
        }
        
        .results.error {
            background: #fadbd8;
            border: 1px solid #e74c3c;
            color: #c0392b;
        }
        
        .results.info {
            background: #d6eaf8;
            border: 1px solid #3498db;
            color: #2471a3;
        }
        
        .summary {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin: 30px 0;
        }
        
        .summary h2 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        .test-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <h1>🔧 Тест исправлений поиска автомобилей</h1>
    <p>Этот тест проверяет исправления в системе поиска автомобилей:</p>
    <ul>
        <li>✅ Фильтрация цен 999999 и 333333 вон (забронированные/лизинговые)</li>
        <li>✅ Исключение бюджетных брендов (Kia, Hyundai, Chevrolet) из премиум категорий</li>
        <li>✅ Корректность параметра date во всех запросах</li>
        <li>✅ Улучшенная логика поиска по всем параметрам</li>
    </ul>

    <div class="test-section">
        <h2 class="test-title">1. Тест фильтрации забронированных автомобилей</h2>
        <p>Проверяем, что автомобили с ценами 999999 и 333333 вон не отображаются</p>
        <button class="test-button" onclick="testReservedCars()">Запустить тест</button>
        <div id="reserved-results" class="results" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h2 class="test-title">2. Тест исключения бюджетных брендов из бизнес-класса</h2>
        <p>Проверяем, что Kia, Hyundai, Chevrolet не попадают в категорию business</p>
        <button class="test-button" onclick="testBusinessCategory()">Запустить тест</button>
        <div id="business-results" class="results" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h2 class="test-title">3. Тест исключения бюджетных брендов из спортивной категории</h2>
        <p>Проверяем, что Kia, Hyundai, Chevrolet не попадают в категорию sport</p>
        <button class="test-button" onclick="testSportCategory()">Запустить тест</button>
        <div id="sport-results" class="results" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h2 class="test-title">4. Тест исключения бюджетных брендов из SUV категории</h2>
        <p>Проверяем, что Kia, Hyundai, Chevrolet не попадают в категорию suv</p>
        <button class="test-button" onclick="testSuvCategory()">Запустить тест</button>
        <div id="suv-results" class="results" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h2 class="test-title">5. Тест поиска по брендам</h2>
        <p>Проверяем, что поиск по брендам работает корректно</p>
        <button class="test-button" onclick="testBrandSearch()">Запустить тест</button>
        <div id="brand-results" class="results" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h2 class="test-title">6. Тест параметра date</h2>
        <p>Проверяем, что параметр date корректно передается во всех запросах</p>
        <button class="test-button" onclick="testDateParameter()">Запустить тест</button>
        <div id="date-results" class="results" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h2 class="test-title">🚀 Запустить все тесты</h2>
        <p>Выполнить полную проверку всех исправлений</p>
        <button class="test-button" onclick="runAllTests()" style="background: #8e44ad;">Запустить все тесты</button>
        <div id="all-results" class="results" style="display:none;"></div>
    </div>

    <div class="summary" id="test-summary" style="display:none;">
        <h2>📊 Сводка результатов тестирования</h2>
        <div class="test-stats">
            <div class="stat-item">
                <span class="stat-number" id="total-tests">0</span>
                <span class="stat-label">Всего тестов</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="passed-tests">0</span>
                <span class="stat-label">Пройдено</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="failed-tests">0</span>
                <span class="stat-label">Провалено</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="success-rate">0%</span>
                <span class="stat-label">Успешность</span>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api/encar-proxy.php'; // Используем относительный путь для корректной работы
        const BUDGET_BRANDS = ['Kia', 'Hyundai', 'Chevrolet', 'Daewoo', 'Lada', 'Dacia', 'Skoda', 'SEAT'];
        
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0
        };

        function getCurrentDate() {
            return new Date().toISOString().split('T')[0];
        }

        function showResults(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = message;
            element.className = `results ${type}`;
        }

        function updateButton(buttonElement, success) {
            if (success) {
                buttonElement.className = 'test-button success';
                buttonElement.textContent = buttonElement.textContent.replace('Запустить', '✅ Пройден');
            } else {
                buttonElement.className = 'test-button error';
                buttonElement.textContent = buttonElement.textContent.replace('Запустить', '❌ Провален');
            }
        }

        function updateTestStats() {
            document.getElementById('total-tests').textContent = testResults.total;
            document.getElementById('passed-tests').textContent = testResults.passed;
            document.getElementById('failed-tests').textContent = testResults.failed;
            
            const successRate = testResults.total > 0 ? 
                Math.round((testResults.passed / testResults.total) * 100) : 0;
            document.getElementById('success-rate').textContent = successRate + '%';
            
            document.getElementById('test-summary').style.display = 'block';
        }

        // Тест 1: Фильтрация забронированных автомобилей
        async function testReservedCars() {
            showResults('reserved-results', '🔄 Загрузка...', 'info');
            testResults.total++;

            try {
                const response = await fetch(`${API_BASE}?limit=50&debug=1&date=${getCurrentDate()}`);
                const data = await response.json();
                
                if (!Array.isArray(data)) {
                    throw new Error('Ответ не является массивом');
                }
                
                // Проверяем наличие автомобилей с проблемными ценами
                const reservedCars = data.filter(car => {
                    const price = parseInt(car.price || 0);
                    return price === 999999 || price === 333333;
                });
                
                if (reservedCars.length === 0) {
                    showResults('reserved-results', 
                        `✅ ТЕСТ ПРОЙДЕН!\n\nПроверено автомобилей: ${data.length}\nЗабронированных автомобилей: 0\n\nФильтрация работает корректно!`, 
                        'success');
                    updateButton(event.target, true);
                    testResults.passed++;
                } else {
                    showResults('reserved-results', 
                        `❌ ТЕСТ ПРОВАЛЕН!\n\nНайдено ${reservedCars.length} забронированных автомобилей:\n${reservedCars.map(car => `ID: ${car.id}, Цена: ${car.price}`).join('\n')}`, 
                        'error');
                    updateButton(event.target, false);
                    testResults.failed++;
                }
                
            } catch (error) {
                showResults('reserved-results', `❌ ОШИБКА: ${error.message}`, 'error');
                updateButton(event.target, false);
                testResults.failed++;
            }
            
            updateTestStats();
        }

        // Тест 2: Исключение бюджетных брендов из бизнес-класса
        async function testBusinessCategory() {
            showResults('business-results', '🔄 Загрузка...', 'info');
            testResults.total++;

            try {
                const response = await fetch(`${API_BASE}?category=business&limit=30&date=${getCurrentDate()}`);
                const data = await response.json();

                if (!Array.isArray(data)) {
                    throw new Error('Ответ не является массивом');
                }

                const budgetCars = data.filter(car => {
                    const brand = (car.brand || car.mark || '').toLowerCase();
                    return BUDGET_BRANDS.some(budgetBrand =>
                        brand.includes(budgetBrand.toLowerCase())
                    );
                });

                if (budgetCars.length === 0) {
                    showResults('business-results',
                        `✅ ТЕСТ ПРОЙДЕН!\n\nПроверено автомобилей: ${data.length}\nБюджетных брендов в бизнес-классе: 0\n\nИсключение работает корректно!`,
                        'success');
                    updateButton(event.target, true);
                    testResults.passed++;
                } else {
                    showResults('business-results',
                        `❌ ТЕСТ ПРОВАЛЕН!\n\nНайдено ${budgetCars.length} бюджетных автомобилей в бизнес-классе:\n${budgetCars.map(car => `${car.brand || car.mark} ${car.model}`).join('\n')}`,
                        'error');
                    updateButton(event.target, false);
                    testResults.failed++;
                }

            } catch (error) {
                showResults('business-results', `❌ ОШИБКА: ${error.message}`, 'error');
                updateButton(event.target, false);
                testResults.failed++;
            }

            updateTestStats();
        }

        // Тест 3: Исключение бюджетных брендов из спортивной категории
        async function testSportCategory() {
            showResults('sport-results', '🔄 Загрузка...', 'info');
            testResults.total++;

            try {
                const response = await fetch(`${API_BASE}?category=sport&limit=30&date=${getCurrentDate()}`);
                const data = await response.json();

                if (!Array.isArray(data)) {
                    throw new Error('Ответ не является массивом');
                }

                const budgetCars = data.filter(car => {
                    const brand = (car.brand || car.mark || '').toLowerCase();
                    return BUDGET_BRANDS.some(budgetBrand =>
                        brand.includes(budgetBrand.toLowerCase())
                    );
                });

                if (budgetCars.length === 0) {
                    showResults('sport-results',
                        `✅ ТЕСТ ПРОЙДЕН!\n\nПроверено автомобилей: ${data.length}\nБюджетных брендов в спортивной категории: 0\n\nИсключение работает корректно!`,
                        'success');
                    updateButton(event.target, true);
                    testResults.passed++;
                } else {
                    showResults('sport-results',
                        `❌ ТЕСТ ПРОВАЛЕН!\n\nНайдено ${budgetCars.length} бюджетных автомобилей в спортивной категории:\n${budgetCars.map(car => `${car.brand || car.mark} ${car.model}`).join('\n')}`,
                        'error');
                    updateButton(event.target, false);
                    testResults.failed++;
                }

            } catch (error) {
                showResults('sport-results', `❌ ОШИБКА: ${error.message}`, 'error');
                updateButton(event.target, false);
                testResults.failed++;
            }

            updateTestStats();
        }

        // Тест 4: Исключение бюджетных брендов из SUV категории
        async function testSuvCategory() {
            showResults('suv-results', '🔄 Загрузка...', 'info');
            testResults.total++;

            try {
                const response = await fetch(`${API_BASE}?category=suv&limit=30&date=${getCurrentDate()}`);
                const data = await response.json();

                if (!Array.isArray(data)) {
                    throw new Error('Ответ не является массивом');
                }

                const budgetCars = data.filter(car => {
                    const brand = (car.brand || car.mark || '').toLowerCase();
                    return BUDGET_BRANDS.some(budgetBrand =>
                        brand.includes(budgetBrand.toLowerCase())
                    );
                });

                if (budgetCars.length === 0) {
                    showResults('suv-results',
                        `✅ ТЕСТ ПРОЙДЕН!\n\nПроверено автомобилей: ${data.length}\nБюджетных брендов в SUV категории: 0\n\nИсключение работает корректно!`,
                        'success');
                    updateButton(event.target, true);
                    testResults.passed++;
                } else {
                    showResults('suv-results',
                        `❌ ТЕСТ ПРОВАЛЕН!\n\nНайдено ${budgetCars.length} бюджетных автомобилей в SUV категории:\n${budgetCars.map(car => `${car.brand || car.mark} ${car.model}`).join('\n')}`,
                        'error');
                    updateButton(event.target, false);
                    testResults.failed++;
                }

            } catch (error) {
                showResults('suv-results', `❌ ОШИБКА: ${error.message}`, 'error');
                updateButton(event.target, false);
                testResults.failed++;
            }

            updateTestStats();
        }

        // Тест 5: Поиск по брендам
        async function testBrandSearch() {
            showResults('brand-results', '🔄 Загрузка...', 'info');
            testResults.total++;

            try {
                const testBrands = ['BMW', 'Mercedes-Benz', 'Audi'];
                let allTestsPassed = true;
                let results = [];

                for (const brand of testBrands) {
                    const response = await fetch(`${API_BASE}?brand=${encodeURIComponent(brand)}&limit=10&date=${getCurrentDate()}`);
                    const data = await response.json();

                    if (!Array.isArray(data)) {
                        throw new Error(`Ответ для бренда ${brand} не является массивом`);
                    }

                    const correctBrandCars = data.filter(car => {
                        const carBrand = (car.brand || car.mark || '').toLowerCase();
                        return carBrand.includes(brand.toLowerCase());
                    });

                    const accuracy = data.length > 0 ? (correctBrandCars.length / data.length) * 100 : 100;
                    results.push(`${brand}: ${correctBrandCars.length}/${data.length} (${accuracy.toFixed(1)}%)`);

                    if (accuracy < 80) {
                        allTestsPassed = false;
                    }
                }

                if (allTestsPassed) {
                    showResults('brand-results',
                        `✅ ТЕСТ ПРОЙДЕН!\n\nРезультаты поиска по брендам:\n${results.join('\n')}\n\nПоиск работает корректно!`,
                        'success');
                    updateButton(event.target, true);
                    testResults.passed++;
                } else {
                    showResults('brand-results',
                        `❌ ТЕСТ ПРОВАЛЕН!\n\nРезультаты поиска по брендам:\n${results.join('\n')}\n\nТочность поиска ниже 80%!`,
                        'error');
                    updateButton(event.target, false);
                    testResults.failed++;
                }

            } catch (error) {
                showResults('brand-results', `❌ ОШИБКА: ${error.message}`, 'error');
                updateButton(event.target, false);
                testResults.failed++;
            }

            updateTestStats();
        }

        // Тест 6: Параметр date
        async function testDateParameter() {
            showResults('date-results', '🔄 Загрузка...', 'info');
            testResults.total++;

            try {
                // Тест без параметра date
                const responseWithoutDate = await fetch(`${API_BASE}?limit=5`);
                const dataWithoutDate = await responseWithoutDate.json();

                // Тест с параметром date
                const responseWithDate = await fetch(`${API_BASE}?limit=5&date=${getCurrentDate()}`);
                const dataWithDate = await responseWithDate.json();

                if (Array.isArray(dataWithoutDate) && Array.isArray(dataWithDate)) {
                    showResults('date-results',
                        `✅ ТЕСТ ПРОЙДЕН!\n\nБез date: ${dataWithoutDate.length} автомобилей\nС date: ${dataWithDate.length} автомобилей\n\nПараметр date обрабатывается корректно!`,
                        'success');
                    updateButton(event.target, true);
                    testResults.passed++;
                } else {
                    showResults('date-results',
                        `❌ ТЕСТ ПРОВАЛЕН!\n\nОдин из запросов вернул некорректный ответ`,
                        'error');
                    updateButton(event.target, false);
                    testResults.failed++;
                }

            } catch (error) {
                showResults('date-results', `❌ ОШИБКА: ${error.message}`, 'error');
                updateButton(event.target, false);
                testResults.failed++;
            }

            updateTestStats();
        }

        // Запуск всех тестов
        async function runAllTests() {
            showResults('all-results', '🔄 Запуск всех тестов...', 'info');

            // Сброс статистики
            testResults = { total: 0, passed: 0, failed: 0 };

            // Сброс кнопок
            document.querySelectorAll('.test-button').forEach(btn => {
                btn.className = 'test-button';
                btn.textContent = btn.textContent.replace(/✅ Пройден|❌ Провален/, 'Запустить');
            });

            try {
                await testReservedCars();
                await new Promise(resolve => setTimeout(resolve, 500));

                await testBusinessCategory();
                await new Promise(resolve => setTimeout(resolve, 500));

                await testSportCategory();
                await new Promise(resolve => setTimeout(resolve, 500));

                await testSuvCategory();
                await new Promise(resolve => setTimeout(resolve, 500));

                await testBrandSearch();
                await new Promise(resolve => setTimeout(resolve, 500));

                await testDateParameter();

                const successRate = (testResults.passed / testResults.total) * 100;

                if (successRate === 100) {
                    showResults('all-results',
                        `🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!\n\nВсего тестов: ${testResults.total}\nПройдено: ${testResults.passed}\nПровалено: ${testResults.failed}\nУспешность: ${successRate.toFixed(1)}%\n\nСистема поиска автомобилей работает корректно!`,
                        'success');
                    updateButton(event.target, true);
                } else {
                    showResults('all-results',
                        `⚠️ НЕКОТОРЫЕ ТЕСТЫ ПРОВАЛЕНЫ\n\nВсего тестов: ${testResults.total}\nПройдено: ${testResults.passed}\nПровалено: ${testResults.failed}\nУспешность: ${successRate.toFixed(1)}%\n\nТребуются дополнительные исправления.`,
                        'error');
                    updateButton(event.target, false);
                }

            } catch (error) {
                showResults('all-results', `❌ ОШИБКА ПРИ ВЫПОЛНЕНИИ ТЕСТОВ: ${error.message}`, 'error');
                updateButton(event.target, false);
            }
        }
    </script>
</body>
</html>
