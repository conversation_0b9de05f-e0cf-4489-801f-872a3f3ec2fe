#!/bin/bash

# Скрипт развертывания SHMS Авто для GitLab CI/CD
# Адаптированная версия deploy-regru.sh для автоматического развертывания

set -e  # Остановка при ошибке

echo "🚀 GitLab CI/CD: Развертывание SHMS Авто"
echo "========================================"

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Функции для вывода сообщений
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

# Проверка Node.js
check_nodejs() {
    log "Проверка Node.js..."
    if ! command -v node &> /dev/null; then
        error "Node.js не установлен!"
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 16 ]; then
        error "Требуется Node.js версии 16 или выше. Текущая версия: $(node -v)"
    fi
    
    log "✅ Node.js $(node -v) готов"
}

# Проверка PM2
check_pm2() {
    log "Проверка PM2..."
    if ! command -v pm2 &> /dev/null; then
        log "Установка PM2..."
        npm install -g pm2
    fi
    log "✅ PM2 готов"
}

# Установка зависимостей
install_dependencies() {
    log "Установка зависимостей..."
    npm ci --production --silent
    log "✅ Зависимости установлены"
}

# Создание необходимых директорий
create_directories() {
    log "Создание директорий..."
    
    mkdir -p logs
    mkdir -p data/uploads
    mkdir -p data/cache
    
    # Установка прав доступа
    chmod 755 data/uploads
    chmod 755 data/cache
    chmod 755 logs
    
    if [ -f "data/stock.db" ]; then
        chmod 644 data/stock.db
    fi
    
    log "✅ Директории готовы"
}

# Проверка конфигурации
check_config() {
    log "Проверка конфигурации..."
    
    if [ ! -f ".env" ]; then
        warn ".env файл не найден, создаем из шаблона..."
        cp .env.example .env
        warn "⚠️  ВАЖНО: .env файл создан из шаблона, проверьте настройки!"
    fi
    
    # Проверка обязательных переменных
    required_vars=("MAIL_USER" "MAIL_PASS" "CONTACT_EMAIL" "SESSION_SECRET")
    missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" .env || grep -q "^$var=.*замените\|^$var=.*your_\|^$var=$" .env; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        warn "Не настроены переменные в .env: ${missing_vars[*]}"
        warn "Продолжаем развертывание, но проверьте настройки!"
    fi
    
    log "✅ Конфигурация проверена"
}

# Остановка старых процессов
stop_old_processes() {
    log "Остановка старых процессов..."
    
    # Остановка PM2 процессов
    pm2 delete shms-auto 2>/dev/null || true
    
    # Остановка процессов на порту 3001
    if lsof -ti:3001 >/dev/null 2>&1; then
        log "Завершение процессов на порту 3001..."
        lsof -ti:3001 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    log "✅ Старые процессы остановлены"
}

# Запуск через PM2
start_pm2() {
    log "Запуск через PM2..."
    
    # Запуск нового процесса
    pm2 start ecosystem.config.js --env production
    
    # Сохранение конфигурации PM2
    pm2 save
    
    # Настройка автозапуска (только если еще не настроено)
    if ! pm2 startup | grep -q "already"; then
        pm2 startup
    fi
    
    log "✅ Приложение запущено через PM2"
}

# Проверка работоспособности
health_check() {
    log "Проверка работоспособности..."
    
    # Ждем запуска сервера
    sleep 5
    
    # Проверка статуса PM2
    if pm2 list | grep -q "shms-auto.*online"; then
        log "✅ PM2 процесс запущен"
    else
        error "PM2 процесс не запущен"
    fi
    
    # Проверка доступности HTTP
    max_attempts=10
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:3001/test >/dev/null 2>&1; then
            log "✅ Сервер отвечает на запросы"
            break
        else
            if [ $attempt -eq $max_attempts ]; then
                error "Сервер не отвечает после $max_attempts попыток"
            fi
            log "Попытка $attempt/$max_attempts: ожидание запуска сервера..."
            sleep 3
            ((attempt++))
        fi
    done
}

# Очистка временных файлов
cleanup() {
    log "Очистка временных файлов..."
    
    # Очистка старых логов (старше 7 дней)
    find logs/ -name "*.log" -mtime +7 -delete 2>/dev/null || true
    
    # Очистка npm кэша
    npm cache clean --force >/dev/null 2>&1 || true
    
    log "✅ Очистка завершена"
}

# Основная функция
main() {
    log "Начало CI/CD развертывания..."
    
    # Переход в директорию приложения
    cd "$(dirname "$0")/.."
    
    check_nodejs
    check_pm2
    check_config
    stop_old_processes
    install_dependencies
    create_directories
    start_pm2
    health_check
    cleanup
    
    log "✅ CI/CD развертывание завершено успешно!"
    
    echo ""
    echo "🎉 SHMS Авто успешно развернут!"
    echo "================================"
    echo "📡 Локальный адрес: http://localhost:3001"
    echo "🌐 Публичный адрес: https://shms-auto.ru"
    echo ""
    echo "📋 Полезные команды:"
    echo "  pm2 list              - список процессов"
    echo "  pm2 logs shms-auto    - просмотр логов"
    echo "  pm2 restart shms-auto - перезапуск"
    echo "  npm run health-check  - проверка здоровья"
}

# Обработка сигналов для корректного завершения
trap 'error "Развертывание прервано"' INT TERM

# Запуск основной функции
main "$@"
