<!DOCTYPE html>
<html lang="ru">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>Добро пожаловать в SHMS - лучший сервис автоподбора!</title>
  <style>
    /* Глобальное устранение отступов */
    *,
    *::before,
    *::after {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      background: rgba(0, 0, 0, 0.5);
      min-height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #333;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 10000;
    }

    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
    }

    .container {
      background: white;
      border-radius: 12px;
      padding: 40px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
      text-align: center;
      max-width: 600px;
      width: 90%;
      max-height: 90vh;
      overflow-y: auto;
      position: relative;
      border: 2px solid #007bff;
    }

    .close-button {
      position: absolute;
      top: 15px;
      right: 20px;
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.3s ease;
    }

    .close-button:hover {
      background: #f0f0f0;
      color: #333;
    }

    .logo {
      margin-bottom: 20px;
      text-align: left;
    }

    .logo-text {
      font-size: 32px;
      font-weight: bold;
      color: #333;
      text-decoration: none;
      letter-spacing: 2px;
    }

    .promo-title {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 30px;
      color: #333;
      line-height: 1.3;
      text-align: center;
    }

    .main-content {
      background: #f0f0f0;
      border-radius: 12px;
      padding: 30px;
      margin: 20px 0;
    }

    .welcome-text {
      font-size: 16px;
      color: #333;
      margin-bottom: 20px;
      line-height: 1.5;
    }

    .promo-intro {
      font-size: 14px;
      color: #666;
      margin-bottom: 15px;
    }

    .to-gift {
      text-align: right;
      margin: 20px 0;
    }

    .to-gift h4 {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 15px;
    }

    .gift-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .gift-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      font-size: 16px;
      color: #333;
    }

    .gift-icon {
      width: 24px;
      height: 24px;
      margin-right: 10px;
      background: #333;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 12px;
    }

    .bottom-text {
      text-align: center;
      margin-top: 30px;
    }

    .bottom-main {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 5px;
    }

    .bottom-sub {
      font-size: 18px;
      color: #dc3545;
      font-weight: 600;
    }

    .promo-code {
      font-size: 36px;
      font-weight: bold;
      color: #333;
      background: transparent;
      padding: 20px;
      border-radius: 12px;
      margin: 20px 0;
      letter-spacing: 3px;
      border: 2px dashed #333;
      text-align: center;
    }

    .copy-button {
      background: #333;
      color: white;
      border: none;
      padding: 15px 30px;
      font-size: 16px;
      font-weight: 600;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      margin: 20px 0;
      width: 100%;
      max-width: 300px;
    }

    .champion-text {
      font-size: 14px;
      color: #666;
      margin: 20px 0;
      text-align: center;
    }

    .copy-button:hover {
      background: #555;
    }

    .copy-button:active {
      transform: translateY(1px);
    }

    .copy-button.success {
      background: #28a745;
    }

    /* Мобильная адаптивность */
    @media (max-width: 768px) {
      .container {
        padding: 20px;
        margin: 10px;
        max-width: 95%;
        border-radius: 8px;
      }

      .main-content {
        padding: 20px;
      }

      .promo-code {
        font-size: 28px;
        padding: 15px;
        letter-spacing: 2px;
      }

      .promo-title {
        font-size: 20px;
        margin-bottom: 20px;
      }

      .logo-text {
        font-size: 24px;
      }

      .to-gift {
        text-align: left;
      }

      .gift-item {
        font-size: 14px;
      }

      .bottom-main,
      .bottom-sub {
        font-size: 16px;
      }
    }

    @media (max-width: 480px) {
      .container {
        padding: 15px;
        margin: 5px;
      }

      .main-content {
        padding: 15px;
      }

      .promo-code {
        font-size: 24px;
        padding: 12px;
        letter-spacing: 1px;
      }

      .copy-button {
        padding: 12px 20px;
        font-size: 14px;
      }

      .promo-title {
        font-size: 18px;
      }

      .logo-text {
        font-size: 20px;
      }
    }
  </style>
</head>

<body>
  <div class="modal-overlay" onclick="closeModal(event)">
    <div class="container" onclick="event.stopPropagation()">
      <button class="close-button" onclick="closeModal()">&times;</button>

      <div class="logo">
        <div class="logo-text">SHMS</div>
      </div>

      <h1 class="promo-title">ДОБРО ПОЖАЛОВАТЬ В SHMS<br>ЛУЧШИЙ СЕРВИС АВТОПОДБОРА!</h1>

      <div class="main-content">
        <p class="welcome-text">
          Ты перешёл по ссылке от <strong>Рафаэля Физиева</strong> — значит, ты уже победил.
        </p>

        <p class="promo-intro">Специально для тебя — промокод ATAMAN:</p>

        <div class="promo-code" id="promoCode">ATAMAN</div>

        <button class="copy-button" id="copyButton" onclick="copyPromocode()">
          Скопировать промокод
        </button>

        <div class="to-gift">
          <h4>ТО в подарок:</h4>
          <ul class="gift-list">
            <li class="gift-item">
              <div class="gift-icon">🛢️</div>
              Замена масла
            </li>
            <li class="gift-item">
              <div class="gift-icon">🔧</div>
              Замена воздушных и масляных фильтров
            </li>
            <li class="gift-item">
              <div class="gift-icon">🛑</div>
              Замена тормозных колодок
            </li>
          </ul>
        </div>

        <p class="champion-text">
          Пройди путь чемпиона — выбери авто без лишних затрат и забот.
        </p>
      </div>

      <div class="bottom-text">
        <div class="bottom-main">Привоз авто из Кореи и Европы —</div>
        <div class="bottom-sub">БЕЗ КОМИССИИ</div>
      </div>
    </div>
  </div>

  <script>
    // Функция закрытия модального окна
    function closeModal(event) {
      if (event && event.target !== event.currentTarget) return;

      // Анимация закрытия
      document.body.style.opacity = '0';
      setTimeout(() => {
        window.history.back();
      }, 300);
    }

    // Закрытие по Escape
    document.addEventListener('keydown', function(event) {
      if (event.key === 'Escape') {
        closeModal();
      }
    });

    // Анимация появления
    document.addEventListener('DOMContentLoaded', function() {
      document.body.style.opacity = '0';
      setTimeout(() => {
        document.body.style.transition = 'opacity 0.3s ease';
        document.body.style.opacity = '1';
      }, 100);
    });

    async function copyPromocode() {
      const promoCode = 'ATAMAN';
      const button = document.getElementById('copyButton');

      try {
        // Современный способ копирования
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(promoCode);
        } else {
          // Fallback для старых браузеров
          const textArea = document.createElement('textarea');
          textArea.value = promoCode;
          textArea.style.position = 'fixed';
          textArea.style.left = '-999999px';
          textArea.style.top = '-999999px';
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();
          document.execCommand('copy');
          textArea.remove();
        }

        // Показываем успешное копирование
        button.textContent = 'Скопировано! Переходим к заказу...';
        button.classList.add('success');

        // Перенаправляем на страницу заказа через 2.5 секунды
        setTimeout(() => {
          window.location.href = '/order.html';
        }, 2500);

      } catch (err) {
        console.error('Ошибка копирования: ', err);
        button.textContent = 'Ошибка копирования';

        setTimeout(() => {
          button.textContent = 'Скопировать промокод';
        }, 2000);
      }
    }
  </script>
</body>

</html>
