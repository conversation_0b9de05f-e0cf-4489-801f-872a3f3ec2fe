const express = require("express");
const path = require("path");
const session = require("express-session");
const fs = require("fs");
require("dotenv").config();

const { sendContactForm } = require("./config/mail-config");

// Добавляем node-fetch для API запросов
let fetch;
(async () => {
  try {
    const fetchModule = await import("node-fetch");
    fetch = fetchModule.default;
  } catch (error) {
    console.warn(
      "node-fetch не найден, API encar может не работать:",
      error.message
    );
  }
})();
const app = express();

// Настройки сервера из переменных окружения
const port = process.env.PORT || 3001;
const host = process.env.HOST || "0.0.0.0";
const domain = process.env.DOMAIN || "localhost";
const site_url = process.env.SITE_URL || "http://localhost";
const isProduction = process.env.NODE_ENV === "production";

try {
  // Загрузка маршрутов админки
  console.log("Загрузка модулей маршрутизаторов...");

  // Проверяем существование файла в новой структуре
  let adminRoutes;
  try {
    // Пробуем загрузить из новой директории server/config/routes.js
    adminRoutes = require("./server/config/routes.js");
    console.log("Найден модуль маршрутизатора server/config/routes.js");
  } catch (routeError) {
    try {
      // Пробуем загрузить из server/config/routes/index.js
      adminRoutes = require("./server/config/routes/index.js");
      console.log("Найден модуль маршрутизатора server/config/routes/index.js");
    } catch (indexError) {
      console.log(
        "Модуль маршрутизатора server/config не найден, создаем заглушку"
      );
      // Создаем простую заглушку для маршрутов
      adminRoutes = require("express").Router();
      adminRoutes.stack = [];
    }
  }

  const uploadRoutes = require("./admin/js/upload-routes");
  console.log("Модули маршрутизаторов загружены успешно");

  // Middleware для парсинга JSON и URL-encoded данных с увеличенным лимитом
  app.use(express.json({ limit: "50mb" }));
  app.use(express.urlencoded({ extended: true, limit: "50mb" }));

  // Настройка сессий
  app.use(
    session({
      secret: process.env.SESSION_SECRET || "your-secret-key",
      resave: false,
      saveUninitialized: false,
      cookie: {
        secure: isProduction && process.env.SECURE_COOKIES === "true", // true для HTTPS в продакшене
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000, // 24 часа
      },
    })
  );

  // Безопасные заголовки для продакшена
  if (isProduction) {
    app.use((req, res, next) => {
      // HTTPS редирект
      if (
        process.env.HTTPS_REDIRECT === "true" &&
        req.header("x-forwarded-proto") !== "https"
      ) {
        return res.redirect(`https://${req.header("host")}${req.url}`);
      }

      // Безопасные заголовки
      res.setHeader("X-Content-Type-Options", "nosniff");
      res.setHeader("X-Frame-Options", "DENY");
      res.setHeader("X-XSS-Protection", "1; mode=block");
      res.setHeader("Referrer-Policy", "strict-origin-when-cross-origin");
      res.setHeader(
        "Permissions-Policy",
        "geolocation=(), microphone=(), camera=()"
      );

      next();
    });
  }

  // Middleware для логирования запросов
  app.use((req, res, next) => {
    const logLevel = process.env.LOG_LEVEL || "info";
    if (logLevel === "info" || logLevel === "debug") {
      console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
    }
    next();
  });

  // Логирование исходящих ответов
  app.use((req, res, next) => {
    const oldSend = res.send;
    res.send = function (data) {
      console.log(
        `Отправка ответа [${res.statusCode}] для ${req.method} ${req.url}`
      );
      return oldSend.apply(res, arguments);
    };
    next();
  });

  // Убедимся, что директория uploads существует
  const uploadsDir = path.join(__dirname, "data", "uploads");
  if (!fs.existsSync(uploadsDir)) {
    console.log("Создание директории uploads...");
    fs.mkdirSync(uploadsDir, { recursive: true });
    console.log("Директория uploads создана:", uploadsDir);
  } else {
    console.log("Директория uploads существует:", uploadsDir);
  }

  // API для автомобилей в наличии - проксирование на PHP сервер (ПРИОРИТЕТНЫЙ МАРШРУТ)
  app.get("/admin-stock/cars", async (req, res) => {
    console.log("=== ВХОД В ОБРАБОТЧИК /admin-stock/cars ===");
    try {
      console.log("Проксирование запроса к admin-stock/cars на PHP сервер");

      // Добавляем CORS заголовки
      res.header("Access-Control-Allow-Origin", "*");
      res.header(
        "Access-Control-Allow-Methods",
        "GET, POST, PUT, DELETE, OPTIONS"
      );
      res.header(
        "Access-Control-Allow-Headers",
        "Origin, X-Requested-With, Content-Type, Accept, Authorization"
      );

      // Используем встроенный http модуль для простого запроса
      const http = require('http');

      const options = {
        hostname: 'localhost',
        port: 8000,
        path: '/admin-stock/cars.php',
        method: 'GET',
        headers: {
          'User-Agent': 'SHMS-Auto-Server/1.0'
        }
      };

      console.log("Запрос к PHP серверу: http://localhost:8000/admin-stock/cars.php");

      const phpRequest = http.request(options, (phpResponse) => {
        let data = '';

        phpResponse.on('data', (chunk) => {
          data += chunk;
        });

        phpResponse.on('end', () => {
          try {
            console.log("Получен ответ от PHP сервера:", data.substring(0, 100) + "...");
            const jsonData = JSON.parse(data);
            console.log("Количество записей:", Array.isArray(jsonData) ? jsonData.length : "не массив");
            res.json(jsonData);
          } catch (parseError) {
            console.error("Ошибка парсинга JSON:", parseError.message);
            console.error("Полученные данные:", data);
            res.status(500).json({ error: "Ошибка парсинга ответа от PHP сервера" });
          }
        });
      });

      phpRequest.on('error', (error) => {
        console.error("Ошибка запроса к PHP серверу:", error.message);
        res.status(502).json({ error: "Ошибка соединения с PHP сервером" });
      });

      phpRequest.end();

    } catch (error) {
      console.error("Ошибка проксирования admin-stock/cars:", error.message);
      res.status(500).json({
        error: "Ошибка сервера при проксировании запроса",
        details: isProduction ? undefined : error.message,
      });
    }
  });

  // Раздача статических файлов из директории public
  app.use(
    express.static("public", {
      dotfiles: "allow",
      etag: true,
      index: false,
      maxAge: "1d",
    })
  );

  // Раздача статических файлов из директории admin
  app.use(
    "/admin",
    express.static("admin", {
      dotfiles: "allow",
      etag: true,
      index: false,
      maxAge: "1d",
    })
  );

  // Раздача загруженных файлов из новой директории data/uploads
  app.use(
    "/uploads",
    express.static("data/uploads", {
      dotfiles: "allow",
      etag: true,
      index: false,
      maxAge: "1d",
    })
  );

  // Простой тестовый маршрут
  app.get("/test", (req, res) => {
    res.json({ message: "Сервер работает!" });
  });

  // Маршруты загрузки файлов (приоритетнее других маршрутов)
  app.use("/admin", uploadRoutes);
  console.log("Маршруты загрузки файлов подключены");

  // Выводим все маршруты для загрузки
  console.log("Доступные маршруты загрузки:");
  uploadRoutes.stack.forEach((r) => {
    if (r.route && r.route.path) {
      console.log(
        `${r.route.stack[0].method.toUpperCase()} /admin${r.route.path}`
      );
    }
  });

  // Подключение маршрутов админки stock
  app.use("/api", adminRoutes); // Подключаем к /api для совместимости со старым кодом
  app.use("/admin", adminRoutes);
  console.log("Маршруты админки подключены");

  // Логирование зарегистрированных маршрутов
  console.log("Зарегистрированные маршруты:");
  adminRoutes.stack.forEach((r) => {
    if (r.route && r.route.path) {
      console.log(
        `${Object.keys(r.route.methods).join(",").toUpperCase()} ${
          r.route.path
        }`
      );
    }
  });

  // Маршрут для главной страницы
  app.get("/", (req, res) => {
    console.log("Запрос главной страницы");
    res.sendFile(path.join(__dirname, "public", "index.html"), (err) => {
      if (err && !res.headersSent) {
        console.error("Ошибка при отправке index.html:", err);
        res.status(500).send("Ошибка при загрузке страницы");
      }
    });
  });

  // Маршрут для страницы stock
  app.get("/stock", (req, res) => {
    console.log("Запрос страницы stock");
    res.sendFile(path.join(__dirname, "public", "stock.html"), (err) => {
      if (err && !res.headersSent) {
        console.error("Ошибка при отправке stock.html:", err);
        res.status(500).send("Ошибка при загрузке страницы");
      }
    });
  });

  // Маршрут для страницы order
  app.get("/order", (req, res) => {
    console.log("Запрос страницы order");
    res.sendFile(path.join(__dirname, "public", "order.html"), (err) => {
      if (err && !res.headersSent) {
        console.error("Ошибка при отправке order.html:", err);
        res.status(500).send("Ошибка при загрузке страницы");
      }
    });
  });

  // Маршрут для страницы contacts
  app.get("/contacts", (req, res) => {
    console.log("Запрос страницы contacts");
    res.sendFile(path.join(__dirname, "public", "contacts.html"), (err) => {
      if (err && !res.headersSent) {
        console.error("Ошибка при отправке contacts.html:", err);
        res.status(500).send("Ошибка при загрузке страницы");
      }
    });
  });

  // Маршрут для админ-панели
  app.get("/admin", (req, res) => {
    res.sendFile(path.join(__dirname, "admin", "index.html"), (err) => {
      if (err && !res.headersSent) {
        console.error("Ошибка при отправке admin/index.html:", err);
        res.status(500).send("Ошибка при загрузке админ-панели");
      }
    });
  });

  // Маршрут для страницы детального просмотра автомобиля
  app.get("/car-details/:id", (req, res) => {
    const id = req.params.id;
    res.sendFile(path.join(__dirname, "public", "car-detail.html"));
  });

  // Маршрут для промо-страницы Физиева
  app.get("/Fiziev", (req, res) => {
    console.log("Запрос промо-страницы Fiziev");
    res.sendFile(path.join(__dirname, "public", "Fiziev.html"), (err) => {
      if (err && !res.headersSent) {
        console.error("Ошибка при отправке Fiziev.html:", err);
        res.status(500).send("Ошибка при загрузке промо-страницы");
      }
    });
  });

  // Альтернативный маршрут для промо-страницы (с маленькой буквы)
  app.get("/fiziev", (req, res) => {
    console.log("Запрос промо-страницы fiziev (redirect)");
    res.redirect("/Fiziev");
  });

  // Обратная совместимость со старым названием
  app.get("/Fiziyev", (req, res) => {
    console.log("Запрос старого URL Fiziyev (redirect)");
    res.redirect("/Fiziev");
  });

  app.get("/fiziyev", (req, res) => {
    console.log("Запрос старого URL fiziyev (redirect)");
    res.redirect("/Fiziev");
  });

  // API маршрут для поиска автомобилей
  app.get("/api/encar", async (req, res) => {
    try {
      // Проверяем доступность fetch
      if (!fetch) {
        console.error("node-fetch не доступен");
        return res.status(500).json({ error: "API временно недоступен" });
      }

      const {
        fileType = "active_offer",
        date,
        brand = "",
        model = "",
        limit = 100,
        offset = 0,
        direct = 1,
      } = req.query;

      if (!date) {
        return res.status(400).json({ error: "Параметр date обязателен" });
      }

      console.log("API encar - получены параметры:", {
        fileType,
        date,
        brand,
        model,
        limit,
        offset,
      });

      // Собираем URL для запроса к внешнему API с параметрами поиска
      const searchParams = new URLSearchParams();
      if (brand) searchParams.append("brand", brand);
      if (model) searchParams.append("model", model);
      searchParams.append("limit", limit);
      if (offset) searchParams.append("offset", offset);

      // Формируем URL для запроса к PHP серверу (encar-proxy.php)
      const phpServerUrl = `${site_url}/api/encar-proxy.php`;
      const allParams = new URLSearchParams(req.query);
      const apiUrl = `${phpServerUrl}?${allParams.toString()}`;
      console.log("Проксирование запроса к PHP серверу:", apiUrl);

      const response = await fetch(apiUrl, {
        timeout: 30000,
        headers: {
          "User-Agent": "SHMS-Auto-Server/1.0",
        },
      });

      if (!response.ok) {
        console.error("Ошибка PHP API:", response.status, response.statusText);
        return res.status(502).json({
          error: "Ошибка PHP API",
          status: response.status,
          statusText: response.statusText,
        });
      }

      const data = await response.json();
      console.log(
        "Получен ответ от PHP API, количество записей:",
        Array.isArray(data) ? data.length : "не массив"
      );

      // Добавляем CORS заголовки
      res.header("Access-Control-Allow-Origin", "*");
      res.header(
        "Access-Control-Allow-Methods",
        "GET, POST, PUT, DELETE, OPTIONS"
      );
      res.header(
        "Access-Control-Allow-Headers",
        "Origin, X-Requested-With, Content-Type, Accept, Authorization"
      );

      res.json(data);
    } catch (error) {
      console.error("Ошибка API encar:", error.message);
      res.status(500).json({
        error: "Ошибка сервера при обработке запроса",
        details: isProduction ? undefined : error.message,
      });
    }
  });

  // API проксирование для mock-data.php (тестовые данные)
  app.get("/api/mock-data", async (req, res) => {
    try {
      // Проверяем доступность fetch
      if (!fetch) {
        console.error("node-fetch не доступен");
        return res.status(500).json({ error: "API временно недоступен" });
      }

      console.log("Проксирование запроса к mock-data.php:", req.query);

      // Формируем URL для запроса к PHP серверу
      const phpServerUrl = `${site_url}/api/mock-data.php`;
      const searchParams = new URLSearchParams(req.query);
      const fullUrl = `${phpServerUrl}?${searchParams.toString()}`;

      console.log("Запрос к PHP серверу:", fullUrl);

      const response = await fetch(fullUrl, {
        timeout: 30000,
        headers: {
          "User-Agent": "SHMS-Auto-Server/1.0",
        },
      });

      if (!response.ok) {
        console.error("Ошибка PHP API:", response.status, response.statusText);
        return res.status(502).json({
          error: "Ошибка PHP API",
          status: response.status,
          statusText: response.statusText,
        });
      }

      const data = await response.json();
      console.log(
        "Получен ответ от PHP API (mock-data), количество записей:",
        Array.isArray(data) ? data.length : "не массив"
      );

      // Добавляем CORS заголовки
      res.header("Access-Control-Allow-Origin", "*");
      res.header(
        "Access-Control-Allow-Methods",
        "GET, POST, PUT, DELETE, OPTIONS"
      );
      res.header(
        "Access-Control-Allow-Headers",
        "Origin, X-Requested-With, Content-Type, Accept, Authorization"
      );

      res.json(data);
    } catch (error) {
      console.error("Ошибка проксирования mock-data:", error.message);
      res.status(500).json({
        error: "Ошибка сервера при проксировании запроса",
        details: isProduction ? undefined : error.message,
      });
    }
  });



  // Обработка отзывов и заявок с сайта
  app.post("/api/send-review", async (req, res) => {
    try {
      const formData = req.body;

      // Валидация обязательных полей
      if (!formData.name && !formData.contact && !formData.message) {
        return res.status(400).json({
          error:
            "Необходимо заполнить хотя бы одно из полей: имя, контакт или сообщение",
        });
      }

      // Отправка email через новую систему
      await sendContactForm(formData);

      console.log("Заявка успешно отправлена:", {
        name: formData.name,
        contact: formData.contact || formData.phone || formData.email,
        formType: formData.formType || "review",
      });

      res.json({
        success: true,
        message:
          "Ваше сообщение успешно отправлено! Мы свяжемся с вами в ближайшее время.",
      });
    } catch (error) {
      console.error("Ошибка отправки заявки:", error);
      res.status(500).json({
        error:
          "Ошибка отправки сообщения. Попробуйте позже или свяжитесь с нами по телефону.",
        details: isProduction ? undefined : error.message,
      });
    }
  });

  // Обработка 404 ошибок
  app.use((req, res) => {
    console.log("404 - Страница не найдена:", req.url);
    res.status(404).send("Страница не найдена");
  });

  // Обработка ошибок
  app.use((err, req, res, next) => {
    console.error("Ошибка обработки запроса:", err);
    res.status(500).send("Ошибка сервера: " + err.message);
  });

  // Запуск сервера
  app.listen(port, host, () => {
    console.log("=================================");
    console.log(`🚀 Сервер SHMS Авто запущен!`);
    console.log(`🌐 Режим: ${process.env.NODE_ENV || "development"}`);
    console.log(`📡 Адрес: http://${host}:${port}`);
    if (process.env.SITE_URL) {
      console.log(`🔗 Публичный URL: ${process.env.SITE_URL}`);
    }
    console.log("📋 Доступные маршруты:");
    console.log("   - Главная страница: /");
    console.log("   - Авто в наличии: /stock");
    console.log("   - Авто под заказ: /order");
    console.log("   - Контакты: /contacts");
    console.log("   - Промо-страница Физиева: /Fiziyev");
    console.log("   - Админ-панель: /admin");
    console.log("   - Загруженные файлы: /uploads");
    console.log("   - API: /api/send-review");
    console.log("=================================");
  });
} catch (error) {
  console.error("КРИТИЧЕСКАЯ ОШИБКА при запуске сервера:", error);
}
