/**
 * SHMS Auto - Reviews Sharing Functionality
 * Позволяет пользователям делиться отзывами в социальных сетях
 */

document.addEventListener('DOMContentLoaded', function() {
    // Добавляем кнопки шеринга в каждое модальное окно отзыва
    addSharingButtonsToReviews();
    
    // Инициализируем обработчики событий для кнопок шеринга
    initShareButtons();
});

/**
 * Добавляет кнопки шеринга в модальные окна отзывов
 */
function addSharingButtonsToReviews() {
    // Получаем все модальные окна с отзывами
    const reviewModals = document.querySelectorAll('.review-article-modal');
    
    // Для каждого модального окна добавляем кнопки шеринга
    reviewModals.forEach(modal => {
        const modalId = modal.id;
        const reviewId = modalId.replace('review-article-', '');
        
        // Получаем заголовок и текст отзыва для шеринга
        const title = modal.querySelector('.review-article-modal__title').textContent;
        const firstParagraph = modal.querySelector('.review-article-modal__body p').textContent;
        
        // Создаем контейнер для кнопок шеринга
        const sharingContainer = document.createElement('div');
        sharingContainer.className = 'review-sharing';
        
        // Добавляем заголовок для блока шеринга
        const sharingTitle = document.createElement('div');
        sharingTitle.className = 'review-sharing__title';
        sharingTitle.textContent = 'Поделиться отзывом:';
        sharingContainer.appendChild(sharingTitle);
        
        // Создаем контейнер для кнопок
        const buttonsContainer = document.createElement('div');
        buttonsContainer.className = 'review-sharing__buttons';
        
        // Добавляем кнопки социальных сетей
        const socialNetworks = [
            { name: 'vk', icon: 'vk', label: 'ВКонтакте' },
            { name: 'telegram', icon: 'telegram', label: 'Telegram' },
            { name: 'whatsapp', icon: 'whatsapp', label: 'WhatsApp' }
        ];
        
        socialNetworks.forEach(network => {
            const button = document.createElement('button');
            button.className = `review-sharing__button review-sharing__button--${network.name}`;
            button.setAttribute('data-review-id', reviewId);
            button.setAttribute('data-network', network.name);
            button.setAttribute('aria-label', `Поделиться в ${network.label}`);
            button.innerHTML = `<span class="review-sharing__icon review-sharing__icon--${network.icon}"></span>`;
            
            // Добавляем всплывающую подсказку
            const tooltip = document.createElement('span');
            tooltip.className = 'review-sharing__tooltip';
            tooltip.textContent = network.label;
            button.appendChild(tooltip);
            
            buttonsContainer.appendChild(button);
        });
        
        // Добавляем кнопки в контейнер
        sharingContainer.appendChild(buttonsContainer);
        
        // Добавляем контейнер с кнопками в модальное окно
        const modalContent = modal.querySelector('.review-article-modal__content');
        modalContent.appendChild(sharingContainer);
    });
}

/**
 * Инициализирует обработчики событий для кнопок шеринга
 */
function initShareButtons() {
    // Обработчик клика по кнопкам шеринга
    document.querySelectorAll('.review-sharing__button').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const reviewId = this.getAttribute('data-review-id');
            const network = this.getAttribute('data-network');
            
            // Получаем информацию об отзыве
            const reviewModal = document.getElementById(`review-article-${reviewId}`);
            const title = reviewModal.querySelector('.review-article-modal__title').textContent;
            const text = reviewModal.querySelector('.review-article-modal__body p').textContent;
            
            // Формируем URL для шеринга
            const shareUrl = encodeURIComponent(`${window.location.origin}${window.location.pathname}?review=${reviewId}`);
            const shareTitle = encodeURIComponent(`Отзыв о ${title} - SHMS Auto`);
            const shareText = encodeURIComponent(text.substring(0, 100) + '...');
            
            // Определяем URL для шеринга в зависимости от социальной сети
            let shareLink = '';
            
            switch(network) {
                case 'vk':
                    shareLink = `https://vk.com/share.php?url=${shareUrl}&title=${shareTitle}&description=${shareText}`;
                    break;
                case 'telegram':
                    shareLink = `https://t.me/share/url?url=${shareUrl}&text=${shareTitle}`;
                    break;
                case 'whatsapp':
                    shareLink = `https://api.whatsapp.com/send?text=${shareTitle}%20${shareUrl}`;
                    break;
            }
            
            // Открываем окно для шеринга
            if (shareLink) {
                window.open(shareLink, '_blank', 'width=640,height=480');
                
                // Анимация нажатия на кнопку
                button.classList.add('review-sharing__button--active');
                setTimeout(() => {
                    button.classList.remove('review-sharing__button--active');
                }, 300);
                
                // Показываем уведомление об успешном шеринге
                showShareNotification(title, network);
            }
        });
    });
    
    // Проверяем наличие параметра review в URL
    checkReviewInUrl();
}

/**
 * Проверяет наличие параметра review в URL и открывает соответствующий отзыв
 */
function checkReviewInUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    const reviewId = urlParams.get('review');
    
    if (reviewId) {
        const reviewModal = document.getElementById(`review-article-${reviewId}`);
        if (reviewModal) {
            // Небольшая задержка для загрузки страницы
            setTimeout(() => {
                reviewModal.classList.add('active');
            }, 500);
        }
    }
}

/**
 * Показывает уведомление об успешном шеринге
 * @param {string} title - Название отзыва
 * @param {string} network - Название социальной сети
 */
function showShareNotification(title, network) {
    // Создаем элемент уведомления
    const notification = document.createElement('div');
    notification.className = 'share-notification';
    
    // Определяем название социальной сети для уведомления
    let networkName = '';
    switch(network) {
        case 'vk': networkName = 'ВКонтакте'; break;
        case 'telegram': networkName = 'Telegram'; break;
        case 'whatsapp': networkName = 'WhatsApp'; break;
    }
    
    // Устанавливаем текст уведомления
    notification.innerHTML = `
        <div class="share-notification__icon share-notification__icon--${network}"></div>
        <div class="share-notification__text">
            Отзыв о ${title} отправлен в ${networkName}
        </div>
    `;
    
    // Добавляем уведомление на страницу
    document.body.appendChild(notification);
    
    // Анимация появления
    setTimeout(() => {
        notification.classList.add('share-notification--visible');
    }, 10);
    
    // Удаляем уведомление через 3 секунды
    setTimeout(() => {
        notification.classList.remove('share-notification--visible');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
} 