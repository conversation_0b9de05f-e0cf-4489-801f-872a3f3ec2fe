const sqlite3 = require('sqlite3').verbose();
const db = new sqlite3.Database('./admin/js/stock.db');

console.log('Детальные страницы автомобилей:');
db.all('SELECT id, car_id, full_title, status FROM car_details ORDER BY id DESC', (err, rows) => {
  if (err) {
    console.error('Ошибка:', err);
    return;
  }
  
  console.log('Найдено записей:', rows.length);
  rows.forEach(row => {
    console.log(`ID: ${row.id}, Car ID: ${row.car_id}, Title: ${row.full_title}, Status: ${row.status}`);
  });
  
  db.close();
});
