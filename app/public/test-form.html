<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест формы обратной связи</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #000; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #333; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>Тест формы обратной связи SHMS</h1>
    
    <form id="testForm">
        <div class="form-group">
            <label for="name">Имя *</label>
            <input type="text" id="name" name="name" value="Тестовый пользователь" required>
        </div>
        
        <div class="form-group">
            <label for="region">Регион *</label>
            <input type="text" id="region" name="region" value="Москва" required>
        </div>
        
        <div class="form-group">
            <label for="carType">Тип автомобиля</label>
            <select id="carType" name="carType">
                <option value="stock">Авто в наличии</option>
                <option value="order">Авто под заказ</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="model">Модель</label>
            <input type="text" id="model" name="model" value="BMW X5">
        </div>
        
        <div class="form-group">
            <label for="phone">Телефон *</label>
            <input type="tel" id="phone" name="phone" value="+7 (999) 123-45-67" required>
        </div>
        
        <div class="form-group">
            <label for="email">Email *</label>
            <input type="email" id="email" name="email" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label for="contact">Способ связи</label>
            <select id="contact" name="contact">
                <option>Telegram</option>
                <option>WhatsApp</option>
                <option>Позвонить вам</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="message">Сообщение</label>
            <textarea id="message" name="message" rows="3">Тестовое сообщение для проверки работы формы обратной связи.</textarea>
        </div>
        
        <div class="form-group">
            <label for="promo">Промокод</label>
            <input type="text" id="promo" name="promo" value="TEST2025">
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" name="consent" checked required>
                Я согласен на обработку персональных данных
            </label>
        </div>
        
        <button type="submit">Отправить тестовое сообщение</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Отправка...</p>';
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());
            data.formType = 'test-feedback';
            
            try {
                const response = await fetch('feedback-handler.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                
                const text = await response.text();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="result success"><strong>Успех!</strong><br>${text}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error"><strong>Ошибка ${response.status}:</strong><br>${text}</div>`;
                }
            } catch (err) {
                resultDiv.innerHTML = `<div class="result error"><strong>Ошибка сети:</strong><br>${err.message}</div>`;
            }
        });
    </script>
</body>
</html>
