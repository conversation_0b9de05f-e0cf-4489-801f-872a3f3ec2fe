// Глобальные переменные для хранения всех машин, фильтров и подсказок
let allCars = [];
let activeFilters = {
  search: "",
  bodyType: "",
  priceRange: { min: 0, max: Infinity },
  powerRange: { min: 0, max: Infinity },
};

// Список брендов автомобилей для автодополнения (резервный список)
const defaultCarBrands = [
  "Audi",
  "BMW",
  "Mercedes-Benz",
  "Porsche",
  "Ferrari",
  "Lamborghini",
  "Bentley",
  "Aston Martin",
  "Alfa Romeo",
  "Jaguar",
  "Land Rover",
  "Rolls-Royce",
  "Maserati",
  "McLaren",
  "Bugatti",
  "Koenigsegg",
  "Pagani",
  "Lexus",
  "Cadillac",
  "Tesla",
];

// Массивы для хранения уникальных брендов и моделей из загруженных данных
let availableCarBrands = [];
let availableCarModels = [];

// Резервные данные для отображения, если API недоступен
const fallbackCars = [];

// Функция для извлечения марок и моделей автомобилей из данных
function extractCarBrandsAndModels(cars) {
  const brands = new Set();
  const models = new Set();
  const brandToModels = {}; // Для хранения моделей по брендам

  cars.forEach((car) => {
    let brand = "";
    let model = "";

    // Извлекаем марку из заголовка или подзаголовка
    if (car.title) {
      const parts = car.title.split(" ");
      if (parts.length > 1 && !parts[0].match(/^\d+$/)) {
        brand = parts[0];
        // Модель может быть второй частью или всем оставшимся после марки
        model = parts
          .slice(1)
          .join(" ")
          .replace(/^\d+\s+/, ""); // Убираем год, если есть
        brands.add(brand);
        if (model) models.add(model);

        // Добавляем модель к соответствующему бренду
        if (!brandToModels[brand]) brandToModels[brand] = new Set();
        brandToModels[brand].add(model);
      }
    }

    if (car.subtitle) {
      const parts = car.subtitle.split("•");
      if (parts[0]) {
        const brandFromSubtitle = parts[0].trim();
        brands.add(brandFromSubtitle);

        // Если мы еще не нашли бренд из заголовка
        if (!brand) brand = brandFromSubtitle;
      }
    }
  });

  // Преобразуем объект брендов и моделей
  const brandModelsMap = {};
  for (const brand in brandToModels) {
    brandModelsMap[brand] = Array.from(brandToModels[brand]).sort();
  }

  return {
    brands: Array.from(brands).sort(),
    models: Array.from(models).sort(),
    brandModelsMap: brandModelsMap,
  };
}

// Функция для загрузки информации о детальных страницах
async function loadDetailPages() {
  try {
    const response = await fetch("/api/published");
    if (!response.ok) {
      console.warn("Не удалось загрузить детальные страницы");
      return [];
    }
    const pages = await response.json();
    console.log("Loaded detail pages:", pages);
    return pages;
  } catch (error) {
    console.error("Ошибка при загрузке детальных страниц:", error);
    return [];
  }
}

// Function to load car cards from the server
async function loadCarCards() {
  console.log("Starting to load car cards...");
  const stockCards = document.querySelector(".stock-cards");

  // Показываем сообщение о загрузке
  if (stockCards) {
    stockCards.innerHTML = `<div style="text-align:center;padding:25px;color:#777;margin:20px 0;background:#f9f9f9;border-radius:8px;">Загрузка автомобилей...</div>`;
  }

  try {
    console.log("Fetching car data from /api/cars");
    const response = await fetch("/api/cars");

    if (!response.ok) {
      console.error(
        "Error response from server:",
        response.status,
        response.statusText
      );
      throw new Error(`Ошибка сервера! Статус: ${response.status}`);
    }

    const cars = await response.json();
    console.log("Loaded cars from server:", cars);

    if (!Array.isArray(cars) || cars.length === 0) {
      console.warn("No cars found or data is not an array:", cars);
      // Показываем сообщение об отсутствии автомобилей
      if (stockCards) {
        stockCards.innerHTML = `<div style="text-align:center;padding:25px;color:#777;margin:20px 0;background:#f9f9f9;border-radius:8px;">
                    Автомобили в наличии отсутствуют. Пожалуйста, проверьте наличие позже.</div>`;
      }
      return [];
    }

    // Загружаем информацию о детальных страницах
    const detailPages = await loadDetailPages();

    // Добавляем ссылки на детальные страницы к карточкам автомобилей
    const carsWithDetails = cars.map((car) => {
      const detailPage = detailPages.find((page) => page.car_id === car.id);
      return {
        ...car,
        detailPage: detailPage ? detailPage.filePath : null,
      };
    });

    allCars = carsWithDetails; // Сохраняем все машины
    const carData = extractCarBrandsAndModels(carsWithDetails);
    availableCarBrands = carData.brands;
    availableCarModels = carData.models;
    initializeFilters(carsWithDetails); // Инициализировать фильтры
    displayCarCards(carsWithDetails);
    return carsWithDetails; // Возвращаем данные для then()
  } catch (error) {
    console.error("Error loading car cards:", error);

    // Отображаем сообщение об ошибке
    if (stockCards) {
      stockCards.innerHTML = `<div style="text-align:center;padding:25px;color:#d9534f;margin:20px 0;background:#fdf7f7;border-radius:8px;border:1px solid #f5e4e4;">
                Не удалось загрузить данные об автомобилях.
                <br><br>
                Пожалуйста, повторите попытку позже или свяжитесь с администратором сайта.
                <br><br>
                Ошибка: ${error.message}
            </div>`;
    }

    return [];
  }
}

// Инициализирует фильтры на основе полученных данных
function initializeFilters(cars) {
  console.log("Initializing filters with cars:", cars);

  // Создаем категории фильтров динамически
  const filtersSection = document.querySelector(".filters");
  const filterMenu = document.getElementById("filter-menu");
  console.log("Filters section found:", filtersSection);

  // Инициализируем выпадающее меню фильтров
  initFilterMenu();

  // Добавляем фильтры в меню
  createFilterGroups(cars, filterMenu);

  // Обработчик формы поиска
  const searchForm = document.querySelector(".search__form");
  console.log("Search form found:", searchForm);

  if (searchForm) {
    // Удаляем предыдущие обработчики, если они были
    const clonedForm = searchForm.cloneNode(true);
    searchForm.parentNode.replaceChild(clonedForm, searchForm);

    // Добавляем новый обработчик для отправки формы
    clonedForm.addEventListener("submit", function (e) {
      e.preventDefault();
      const searchInput = this.querySelector(".search__input");
      if (searchInput) {
        console.log("Search submitted with value:", searchInput.value);
        activeFilters.search = searchInput.value.trim();
        updateActiveFilterTags(); // Обновляем отображение активных фильтров
        applyFilters();

        // Закрываем выпадающий список автодополнения
        const dropdown = document.getElementById("autocomplete-dropdown");
        if (dropdown) {
          dropdown.classList.remove("show");
        }
      } else {
        console.error("Search input not found inside form");
      }
    });

    // Добавляем обработчик клика на кнопку поиска
    const searchBtn = clonedForm.querySelector(".search__btn");
    if (searchBtn) {
      searchBtn.addEventListener("click", function (e) {
        e.preventDefault();
        const searchInput = clonedForm.querySelector(".search__input");
        if (searchInput) {
          console.log("Search button clicked with value:", searchInput.value);
          activeFilters.search = searchInput.value.trim();
          updateActiveFilterTags();
          applyFilters();

          // Закрываем выпадающий список автодополнения
          const dropdown = document.getElementById("autocomplete-dropdown");
          if (dropdown) {
            dropdown.classList.remove("show");
          }
        }
      });
    }
  } else {
    console.error(
      "Search form not found - cannot initialize search functionality"
    );
  }

  // Добавляем счетчик результатов
  if (!document.querySelector(".results-counter")) {
    const resultsCounter = document.createElement("div");
    resultsCounter.className = "results-counter";
    resultsCounter.textContent = `Найдено автомобилей: ${cars.length}`;
    filtersSection.appendChild(resultsCounter);
  }

  // Удаляем старые фильтры
  removeOldFilters();

  // Обновляем активные фильтры
  updateActiveFilterTags();
}

// Инициализирует выпадающее меню фильтров
function initFilterMenu() {
  const filterBtn = document.getElementById("filter-menu-btn");
  const filterMenu = document.getElementById("filter-menu");

  // Обработчик для открытия/закрытия меню фильтров
  if (filterBtn && filterMenu) {
    filterBtn.addEventListener("click", function (e) {
      e.stopPropagation();
      filterMenu.classList.toggle("show");
    });

    // Закрытие меню при клике вне его
    document.addEventListener("click", function (e) {
      if (!filterMenu.contains(e.target) && e.target !== filterBtn) {
        filterMenu.classList.remove("show");
      }
    });

    // Предотвращаем закрытие меню при клике внутри него
    filterMenu.addEventListener("click", function (e) {
      e.stopPropagation();
    });
  }
}

// Создает группы фильтров и добавляет их в меню
function createFilterGroups(cars, filterMenu) {
  if (!filterMenu) return;

  // Очищаем существующие фильтры
  filterMenu.innerHTML = "";

  // Заголовок меню
  const menuTitle = document.createElement("h3");
  menuTitle.textContent = "Фильтры";
  menuTitle.style.margin = "0 0 10px 0";
  filterMenu.appendChild(menuTitle);

  // Добавляем группу фильтра по типу кузова
  addBodyTypeFilter(cars, filterMenu);

  // Добавляем группу фильтра по цене
  addPriceFilter(filterMenu);

  // Добавляем группу сортировки
  addSortFilter(filterMenu);

  // Добавляем кнопку сброса всех фильтров
  addResetAllFiltersButton(filterMenu);
}

// Добавляет фильтр по типу кузова
function addBodyTypeFilter(cars, container) {
  // Собираем уникальные типы кузовов
  const bodyTypes = [
    ...new Set(cars.map((car) => car.body_type).filter(Boolean)),
  ];

  if (bodyTypes.length === 0) return;

  const group = document.createElement("div");
  group.className = "filter-group";

  const label = document.createElement("div");
  label.className = "filter-label";
  label.textContent = "Тип кузова";
  group.appendChild(label);

  const options = document.createElement("div");
  options.className = "filter-options";

  // Опция "Все типы"
  addFilterOption(
    options,
    "",
    "Все типы",
    function () {
      activeFilters.bodyType = "";
      updateActiveFilterTags();
      applyFilters();
    },
    !activeFilters.bodyType
  );

  // Опции для каждого типа кузова
  bodyTypes.forEach((type) => {
    addFilterOption(
      options,
      type,
      type,
      function () {
        activeFilters.bodyType = type;
        updateActiveFilterTags();
        applyFilters();
      },
      activeFilters.bodyType === type
    );
  });

  group.appendChild(options);
  container.appendChild(group);
}

// Добавляет фильтр по цене
function addPriceFilter(container) {
  const priceRanges = [
    { min: 0, max: Infinity, label: "Все цены" },
    { min: 0, max: 100000, label: "До $100,000" },
    { min: 100000, max: 200000, label: "$100,000 - $200,000" },
    { min: 200000, max: 300000, label: "$200,000 - $300,000" },
    { min: 300000, max: Infinity, label: "От $300,000" },
  ];

  const group = document.createElement("div");
  group.className = "filter-group";

  const label = document.createElement("div");
  label.className = "filter-label";
  label.textContent = "Цена";
  group.appendChild(label);

  const options = document.createElement("div");
  options.className = "filter-options";

  // Добавляем опции цены
  priceRanges.forEach((range) => {
    const isActive =
      activeFilters.priceRange.min === range.min &&
      activeFilters.priceRange.max === range.max;

    addFilterOption(
      options,
      "",
      range.label,
      function () {
        activeFilters.priceRange.min = range.min;
        activeFilters.priceRange.max = range.max;
        updateActiveFilterTags();
        applyFilters();
      },
      isActive
    );
  });

  group.appendChild(options);
  container.appendChild(group);
}

// Добавляет фильтр сортировки
function addSortFilter(container) {
  const sortTypes = [
    { value: "default", label: "По умолчанию" },
    { value: "price-asc", label: "Цена: по возрастанию ↑" },
    { value: "price-desc", label: "Цена: по убыванию ↓" },
    { value: "power-desc", label: "Мощность: по убыванию" },
  ];

  const group = document.createElement("div");
  group.className = "filter-group";

  const label = document.createElement("div");
  label.className = "filter-label";
  label.textContent = "Сортировка";
  group.appendChild(label);

  const options = document.createElement("div");
  options.className = "filter-options";

  // Находим текущий тип сортировки
  const currentSortBtn = document.querySelector(
    ".sort-group .filter-option.active"
  );
  const currentSortType = currentSortBtn
    ? currentSortBtn.dataset.sort
    : "default";

  // Добавляем опции сортировки
  sortTypes.forEach((sort) => {
    addFilterOption(
      options,
      sort.value,
      sort.label,
      function () {
        sortCars(sort.value);
        updateActiveFilterTags();
      },
      sort.value === currentSortType
    );
  });

  group.appendChild(options);
  container.appendChild(group);
}

// Добавляет кнопку для сброса всех фильтров
function addResetAllFiltersButton(container) {
  const resetBtn = document.createElement("button");
  resetBtn.className = "reset-all-filters-btn";
  resetBtn.textContent = "Сбросить все фильтры";

  resetBtn.addEventListener("click", function () {
    resetAllFilters();
  });

  container.appendChild(resetBtn);
}

// Добавляет опцию фильтра с обработчиком
function addFilterOption(
  container,
  value,
  label,
  clickHandler,
  isActive = false
) {
  const option = document.createElement("button");
  option.className = "filter-option" + (isActive ? " active" : "");
  option.setAttribute("data-value", value);
  option.textContent = label;

  option.addEventListener("click", function () {
    // Деактивируем другие опции в этой группе
    const siblings = container.querySelectorAll(".filter-option");
    siblings.forEach((sib) => sib.classList.remove("active"));

    // Активируем текущую опцию
    this.classList.add("active");

    // Вызываем обработчик
    clickHandler();
  });

  container.appendChild(option);
  return option;
}

// Сбрасывает все фильтры
function resetAllFilters() {
  // Сбрасываем все фильтры
  activeFilters = {
    search: "",
    bodyType: "",
    priceRange: { min: 0, max: Infinity },
    powerRange: { min: 0, max: Infinity },
  };

  // Сбрасываем поле поиска
  const searchInput = document.querySelector(".search__input");
  if (searchInput) {
    searchInput.value = "";
  }

  // Сбрасываем активные кнопки фильтров
  document.querySelectorAll(".filter-option").forEach((btn) => {
    const parentGroup = btn.closest(".filter-group");
    const isDefaultButton =
      btn.dataset.value === "" ||
      btn.dataset.value === "default" ||
      btn.textContent === "Все типы" ||
      btn.textContent === "Все цены" ||
      btn.textContent === "По умолчанию";

    if (isDefaultButton && parentGroup) {
      // Активируем только кнопки "по умолчанию"
      const buttons = parentGroup.querySelectorAll(".filter-option");
      buttons.forEach((b) => b.classList.remove("active"));
      btn.classList.add("active");
    }
  });

  // Очищаем URL
  window.history.pushState({}, "", window.location.pathname);

  // Обновляем отображение и применяем фильтры
  updateActiveFilterTags();

  // Отображаем все автомобили
  displayCarCards(allCars);

  // Обновляем счетчик результатов
  const resultsCounter = document.querySelector(".results-counter");
  if (resultsCounter) {
    resultsCounter.textContent = `Найдено автомобилей: ${allCars.length}`;
  }

  // Закрываем меню фильтров
  const filterMenu = document.getElementById("filter-menu");
  if (filterMenu) {
    filterMenu.classList.remove("show");
  }
}

// Обновляет отображение активных фильтров
function updateActiveFilterTags() {
  const container = document.getElementById("active-filters");
  if (!container) return;

  // Очищаем существующие теги
  container.innerHTML = "";

  // Считаем количество активных фильтров
  let activeFiltersCount = 0;

  // Добавляем тег для текущего поиска
  if (activeFilters.search) {
    addActiveFilterTag(container, "Поиск", activeFilters.search, function () {
      activeFilters.search = "";
      const searchInput = document.querySelector(".search__input");
      if (searchInput) searchInput.value = "";
      applyFilters();
      updateActiveFilterTags();
    });
    activeFiltersCount++;
  }

  // Добавляем тег для типа кузова
  if (activeFilters.bodyType) {
    addActiveFilterTag(container, "Кузов", activeFilters.bodyType, function () {
      activeFilters.bodyType = "";

      // Активируем кнопку "Все типы"
      const bodyTypeOptions = document.querySelectorAll(
        '.filter-options .filter-option[data-value=""]'
      );
      bodyTypeOptions.forEach((btn) => {
        const parentGroup = btn.closest(".filter-group");
        if (
          parentGroup &&
          parentGroup.querySelector(".filter-label").textContent ===
            "Тип кузова"
        ) {
          parentGroup
            .querySelectorAll(".filter-option")
            .forEach((b) => b.classList.remove("active"));
          btn.classList.add("active");
        }
      });

      applyFilters();
      updateActiveFilterTags();
    });
    activeFiltersCount++;
  }

  // Добавляем тег для диапазона цен
  if (
    activeFilters.priceRange.min > 0 ||
    activeFilters.priceRange.max < Infinity
  ) {
    let priceLabel = "";

    if (
      activeFilters.priceRange.min > 0 &&
      activeFilters.priceRange.max < Infinity
    ) {
      priceLabel = `$${activeFilters.priceRange.min.toLocaleString()} - $${activeFilters.priceRange.max.toLocaleString()}`;
    } else if (activeFilters.priceRange.min > 0) {
      priceLabel = `От $${activeFilters.priceRange.min.toLocaleString()}`;
    } else if (activeFilters.priceRange.max < Infinity) {
      priceLabel = `До $${activeFilters.priceRange.max.toLocaleString()}`;
    }

    addActiveFilterTag(container, "Цена", priceLabel, function () {
      activeFilters.priceRange = { min: 0, max: Infinity };

      // Активируем кнопку "Все цены"
      const priceOptions = document.querySelectorAll(
        ".filter-options .filter-option"
      );
      priceOptions.forEach((btn) => {
        const parentGroup = btn.closest(".filter-group");
        if (
          parentGroup &&
          parentGroup.querySelector(".filter-label").textContent === "Цена" &&
          btn.textContent === "Все цены"
        ) {
          parentGroup
            .querySelectorAll(".filter-option")
            .forEach((b) => b.classList.remove("active"));
          btn.classList.add("active");
        }
      });

      applyFilters();
      updateActiveFilterTags();
    });
    activeFiltersCount++;
  }

  // Добавляем тег для типа сортировки
  const sortBtn = document.querySelector(
    '.filter-option[data-value^="price-"].active, .filter-option[data-value="power-desc"].active'
  );
  if (sortBtn && sortBtn.classList.contains("active")) {
    const sortValue = sortBtn.dataset.value;
    let sortLabel = "";

    switch (sortValue) {
      case "price-asc":
        sortLabel = "Цена ↑";
        break;
      case "price-desc":
        sortLabel = "Цена ↓";
        break;
      case "power-desc":
        sortLabel = "Мощность ↓";
        break;
    }

    if (sortLabel) {
      addActiveFilterTag(container, "Сортировка", sortLabel, function () {
        // Активируем кнопку "По умолчанию"
        const sortOptions = document.querySelectorAll(
          '.filter-options .filter-option[data-value="default"]'
        );
        sortOptions.forEach((btn) => {
          const parentGroup = btn.closest(".filter-group");
          if (
            parentGroup &&
            parentGroup.querySelector(".filter-label").textContent ===
              "Сортировка"
          ) {
            parentGroup
              .querySelectorAll(".filter-option")
              .forEach((b) => b.classList.remove("active"));
            btn.classList.add("active");
          }
        });

        sortCars("default");
        updateActiveFilterTags();
      });
      activeFiltersCount++;
    }
  }

  // Обновляем индикатор на кнопке меню фильтров
  const filterMenuBtn = document.getElementById("filter-menu-btn");
  if (filterMenuBtn) {
    if (activeFiltersCount > 0) {
      filterMenuBtn.setAttribute("data-count", activeFiltersCount);
      filterMenuBtn.classList.add("has-active-filters");
    } else {
      filterMenuBtn.removeAttribute("data-count");
      filterMenuBtn.classList.remove("has-active-filters");
    }
  }
}

// Добавляет тег активного фильтра
function addActiveFilterTag(container, name, value, removeHandler) {
  const tag = document.createElement("div");
  tag.className = "active-filter-tag";

  tag.innerHTML = `
        <span>${name}:</span> ${value}
        <div class="filter-tag-remove">×</div>
    `;

  // Добавляем обработчик удаления
  const removeBtn = tag.querySelector(".filter-tag-remove");
  if (removeBtn && removeHandler) {
    removeBtn.addEventListener("click", removeHandler);
  }

  container.appendChild(tag);
}

// Обновляет URL с текущими параметрами фильтрации
function updateURLParams() {
  const params = new URLSearchParams();

  if (activeFilters.search) {
    params.set("search", activeFilters.search);
  }

  if (activeFilters.bodyType) {
    params.set("body", activeFilters.bodyType);
  }

  if (activeFilters.priceRange.min > 0) {
    params.set("min_price", activeFilters.priceRange.min);
  }

  if (activeFilters.priceRange.max < Infinity) {
    params.set("max_price", activeFilters.priceRange.max);
  }

  // Текущая сортировка
  const sortBtn = document.querySelector(".sort-group .filter-option.active");
  if (sortBtn && sortBtn.dataset.sort) {
    params.set("sort", sortBtn.dataset.sort);
  }

  // Обновляем URL без перезагрузки страницы
  const newURL = params.toString()
    ? `?${params.toString()}`
    : window.location.pathname;
  window.history.pushState({}, "", newURL);
}

// Загружает параметры фильтрации из URL
function loadURLParams() {
  console.log("Loading URL parameters");
  const params = new URLSearchParams(window.location.search);
  let needToApplyFilters = false;

  // Загружаем параметры поиска
  if (params.has("search")) {
    activeFilters.search = params.get("search");
    const searchInput = document.querySelector(".search__input");
    if (searchInput) {
      searchInput.value = activeFilters.search;
      console.log("Set search value from URL:", activeFilters.search);
    }
    needToApplyFilters = true;
  }

  // Загружаем тип кузова
  if (params.has("body")) {
    activeFilters.bodyType = params.get("body");
    needToApplyFilters = true;
    console.log("Set body type from URL:", activeFilters.bodyType);
  }

  // Загружаем диапазон цен
  if (params.has("min_price")) {
    activeFilters.priceRange.min = parseInt(params.get("min_price"));
    needToApplyFilters = true;
    console.log("Set min price from URL:", activeFilters.priceRange.min);
  }

  if (params.has("max_price")) {
    activeFilters.priceRange.max = parseInt(params.get("max_price"));
    needToApplyFilters = true;
    console.log("Set max price from URL:", activeFilters.priceRange.max);
  }

  // Активируем соответствующие кнопки после создания фильтров
  if (needToApplyFilters) {
    console.log("Need to apply filters from URL");

    // Используем небольшую задержку, чтобы убедиться, что фильтры созданы
    setTimeout(() => {
      // Активируем кнопку типа кузова
      if (activeFilters.bodyType) {
        const bodyTypeBtn = document.querySelector(
          `.filter-option[data-value="${activeFilters.bodyType}"]`
        );
        if (bodyTypeBtn) {
          const parentGroup = bodyTypeBtn.closest(".filter-group");
          if (parentGroup) {
            parentGroup
              .querySelectorAll(".filter-option")
              .forEach((btn) => btn.classList.remove("active"));
            bodyTypeBtn.classList.add("active");
            console.log("Activated body type button:", bodyTypeBtn);
          }
        }
      }

      // Активируем кнопку цены
      if (
        activeFilters.priceRange.min > 0 ||
        activeFilters.priceRange.max < Infinity
      ) {
        const priceBtn = document.querySelector(
          `.filter-option[data-min="${activeFilters.priceRange.min}"][data-max="${activeFilters.priceRange.max}"]`
        );
        if (priceBtn) {
          const parentGroup = priceBtn.closest(".filter-group");
          if (parentGroup) {
            parentGroup
              .querySelectorAll(".filter-option")
              .forEach((btn) => btn.classList.remove("active"));
            priceBtn.classList.add("active");
            console.log("Activated price button:", priceBtn);
          }
        }
      }

      // Применяем сортировку
      if (params.has("sort")) {
        const sortType = params.get("sort");
        const sortBtn = document.querySelector(
          `.filter-option[data-sort="${sortType}"]`
        );
        if (sortBtn) {
          const parentGroup = sortBtn.closest(".filter-group");
          if (parentGroup) {
            parentGroup
              .querySelectorAll(".filter-option")
              .forEach((btn) => btn.classList.remove("active"));
            sortBtn.classList.add("active");
            console.log("Activated sort button:", sortBtn);
          }
        }
      }

      // Применяем фильтры
      applyFilters();
    }, 500); // Увеличиваем задержку до 500мс для уверенности
  }
}

// Применяет активные фильтры к списку машин
function applyFilters() {
  console.log("Applying filters:", activeFilters);

  // Фильтруем автомобили
  const filteredCars = filterCars(allCars);

  // Сортируем результаты (если есть активная сортировка)
  const currentSortBtn = document.querySelector(
    '.filter-option[data-value^="price-"].active, .filter-option[data-value="power-desc"].active'
  );
  let sortedFilteredCars = filteredCars;

  if (currentSortBtn) {
    const sortType = currentSortBtn.getAttribute("data-value");
    if (sortType) {
      console.log("Applying current sort:", sortType);
      sortedFilteredCars = sortCarsByType(filteredCars, sortType);
    }
  }

  // Обновляем счетчик результатов
  const resultsCounter = document.querySelector(".results-counter");
  if (resultsCounter) {
    resultsCounter.textContent = `Найдено автомобилей: ${sortedFilteredCars.length}`;
  }

  // Обновляем URL с текущими параметрами
  updateURLParams();

  // Отображаем отфильтрованные и отсортированные машины
  displayCarCards(sortedFilteredCars);

  // Убеждаемся, что старые фильтры не активны
  removeOldFilters();
}

// Функция для определения метода сортировки
function sortCars(sortType) {
  console.log(`Sorting cars by type: ${sortType}`);

  // Применяем текущие фильтры перед сортировкой
  let filteredCars = filterCars(allCars);

  // Сортируем отфильтрованные машины
  filteredCars = sortCarsByType(filteredCars, sortType);

  // Отображаем отсортированные машины
  displayCarCards(filteredCars);

  // Обновляем URL с параметром сортировки
  updateURLParams();
}

// Фильтрует автомобили по активным фильтрам
function filterCars(cars) {
  if (!cars || !Array.isArray(cars)) return [];

  let filteredCars = [...cars];

  // Фильтрация по тексту поиска
  if (activeFilters.search) {
    const searchTerm = activeFilters.search.toLowerCase();
    filteredCars = filteredCars.filter((car) => {
      if (!car) return false;

      const titleMatch =
        car.title && car.title.toLowerCase().includes(searchTerm);
      const subtitleMatch =
        car.subtitle && car.subtitle.toLowerCase().includes(searchTerm);
      const bodyMatch =
        car.body_type && car.body_type.toLowerCase().includes(searchTerm);
      const engineMatch =
        car.engine && car.engine.toLowerCase().includes(searchTerm);
      const transmissionMatch =
        car.transmission && car.transmission.toLowerCase().includes(searchTerm);

      return (
        titleMatch ||
        subtitleMatch ||
        bodyMatch ||
        engineMatch ||
        transmissionMatch
      );
    });
  }

  // Фильтрация по типу кузова
  if (activeFilters.bodyType) {
    filteredCars = filteredCars.filter((car) => {
      if (!car || !car.body_type) return false;
      return (
        car.body_type.toLowerCase() === activeFilters.bodyType.toLowerCase()
      );
    });
  }

  // Фильтрация по цене
  if (
    activeFilters.priceRange.min > 0 ||
    activeFilters.priceRange.max < Infinity
  ) {
    filteredCars = filteredCars.filter((car) => {
      if (!car || !car.price) return false;

      // Извлекаем числовое значение цены
      const priceStr = car.price.replace(/[^\d]/g, "");
      const price = parseInt(priceStr, 10);

      if (isNaN(price)) {
        return false;
      }

      return (
        price >= activeFilters.priceRange.min &&
        price <= activeFilters.priceRange.max
      );
    });
  }

  return filteredCars;
}

// Функция для фактической сортировки массива автомобилей
function sortCarsByType(cars, sortType) {
  console.log(`Actually sorting ${cars.length} cars by type: ${sortType}`);

  let sortedCars = [...cars]; // Копируем массив для сортировки

  switch (sortType) {
    case "price-asc":
      sortedCars.sort((a, b) => {
        const priceA = parseInt(a.price.replace(/[^\d]/g, "")) || 0;
        const priceB = parseInt(b.price.replace(/[^\d]/g, "")) || 0;
        return priceA - priceB;
      });
      break;
    case "price-desc":
      sortedCars.sort((a, b) => {
        const priceA = parseInt(a.price.replace(/[^\d]/g, "")) || 0;
        const priceB = parseInt(b.price.replace(/[^\d]/g, "")) || 0;
        return priceB - priceA;
      });
      break;
    case "power-desc":
      sortedCars.sort((a, b) => {
        const powerA = parseInt(a.power) || 0;
        const powerB = parseInt(b.power) || 0;
        return powerB - powerA;
      });
      break;
    case "default":
    default:
      // Оставляем исходный порядок
      break;
  }

  return sortedCars;
}

// Функция для отображения карточек автомобилей
function displayCarCards(cars) {
  const stockCards = document.querySelector(".stock-cards");
  stockCards.innerHTML = "";
  if (!Array.isArray(cars) || cars.length === 0) {
    stockCards.innerHTML =
      '<div style="text-align:center;padding:30px;color:#666;">Нет автомобилей в наличии</div>';
    return;
  }
  cars.forEach((car) => {
    const carCard = createCarCard(car);
    stockCards.appendChild(carCard);
  });
}

// Функция для форматирования цветов интерьера
function formatInteriorColors(colors) {
  console.log("Formatting interior colors, raw input:", colors);
  if (!colors) return "";

  try {
    // Пробуем распарсить JSON формат (новый)
    const colorObjects = JSON.parse(colors);
    console.log("Parsed colors as JSON:", colorObjects);
    if (Array.isArray(colorObjects)) {
      let dotsHtml = '<div class="color-dots-container">';

      colorObjects.forEach((color) => {
        dotsHtml += `<span class="dot" style="background-color: ${color.hex};" title="${color.name}"></span>`;
      });

      dotsHtml += "</div>";
      // Убираем отображение имен цветов
      return dotsHtml;
    }
  } catch (e) {
    // Старый формат (обратная совместимость)
    console.log("Error parsing JSON, using legacy format. Error:", e);
    return colors
      .split(",")
      .map((color) => {
        const colorClass = color.trim().toLowerCase();
        return `<span class="dot dot--${colorClass}"></span>`;
      })
      .join("");
  }
}

// Функция для форматирования цены (для авто в наличии - уже в долларах)
function formatPrice(price) {
  if (!price) return "";

  // Для авто в наличии цены уже в долларах из админки
  const numericPrice = typeof price === 'string' ? price.replace(/[^\d.]/g, "") : price;
  return `$${Number(numericPrice).toLocaleString("en-US", {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  })}`;
}

// Функция для создания карточки автомобиля (идеально по шаблону)
function createCarCard(car) {
  console.log("Creating card for car:", car);

  if (!car || typeof car !== "object") {
    console.error("Invalid car data", car);
    return document.createElement("div"); // Возвращаем пустой элемент
  }

  // Проверяем, что у машины есть все необходимые поля
  const defaultImage = "/assets/img/cars/default-car.jpg";
  const safeGetValue = (value, defaultVal = "") => (value ? value : defaultVal);

  const card = document.createElement("div");
  card.className = "car-card";

  // Проверяем, есть ли детальная страница для этого автомобиля
  const detailPage = car.detailPage || null;

  // Если есть детальная страница, делаем карточку кликабельной
  if (detailPage) {
    card.style.cursor = "pointer";
    card.addEventListener("click", () => {
      window.location.href = detailPage;
    });
  }

  card.innerHTML = `
        <div class="car-card__header">
          <div>
            <div class="car-card__title">${safeGetValue(car.title)}</div>
            <div class="car-card__subtitle">${safeGetValue(car.subtitle)}</div>
          </div>
        </div>
        <div class="car-card__image">
          <img src="${safeGetValue(
            car.image_path,
            defaultImage
          )}" alt="${safeGetValue(
    car.title,
    "Автомобиль"
  )}" onerror="this.src='${defaultImage}'">
        </div>
        <div class="car-card__specs-row">
          <div class="car-card__spec">
            <span class="car-card__spec-main">${safeGetValue(car.power)}</span>
            <div class="car-card__spec-label">Мощность</div>
          </div>
          <div class="car-card__spec-divider"></div>
          <div class="car-card__spec">
            <span class="car-card__spec-main">${safeGetValue(
              car.mileage
            )}</span>
            <div class="car-card__spec-label">Пробег</div>
          </div>
        </div>
        <div class="car-card__info">
          <div class="car-card__info-list">
            <div>Тип кузова <b>${safeGetValue(car.body_type)}</b></div>
            <div>Двигатель <b>${safeGetValue(car.engine)}</b></div>
            <div>Трансмиссия <b>${safeGetValue(car.transmission)}</b></div>
            <div>Цвета интерьера ${formatInteriorColors(
              car.interior_colors
            )}</div>
            <div>Расход <b>${safeGetValue(car.consumption)}</b></div>
            <div>Вместимость <b>${safeGetValue(car.capacity)}</b></div>
          </div>
          <div class="car-card__price-block">
            <div class="car-card__price">${formatPrice(
              safeGetValue(car.price)
            )}</div>
            <div class="car-card__in-stock">В наличии в Москве</div>
          </div>
        </div>
    `;
  return card;
}

// Удаляет старые фильтры, которые могут появляться на странице
function removeOldFilters() {
  // Находим и удаляем старые фильтры
  const oldFilters = document.querySelector(".additional-filters");
  if (oldFilters) {
    oldFilters.style.display = "none";
    oldFilters.innerHTML = "";
  }

  // Скрываем старую кнопку "Все автомобили"
  const oldBtn = document.getElementById("show-all-filter");
  if (oldBtn) {
    oldBtn.style.display = "none";
  }
}

// Функция для инициализации автодополнения
function initializeAutocomplete() {
  const searchInput = document.querySelector(".search__input");
  const dropdownContainer = document.getElementById("autocomplete-dropdown");
  const searchForm = document.querySelector(".search__form");

  if (!searchInput || !dropdownContainer) return;

  // Функция для фильтрации брендов и моделей по введенному тексту
  function filterSuggestions(searchText) {
    if (!searchText) return [];
    searchText = searchText.toLowerCase();

    // Фильтруем бренды
    const brandMatches = availableCarBrands
      .filter((brand) => brand.toLowerCase().includes(searchText))
      .map((brand) => ({ text: brand, type: "brand" }));

    // Фильтруем модели
    const modelMatches = availableCarModels
      .filter((model) => model.toLowerCase().includes(searchText))
      .map((model) => ({ text: model, type: "model" }));

    // Сначала бренды, затем модели, ограничиваем до 10 результатов
    return [...brandMatches, ...modelMatches].slice(0, 10);
  }

  // Функция для создания и отображения выпадающего списка
  function showDropdown(suggestions) {
    dropdownContainer.innerHTML = "";

    if (suggestions.length === 0) {
      dropdownContainer.classList.remove("show");
      return;
    }

    suggestions.forEach((suggestion) => {
      const item = document.createElement("div");
      item.className = "autocomplete-item";
      if (suggestion.type === "model") {
        item.classList.add("autocomplete-model");
      } else {
        item.classList.add("autocomplete-brand");
      }

      item.textContent = suggestion.text;

      item.addEventListener("click", () => {
        searchInput.value = suggestion.text;
        dropdownContainer.classList.remove("show");

        // Не применяем фильтр автоматически, просто заполняем поле
        // activeFilters.search = suggestion.text;
        // applyFilters();
        // updateActiveFilterTags();
      });

      dropdownContainer.appendChild(item);
    });

    dropdownContainer.classList.add("show");
  }

  // Обработчик ввода текста
  searchInput.addEventListener("input", function () {
    const value = this.value.trim();
    if (value.length > 0) {
      const matches = filterSuggestions(value);
      showDropdown(matches);
    } else {
      dropdownContainer.classList.remove("show");
    }
  });

  // Закрыть выпадающий список при клике вне элемента
  document.addEventListener("click", function (e) {
    if (
      !searchInput.contains(e.target) &&
      !dropdownContainer.contains(e.target)
    ) {
      dropdownContainer.classList.remove("show");
    }
  });

  // Обработка нажатий клавиш для навигации по списку
  searchInput.addEventListener("keydown", function (e) {
    if (!dropdownContainer.classList.contains("show")) return;

    const items = dropdownContainer.querySelectorAll(".autocomplete-item");
    const activeItem = dropdownContainer.querySelector(
      ".autocomplete-item.active"
    );
    let activeIndex = -1;

    if (activeItem) {
      activeIndex = Array.from(items).indexOf(activeItem);
    }

    // Стрелка вниз
    if (e.key === "ArrowDown") {
      e.preventDefault();
      if (activeIndex < items.length - 1) {
        if (activeItem) activeItem.classList.remove("active");
        items[activeIndex + 1].classList.add("active");
        items[activeIndex + 1].scrollIntoView({ block: "nearest" });
      }
    }
    // Стрелка вверх
    else if (e.key === "ArrowUp") {
      e.preventDefault();
      if (activeIndex > 0) {
        if (activeItem) activeItem.classList.remove("active");
        items[activeIndex - 1].classList.add("active");
        items[activeIndex - 1].scrollIntoView({ block: "nearest" });
      }
    }
    // Enter
    else if (e.key === "Enter" && activeItem) {
      e.preventDefault();
      searchInput.value = activeItem.textContent;
      dropdownContainer.classList.remove("show");

      // При нажатии Enter отправляем форму поиска вместо автоматического поиска
      if (searchForm) {
        const submitEvent = new Event("submit");
        searchForm.dispatchEvent(submitEvent);
      }
    }
    // Escape
    else if (e.key === "Escape") {
      dropdownContainer.classList.remove("show");
    }
  });
}

// Добавляем вызов функции инициализации автодополнения при загрузке страницы
document.addEventListener("DOMContentLoaded", function () {
  loadCarCards().then(() => {
    console.log("Car cards loaded!");
    initializeAutocomplete();
  });
});
