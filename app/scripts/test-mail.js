#!/usr/bin/env node

/**
 * Скрипт для тестирования почтовой системы SHMS Авто
 */

require('dotenv').config();
const { sendContactForm, sendEmail } = require('../config/mail-config');

// Цвета для консоли
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Тестовые данные
const testFormData = {
  name: 'Тестовый пользователь',
  region: 'Москва',
  carType: 'Седан',
  model: 'BMW X5',
  phone: '+7 (999) 123-45-67',
  email: '<EMAIL>',
  contactMethod: 'Телефон',
  message: 'Это тестовое сообщение для проверки работы почтовой системы SHMS Авто.',
  formType: 'Тестовая заявка'
};

async function testBasicEmail() {
  log('📧 Тестирование базовой отправки email...', 'yellow');
  
  try {
    const result = await sendEmail({
      to: process.env.MANAGER_EMAIL || process.env.CONTACT_EMAIL,
      subject: 'Тест почтовой системы SHMS Авто',
      text: 'Это тестовое сообщение для проверки работы почтовой системы.',
      html: '<h2>Тест почтовой системы SHMS Авто</h2><p>Это тестовое сообщение для проверки работы почтовой системы.</p>'
    });
    
    log('✅ Базовый email отправлен успешно!', 'green');
    log(`📨 Message ID: ${result.messageId}`, 'blue');
    return true;
  } catch (error) {
    log(`❌ Ошибка отправки базового email: ${error.message}`, 'red');
    return false;
  }
}

async function testContactForm() {
  log('📝 Тестирование формы обратной связи...', 'yellow');
  
  try {
    const result = await sendContactForm(testFormData);
    
    log('✅ Форма обратной связи отправлена успешно!', 'green');
    log(`📨 Message ID: ${result.messageId}`, 'blue');
    return true;
  } catch (error) {
    log(`❌ Ошибка отправки формы: ${error.message}`, 'red');
    return false;
  }
}

async function testMailConfiguration() {
  log('⚙️  Проверка конфигурации почты...', 'yellow');
  
  const requiredVars = [
    'MAIL_HOST',
    'MAIL_PORT',
    'MAIL_USER',
    'MAIL_PASS',
    'CONTACT_EMAIL'
  ];
  
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    log(`❌ Отсутствуют переменные окружения: ${missing.join(', ')}`, 'red');
    return false;
  }
  
  log('✅ Все необходимые переменные окружения настроены', 'green');
  
  // Проверяем настройки
  log(`📧 SMTP сервер: ${process.env.MAIL_HOST}:${process.env.MAIL_PORT}`, 'blue');
  log(`👤 Пользователь: ${process.env.MAIL_USER}`, 'blue');
  log(`📬 Получатель: ${process.env.CONTACT_EMAIL}`, 'blue');
  
  return true;
}

async function runMailTests() {
  log('🧪 SHMS Авто - Тестирование почтовой системы', 'blue');
  log('===============================================', 'blue');
  
  const results = {
    timestamp: new Date().toISOString(),
    tests: {}
  };
  
  // Проверка конфигурации
  results.tests.configuration = await testMailConfiguration();
  
  if (!results.tests.configuration) {
    log('❌ Тестирование прервано из-за проблем с конфигурацией', 'red');
    return results;
  }
  
  // Тест базовой отправки
  results.tests.basicEmail = await testBasicEmail();
  
  // Небольшая пауза между тестами
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Тест формы обратной связи
  results.tests.contactForm = await testContactForm();
  
  // Итоговый результат
  log('===============================================', 'blue');
  
  const allPassed = Object.values(results.tests).every(result => result === true);
  
  if (allPassed) {
    log('🎉 Все тесты прошли успешно!', 'green');
    log('📧 Почтовая система готова к работе', 'green');
  } else {
    log('⚠️  Некоторые тесты не прошли', 'yellow');
    log('🔧 Проверьте настройки почты в .env файле', 'yellow');
  }
  
  return results;
}

// Функция для интерактивного тестирования
async function interactiveTest() {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  function question(prompt) {
    return new Promise(resolve => {
      rl.question(prompt, resolve);
    });
  }
  
  log('🎯 Интерактивное тестирование почты', 'blue');
  log('==================================', 'blue');
  
  const email = await question('Введите email для тестирования (или Enter для использования из .env): ');
  const subject = await question('Введите тему письма (или Enter для стандартной): ');
  const message = await question('Введите сообщение (или Enter для стандартного): ');
  
  rl.close();
  
  try {
    const result = await sendEmail({
      to: email || process.env.CONTACT_EMAIL,
      subject: subject || 'Интерактивный тест SHMS Авто',
      text: message || 'Это интерактивное тестовое сообщение.',
      html: `<h2>Интерактивный тест SHMS Авто</h2><p>${message || 'Это интерактивное тестовое сообщение.'}</p>`
    });
    
    log('✅ Письмо отправлено успешно!', 'green');
    log(`📨 Message ID: ${result.messageId}`, 'blue');
  } catch (error) {
    log(`❌ Ошибка отправки: ${error.message}`, 'red');
  }
}

// Запуск если файл вызван напрямую
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--interactive') || args.includes('-i')) {
    interactiveTest().catch(error => {
      log(`💥 Ошибка: ${error.message}`, 'red');
      process.exit(1);
    });
  } else {
    runMailTests()
      .then(results => {
        const allPassed = Object.values(results.tests).every(result => result === true);
        process.exit(allPassed ? 0 : 1);
      })
      .catch(error => {
        log(`💥 Критическая ошибка: ${error.message}`, 'red');
        process.exit(1);
      });
  }
}

module.exports = { runMailTests, testBasicEmail, testContactForm };
