<?php
// Тестовый скрипт для проверки конвертации цен

// Функция для получения курсов валют (копия из основного файла)
function getExchangeRates() {
    try {
        $url = 'http://localhost:8080/api/exchange-rates.php';
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 3);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($httpCode === 200 && $response) {
            $data = json_decode($response, true);
            if ($data && $data['success']) {
                $krwToRub = $data['KRW_to_RUB'];
                $usdRate = $data['USD_rates']['usdtrub']['sell'] ?? 95;

                $cachedRates = [
                    'KRW_to_RUB' => $krwToRub,
                    'USD_rate' => $usdRate
                ];
                echo "API rates loaded: KRW_to_RUB={$krwToRub}, USD_rate={$usdRate}\n";
                return $cachedRates;
            }
        }
    } catch (Exception $e) {
        echo "Error getting exchange rates: " . $e->getMessage() . "\n";
    }

    // Fallback курсы если API недоступен
    $cachedRates = [
        'KRW_to_RUB' => 0.075, // Примерный курс воны к рублю
        'USD_rate' => 95       // Примерный курс доллара
    ];
    echo "Using fallback rates: KRW_to_RUB=0.075, USD_rate=95\n";
    return $cachedRates;
}

// Функция для конвертации цены из корейских вон в доллары (копия из основного файла)
function convertKRWToUSD($priceInKRW, $rates) {
    if (empty($priceInKRW) || $priceInKRW <= 0) {
        return 0;
    }

    // Формула: цена_в_сокращенном_виде × 10,000 → воны → рубли (через ЦБ) → доллары (через обменник)
    // 1. Умножаем на 10,000 (цены в CSV в сокращенном виде)
    $realPriceInKRW = $priceInKRW * 10000;

    // 2. Конвертируем воны в рубли
    $priceInRUB = $realPriceInKRW * $rates['KRW_to_RUB'];

    // 3. Конвертируем рубли в доллары
    $priceInUSD = $priceInRUB / $rates['USD_rate'];

    $result = round($priceInUSD, 0); // Округляем до целых долларов

    echo "Price conversion: {$priceInKRW} → {$realPriceInKRW} KRW → {$priceInRUB} RUB → {$result} USD\n";
    echo "Rates used: KRW_to_RUB={$rates['KRW_to_RUB']}, USD_rate={$rates['USD_rate']}\n";

    return $result;
}

// Тестируем конвертацию
echo "=== Тест конвертации цен ===\n\n";

$rates = getExchangeRates();
echo "\n";

// Тестируем проблемную цену 2600
echo "Тестируем цену 2600 (из проблемного автомобиля):\n";
$result = convertKRWToUSD(2600, $rates);
echo "Результат: $" . number_format($result) . "\n\n";

// Тестируем другие цены для сравнения
$testPrices = [130, 159, 1000, 5000];
foreach ($testPrices as $price) {
    echo "Тестируем цену {$price}:\n";
    $result = convertKRWToUSD($price, $rates);
    echo "Результат: $" . number_format($result) . "\n\n";
}
