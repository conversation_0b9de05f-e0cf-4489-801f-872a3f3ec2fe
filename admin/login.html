<!DOCTYPE html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Вход в админку | SHMS Auto</title>
    <link rel="stylesheet" href="css/login.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"
    />
    <link rel="icon" href="../public/favicon.ico" type="image/x-icon" />
  </head>
  <body>
    <div class="login-container">
      <div class="login-background">
        <div class="login-overlay"></div>
      </div>

      <div class="login-content">
        <div class="login-card">
          <div class="login-header">
            <div class="login-logo">
              <i class="fas fa-car"></i>
              <h1>SHMS Auto</h1>
            </div>
            <h2>Вход в админку</h2>
            <p>Введите ваши учетные данные для доступа к панели управления</p>
          </div>

          <form id="loginForm" class="login-form">
            <div class="form-group">
              <label for="username">
                <i class="fas fa-user"></i>
                Логин
              </label>
              <input
                type="text"
                id="username"
                name="username"
                required
                autocomplete="username"
                placeholder="Введите ваш логин"
              />
              <div class="field-error" id="username-error"></div>
            </div>

            <div class="form-group">
              <label for="password">
                <i class="fas fa-lock"></i>
                Пароль
              </label>
              <div class="password-input">
                <input
                  type="password"
                  id="password"
                  name="password"
                  required
                  autocomplete="current-password"
                  placeholder="Введите ваш пароль"
                />
                <button
                  type="button"
                  class="password-toggle"
                  id="passwordToggle"
                >
                  <i class="fas fa-eye"></i>
                </button>
              </div>
              <div class="field-error" id="password-error"></div>
            </div>

            <div class="form-options">
              <label class="remember-me">
                <input type="checkbox" id="rememberMe" name="rememberMe" />
                <span class="checkmark"></span>
                Запомнить меня
              </label>
            </div>

            <button type="submit" class="login-button" id="loginButton">
              <span class="button-text">Войти</span>
              <span class="button-loader" style="display: none">
                <i class="fas fa-spinner fa-spin"></i>
              </span>
            </button>

            <div class="form-message" id="formMessage"></div>
          </form>

          <div class="login-footer">
            <p>
              <i class="fas fa-shield-alt"></i>
              Защищенный доступ к системе управления
            </p>
            <div class="security-info">
              <small>
                <i class="fas fa-info-circle"></i>
                Все попытки входа логируются в целях безопасности
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Уведомления -->
    <div id="notification" class="notification">
      <div class="notification-content">
        <div class="notification-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="notification-message">
          <div class="notification-title"></div>
          <div class="notification-text"></div>
        </div>
        <button class="notification-close">&times;</button>
      </div>
    </div>

    <script src="js/login.js"></script>
  </body>
</html>
