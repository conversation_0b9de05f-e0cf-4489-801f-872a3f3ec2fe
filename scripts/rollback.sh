#!/bin/bash

# Скрипт отката SHMS Авто к предыдущей версии
# Используется при ошибках развертывания

set -e

echo "🔄 Откат SHMS Авто к предыдущей версии"
echo "======================================"

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Функции для вывода
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

# Настройки
BACKUP_DIR="$HOME/backups"
APP_DIR="$(pwd)"
BACKUP_PREFIX="shms-auto-backup"

# Поиск последнего бэкапа
find_latest_backup() {
    log "Поиск последнего бэкапа..."
    
    if [ ! -d "$BACKUP_DIR" ]; then
        error "Директория бэкапов не найдена: $BACKUP_DIR"
    fi
    
    LATEST_BACKUP=$(find "$BACKUP_DIR" -name "${BACKUP_PREFIX}-*" -type d | sort -r | head -n 1)
    
    if [ -z "$LATEST_BACKUP" ]; then
        error "Бэкапы не найдены в $BACKUP_DIR"
    fi
    
    log "Найден бэкап: $LATEST_BACKUP"
    echo "$LATEST_BACKUP"
}

# Остановка текущего приложения
stop_application() {
    log "Остановка текущего приложения..."
    
    # Остановка PM2
    pm2 delete shms-auto 2>/dev/null || true
    
    # Остановка процессов на порту 3001
    if lsof -ti:3001 >/dev/null 2>&1; then
        log "Завершение процессов на порту 3001..."
        lsof -ti:3001 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    log "✅ Приложение остановлено"
}

# Восстановление из бэкапа
restore_from_backup() {
    local backup_path="$1"
    
    log "Восстановление из бэкапа: $backup_path"
    
    # Создание временного бэкапа текущего состояния
    local temp_backup="$BACKUP_DIR/temp-before-rollback-$(date +%Y%m%d_%H%M%S)"
    log "Создание временного бэкапа: $temp_backup"
    mkdir -p "$temp_backup"
    cp -r "$APP_DIR"/* "$temp_backup/" 2>/dev/null || true
    
    # Восстановление файлов (исключая данные и логи)
    log "Восстановление файлов приложения..."
    
    # Сохраняем важные файлы
    cp "$APP_DIR/.env" "/tmp/.env.backup" 2>/dev/null || true
    cp -r "$APP_DIR/data" "/tmp/data.backup" 2>/dev/null || true
    cp -r "$APP_DIR/logs" "/tmp/logs.backup" 2>/dev/null || true
    
    # Очищаем директорию (кроме данных)
    find "$APP_DIR" -mindepth 1 -maxdepth 1 ! -name 'data' ! -name 'logs' ! -name '.env' -exec rm -rf {} + 2>/dev/null || true
    
    # Восстанавливаем из бэкапа
    cp -r "$backup_path"/* "$APP_DIR/"
    
    # Восстанавливаем сохраненные файлы
    cp "/tmp/.env.backup" "$APP_DIR/.env" 2>/dev/null || true
    cp -r "/tmp/data.backup" "$APP_DIR/data" 2>/dev/null || true
    cp -r "/tmp/logs.backup" "$APP_DIR/logs" 2>/dev/null || true
    
    # Очистка временных файлов
    rm -rf "/tmp/.env.backup" "/tmp/data.backup" "/tmp/logs.backup" 2>/dev/null || true
    
    log "✅ Файлы восстановлены"
}

# Установка зависимостей
install_dependencies() {
    log "Установка зависимостей..."
    
    if [ -f "package.json" ]; then
        npm ci --production --silent
        log "✅ Зависимости установлены"
    else
        warn "package.json не найден, пропускаем установку зависимостей"
    fi
}

# Запуск приложения
start_application() {
    log "Запуск приложения..."
    
    # Проверка наличия ecosystem.config.js
    if [ ! -f "ecosystem.config.js" ]; then
        error "ecosystem.config.js не найден"
    fi
    
    # Запуск через PM2
    pm2 start ecosystem.config.js --env production
    pm2 save
    
    log "✅ Приложение запущено"
}

# Проверка работоспособности после отката
verify_rollback() {
    log "Проверка работоспособности после отката..."
    
    # Ждем запуска
    sleep 5
    
    # Проверка PM2
    if pm2 list | grep -q "shms-auto.*online"; then
        log "✅ PM2 процесс запущен"
    else
        error "PM2 процесс не запущен после отката"
    fi
    
    # Проверка HTTP
    max_attempts=5
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:3001/test >/dev/null 2>&1; then
            log "✅ Сервер отвечает на запросы"
            break
        else
            if [ $attempt -eq $max_attempts ]; then
                error "Сервер не отвечает после отката"
            fi
            log "Попытка $attempt/$max_attempts: проверка сервера..."
            sleep 3
            ((attempt++))
        fi
    done
}

# Основная функция отката
main() {
    log "Начало процедуры отката..."
    
    # Переход в директорию приложения
    cd "$(dirname "$0")/.."
    
    # Поиск бэкапа
    BACKUP_PATH=$(find_latest_backup)
    
    if [ -z "$BACKUP_PATH" ]; then
        error "Не удалось найти бэкап для отката"
    fi
    
    # Подтверждение отката
    echo ""
    echo "⚠️  ВНИМАНИЕ: Будет выполнен откат к версии из бэкапа:"
    echo "   $BACKUP_PATH"
    echo ""
    
    # В CI/CD режиме автоматически подтверждаем
    if [ "$CI" = "true" ] || [ "$GITLAB_CI" = "true" ]; then
        log "CI/CD режим: автоматическое подтверждение отката"
    else
        read -p "Продолжить откат? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "Откат отменен пользователем"
            exit 0
        fi
    fi
    
    # Выполнение отката
    stop_application
    restore_from_backup "$BACKUP_PATH"
    install_dependencies
    start_application
    verify_rollback
    
    log "✅ Откат выполнен успешно!"
    
    echo ""
    echo "🎉 Откат SHMS Авто завершен!"
    echo "============================"
    echo "📡 Приложение доступно: http://localhost:3001"
    echo "🌐 Публичный адрес: https://shms-auto.ru"
    echo ""
    echo "📋 Восстановлена версия из: $(basename "$BACKUP_PATH")"
    echo ""
    echo "⚠️  Рекомендации:"
    echo "  1. Проверьте работу сайта"
    echo "  2. Проверьте логи: pm2 logs shms-auto"
    echo "  3. Исправьте проблемы перед следующим развертыванием"
}

# Обработка сигналов
trap 'error "Откат прерван"' INT TERM

# Запуск
main "$@"
