<?php
// Скрипт для очистки кэша переводов

// Устанавливаем заголовок
header('Content-Type: text/html; charset=utf-8');
echo "<h1>Очистка кэша переводов</h1>";

// Путь к кэшу переводов
$cacheDir = __DIR__ . '/cache/translations/';

// Проверяем существование директории
if (!is_dir($cacheDir)) {
    echo "<p>Директория кэша переводов не найдена: $cacheDir</p>";
    echo "<p>Создаём директорию...</p>";
    
    if (mkdir($cacheDir, 0777, true)) {
        echo "<p>Директория создана успешно.</p>";
    } else {
        echo "<p>Ошибка при создании директории.</p>";
    }
    exit;
}

// Получаем все файлы в директории кэша
$files = glob($cacheDir . '*.txt');
$count = count($files);

echo "<p>Найдено файлов кэша: $count</p>";

// Добавляем кнопку для подтверждения удаления
if ($count > 0 && !isset($_GET['confirm'])) {
    echo "<p>Вы уверены, что хотите удалить все файлы кэша переводов?</p>";
    echo "<a href='?confirm=1' style='display:inline-block; padding:10px; background:#f44336; color:white; text-decoration:none; border-radius:3px; margin-right:10px;'>Да, удалить</a>";
    echo "<a href='javascript:history.back()' style='display:inline-block; padding:10px; background:#4CAF50; color:white; text-decoration:none; border-radius:3px;'>Нет, вернуться</a>";
    exit;
}

// Если подтверждено, удаляем файлы
if (isset($_GET['confirm']) && $_GET['confirm'] == 1) {
    $deleted = 0;
    
    foreach ($files as $file) {
        if (unlink($file)) {
            $deleted++;
        }
    }
    
    echo "<p>Удалено файлов: $deleted из $count</p>";
    echo "<p>Кэш переводов очищен успешно!</p>";
    echo "<a href='javascript:history.back()' style='display:inline-block; padding:10px; background:#4CAF50; color:white; text-decoration:none; border-radius:3px;'>Вернуться</a>";
} else {
    echo "<p>Нет файлов для удаления.</p>";
    echo "<a href='javascript:history.back()' style='display:inline-block; padding:10px; background:#4CAF50; color:white; text-decoration:none; border-radius:3px;'>Вернуться</a>";
}
?> 