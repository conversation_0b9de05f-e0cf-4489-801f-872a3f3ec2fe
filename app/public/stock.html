<!DOCTYPE html>
<html lang="ru">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Авто в наличии | SHMS</title>
  <link rel="stylesheet" href="css/layout.css" />
  <link rel="stylesheet" href="css/stock.css" />
  <link rel="stylesheet" href="css/footer.css" />
  <link rel="stylesheet" href="css/mobile-header.css" />
  <link rel="stylesheet" href="css/hero.css" />
</head>

<body>
  <!-- Mobile Header -->
  <header class="mobile-header">
    <button class="burger" id="burgerBtn" aria-label="Открыть меню">
      <span></span>
      <span></span>
      <span></span>
    </button>
    <a href="index.html" class="mobile-header__logo">
      <img src="/Img/logo.png" alt="SHMS" class="mobile-header__logo-img">
    </a>
  </header>

  <!-- Пустой раздел для отступа -->
  <div class="mobile-header-spacer"></div>

  <!-- Mobile Menu -->
  <nav class="dropdown-menu" id="dropdownMenu">
    <a href="stock.html">Авто в наличии</a>
    <a href="order.html">Авто под заказ</a>
    <a href="contacts.html">Наши контакты</a>
    <a href="feedback-mockup.html">Обратная связь</a>
  </nav>

  <!-- Hero / Title Section -->
  <section class="hero-stock">
    <div class="hero-header-group">
      <div class="header__left">
        <a href="#" class="header__back-link" onclick="window.history.back(); return false;">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 12H5M5 12L12 19M5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
          Назад
        </a>
      </div>
      <div class="header__center-group">
        <nav class="header__nav">
          <a href="index.html" class="header__logo-link"><img src="/Img/logo.png" alt="SHMS"
              class="header__logo-img"></a>
          <div class="header__nav-links">
            <a href="stock.html" class="header__nav-link active">Авто в наличии</a>
            <a href="order.html" class="header__nav-link">Заказать авто</a>
            <a href="contacts.html" class="header__nav-link">Наши контакты</a>
            <a href="feedback-mockup.html" class="header__nav-link">Обратная связь</a>
          </div>
        </nav>
      </div>
    </div>
  </section>
  <section class="hero-stock hero-stock--custom">
    <div class="hero-stock__visual">
      <div class="hero-stock__center">
        <h1 class="hero-stock__title-main">Авто в наличии</h1>
      </div>
    </div>
  </section>

  <!-- Фильтры -->
  <section class="filters">
    <div class="filters-row">
      <div class="filter-dropdown">
        <button class="filter-menu-btn" id="filter-menu-btn">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 6H17M6 10H14M8 14H12" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
          </svg>
          Фильтры
        </button>
        <div class="filter-menu" id="filter-menu">
          <!-- Дополнительные фильтры будут добавлены через JavaScript -->
        </div>
      </div>
      <form class="search__form" autocomplete="off">
        <div class="autocomplete-container">
          <input type="text" class="search__input" placeholder="Поиск по марке, модели или характеристикам" />
          <div class="autocomplete-dropdown" id="autocomplete-dropdown"></div>
        </div>
        <button type="submit" class="search__btn" aria-label="Найти авто">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="9" cy="9" r="7" stroke="currentColor" stroke-width="2" />
            <line x1="14.4142" y1="14" x2="18" y2="17.5858" stroke="currentColor" stroke-width="2"
              stroke-linecap="round" />
          </svg>
        </button>
      </form>
    </div>
    <div class="active-filters-container">
      <div class="active-filters" id="active-filters"></div>
    </div>
  </section>

  <!-- Список авто, карточки автомобилей -->
  <main class="stock-list">
    <div class="stock-cards">
      <!-- Car cards will be dynamically loaded here -->
    </div>
  </main>

  <!-- Footer -->
  <footer class="footer">
    <div class="footer__info">
      <div class="footer__left">
        <div class="footer__logo">
          <img src="/assets/img/icons/logo-shms.png" alt="SHMS" class="footer__logo-img" />
        </div>
        <div class="footer__address">
          Наш адрес:<br />
          Москва,<br />
          Пресненская набережная 12<br />
          Башня "Федерация"<br />
          12 этаж, офис К2
        </div>
      </div>
      <div class="footer__right">
        <nav class="footer__nav">
          <a href="order.html">Под заказ</a>
          <a href="contacts.html">Контакты</a>
          <a href="stock.html">Авто в наличии</a>
        </nav>
        <div class="footer__requisites">
          <div class="footer__requisites-title">Реквизиты организации</div>
          <div class="footer__requisites-info">
            ИП Шамаев Мансур Махмудович<br />
            ИНН 201578554480, ОГРН 324200000020490<br />
          </div>
        </div>
      </div>
    </div>
    <div class="footer__copyright">© 2025 Все права защищены</div>
  </footer>

  <script src="js/menu.js"></script>
  <script src="js/stock.js"></script>
</body>

</html>