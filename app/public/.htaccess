# Включение перезаписи URL
RewriteEngine On

# Установка базового URL (закомментировано, раскомментировать при необходимости)
# RewriteBase /

# Перенаправление HTTP на HTTPS (закомментировано, раскомментировать при необходимости)
# RewriteCond %{HTTPS} off
# RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Направление запросов к API на соответствующие файлы
RewriteRule ^api/(.*)$ api/$1 [L]

# Кэширование статических файлов
<IfModule mod_expires.c>
  ExpiresActive On
  ExpiresByType image/jpeg "access plus 1 year"
  ExpiresByType image/png "access plus 1 year"
  ExpiresByType image/gif "access plus 1 year"
  ExpiresByType image/webp "access plus 1 year"
  ExpiresByType image/svg+xml "access plus 1 year"
  ExpiresByType image/x-icon "access plus 1 year"
  ExpiresByType text/css "access plus 1 month"
  ExpiresByType text/javascript "access plus 1 month"
  ExpiresByType application/javascript "access plus 1 month"
</IfModule>

# Сжатие файлов
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/json application/xml
</IfModule>

# Обработка ошибок для основного сайта
ErrorDocument 404 /404.html

# Обработка запросов к несуществующим статическим файлам
<IfModule mod_rewrite.c>
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteRule ^(.+)\.(js|css|jpg|jpeg|png|gif|ico)$ - [L,R=404]
</IfModule> 