#!/usr/bin/env node

/**
 * Скрипт для тестирования системы авторизации
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, '../data/stock.db');

console.log('🔍 Тестирование системы авторизации SHMS Auto');
console.log('==============================================\n');

async function testAuth() {
    try {
        console.log('📋 Проверка базы данных...');
        
        const db = new sqlite3.Database(dbPath, (err) => {
            if (err) {
                console.error('❌ Ошибка подключения к БД:', err.message);
                return;
            }
            console.log('✅ Подключение к БД успешно');
        });

        // Проверяем существование таблиц
        console.log('\n📋 Проверка таблиц...');
        
        const tables = await new Promise((resolve, reject) => {
            db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        const tableNames = tables.map(t => t.name);
        console.log('📊 Найденные таблицы:', tableNames);

        // Проверяем таблицу admins
        if (tableNames.includes('admins')) {
            console.log('✅ Таблица admins существует');
            
            const admins = await new Promise((resolve, reject) => {
                db.all("SELECT id, username, email, is_active, created_at FROM admins", (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows);
                });
            });

            console.log(`👥 Найдено администраторов: ${admins.length}`);
            admins.forEach(admin => {
                console.log(`   - ${admin.username} (${admin.email}) - ${admin.is_active ? 'активен' : 'неактивен'}`);
            });
        } else {
            console.log('❌ Таблица admins не найдена');
        }

        // Проверяем таблицу security_logs
        if (tableNames.includes('security_logs')) {
            console.log('✅ Таблица security_logs существует');
        } else {
            console.log('❌ Таблица security_logs не найдена');
        }

        // Проверяем таблицу login_attempts
        if (tableNames.includes('login_attempts')) {
            console.log('✅ Таблица login_attempts существует');
        } else {
            console.log('❌ Таблица login_attempts не найдена');
        }

        db.close();

        console.log('\n📋 Проверка файлов...');
        
        const fs = require('fs');
        
        // Проверяем файлы авторизации
        const authFiles = [
            '../server/controllers/authController.js',
            '../server/models/Admin.js',
            '../server/middleware/auth.js',
            '../server/utils/security.js',
            '../server/utils/logger.js',
            '../admin/login.html',
            '../admin/js/login.js'
        ];

        authFiles.forEach(file => {
            const filePath = path.join(__dirname, file);
            if (fs.existsSync(filePath)) {
                console.log(`✅ ${file}`);
            } else {
                console.log(`❌ ${file} - НЕ НАЙДЕН`);
            }
        });

        console.log('\n📋 Проверка директорий...');
        
        const dirs = [
            '../data/sessions',
            '../data/uploads',
            '../logs'
        ];

        dirs.forEach(dir => {
            const dirPath = path.join(__dirname, dir);
            if (fs.existsSync(dirPath)) {
                console.log(`✅ ${dir}`);
            } else {
                console.log(`❌ ${dir} - НЕ НАЙДЕНА`);
            }
        });

        console.log('\n🎉 Проверка завершена!');
        
    } catch (error) {
        console.error('❌ Ошибка при тестировании:', error.message);
    }
}

testAuth().then(() => {
    process.exit(0);
}).catch(error => {
    console.error('Критическая ошибка:', error);
    process.exit(1);
});
