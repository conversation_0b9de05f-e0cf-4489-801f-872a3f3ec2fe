const sqlite3 = require("sqlite3").verbose();
const bcrypt = require("bcrypt");
const crypto = require("crypto");
const path = require("path");

class Admin {
  constructor() {
    this.dbPath = path.join(__dirname, "../../data/stock.db");
  }

  // Получение соединения с базой данных
  getDatabase() {
    return new sqlite3.Database(this.dbPath, (err) => {
      if (err) {
        console.error("Ошибка подключения к базе данных:", err.message);
      }
    });
  }

  // Создание нового администратора
  async create(userData) {
    return new Promise((resolve, reject) => {
      const db = this.getDatabase();
      const { username, email, password, fullName } = userData;

      // Хешируем пароль
      bcrypt.hash(password, 12, (err, hash) => {
        if (err) {
          db.close();
          return reject(new Error("Ошибка хеширования пароля"));
        }

        const sql = `
                    INSERT INTO admins (username, email, password_hash, full_name)
                    VALUES (?, ?, ?, ?)
                `;

        db.run(sql, [username, email, hash, fullName], function (err) {
          if (err) {
            db.close();
            if (err.code === "SQLITE_CONSTRAINT_UNIQUE") {
              return reject(
                new Error(
                  "Пользователь с таким логином или email уже существует"
                )
              );
            }
            return reject(
              new Error("Ошибка создания администратора: " + err.message)
            );
          }

          db.close();
          resolve({
            id: this.lastID,
            username,
            email,
            fullName,
            isActive: true,
            createdAt: new Date().toISOString(),
          });
        });
      });
    });
  }

  // Поиск администратора по логину
  async findByUsername(username) {
    return new Promise((resolve, reject) => {
      const db = this.getDatabase();
      const sql = `
                SELECT id, username, email, password_hash, full_name, is_active, 
                       created_at, last_login, reset_token, reset_token_expires
                FROM admins 
                WHERE username = ? AND is_active = 1
            `;

      db.get(sql, [username], (err, row) => {
        db.close();
        if (err) {
          return reject(
            new Error("Ошибка поиска администратора: " + err.message)
          );
        }
        resolve(row || null);
      });
    });
  }

  // Поиск администратора по email
  async findByEmail(email) {
    return new Promise((resolve, reject) => {
      const db = this.getDatabase();
      const sql = `
                SELECT id, username, email, password_hash, full_name, is_active, 
                       created_at, last_login, reset_token, reset_token_expires
                FROM admins 
                WHERE email = ? AND is_active = 1
            `;

      db.get(sql, [email], (err, row) => {
        db.close();
        if (err) {
          return reject(
            new Error("Ошибка поиска администратора: " + err.message)
          );
        }
        resolve(row || null);
      });
    });
  }

  // Поиск администратора по ID
  async findById(id) {
    return new Promise((resolve, reject) => {
      const db = this.getDatabase();
      const sql = `
                SELECT id, username, email, full_name, is_active, 
                       created_at, last_login
                FROM admins 
                WHERE id = ? AND is_active = 1
            `;

      db.get(sql, [id], (err, row) => {
        db.close();
        if (err) {
          return reject(
            new Error("Ошибка поиска администратора: " + err.message)
          );
        }
        resolve(row || null);
      });
    });
  }

  // Проверка пароля
  async verifyPassword(password, hash) {
    return bcrypt.compare(password, hash);
  }

  // Обновление времени последнего входа
  async updateLastLogin(id) {
    return new Promise((resolve, reject) => {
      const db = this.getDatabase();
      const sql =
        "UPDATE admins SET last_login = CURRENT_TIMESTAMP WHERE id = ?";

      db.run(sql, [id], (err) => {
        db.close();
        if (err) {
          return reject(
            new Error("Ошибка обновления времени входа: " + err.message)
          );
        }
        resolve();
      });
    });
  }

  // Генерация токена сброса пароля
  async generateResetToken(email) {
    return new Promise((resolve, reject) => {
      const db = this.getDatabase();
      const token = crypto.randomBytes(32).toString("hex");
      const expires = new Date(Date.now() + 3600000); // 1 час

      const sql = `
                UPDATE admins 
                SET reset_token = ?, reset_token_expires = ? 
                WHERE email = ? AND is_active = 1
            `;

      db.run(sql, [token, expires.toISOString(), email], function (err) {
        db.close();
        if (err) {
          return reject(new Error("Ошибка генерации токена: " + err.message));
        }
        if (this.changes === 0) {
          return reject(new Error("Пользователь с таким email не найден"));
        }
        resolve(token);
      });
    });
  }

  // Поиск администратора по токену сброса
  async findByResetToken(token) {
    return new Promise((resolve, reject) => {
      const db = this.getDatabase();
      const sql = `
                SELECT id, username, email, full_name
                FROM admins 
                WHERE reset_token = ? AND reset_token_expires > CURRENT_TIMESTAMP AND is_active = 1
            `;

      db.get(sql, [token], (err, row) => {
        db.close();
        if (err) {
          return reject(new Error("Ошибка поиска токена: " + err.message));
        }
        resolve(row || null);
      });
    });
  }

  // Сброс пароля
  async resetPassword(token, newPassword) {
    return new Promise((resolve, reject) => {
      const db = this.getDatabase();

      // Хешируем новый пароль
      bcrypt.hash(newPassword, 12, (err, hash) => {
        if (err) {
          db.close();
          return reject(new Error("Ошибка хеширования пароля"));
        }

        const sql = `
                    UPDATE admins 
                    SET password_hash = ?, reset_token = NULL, reset_token_expires = NULL
                    WHERE reset_token = ? AND reset_token_expires > CURRENT_TIMESTAMP AND is_active = 1
                `;

        db.run(sql, [hash, token], function (err) {
          db.close();
          if (err) {
            return reject(new Error("Ошибка сброса пароля: " + err.message));
          }
          if (this.changes === 0) {
            return reject(new Error("Недействительный или истекший токен"));
          }
          resolve();
        });
      });
    });
  }

  // Получение всех администраторов
  async getAll() {
    return new Promise((resolve, reject) => {
      const db = this.getDatabase();
      const sql = `
                SELECT id, username, email, full_name, is_active, created_at, last_login
                FROM admins 
                ORDER BY created_at DESC
            `;

      db.all(sql, [], (err, rows) => {
        db.close();
        if (err) {
          return reject(
            new Error("Ошибка получения списка администраторов: " + err.message)
          );
        }
        resolve(rows || []);
      });
    });
  }

  // Деактивация администратора
  async deactivate(id) {
    return new Promise((resolve, reject) => {
      const db = this.getDatabase();
      const sql = "UPDATE admins SET is_active = 0 WHERE id = ?";

      db.run(sql, [id], function (err) {
        db.close();
        if (err) {
          return reject(
            new Error("Ошибка деактивации администратора: " + err.message)
          );
        }
        if (this.changes === 0) {
          return reject(new Error("Администратор не найден"));
        }
        resolve();
      });
    });
  }

  // Активация администратора
  async activate(id) {
    return new Promise((resolve, reject) => {
      const db = this.getDatabase();
      const sql = "UPDATE admins SET is_active = 1 WHERE id = ?";

      db.run(sql, [id], function (err) {
        db.close();
        if (err) {
          return reject(
            new Error("Ошибка активации администратора: " + err.message)
          );
        }
        if (this.changes === 0) {
          return reject(new Error("Администратор не найден"));
        }
        resolve();
      });
    });
  }

  // Обновление пароля администратора
  async updatePassword(id, newPassword) {
    return new Promise((resolve, reject) => {
      const db = this.getDatabase();

      // Хешируем новый пароль
      bcrypt.hash(newPassword, 12, (err, hash) => {
        if (err) {
          db.close();
          return reject(new Error("Ошибка хеширования пароля"));
        }

        const sql =
          "UPDATE admins SET password_hash = ? WHERE id = ? AND is_active = 1";

        db.run(sql, [hash, id], function (err) {
          db.close();
          if (err) {
            return reject(
              new Error("Ошибка обновления пароля: " + err.message)
            );
          }
          if (this.changes === 0) {
            return reject(new Error("Администратор не найден"));
          }
          resolve();
        });
      });
    });
  }
}

module.exports = Admin;
