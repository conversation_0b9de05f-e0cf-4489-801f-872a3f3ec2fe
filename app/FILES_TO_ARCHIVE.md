# ФАЙЛЫ ДЛЯ АРХИВИРОВАНИЯ/УДАЛЕНИЯ

## .bak файлы (найденные)
- public/api/encar-proxy.php.bak
- public/index.html.bak
- public/contacts.html.bak
- public/order.html.bak
- public/stock.html.bak
- public/car-detail.html.bak
- public/mobile-header.css.bak
- public/order-encar.js.bak

## Тестовые и отладочные файлы
- public/api-tester.html
- public/upload-test.html
- public/feedback-mockup.html
- public/feedback-mockup.css
- scripts/api-check.php
- scripts/analyze-api-structure.php
- scripts/create-images.php
- scripts/create-placeholder.php
- scripts/create-placeholder-simple.php
- scripts/create-placeholders.php
- scripts/create-default-logo.php
- check-db.js

## Примеры и демо файлы
- public/Primer/ (вся папка)
  - public/Primer/2024-audi-rs6-avante-perfomance.html
  - public/Primer/car-detail.css
  - public/Primer/footer.css
  - public/Primer/menu.js

## Дублирующиеся файлы
- public/cars/2024-audi-rs6-avante-perfomance-1.html (дубликат из Primer)

## Временные и кэш файлы
- cache/ (вся папка - будет перемещена в data/cache/)
- public/api/cache/ (кэш API)
- public/data/ (будет перемещена в data/)

## Неиспользуемые скрипты
- scripts/download_csv.php
- scripts/download_csv.py
- scripts/mock-data.php
- scripts/fetch-real-cars.js
- scripts/encar-proxy.js
- scripts/cars-csv-to-json.php

## Почтовые скрипты (возможно устаревшие)
- Pochta/ (вся папка)
  - Pochta/PHPMailer/
  - Pochta/send-mail.php
  - Pochta/test-mail.php

## Сертификаты и конфигурация
- cacert.pem

## Статус файлов для принятия решения
**ТРЕБУЕТ ПРОВЕРКИ:**
- public/api/dummy.txt
- public/api/encar_extra_description.txt
- public/api/test.php
- public/api/test-curl.php
- public/api/test-google-translate.php
- public/api/test-translation.php
- public/api/debug-csv.php
- public/api/debug-data.php
- public/api/debug-volvo.php

## Рекомендации по действиям

### ПЕРЕМЕСТИТЬ В ARCHIVE/
- Все .bak файлы
- public/Primer/
- Тестовые файлы
- Неиспользуемые скрипты

### УДАЛИТЬ ПОЛНОСТЬЮ
- Временные кэш файлы (после перемещения актуальных)
- Дублирующиеся файлы

### ОСТАВИТЬ (НО ПЕРЕМЕСТИТЬ)
- cache/ → data/cache/
- public/data/ → data/
- public/uploads/ → data/uploads/

### ТРЕБУЕТ РЕШЕНИЯ ПОЛЬЗОВАТЕЛЯ
- Pochta/ - нужна ли почтовая функциональность?
- cacert.pem - используется ли?
- Отладочные API файлы - нужны ли для разработки?
