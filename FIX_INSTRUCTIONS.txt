===============================================
🔧 ИСПРАВЛЕНИЕ ПРОБЛЕМ С БАЗОЙ ДАННЫХ
===============================================

❌ ПРОБЛЕМА:
   "SQLITE_CONSTRAINT: NOT NULL constraint failed: admins.email"

🔧 РЕШЕНИЕ:

ВАРИАНТ 1 - Исправить существующую базу данных:
   node fix-database.js

ВАРИАНТ 2 - Полный сброс базы данных (удалит всех админов):
   node reset-database.js

===============================================

⚡ БЫСТРОЕ ИСПРАВЛЕНИЕ:

1. Исправить базу данных:
   node fix-database.js

2. Создать администратора:
   node create-admin.js

3. Запустить сервер:
   npm run local

===============================================

💡 ДОПОЛНИТЕЛЬНЫЕ КОМАНДЫ:

   node list-admins.js    - показать администраторов
   node delete-admin.js   - удалить администратора
   node fix-database.js   - исправить схему БД
   node reset-database.js - полный сброс БД

===============================================
