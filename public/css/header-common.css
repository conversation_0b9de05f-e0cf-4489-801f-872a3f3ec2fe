/* === ОБЩИЕ СТИЛИ ШАПКИ ДЛЯ ВСЕХ СТРАНИЦ === */

/* Константы для единообразия */
:root {
  --header-height-desktop: 60px;
  --header-height-mobile: 60px;
  --logo-height-desktop: 30px;
  --logo-height-mobile: 30px;
  --header-z-index: 1000;
}

/* === ДЕСКТОПНАЯ ШАПКА === */
.hero-header-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: var(--header-z-index);
  height: var(--header-height-desktop);
  min-height: var(--header-height-desktop);
}

.header__left {
  display: flex;
  align-items: center;
}

.header__back-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #333;
  text-decoration: none;
  font-size: 1rem;
  transition: all 0.3s ease;
  padding: 0;
  border-radius: 4px;
  height: var(--header-height-desktop);
  line-height: var(--header-height-desktop);
}

.header__back-link:hover {
  color: #000;
  background: rgba(0, 0, 0, 0.05);
  transform: translateX(-2px);
}

.header__back-link svg {
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;
}

.header__back-link:hover svg {
  transform: translateX(-2px);
}

.header__center-group {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header__nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.header__logo-link {
  font-family: "Inter", Arial, sans-serif;
  font-size: 1.5rem;
  font-weight: 400;
  letter-spacing: 4px;
  color: #000;
  text-transform: uppercase;
  text-decoration: none;
  margin-right: 2rem;
  transition: all 0.3s ease;
  position: relative;
  line-height: var(--header-height-desktop);
  padding: 0;
  display: flex;
  align-items: center;
}

.header__logo-link:hover {
  transform: translateY(-2px);
  color: #333;
}

.header__logo-link::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 2px;
  background: #000;
  transform: scaleX(0);
  transition: transform 0.3s ease;
  transform-origin: right;
}

.header__logo-link:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

.header__nav-links {
  display: flex;
  gap: 1.5rem;
}

.header__nav-link {
  color: #333;
  text-decoration: none;
  font-size: 1rem;
  transition: all 0.3s ease;
  position: relative;
  padding: 0 0.5rem;
  font-weight: 400;
  line-height: var(--header-height-desktop);
  height: var(--header-height-desktop);
  display: flex;
  align-items: center;
}

.header__nav-link:hover {
  color: #000;
  transform: translateY(-2px);
}

.header__nav-link.active {
  color: #000;
  font-weight: 500;
}

/* Удаляем подчеркивание */
.header__nav-link::after,
.header__nav-link.active::after {
  display: none !important;
}

.header__logo-img {
  height: var(--logo-height-desktop) !important;
  width: auto !important;
  object-fit: contain !important;
  display: block !important;
}

/* === МОБИЛЬНАЯ ШАПКА === */
.mobile-header {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: var(--header-height-mobile);
  background: #fff;
  box-shadow: 0 1px 3px rgba(0,0,0,0.08);
  padding: 0;
  z-index: var(--header-z-index);
}

.mobile-header__logo {
  position: static;
  margin: 0 auto;
  width: auto;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-decoration: none;
}

.mobile-header__logo-img {
  height: var(--logo-height-mobile) !important;
  width: auto !important;
  object-fit: contain !important;
}

.mobile-header-spacer {
  display: none;
  height: var(--header-height-mobile);
  width: 100%;
}

/* === МЕДИА-ЗАПРОСЫ === */

/* Мобильные устройства */
@media (max-width: 768px) {
  .hero-header-group {
    display: none;
  }

  .mobile-header {
    display: flex;
  }

  .mobile-header-spacer {
    display: block;
  }
}

/* Десктопные устройства */
@media (min-width: 769px) {
  .hero-header-group {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 0 2rem !important;
    width: 100% !important;
    height: var(--header-height-desktop) !important;
    background: #fff !important;
    position: relative !important;
    box-sizing: border-box !important;
  }

  .header__left {
    display: flex !important;
    align-items: center !important;
    margin-left: 0 !important;
  }

  .header__center-group {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex: 1 1 auto !important;
  }

  .header__logo-link {
    margin-right: 2rem !important;
    margin-left: 0 !important;
    padding: 0 !important;
  }

  /* Стили навигационных ссылок для десктопа */
  .header__nav-link {
    font-weight: 300 !important;
  }

  .header__nav-link.active {
    font-weight: 400 !important;
  }

  .burger,
  .dropdown-menu {
    display: none !important;
  }

  .mobile-header,
  .mobile-header-spacer {
    display: none !important;
  }
}

/* Средние экраны */
@media (max-width: 900px) and (min-width: 769px) {
  .hero-header-group {
    padding: 0 20px;
  }

  .header__logo-link {
    font-size: 22px;
    letter-spacing: 4px;
  }

  .hero-header-group .header__center-group {
    gap: 1.5rem;
  }

  .hero-header-group .header__nav {
    gap: 1.5rem;
  }
}

/* === СПЕЦИАЛЬНЫЕ СТИЛИ ДЛЯ СТРАНИЦЫ STOCK === */
/* Логотип отображается в нормальной позиции без принудительных сдвигов */

/* Стандартные стили логотипа для страницы stock */
body.stock-page .header__logo-link {
  margin-left: 0 !important;
  margin-right: 2rem !important;
  position: relative !important;
}

/* Стандартные стили изображения логотипа */
body.stock-page .header__logo-img {
  height: 30px !important;
  width: auto !important;
  transform: none !important;
  margin-top: 0 !important;
  transition: transform 0.3s ease !important;
  position: relative !important;
}

/* Анимация при hover - логотип поднимается вверх */
body.stock-page .header__logo-link:hover .header__logo-img {
  transform: translateY(-2px) !important;
}
