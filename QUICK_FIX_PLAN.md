# ⚡ ПЛАН БЫСТРОГО ИСПРАВЛЕНИЯ ПОИСКА АВТОМОБИЛЕЙ

## 🚨 КРИТИЧЕСКИЕ ПРОБЛЕМЫ

1. **PHP файлы не выполняются на Node.js сервере** → API поиска автомобилей не работает
2. **Неправильные имена CSV файлов** → Данные не находятся

## ✅ ХОРОШИЕ НОВОСТИ!

**У вас есть внешний API сервер**: `https://autobase-wade.auto-parser.ru`
**Есть локальные CSV данные**: `public/api/encar_data/encar_2025-06-07.csv`

---

## 🎯 БЫСТРЫЕ РЕШЕНИЯ

### Вариант 1: Исправить имена CSV файлов (5 минут)

#### Шаг 1: Переименовать существующий файл

```bash
# В папке public/api/encar_data/
mv encar_2025-06-07.csv encar_active_2025-01-16.csv
```

#### Шаг 2: Протестировать через PHP сервер

```bash
# Запустить PHP сервер
cd public
php -S localhost:8080

# Тест API
curl "http://localhost:8080/api/encar-proxy.php?date=2025-01-16&limit=5"
```

### Вариант 2: Запуск PHP сервера параллельно

#### Шаг 1: Запустить встроенный PHP сервер

```bash
# В отдельном терминале:
cd public
php -S localhost:8080

# Или из корня проекта:
php -S localhost:8080 -t public/
```

#### Шаг 2: Обновить конфигурацию в server-local.js

```javascript
// Строка 334: изменить URL
const phpServerUrl = `http://localhost:8080/api/encar-proxy.php`;
```

#### Шаг 3: Перезапустить Node.js сервер

```bash
node server-local.js
```

#### Шаг 4: Тестирование

```bash
# Тест PHP сервера:
curl "http://localhost:8080/api/encar-proxy.php?check=1"

# Тест через Node.js:
curl "http://localhost:3000/api/encar?date=2025-01-16&limit=5"
```

---

## 🔧 АЛЬТЕРНАТИВНЫЕ РЕШЕНИЯ

### Вариант 3: Использовать внешний API напрямую (30 минут)

#### Создать файл `server/api/encar-external.js`:

```javascript
const fetch = require("node-fetch");

async function fetchFromExternalAPI(date, filters = {}) {
  const apiHost = "https://autobase-wade.auto-parser.ru";
  const auth =
    "Basic " + Buffer.from("admin:n2Q8ewyLft9qgPmim5ng").toString("base64");
  const url = `${apiHost}/encar/${date}/active_offer.csv`;

  try {
    const response = await fetch(url, {
      headers: { Authorization: auth },
      timeout: 30000,
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const csvData = await response.text();
    return parseCSVData(csvData, filters);
  } catch (error) {
    console.error("External API error:", error);
    return [];
  }
}

function parseCSVData(csvData, filters) {
  const lines = csvData.split("\n");
  const headers = lines[0].split("|");
  const cars = [];

  for (
    let i = 1;
    i < lines.length && cars.length < (filters.limit || 20);
    i++
  ) {
    const values = lines[i].split("|");
    if (values.length === headers.length) {
      const car = {};
      headers.forEach((header, index) => {
        car[header] = values[index];
      });

      // Применяем фильтры
      if (
        filters.brand &&
        !car.mark?.toLowerCase().includes(filters.brand.toLowerCase())
      ) {
        continue;
      }
      if (
        filters.model &&
        !car.model?.toLowerCase().includes(filters.model.toLowerCase())
      ) {
        continue;
      }

      cars.push(car);
    }
  }

  return cars;
}

module.exports = { fetchFromExternalAPI };
```

#### Обновить маршрут в server-local.js:

```javascript
const { fetchFromExternalAPI } = require("./server/api/encar-external");

app.get("/api/encar", async (req, res) => {
  try {
    const { date, brand = "", model = "", limit = 20 } = req.query;

    if (!date) {
      return res.status(400).json({ error: "Параметр date обязателен" });
    }

    const cars = await fetchFromExternalAPI(date, { brand, model, limit });
    res.json(cars);
  } catch (error) {
    console.error("❌ Ошибка API encar:", error);
    res.status(500).json({ error: "Ошибка сервера" });
  }
});
```

### Вариант 4: Переписать API на Node.js

#### Создать файл `server/api/encar-node.js`:

```javascript
const fs = require("fs");
const path = require("path");

// Функция для чтения CSV файлов
function readEncarCSV(date, filters = {}) {
  const csvPath = path.join(
    __dirname,
    "../../public/api/encar_data/encar_" + date + ".csv"
  );

  if (!fs.existsSync(csvPath)) {
    return [];
  }

  // Читаем и парсим CSV
  const csvData = fs.readFileSync(csvPath, "utf8");
  const lines = csvData.split("\n");
  const headers = lines[0].split("|");

  const cars = [];
  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split("|");
    if (values.length === headers.length) {
      const car = {};
      headers.forEach((header, index) => {
        car[header] = values[index];
      });
      cars.push(car);
    }
  }

  return cars;
}

module.exports = { readEncarCSV };
```

#### Обновить маршрут в server-local.js:

```javascript
// Заменить существующий маршрут /api/encar
const { readEncarCSV } = require("./server/api/encar-node");

app.get("/api/encar", async (req, res) => {
  try {
    const { date, brand = "", model = "", limit = 20 } = req.query;

    if (!date) {
      return res.status(400).json({ error: "Параметр date обязателен" });
    }

    const cars = readEncarCSV(date, { brand, model, limit });
    res.json(cars.slice(0, parseInt(limit)));
  } catch (error) {
    console.error("❌ Ошибка API encar:", error);
    res.status(500).json({ error: "Ошибка сервера" });
  }
});
```

---

## 🛠️ ДОЛГОСРОЧНОЕ РЕШЕНИЕ (2-3 часа)

### Вариант 3: Полная интеграция с Express

#### 1. Установить зависимости:

```bash
npm install csv-parser multer
```

#### 2. Создать middleware для PHP-подобной функциональности

#### 3. Настроить автоматическое обновление данных

#### 4. Добавить кеширование и оптимизацию

---

## 📋 ЧЕКЛИСТ ПРОВЕРКИ

### После любого исправления проверить:

- [ ] **PHP сервер запущен** (для варианта 1)

  ```bash
  curl "http://localhost:8080/api/encar-proxy.php?check=1"
  ```

- [ ] **Node.js API работает**

  ```bash
  curl "http://localhost:3000/api/encar?date=2025-01-16&limit=5"
  ```

- [ ] **Поиск на сайте работает**

  - Открыть http://localhost:3000/order
  - Ввести "BMW" в поиск
  - Проверить результаты

- [ ] **Курсы валют работают**

  ```bash
  curl "http://localhost:3000/api/exchange-rates.php"
  ```

- [ ] **Цены рассчитываются корректно**
  - Проверить отображение цен в USD
  - Убедиться что используется актуальный курс

---

## 🚀 РЕКОМЕНДУЕМЫЙ ПЛАН

### СЕЙЧАС (немедленно):

1. **Исправить имена файлов** (Вариант 1) - 5 минут
2. **Запустить PHP сервер** (Вариант 2) - 5 минут
3. **Протестировать** - 5 минут

### АЛЬТЕРНАТИВНО:

1. **Использовать внешний API** (Вариант 3) - 30 минут
   - Более надежно, всегда актуальные данные
   - Не зависит от локальных файлов

### ПОЗЖЕ (в течение недели):

1. **Переписать на Node.js** (Вариант 4) - для стабильности
2. **Добавить мониторинг** - для отслеживания проблем
3. **Улучшить обработку ошибок** - для UX

---

## 💡 ДОПОЛНИТЕЛЬНЫЕ УЛУЧШЕНИЯ

### Обработка ошибок в order-encar.js:

```javascript
// Добавить в fetchCarsData():
.catch(error => {
  console.error("API Error:", error);

  // Показать пользователю понятное сообщение
  const container = document.querySelector('.order-cards');
  container.innerHTML = `
    <div class="error-message">
      <h3>Временные проблемы с поиском</h3>
      <p>Мы работаем над устранением проблемы. Попробуйте позже.</p>
      <button onclick="location.reload()">Обновить страницу</button>
    </div>
  `;
});
```

### Индикатор статуса API:

```javascript
// Добавить проверку доступности API
async function checkApiStatus() {
  try {
    const response = await fetch("/test");
    return response.ok;
  } catch {
    return false;
  }
}
```

---

## 📞 ПОДДЕРЖКА

**Если проблемы остаются**:

1. Проверить логи Node.js сервера
2. Проверить логи PHP сервера (если используется)
3. Проверить консоль браузера на странице /order
4. Убедиться что все файлы существуют и доступны

**Контакты для помощи**:

- Логи сервера: `console.log` в терминале
- Ошибки браузера: F12 → Console
- Сетевые запросы: F12 → Network

---

## ✅ ОЖИДАЕМЫЙ РЕЗУЛЬТАТ

После исправления:

- ✅ Поиск автомобилей работает на странице /order
- ✅ Отображаются реальные данные от Encar API
- ✅ Цены рассчитываются по актуальным курсам валют
- ✅ Система стабильно работает без ошибок

**Время на исправление**: 30 минут - 1 час
**Сложность**: Низкая (настройка) / Средняя (переписывание)
