<?php
/**
 * 🚀 МОЩНЫЙ ГЕНЕРАТОР КЭША для 4-уровневой системы категорий
 * 
 * Этот скрипт НЕ подключает encar-proxy.php, чтобы избежать конфликтов
 * Выполняет полную обработку CSV файла и создает:
 * 1. Разбивку всей базы на 4 категории приоритета
 * 2. Кэширование всех возможных марок автомобилей
 * 3. Индексы по маркам, моделям, годам, ценам
 * 4. Предзагруженные фильтры для каждой категории
 */

// Увеличиваем лимиты для обработки больших файлов
ini_set('max_execution_time', 1200); // 20 минут
ini_set('memory_limit', '8G'); // 8GB для больших CSV файлов

// Устанавливаем заголовки для JSON ответа
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

/**
 * Функция для определения приоритета бренда (1-4, где 1 - высший приоритет)
 */
function getBrandPriority($brand) {
    // Переводим марку для правильного сравнения
    $translatedBrand = translateBrand($brand);
    
    // 4-уровневая система приоритетов брендов
    $brandPriorities = [
        // 1 категория - Супер премиум люкс
        1 => [
            'Rolls-Royce', 'Bentley', 'Ferrari', 'Lamborghini', 'McLaren', 
            'Bugatti', 'Koenigsegg', 'Pagani', 'Aston Martin', 'Maybach',
            'Mercedes-Maybach', 'Brabus', 'Alpina', 'Lucid Motors', 'Rimac',
            'Pininfarina', 'Spyker'
        ],
        // 2 категория - Премиум
        2 => [
            'Mercedes-Benz', 'BMW', 'Audi', 'Porsche', 'Lexus', 'Cadillac',
            'Maserati', 'Jaguar', 'Land Rover', 'Range Rover', 'Genesis',
            'Infiniti', 'Acura', 'Lincoln'
        ],
        // 3 категория - Средние
        3 => [
            'Toyota', 'Honda', 'Nissan', 'Mazda', 'Subaru', 'Mitsubishi',
            'Volvo', 'Jeep', 'Chrysler', 'Dodge', 'Buick', 'GMC'
        ],
        // 4 категория - Бюджетные
        4 => [
            'Kia', 'Hyundai', 'Chevrolet', 'ChevroletGMDaewoo', 'Daewoo', 
            'Lada', 'Dacia', 'Skoda', 'SEAT', 'Ford', 'Renault', 'Peugeot',
            'Citroen', 'Fiat', 'Opel', 'Suzuki', 'Isuzu', 'Renault-KoreaSamsung'
        ]
    ];
    
    // Ищем бренд в категориях приоритета
    foreach ($brandPriorities as $priority => $brands) {
        foreach ($brands as $priorityBrand) {
            if (strcasecmp($translatedBrand, $priorityBrand) === 0) {
                return $priority;
            }
        }
    }
    
    // Если бренд не найден, возвращаем самый низкий приоритет
    return 4;
}

/**
 * Функция для перевода марки автомобиля
 */
function translateBrand($brand) {
    $translations = [
        '현대' => 'Hyundai',
        '기아' => 'Kia',
        '쉐보레(GM대우)' => 'ChevroletGMDaewoo',
        '르노삼성' => 'Renault-KoreaSamsung',
        '볼보' => 'Volvo',
        '메르세데스-벤츠' => 'Mercedes-Benz',
        'BMW' => 'BMW',
        '아우디' => 'Audi'
    ];
    
    return $translations[$brand] ?? $brand;
}

/**
 * Функция для получения названия категории по приоритету
 */
function getPriorityName($priority) {
    $names = [
        1 => 'Супер премиум люкс',
        2 => 'Премиум',
        3 => 'Средние',
        4 => 'Бюджетные'
    ];
    
    return $names[$priority] ?? 'Неизвестная категория';
}

/**
 * Функция для форматирования размера файла
 */
function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB'];
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

/**
 * Функция для чтения CSV файла
 */
function readCSVFile($date) {
    // Ищем CSV файл в разных возможных местах
    $possiblePaths = [
        __DIR__ . "/encar_data/encar_active_{$date}.csv",
        __DIR__ . "/encar_data/encar_active_2025-01-16.csv", // Fallback к существующему файлу
        __DIR__ . "/../../data/active_offer_{$date}.csv"
    ];

    $csvFile = null;
    foreach ($possiblePaths as $path) {
        if (file_exists($path)) {
            $csvFile = $path;
            break;
        }
    }

    if (!$csvFile) {
        throw new Exception("CSV file not found. Tried paths: " . implode(', ', $possiblePaths));
    }
    
    $cars = [];
    $handle = fopen($csvFile, 'r');
    
    if ($handle === false) {
        throw new Exception("Cannot open CSV file: $csvFile");
    }
    
    // Читаем заголовки
    $headers = fgetcsv($handle, 0, '|'); // Используем | как разделитель
    if ($headers === false) {
        throw new Exception("Cannot read CSV headers");
    }

    $rowCount = 0;
    $validCars = 0;
    while (($row = fgetcsv($handle, 0, '|')) !== false) { // Используем | как разделитель
        $rowCount++;

        if (count($row) !== count($headers)) {
            continue; // Пропускаем некорректные строки
        }

        $car = array_combine($headers, $row);

        // Проверяем, что есть основные поля
        if (empty($car['mark']) && empty($car['brand'])) {
            continue; // Пропускаем строки без марки
        }

        // Пропускаем забронированные автомобили (если есть такие цены)
        $price = (int)($car['price'] ?? 0);
        if ($price === 999999 || $price === 333333) {
            continue;
        }

        $cars[] = $car;
        $validCars++;
    }
    
    fclose($handle);
    
    return [
        'cars' => $cars,
        'total_rows' => $rowCount,
        'valid_cars' => $validCars
    ];
}

/**
 * 🚀 МОЩНАЯ ФУНКЦИЯ ПОЛНОГО КЭШИРОВАНИЯ CSV БАЗЫ ДАННЫХ
 */
function superFastCacheGeneration($date) {
    $results = [
        'success' => true,
        'message' => 'Super fast cache generation completed successfully',
        'categories' => [],
        'brand_index' => [],
        'total_cars' => 0,
        'execution_time' => 0,
        'memory_usage' => 0,
        'cache_files' => []
    ];
    
    $startTime = microtime(true);
    $startMemory = memory_get_usage(true);
    
    try {
        echo "=== SUPER FAST CACHE GENERATION START ===\n";
        echo "Processing CSV file for date: $date\n";
        
        // Загружаем ВСЕ автомобили из CSV
        $csvData = readCSVFile($date);
        $allCars = $csvData['cars'];
        $totalCars = count($allCars);
        
        echo "Loaded $totalCars cars from CSV (processed {$csvData['total_rows']} rows)\n";
        
        // Создаем директорию для кэша
        $cacheDir = __DIR__ . '/../../cache';
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0777, true);
        }
        
        // Инициализируем структуры данных
        $categorizedCars = [1 => [], 2 => [], 3 => [], 4 => []];
        $brandIndex = [];
        
        // ЭТАП 1: Обрабатываем каждый автомобиль и распределяем по категориям
        echo "STAGE 1: Categorizing cars by brand priority...\n";
        foreach ($allCars as $car) {
            $brand = $car['brand'] ?? $car['mark'] ?? '';
            $model = $car['model'] ?? '';
            $year = (int)($car['year'] ?? 0);
            $price = (int)($car['price'] ?? 0);
            
            // Определяем приоритет бренда
            $priority = getBrandPriority($brand);
            
            // Добавляем автомобиль в соответствующую категорию
            $categorizedCars[$priority][] = $car;
            
            // Индексируем бренды
            if (!isset($brandIndex[$brand])) {
                $brandIndex[$brand] = [
                    'priority' => $priority,
                    'count' => 0,
                    'models' => [],
                    'price_min' => PHP_INT_MAX,
                    'price_max' => 0,
                    'year_min' => PHP_INT_MAX,
                    'year_max' => 0
                ];
            }
            $brandIndex[$brand]['count']++;
            
            // Обновляем диапазоны цен и годов
            if ($price > 0) {
                $brandIndex[$brand]['price_min'] = min($brandIndex[$brand]['price_min'], $price);
                $brandIndex[$brand]['price_max'] = max($brandIndex[$brand]['price_max'], $price);
            }
            
            if ($year > 0) {
                $brandIndex[$brand]['year_min'] = min($brandIndex[$brand]['year_min'], $year);
                $brandIndex[$brand]['year_max'] = max($brandIndex[$brand]['year_max'], $year);
            }
            
            if (!empty($model) && !in_array($model, $brandIndex[$brand]['models'])) {
                $brandIndex[$brand]['models'][] = $model;
            }
        }
        
        // ЭТАП 2: Создаем кэш-файлы для каждой категории
        echo "STAGE 2: Creating cache files...\n";
        foreach ($categorizedCars as $priority => $cars) {
            $categoryName = getPriorityName($priority);
            $cacheFile = $cacheDir . "/super_priority_{$priority}_{$date}.json";
            
            $cacheData = [
                'priority' => $priority,
                'category_name' => $categoryName,
                'date' => $date,
                'cars' => $cars,
                'count' => count($cars),
                'generated_at' => date('Y-m-d H:i:s'),
                'expires_at' => date('Y-m-d H:i:s', strtotime('+1 day')),
                'version' => '2.0_super_fast'
            ];
            
            file_put_contents($cacheFile, json_encode($cacheData, JSON_UNESCAPED_UNICODE));
            
            $results['categories'][$priority] = [
                'name' => $categoryName,
                'count' => count($cars),
                'cache_file' => basename($cacheFile),
                'file_size' => filesize($cacheFile)
            ];
            
            $results['cache_files'][] = $cacheFile;
            echo "Created cache for priority $priority ($categoryName): " . count($cars) . " cars\n";
        }
        
        // ЭТАП 3: Создаем индекс брендов
        echo "STAGE 3: Creating brand index...\n";
        $brandIndexFile = $cacheDir . "/brand_index_{$date}.json";
        file_put_contents($brandIndexFile, json_encode($brandIndex, JSON_UNESCAPED_UNICODE));
        $results['cache_files'][] = $brandIndexFile;
        
        // ЭТАП 4: Создаем мастер-индекс
        echo "STAGE 4: Creating master index...\n";
        $masterIndexFile = $cacheDir . "/master_index_{$date}.json";
        $masterIndex = [
            'date' => $date,
            'total_cars' => $totalCars,
            'categories' => $results['categories'],
            'brand_count' => count($brandIndex),
            'generated_at' => date('Y-m-d H:i:s'),
            'expires_at' => date('Y-m-d H:i:s', strtotime('+1 day')),
            'version' => '2.0_super_fast'
        ];
        
        file_put_contents($masterIndexFile, json_encode($masterIndex, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
        $results['cache_files'][] = $masterIndexFile;
        
        $results['total_cars'] = $totalCars;
        $results['brand_index'] = array_keys($brandIndex);
        
    } catch (Exception $e) {
        $results['success'] = false;
        $results['message'] = 'Super fast cache generation failed: ' . $e->getMessage();
        echo "Cache generation error: " . $e->getMessage() . "\n";
    }
    
    $results['execution_time'] = round(microtime(true) - $startTime, 2);
    $results['memory_usage'] = memory_get_usage(true) - $startMemory;
    
    echo "=== SUPER FAST CACHE GENERATION END ===\n";
    echo "Execution time: " . $results['execution_time'] . " seconds\n";
    echo "Memory usage: " . formatBytes($results['memory_usage']) . "\n";
    
    return $results;
}

// Основная логика выполнения
try {
    $action = $_GET['action'] ?? 'generate';
    $date = $_GET['date'] ?? date('Y-m-d');
    
    switch ($action) {
        case 'generate':
            $result = superFastCacheGeneration($date);
            break;
            
        case 'status':
            $cacheDir = __DIR__ . '/../../cache';
            $files = glob($cacheDir . '/*.json');
            $result = [
                'success' => true,
                'message' => 'Cache status retrieved',
                'cache_files' => array_map('basename', $files),
                'count' => count($files),
                'cache_dir' => $cacheDir,
                'total_size' => array_sum(array_map('filesize', $files))
            ];
            break;
            
        default:
            $result = [
                'success' => false,
                'message' => 'Unknown action. Available actions: generate, status'
            ];
    }
    
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Super cache generator error: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}
?>
