<?php
// Тестовый скрипт для проверки API фильтрации по категориям
header('Content-Type: text/html; charset=utf-8');

echo "<h1>Тест API фильтрации по категориям</h1>";

$baseUrl = 'http://localhost/api/encar-proxy.php';
$categories = ['business', 'sport', 'suv'];

foreach ($categories as $category) {
    echo "<h2>Тестирование категории: $category</h2>";
    
    $url = $baseUrl . '?' . http_build_query([
        'category' => $category,
        'limit' => 5,
        'offset' => 0,
        'debug' => 1
    ]);
    
    echo "<p><strong>URL:</strong> <a href='$url' target='_blank'>$url</a></p>";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 30
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    
    if ($response === false) {
        echo "<p style='color: red;'>Ошибка при запросе к API</p>";
        continue;
    }
    
    $data = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "<p style='color: red;'>Ошибка декодирования JSON: " . json_last_error_msg() . "</p>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
        continue;
    }
    
    if (isset($data['error'])) {
        echo "<p style='color: red;'>Ошибка API: " . htmlspecialchars($data['error']) . "</p>";
        continue;
    }
    
    $count = is_array($data) ? count($data) : 0;
    echo "<p><strong>Найдено автомобилей:</strong> $count</p>";
    
    if ($count > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Марка</th><th>Модель</th><th>Год</th><th>Цена</th><th>Категория</th></tr>";
        
        foreach (array_slice($data, 0, 5) as $car) {
            $brand = htmlspecialchars($car['mark'] ?? $car['brand'] ?? 'N/A');
            $model = htmlspecialchars($car['model'] ?? 'N/A');
            $year = htmlspecialchars($car['year'] ?? 'N/A');
            $price = htmlspecialchars($car['price_formatted'] ?? $car['price'] ?? 'N/A');
            
            // Определяем категорию автомобиля
            include_once 'public/api/encar-proxy.php';
            $carCategory = determineCarCategory($car['mark'] ?? $car['brand'] ?? '', $car['model'] ?? '');
            
            echo "<tr>";
            echo "<td>$brand</td>";
            echo "<td>$model</td>";
            echo "<td>$year</td>";
            echo "<td>$price</td>";
            echo "<td>" . htmlspecialchars($carCategory) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    echo "<hr>";
}

echo "<h2>Тест без фильтра категории</h2>";
$url = $baseUrl . '?' . http_build_query([
    'limit' => 10,
    'offset' => 0,
    'debug' => 1
]);

echo "<p><strong>URL:</strong> <a href='$url' target='_blank'>$url</a></p>";

$response = file_get_contents($url, false, $context);
if ($response !== false) {
    $data = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE && !isset($data['error'])) {
        $count = is_array($data) ? count($data) : 0;
        echo "<p><strong>Всего автомобилей (без фильтра):</strong> $count</p>";
    }
}
?>
