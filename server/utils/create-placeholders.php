<?php
// Скрипт для создания правильных файлов-заглушек для изображений автомобилей

// Убедимся, что есть нормальный placeholder
if (!file_exists('Img/placeholder-car.jpg') || filesize('Img/placeholder-car.jpg') < 5000) {
    echo "Создаем основной placeholder...\n";
    
    // Создаем базовый placeholder с текстом
    $img = imagecreatetruecolor(800, 500);
    
    // Цвета
    $bg_color = imagecolorallocate($img, 240, 240, 240);
    $text_color = imagecolorallocate($img, 80, 80, 80);
    $border_color = imagecolorallocate($img, 200, 200, 200);
    
    // Заливаем фон
    imagefill($img, 0, 0, $bg_color);
    
    // Рисуем рамку
    imagerectangle($img, 0, 0, 799, 499, $border_color);
    
    // Добавляем текст
    $text = "PREMIUM AUTO";
    $font_size = 5;
    $text_width = imagefontwidth($font_size) * strlen($text);
    $text_height = imagefontheight($font_size);
    
    $x = (800 - $text_width) / 2;
    $y = (500 - $text_height) / 2;
    
    imagestring($img, $font_size, $x, $y, $text, $text_color);
    
    // Сохраняем изображение
    imagejpeg($img, 'Img/placeholder-car.jpg', 90);
    imagedestroy($img);
    
    echo "Основной placeholder создан\n";
}

// Список автомобилей для создания заглушек
$car_images = [
    'bmw-m5.jpg',
    'bmw-m4.jpg',
    'audi-rs7.jpg',
    'ferrari.jpg',
    'lamborghini.jpg',
    'bentley-bentayga.jpg',
    'bentley-flying-spur.jpg',
    'bentley-continental.jpg',
    'aston-martin.jpg'
];

// Создаем заглушки для каждого автомобиля
foreach ($car_images as $car_image) {
    $full_path = 'Img/' . $car_image;
    
    if (!file_exists($full_path) || filesize($full_path) < 5000) {
        echo "Создаем placeholder для {$car_image}...\n";
        
        // Создаем изображение
        $img = imagecreatetruecolor(800, 500);
        
        // Цвета
        $bg_color = imagecolorallocate($img, 240, 240, 240);
        $text_color = imagecolorallocate($img, 60, 60, 60);
        $border_color = imagecolorallocate($img, 200, 200, 200);
        
        // Заливаем фон
        imagefill($img, 0, 0, $bg_color);
        
        // Рисуем рамку
        imagerectangle($img, 0, 0, 799, 499, $border_color);
        
        // Получаем имя модели из имени файла
        $car_name = str_replace(['-', '.jpg'], [' ', ''], $car_image);
        $car_name = ucwords($car_name);
        
        // Добавляем текст модели автомобиля
        $font_size = 5;
        $text_width = imagefontwidth($font_size) * strlen($car_name);
        $text_height = imagefontheight($font_size);
        
        $x = (800 - $text_width) / 2;
        $y = (500 - $text_height) / 2;
        
        imagestring($img, $font_size, $x, $y, $car_name, $text_color);
        
        // Сохраняем изображение
        imagejpeg($img, $full_path, 90);
        imagedestroy($img);
        
        echo "Placeholder для {$car_image} создан\n";
    } else {
        echo "Файл {$car_image} уже существует и имеет нормальный размер\n";
    }
}

echo "Все placeholders созданы успешно!\n";
?> 