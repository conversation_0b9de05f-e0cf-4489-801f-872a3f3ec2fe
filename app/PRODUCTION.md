# SHMS Авто - Руководство по развертыванию на Рег.ру

## 📋 Требования

### Сервер
- **VPS/Облачный сервер** на Рег.ру с Ubuntu 20.04+
- **Node.js** версии 16+ 
- **PM2** для управления процессами
- **Nginx** для проксирования (опционально)
- **PHP** 7.4+ для дополнительных скриптов

### Ресурсы
- **RAM**: минимум 1GB, рекомендуется 2GB+
- **Диск**: минимум 10GB свободного места
- **CPU**: 1 ядро (рекомендуется 2+)

## 🚀 Пошаговое развертывание

### 1. Подготовка сервера

```bash
# Обновление системы
sudo apt update && sudo apt upgrade -y

# Установка Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Установка PM2 глобально
sudo npm install -g pm2

# Установка дополнительных пакетов
sudo apt install -y git nginx php-fpm php-mysql php-curl
```

### 2. Клонирование проекта

```bash
# Переход в директорию веб-сервера
cd /var/www

# Клонирование репозитория
sudo git clone https://github.com/your-username/shms-auto.git
sudo chown -R $USER:$USER shms-auto
cd shms-auto

# Установка зависимостей
npm install
```

### 3. Настройка переменных окружения

```bash
# Копирование и редактирование .env файла
cp .env.example .env
nano .env
```

**Обязательные настройки в .env:**

```env
# Продакшен режим
NODE_ENV=production
PORT=3001
HOST=0.0.0.0

# Ваш домен
DOMAIN=shms-auto.ru
SITE_URL=https://shms-auto.ru

# Настройки почты Рег.ру
MAIL_HOST=mail.hosting.reg.ru
MAIL_PORT=465
MAIL_SECURE=true
MAIL_USER=<EMAIL>
MAIL_PASS=ваш_пароль_от_почты

# Куда отправлять заявки (ВАША почта)
CONTACT_EMAIL=ваша-почта@gmail.com
MANAGER_EMAIL=ваша-почта@gmail.com

# Безопасность
SESSION_SECRET=сгенерируйте-длинный-случайный-ключ
HTTPS_REDIRECT=true
SECURE_COOKIES=true
```

### 4. Создание необходимых директорий

```bash
# Создание директорий
mkdir -p logs data/uploads data/cache

# Установка прав доступа
chmod 755 data/uploads
chmod 755 logs
chmod 644 data/stock.db
```

### 5. Запуск приложения

```bash
# Тестовый запуск
npm run production

# Если все работает, запуск через PM2
npm run pm2

# Настройка автозапуска PM2
pm2 startup
pm2 save
```

## 🔧 Настройка Nginx (опционально)

Создайте конфигурацию Nginx:

```bash
sudo nano /etc/nginx/sites-available/shms-auto
```

```nginx
server {
    listen 80;
    server_name shms-auto.ru www.shms-auto.ru;
    
    # Редирект на HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name shms-auto.ru www.shms-auto.ru;
    
    # SSL сертификаты (настройте через Let's Encrypt)
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # Проксирование на Node.js приложение
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Статические файлы
    location /uploads/ {
        alias /var/www/shms-auto/data/uploads/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
}
```

Активация конфигурации:

```bash
sudo ln -s /etc/nginx/sites-available/shms-auto /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 📧 Настройка почты

### Создание почтового ящика на Рег.ру

1. Войдите в панель управления Рег.ру
2. Перейдите в раздел "Почта"
3. Создайте почтовый ящик `<EMAIL>`
4. Запишите пароль для использования в .env

### Тестирование почты

```bash
# Тест конфигурации почты
npm run test-mail

# Интерактивный тест
npm run test-mail -- --interactive
```

## 🔍 Мониторинг и обслуживание

### Проверка состояния

```bash
# Проверка работоспособности
npm run health-check

# Мониторинг PM2
npm run pm2:monit

# Просмотр логов
npm run pm2:logs
```

### Резервное копирование

```bash
# Создание бэкапа базы данных
npm run backup-db

# Ручное копирование важных файлов
cp data/stock.db backups/stock-$(date +%Y%m%d).db
tar -czf backups/uploads-$(date +%Y%m%d).tar.gz data/uploads/
```

### Обновление приложения

```bash
# Остановка приложения
npm run pm2:stop

# Обновление кода
git pull origin main

# Установка новых зависимостей
npm install

# Перезапуск
npm run pm2:restart
```

## 🚨 Устранение неполадок

### Проблемы с запуском

1. Проверьте логи: `npm run pm2:logs`
2. Убедитесь, что порт 3001 свободен: `netstat -tlnp | grep 3001`
3. Проверьте права доступа к файлам: `ls -la data/`

### Проблемы с почтой

1. Проверьте настройки в .env файле
2. Убедитесь, что почтовый ящик создан на Рег.ру
3. Запустите тест почты: `npm run test-mail`

### Проблемы с базой данных

1. Проверьте существование файла: `ls -la data/stock.db`
2. Проверьте права доступа: `chmod 644 data/stock.db`
3. Запустите проверку: `npm run health-check`

## 📞 Поддержка

При возникновении проблем:

1. Проверьте логи приложения
2. Запустите health-check
3. Обратитесь к документации Рег.ру
4. Свяжитесь с технической поддержкой

## 🔐 Безопасность

- Регулярно обновляйте зависимости: `npm audit fix`
- Используйте сильные пароли
- Настройте SSL сертификаты
- Ограничьте доступ к админ-панели по IP
- Регулярно создавайте резервные копии
