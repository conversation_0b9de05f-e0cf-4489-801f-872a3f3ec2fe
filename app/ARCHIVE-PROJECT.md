# 📦 Подготовка проекта для загрузки на сервер

## 🎯 Что нужно сделать перед загрузкой

### 1. Создание архива проекта

**Вариант A: Через командную строку (Windows)**
```cmd
# Перейдите в папку с проектом
cd "C:\Users\<USER>\OneDrive\Рабочий стол\Site 2000"

# Создайте архив (если есть 7-Zip)
7z a shms-auto-deploy.zip . -x!node_modules -x!.git -x!logs -x!"data/uploads/*"
```

**Вариант B: Вручную**
1. Создайте новую папку `shms-auto-deploy`
2. Скопируйте ВСЕ файлы проекта КРОМЕ:
   - `node_modules/` (будет установлено на сервере)
   - `.git/` (если есть)
   - `logs/` (создастся автоматически)
   - Большие файлы из `data/uploads/` (можно загрузить отдельно)

### 2. Проверьте эти файлы перед архивированием

**Обязательные файлы:**
- ✅ `.env` (с вашими настройками)
- ✅ `package.json`
- ✅ `server.js`
- ✅ `ecosystem.config.js`
- ✅ `deploy-regru.sh`
- ✅ `config/mail-config.js`
- ✅ `data/stock.db`
- ✅ Все файлы из `public/`
- ✅ Все файлы из `admin/`
- ✅ Все файлы из `server/`
- ✅ `scripts/` папка

**Проверьте настройки в .env:**
```env
NODE_ENV=production
PORT=3001
HOST=0.0.0.0
DOMAIN=shms-auto.ru
SITE_URL=https://shms-auto.ru

MAIL_HOST=smtp.yandex.ru
MAIL_PORT=465
MAIL_SECURE=true
MAIL_USER=<EMAIL>
MAIL_PASS=SHMSAutogroup777!

CONTACT_EMAIL=<EMAIL>
MANAGER_EMAIL=<EMAIL>

SESSION_SECRET=shms-auto-2024-super-secret-key-kJ8mN2pQ5rT9wX3zA6bC1dF4gH7jK0lM
```

### 3. Способы загрузки на сервер

**Способ 1: SFTP (рекомендуется)**
1. Используйте FileZilla или WinSCP
2. Подключитесь к серверу:
   - Хост: IP вашего VPS
   - Пользователь: shmsauto
   - Пароль: тот что создали
   - Порт: 22
3. Загрузите архив в `/home/<USER>/`
4. Распакуйте на сервере

**Способ 2: Git (если настроен)**
```bash
# На сервере
git clone https://github.com/ваш-username/shms-auto.git
```

**Способ 3: SCP**
```bash
# С вашего компьютера
scp shms-auto-deploy.zip shmsauto@ваш-ip:/home/<USER>/
```

### 4. Команды для распаковки на сервере

```bash
# Подключение к серверу
ssh shmsauto@ваш-ip

# Распаковка архива
cd /home/<USER>
unzip shms-auto-deploy.zip -d shms-auto
cd shms-auto

# Или если tar.gz
tar -xzf shms-auto-deploy.tar.gz
```

### 5. Быстрый чеклист файлов

Убедитесь, что эти файлы есть в архиве:

**Корневые файлы:**
- [ ] `.env`
- [ ] `package.json`
- [ ] `server.js`
- [ ] `ecosystem.config.js`
- [ ] `deploy-regru.sh`
- [ ] `DEPLOY-REGRU.md`
- [ ] `QUICK-DEPLOY.md`

**Папки:**
- [ ] `admin/` (вся папка)
- [ ] `public/` (вся папка)
- [ ] `server/` (вся папка)
- [ ] `config/` (вся папка)
- [ ] `scripts/` (вся папка)
- [ ] `data/` (включая stock.db, но можно без больших uploads)

**Размер архива должен быть:** ~50-100 МБ (без node_modules и больших изображений)

### 6. После загрузки на сервер

```bash
# Установка зависимостей
npm install

# Запуск автоматического развертывания
chmod +x deploy-regru.sh
./deploy-regru.sh
```

## 🚀 Готовые команды для копирования

**Создание архива (PowerShell):**
```powershell
Compress-Archive -Path * -DestinationPath shms-auto-deploy.zip -Exclude node_modules,logs,.git
```

**Загрузка через SCP:**
```bash
scp shms-auto-deploy.zip shmsauto@ваш-ip:/home/<USER>/
```

**Развертывание на сервере:**
```bash
ssh shmsauto@ваш-ip
cd /home/<USER>
unzip shms-auto-deploy.zip -d shms-auto
cd shms-auto
npm install
chmod +x deploy-regru.sh
./deploy-regru.sh
```

## ⚠️ Важные моменты

1. **НЕ включайте в архив:**
   - `node_modules/` (большой размер)
   - `.git/` (не нужно)
   - `logs/` (создастся автоматически)

2. **Обязательно включите:**
   - `.env` с правильными настройками
   - `data/stock.db` (ваша база данных)
   - Все исходные файлы проекта

3. **Проверьте права доступа:**
   - `deploy-regru.sh` должен быть исполняемым
   - `.env` должен содержать правильные пароли

4. **Размер файлов:**
   - Если `data/uploads/` очень большая, загрузите её отдельно
   - Основной архив должен быть < 500 МБ

Ваш проект готов к развертыванию! 🎉
