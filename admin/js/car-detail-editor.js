document.addEventListener("DOMContentLoaded", function () {
  // Editor state
  const editorState = {
    currentStep: "basics",
    completedSteps: [],
    carId: null,
    detailId: null,
    isEditing: false,
    images: [],
    descriptionEditor: null,
    dragSources: {
      gallery: null,
      mainFeatures: null,
      additionalFeatures: null,
    },
    templates: {
      luxury: {
        fullTitle: "2024 Audi RS6 Avant Performance",
        price: "$307,286",
        moscowPrice: "$311,328",
        bodyType: "AWD",
        mileage: "78 км",
        power: "630 л.с.",
      },
      sport: {
        fullTitle: "2024 Mercedes-AMG GT 63 S E Performance",
        price: "$218,900",
        moscowPrice: "$224,450",
        bodyType: "Купе",
        mileage: "15 км",
        power: "843 л.с.",
      },
      suv: {
        fullTitle: "2024 BMW X7 M60i",
        price: "$182,500",
        moscowPrice: "$186,100",
        bodyType: "SUV",
        mileage: "38 км",
        power: "530 л.с.",
      },
    },
  };

  // DOM Elements
  const elements = {
    editorSteps: document.querySelectorAll(".editor-step"),
    stepContents: document.querySelectorAll(".step-content"),
    nextButtons: document.querySelectorAll(".next-step"),
    prevButtons: document.querySelectorAll(".prev-step"),
    progressBar: document.getElementById("progress-bar"),
    carSelect: document.getElementById("select-car"),
    loadCarBtn: document.getElementById("load-car-btn"),
    saveBtn: document.getElementById("save-draft-btn"),
    publishBtn: document.getElementById("publish-btn"),
    templateButtons: document.querySelectorAll(".template-btn"),
    carDetailForm: document.getElementById("car-detail-form"),
    uploadZone: document.getElementById("upload-zone"),
    imageGallery: document.getElementById("image-gallery"),
    addSpecBtn: document.getElementById("add-spec-btn"),
    addColorBtn: document.getElementById("add-color-btn"),
    addMainFeatureBtn: document.getElementById("add-main-feature-btn"),
    addAdditionalFeatureBtn: document.getElementById(
      "add-additional-feature-btn"
    ),
    previewFrame: document.getElementById("preview-frame"),
    previewDeviceBtns: document.querySelectorAll(".preview-device-btn"),
    generateBtn: document.getElementById("generate-btn"),
    finalPublishBtn: document.getElementById("final-publish-btn"),
    viewPublishedPagesBtn: document.getElementById("view-published-pages"),
  };

  // Initialize the editor
  function initEditor() {
    // Initialize rich text editor
    if (document.getElementById("description-editor")) {
      editorState.descriptionEditor = new Quill("#description-editor", {
        theme: "snow",
        modules: {
          toolbar: [
            ["bold", "italic", "underline"],
            [{ list: "ordered" }, { list: "bullet" }],
            ["clean"],
          ],
        },
        placeholder: "Начните вводить описание автомобиля...",
      });

      // Update hidden field with editor content when changed
      editorState.descriptionEditor.on("text-change", function () {
        document.getElementById("description").value =
          editorState.descriptionEditor.root.innerHTML;

        // Update live preview
        if (document.getElementById("description-preview")) {
          document.getElementById("description-preview").innerHTML =
            editorState.descriptionEditor.root.innerHTML;
        }
      });
    }

    // Setup event listeners
    setupEventListeners();

    // Load car list
    loadCars();

    // Update progress bar
    updateProgress();
  }

  // Setup all event listeners
  function setupEventListeners() {
    // Step navigation
    elements.editorSteps.forEach((step) => {
      step.addEventListener("click", () => navigateToStep(step.dataset.step));
    });

    // View published pages button
    elements.viewPublishedPagesBtn.addEventListener(
      "click",
      viewPublishedPages
    );

    elements.nextButtons.forEach((btn) => {
      btn.addEventListener("click", () => {
        const currentStep = btn.closest(".step-content");
        const stepId = currentStep.id.replace("step-", "");
        const nextStepIndex = getStepIndex(stepId) + 1;

        if (nextStepIndex < elements.editorSteps.length) {
          const nextStep = elements.editorSteps[nextStepIndex].dataset.step;
          navigateToStep(nextStep);
        }
      });
    });

    elements.prevButtons.forEach((btn) => {
      btn.addEventListener("click", () => {
        const currentStep = btn.closest(".step-content");
        const stepId = currentStep.id.replace("step-", "");
        const prevStepIndex = getStepIndex(stepId) - 1;

        if (prevStepIndex >= 0) {
          const prevStep = elements.editorSteps[prevStepIndex].dataset.step;
          navigateToStep(prevStep);
        }
      });
    });

    // Car loading
    elements.loadCarBtn.addEventListener("click", loadSelectedCar);

    // Template buttons
    elements.templateButtons.forEach((btn) => {
      btn.addEventListener("click", () => {
        applyTemplate(btn.dataset.template);
      });
    });

    // Form saving
    elements.saveBtn.addEventListener("click", saveAsDraft);
    elements.publishBtn.addEventListener("click", publishCarDetail);

    // Upload zone
    if (elements.uploadZone) {
      setupImageUpload();
    }

    // Add buttons for dynamic fields
    if (elements.addSpecBtn) {
      elements.addSpecBtn.addEventListener("click", addSpecField);
    }

    if (elements.addColorBtn) {
      elements.addColorBtn.addEventListener("click", addColorField);
    }

    if (elements.addMainFeatureBtn) {
      elements.addMainFeatureBtn.addEventListener("click", () =>
        addFeatureField("main")
      );
    }

    if (elements.addAdditionalFeatureBtn) {
      elements.addAdditionalFeatureBtn.addEventListener("click", () =>
        addFeatureField("additional")
      );
    }

    // Preview device buttons
    elements.previewDeviceBtns.forEach((btn) => {
      btn.addEventListener("click", () =>
        switchPreviewDevice(btn.dataset.device)
      );
    });

    // Generate HTML button
    if (elements.generateBtn) {
      elements.generateBtn.addEventListener("click", generateHtmlPage);
    }

    // Final publish button
    if (elements.finalPublishBtn) {
      elements.finalPublishBtn.addEventListener("click", publishAndGenerate);
    }

    // Content suggestion chips
    document.querySelectorAll(".suggestion-chip").forEach((chip) => {
      chip.addEventListener("click", () => {
        insertContentSuggestion(chip.dataset.content);
      });
    });
  }

  // Navigate to a specific step
  function navigateToStep(stepId) {
    // Update active step
    elements.editorSteps.forEach((step) => {
      step.classList.remove("active");
      if (step.dataset.step === stepId) {
        step.classList.add("active");
      }
    });

    // Update step content visibility
    elements.stepContents.forEach((content) => {
      content.classList.remove("active");
      if (content.id === `step-${stepId}`) {
        content.classList.add("active");
      }
    });

    // Update editor state
    editorState.currentStep = stepId;

    // Mark step as completed if not already
    if (!editorState.completedSteps.includes(stepId)) {
      const currentIndex = getStepIndex(stepId);
      for (let i = 0; i < currentIndex; i++) {
        const stepId = elements.editorSteps[i].dataset.step;
        if (!editorState.completedSteps.includes(stepId)) {
          editorState.completedSteps.push(stepId);
        }
      }
    }

    // Update progress
    updateProgress();

    // Generate preview if on preview step
    if (stepId === "preview") {
      generatePreview();
    }
  }

  // Get index of a step by its ID
  function getStepIndex(stepId) {
    for (let i = 0; i < elements.editorSteps.length; i++) {
      if (elements.editorSteps[i].dataset.step === stepId) {
        return i;
      }
    }
    return -1;
  }

  // Update progress bar and step indicators
  function updateProgress() {
    // Mark completed steps
    elements.editorSteps.forEach((step) => {
      step.classList.remove("completed");
      if (editorState.completedSteps.includes(step.dataset.step)) {
        step.classList.add("completed");
      }
    });

    // Update progress bar
    const totalSteps = elements.editorSteps.length;
    const completedSteps = editorState.completedSteps.length;
    const progress = (completedSteps / totalSteps) * 100;
    elements.progressBar.style.width = `${progress}%`;
  }

  // Load cars from the server
  async function loadCars() {
    try {
      const response = await fetch("/api/cars");
      const cars = await response.json();

      elements.carSelect.innerHTML =
        '<option value="">-- Выберите автомобиль --</option>';

      cars.forEach((car) => {
        const option = document.createElement("option");
        option.value = car.id;
        option.textContent = car.title;
        elements.carSelect.appendChild(option);
      });
    } catch (error) {
      showNotification(
        "Ошибка",
        "Не удалось загрузить список автомобилей",
        "error"
      );
    }
  }

  // Load selected car data
  async function loadSelectedCar() {
    const carId = elements.carSelect.value;

    if (!carId) {
      showNotification("Ошибка", "Выберите автомобиль", "error");
      return;
    }

    try {
      // Check if there is already a detail page for this car
      const detailResponse = await fetch(
        `/admin-stock/car-details/car/${carId}`
      );
      const detailData = await detailResponse.json();

      if (detailData) {
        // If exists, load for editing
        showNotification(
          "Информация",
          "Для этого автомобиля уже есть детальная страница",
          "info"
        );
        loadDetailForEditing(detailData.id);
        return;
      }

      // Get car data
      const carResponse = await fetch(`/admin-stock/cars/${carId}`);
      const carData = await carResponse.json();

      // Reset form
      resetForm();

      // Set car ID
      document.getElementById("car-id").value = carId;
      editorState.carId = carId;

      // Format full title with year
      let fullTitle = carData.title;
      if (!fullTitle.includes("202")) {
        // Add current year if not already present
        const currentYear = new Date().getFullYear();
        fullTitle = `${currentYear} ${fullTitle}`;
      }
      document.getElementById("full-title").value = fullTitle;

      // Set prices
      let priceValue = carData.price || "";
      if (
        priceValue &&
        !isNaN(parseFloat(priceValue.replace(/[$,]/g, ""))) &&
        !priceValue.includes("$")
      ) {
        priceValue = `$${priceValue}`;
      }
      document.getElementById("price").value = priceValue;

      // Calculate Moscow price (add 1-3%)
      let moscowPrice = "";
      if (priceValue) {
        let basePrice = parseFloat(priceValue.replace(/[$,]/g, ""));
        if (!isNaN(basePrice)) {
          const markup = 1 + (Math.random() * 2 + 1) / 100;
          const calculatedPrice = Math.round(basePrice * markup);
          moscowPrice = `$${calculatedPrice.toLocaleString("en-US")}`;
        }
      }
      document.getElementById("moscow-price").value = moscowPrice || priceValue;

      // Set info blocks
      document.getElementById("body-type").value = carData.body_type || "";
      document.getElementById("mileage").value = carData.mileage || "";
      document.getElementById("power").value = carData.power || "";

      // Update editor state
      editorState.isEditing = false;

      showNotification("Успех", "Данные автомобиля загружены", "success");
    } catch (error) {
      console.error("Ошибка при загрузке данных автомобиля:", error);
      showNotification(
        "Ошибка",
        "Не удалось загрузить данные автомобиля",
        "error"
      );
    }
  }

  // Load detail data for editing
  async function loadDetailForEditing(detailId) {
    try {
      const response = await fetch(`/api/car-details/${detailId}`);
      const detail = await response.json();

      // Reset form
      resetForm();

      // Set form fields
      document.getElementById("detail-id").value = detail.id;
      document.getElementById("car-id").value = detail.car_id;
      document.getElementById("full-title").value = detail.full_title || "";
      document.getElementById("price").value = detail.price || "";
      document.getElementById("moscow-price").value = detail.moscow_price || "";

      // Set car info from specs if available
      if (detail.specs) {
        const specs = detail.specs.split("\n");
        specs.forEach((spec) => {
          const [key, value] = spec.split(":").map((s) => s.trim());
          if (key && value) {
            if (
              key.toLowerCase() === "привод" ||
              key.toLowerCase().includes("кузов")
            ) {
              document.getElementById("body-type").value = value;
            } else if (key.toLowerCase().includes("пробег")) {
              document.getElementById("mileage").value = value;
            } else if (key.toLowerCase().includes("мощность")) {
              document.getElementById("power").value = value;
            }
          }
        });
      }

      // Set description in rich text editor
      if (editorState.descriptionEditor && detail.description) {
        editorState.descriptionEditor.root.innerHTML = detail.description;
        document.getElementById("description").value = detail.description;
      }

      // Load specs
      if (detail.specs) {
        loadSpecsFromText(detail.specs);
      }

      // Load best features
      if (detail.best_features) {
        loadFeaturesFromText(detail.best_features);
      }

      // Load images
      if (detail.images) {
        try {
          editorState.images = JSON.parse(detail.images);
          renderGallery();
        } catch (e) {
          console.error("Ошибка при парсинге JSON изображений:", e);
        }
      }

      // Update editor state
      editorState.detailId = detail.id;
      editorState.carId = detail.car_id;
      editorState.isEditing = true;

      // Select car in dropdown
      elements.carSelect.value = detail.car_id;

      showNotification(
        "Успех",
        "Данные загружены для редактирования",
        "success"
      );
    } catch (error) {
      console.error("Ошибка при загрузке данных для редактирования:", error);
      showNotification(
        "Ошибка",
        "Не удалось загрузить данные для редактирования",
        "error"
      );
    }
  }

  // Show notification
  function showNotification(title, message, type = "info") {
    const notification = document.getElementById("notification");
    if (!notification) return;

    const notificationTitle = notification.querySelector(".notification-title");
    const notificationText = notification.querySelector(".notification-text");

    notificationTitle.textContent = title;
    notificationText.textContent = message;

    notification.className = "notification";
    notification.classList.add("show");
    notification.classList.add(`notification-${type}`);

    setTimeout(() => {
      notification.classList.remove("show");
    }, 5000);
  }

  // Apply a template to the form
  function applyTemplate(templateType) {
    if (!editorState.templates[templateType]) {
      showNotification("Ошибка", "Шаблон не найден", "error");
      return;
    }

    const template = editorState.templates[templateType];

    // For basic info templates
    if (
      templateType === "luxury" ||
      templateType === "sport" ||
      templateType === "suv"
    ) {
      document.getElementById("full-title").value = template.fullTitle;
      document.getElementById("price").value = template.price;
      document.getElementById("moscow-price").value = template.moscowPrice;
      document.getElementById("body-type").value = template.bodyType;
      document.getElementById("mileage").value = template.mileage;
      document.getElementById("power").value = template.power;
    }

    // For description templates
    else if (templateType === "luxury-desc") {
      const luxuryDesc = `<p><strong>${
        document.getElementById("full-title").value || "2024 Премиум Автомобиль"
      }</strong> — квинтэссенция роскоши и изысканности.</p>
<p>Этот автомобиль создан для тех, кто ценит комфорт премиум-класса, внимание к деталям и превосходное качество отделки. Мощный ${
        document.getElementById("power").value || ""
      } двигатель сочетает производительность и утонченность, обеспечивая плавное и уверенное движение.</p>
<p>Внутри вас встречает салон, отделанный материалами высочайшего качества. Кожа Наппа с декоративной прострочкой, вставки из натурального дерева и алюминия создают атмосферу роскоши и благородства.</p>
<p>Инновационные технологии, интегрированные в интуитивно понятный интерфейс, обеспечивают максимальный комфорт и контроль.</p>`;

      if (editorState.descriptionEditor) {
        editorState.descriptionEditor.root.innerHTML = luxuryDesc;
        document.getElementById("description").value = luxuryDesc;
        document.getElementById("description-preview").innerHTML = luxuryDesc;
      }
    } else if (templateType === "sport-desc") {
      const sportDesc = `<p><strong>${
        document.getElementById("full-title").value ||
        "2024 Спортивный Автомобиль"
      }</strong> — воплощение скорости и адреналина.</p>
<p>Созданный для настоящих ценителей драйва, этот автомобиль сочетает в себе бескомпромиссную производительность и передовые технологии. Мощный ${
        document.getElementById("power").value || ""
      } двигатель обеспечивает молниеносный разгон и захватывающую динамику.</p>
<p>Аэродинамический дизайн не только привлекает взгляды, но и оптимизирует поток воздуха для максимальной устойчивости на высоких скоростях. Спортивная подвеска и точное рулевое управление дарят непревзойденное ощущение контроля.</p>
<p>Внутри — кокпит, ориентированный на водителя, с интуитивным расположением органов управления и спортивными сиденьями, обеспечивающими идеальную поддержку при динамичном вождении.</p>`;

      if (editorState.descriptionEditor) {
        editorState.descriptionEditor.root.innerHTML = sportDesc;
        document.getElementById("description").value = sportDesc;
        document.getElementById("description-preview").innerHTML = sportDesc;
      }
    } else if (templateType === "suv-desc") {
      const suvDesc = `<p><strong>${
        document.getElementById("full-title").value ||
        "2024 Премиальный Внедорожник"
      }</strong> — идеальное сочетание мощности, универсальности и комфорта.</p>
<p>Этот внедорожник создан для тех, кто не признает компромиссов и стремится к совершенству во всем. Мощный ${
        document.getElementById("power").value || ""
      } двигатель и система полного привода гарантируют уверенное движение в любых условиях.</p>
<p>Просторный и продуманный салон с тремя рядами сидений обеспечивает комфорт для всех пассажиров. Премиальные материалы отделки и исключительная шумоизоляция создают атмосферу полного спокойствия и роскоши.</p>
<p>Инновационные системы помощи водителю и технологии безопасности делают каждую поездку уверенной и безопасной, как на городских улицах, так и за пределами дорог.</p>`;

      if (editorState.descriptionEditor) {
        editorState.descriptionEditor.root.innerHTML = suvDesc;
        document.getElementById("description").value = suvDesc;
        document.getElementById("description-preview").innerHTML = suvDesc;
      }
    }

    // For specs templates
    else if (templateType === "luxury-specs") {
      const specContainer = document.getElementById("specs-container");
      const specRows = specContainer.querySelectorAll(".spec-row");

      // Update existing required specs first
      updateRequiredSpecValues([
        ["Привод", "Полный привод quattro"],
        ["Двигатель", "4.0L V8 Twin-Turbo"],
        ["Мощность", document.getElementById("power").value || "571 л.с."],
        ["Трансмиссия", "8-ступенчатая АКПП tiptronic"],
        ["Цвет экстерьера", "Daytona Grey Pearl Effect"],
      ]);

      // Add luxury specific specs
      addSpecsIfNotExist([
        ["Разгон 0-100 км/ч", "3.6 сек"],
        ["Максимальная скорость", "305 км/ч"],
        ["Расход топлива", "11.5 л/100 км"],
        ["Материал отделки салона", "Кожа Valcona"],
        ["Колесные диски", '22" легкосплавные RS-дизайн'],
        ["Аудиосистема", "Bang & Olufsen 3D Advanced"],
      ]);
    } else if (templateType === "sport-specs") {
      const specContainer = document.getElementById("specs-container");
      const specRows = specContainer.querySelectorAll(".spec-row");

      // Update existing required specs first
      updateRequiredSpecValues([
        ["Привод", "Полный привод 4MATIC+"],
        ["Двигатель", "4.0L V8 Twin-Turbo + электромотор"],
        ["Мощность", document.getElementById("power").value || "843 л.с."],
        ["Трансмиссия", "9-ступенчатая AMG SPEEDSHIFT"],
        ["Цвет экстерьера", "Obsidian Black Metallic"],
      ]);

      // Add sport specific specs
      addSpecsIfNotExist([
        ["Разгон 0-100 км/ч", "2.9 сек"],
        ["Максимальная скорость", "316 км/ч"],
        ["Крутящий момент", "1470 Нм"],
        ["Тормозная система", "Карбон-керамические"],
        ["Тип привода", "AMG Performance 4MATIC+"],
        ["Режимы движения", "Comfort, Sport, Sport+, Race, Individual"],
      ]);
    } else if (templateType === "suv-specs") {
      const specContainer = document.getElementById("specs-container");
      const specRows = specContainer.querySelectorAll(".spec-row");

      // Update existing required specs first
      updateRequiredSpecValues([
        ["Привод", "xDrive (полный)"],
        ["Двигатель", "4.4L V8 M TwinPower Turbo"],
        ["Мощность", document.getElementById("power").value || "530 л.с."],
        ["Трансмиссия", "8-ступенчатая Steptronic Sport"],
        ["Цвет экстерьера", "Mineral White Metallic"],
      ]);

      // Add SUV specific specs
      addSpecsIfNotExist([
        ["Разгон 0-100 км/ч", "4.7 сек"],
        ["Дорожный просвет", "221 мм"],
        ["Объем багажника", "750/2120 л"],
        ["Количество мест", "7"],
        ["Технологии", "xOffroad package, адаптивная пневмоподвеска"],
        ["Максимальная нагрузка", "3500 кг"],
      ]);
    }

    // For features templates
    else if (templateType === "luxury-features") {
      clearFeaturesContainer();

      // Add main features
      const mainFeatures = [
        "Адаптивная пневмоподвеска с электронным управлением",
        "Матричные HD LED-фары с лазерной технологией",
        "Панорамная крыша с электроприводом",
        "Четырехзонный климат-контроль",
        "Вентиляция и массаж передних и задних сидений",
      ];

      // Add additional features
      const additionalFeatures = [
        "Аудиосистема Bang & Olufsen 3D Advanced Sound System",
        "Система ночного видения с распознаванием пешеходов",
        "Пакет Ambient Lighting Plus с 30 цветами подсветки",
        "Электропривод крышки багажника с функцией открывания жестом",
        "Беспроводная зарядка для смартфонов",
        "Обивка сидений кожей Valcona с декоративной прострочкой",
      ];

      populateFeatures("main", mainFeatures);
      populateFeatures("additional", additionalFeatures);
    } else if (templateType === "sport-features") {
      clearFeaturesContainer();

      // Add main features
      const mainFeatures = [
        "Карбон-керамическая тормозная система",
        "Электронная система динамической стабилизации AMG DYNAMICS",
        "Активная аэродинамика с автоматически регулируемым задним спойлером",
        "Спортивные ковшеобразные сиденья AMG Performance",
        "Рулевое управление с переменным передаточным отношением",
      ];

      // Add additional features
      const additionalFeatures = [
        "Пакет AMG Track Pace для записи данных на гоночном треке",
        "Выхлопная система AMG Performance с регулируемыми заслонками",
        "Карбоновые элементы экстерьера и интерьера",
        "Спортивные шины Michelin Pilot Sport Cup 2",
        "Система кругового обзора 360°",
        "Пакет AMG DYNAMIC PLUS с дополнительными режимами движения",
      ];

      populateFeatures("main", mainFeatures);
      populateFeatures("additional", additionalFeatures);
    } else if (templateType === "suv-features") {
      clearFeaturesContainer();

      // Add main features
      const mainFeatures = [
        "Интеллектуальная система полного привода xDrive",
        "Адаптивная пневмоподвеска с регулировкой клиренса",
        "Внедорожный пакет xOffroad с 4 режимами движения",
        "Пакет Executive для задних пассажиров",
        "Пакет Driving Assistant Professional",
      ];

      // Add additional features
      const additionalFeatures = [
        "Система кругового обзора 360° с функцией 3D View",
        "Панорамная стеклянная крыша Sky Lounge с LED-подсветкой",
        "Задние сиденья с электрорегулировкой и подогревом",
        "Система Parking Assistant Plus с дистанционным управлением",
        "Мультимедийная система для задних пассажиров",
        "Кожаная отделка приборной панели и дверных панелей",
      ];

      populateFeatures("main", mainFeatures);
      populateFeatures("additional", additionalFeatures);
    }

    showNotification("Успех", "Шаблон применен", "success");
  }

  // Update required specification values
  function updateRequiredSpecValues(specs) {
    const specContainer = document.getElementById("specs-container");
    const specRows = specContainer.querySelectorAll(".spec-row");

    // Update first 5 rows (required specs)
    for (let i = 0; i < Math.min(specs.length, 5); i++) {
      if (specRows[i]) {
        const keyInput = specRows[i].querySelector(".spec-key");
        const valueInput = specRows[i].querySelector(".spec-value");

        if (keyInput && valueInput) {
          keyInput.value = specs[i][0];
          valueInput.value = specs[i][1];
        }
      }
    }
  }

  // Add specifications if they don't exist
  function addSpecsIfNotExist(specs) {
    const specContainer = document.getElementById("specs-container");
    const existingKeys = Array.from(
      specContainer.querySelectorAll(".spec-key")
    ).map((input) => input.value.toLowerCase());

    specs.forEach((spec) => {
      if (!existingKeys.includes(spec[0].toLowerCase())) {
        const specRow = document.createElement("div");
        specRow.className = "spec-row";

        const keyInput = document.createElement("input");
        keyInput.type = "text";
        keyInput.className = "form-control spec-key";
        keyInput.value = spec[0];

        const valueInput = document.createElement("input");
        valueInput.type = "text";
        valueInput.className = "form-control spec-value";
        valueInput.value = spec[1];

        const removeBtn = document.createElement("button");
        removeBtn.type = "button";
        removeBtn.className = "btn btn-danger remove-btn";
        removeBtn.innerHTML = '<i class="fas fa-times"></i>';
        removeBtn.addEventListener("click", function () {
          specRow.remove();
        });

        specRow.appendChild(keyInput);
        specRow.appendChild(valueInput);
        specRow.appendChild(removeBtn);

        specContainer.appendChild(specRow);
      }
    });
  }

  // Clear features containers
  function clearFeaturesContainer() {
    const mainContainer = document.getElementById("main-features");
    const additionalContainer = document.getElementById("additional-features");

    if (mainContainer) {
      mainContainer.innerHTML = "";
    }

    if (additionalContainer) {
      additionalContainer.innerHTML = "";
    }
  }

  // Populate features of a specific type
  function populateFeatures(type, features) {
    const containerId =
      type === "main" ? "main-features" : "additional-features";
    const containerClass =
      type === "main" ? "main-feature" : "additional-feature";
    const container = document.getElementById(containerId);

    if (!container) return;

    features.forEach((feature) => {
      const featureRow = document.createElement("div");
      featureRow.className = `feature-row ${containerClass}`;

      const handle = document.createElement("i");
      handle.className = "fas fa-grip-vertical draggable-handle";

      const input = document.createElement("input");
      input.type = "text";
      input.className = "form-control";
      input.value = feature;

      const removeBtn = document.createElement("button");
      removeBtn.type = "button";
      removeBtn.className = "btn btn-danger remove-feature-btn";
      removeBtn.innerHTML = '<i class="fas fa-times"></i>';
      removeBtn.addEventListener("click", function () {
        featureRow.remove();
      });

      featureRow.appendChild(handle);
      featureRow.appendChild(input);
      featureRow.appendChild(removeBtn);

      container.appendChild(featureRow);
    });
  }

  // Add a new specification field
  function addSpecField() {
    const specContainer = document.getElementById("specs-container");

    const specRow = document.createElement("div");
    specRow.className = "spec-row";

    const keyInput = document.createElement("input");
    keyInput.type = "text";
    keyInput.className = "form-control spec-key";
    keyInput.placeholder = "Название";

    const valueInput = document.createElement("input");
    valueInput.type = "text";
    valueInput.className = "form-control spec-value";
    valueInput.placeholder = "Значение";

    const removeBtn = document.createElement("button");
    removeBtn.type = "button";
    removeBtn.className = "btn btn-danger remove-btn";
    removeBtn.innerHTML = '<i class="fas fa-times"></i>';
    removeBtn.addEventListener("click", function () {
      specRow.remove();
    });

    specRow.appendChild(keyInput);
    specRow.appendChild(valueInput);
    specRow.appendChild(removeBtn);

    specContainer.appendChild(specRow);
  }

  // Add a new color field
  function addColorField() {
    const colorsContainer = document.getElementById(
      "interior-colors-container"
    );

    const colorRow = document.createElement("div");
    colorRow.className = "color-row";

    const colorPreview = document.createElement("div");
    colorPreview.className = "color-preview";
    colorPreview.style.backgroundColor = "#000000";

    const colorSelect = document.createElement("select");
    colorSelect.className = "form-control color-select";

    const colors = [
      { value: "black", text: "Черный", hex: "#000000" },
      { value: "white", text: "Белый", hex: "#FFFFFF" },
      { value: "red", text: "Красный", hex: "#D20000" },
      { value: "blue", text: "Синий", hex: "#0066CC" },
      { value: "gray", text: "Серый", hex: "#808080" },
      { value: "orange", text: "Оранжевый", hex: "#FF7700" },
      { value: "brown", text: "Коричневый", hex: "#8B4513" },
      { value: "beige", text: "Бежевый", hex: "#F5F5DC" },
    ];

    colors.forEach((color) => {
      const option = document.createElement("option");
      option.value = color.value;
      option.textContent = color.text;
      colorSelect.appendChild(option);
    });

    colorSelect.addEventListener("change", function () {
      const selectedColor = colors.find((c) => c.value === this.value);
      if (selectedColor) {
        colorPreview.style.backgroundColor = selectedColor.hex;
      }
    });

    const removeBtn = document.createElement("button");
    removeBtn.type = "button";
    removeBtn.className = "btn btn-danger remove-color-btn";
    removeBtn.innerHTML = '<i class="fas fa-times"></i>';
    removeBtn.addEventListener("click", function () {
      colorRow.remove();
    });

    colorRow.appendChild(colorPreview);
    colorRow.appendChild(colorSelect);
    colorRow.appendChild(removeBtn);

    colorsContainer.appendChild(colorRow);
  }

  // Add a new feature field
  function addFeatureField(type) {
    const containerId =
      type === "main" ? "main-features" : "additional-features";
    const containerClass =
      type === "main" ? "main-feature" : "additional-feature";
    const container = document.getElementById(containerId);

    const featureRow = document.createElement("div");
    featureRow.className = `feature-row ${containerClass}`;

    const handle = document.createElement("i");
    handle.className = "fas fa-grip-vertical draggable-handle";

    const input = document.createElement("input");
    input.type = "text";
    input.className = "form-control";
    input.placeholder = "Введите особенность";

    const removeBtn = document.createElement("button");
    removeBtn.type = "button";
    removeBtn.className = "btn btn-danger remove-feature-btn";
    removeBtn.innerHTML = '<i class="fas fa-times"></i>';
    removeBtn.addEventListener("click", function () {
      featureRow.remove();
    });

    featureRow.appendChild(handle);
    featureRow.appendChild(input);
    featureRow.appendChild(removeBtn);

    container.appendChild(featureRow);
  }

  // Setup image upload functionality
  function setupImageUpload() {
    const uploadZone = document.getElementById("upload-zone");
    const fileInput = uploadZone.querySelector(".file-input");

    // Drag and drop events
    uploadZone.addEventListener("dragover", (e) => {
      e.preventDefault();
      uploadZone.classList.add("drag-over");
    });

    uploadZone.addEventListener("dragleave", () => {
      uploadZone.classList.remove("drag-over");
    });

    uploadZone.addEventListener("drop", (e) => {
      e.preventDefault();
      uploadZone.classList.remove("drag-over");

      if (e.dataTransfer.files.length) {
        fileInput.files = e.dataTransfer.files;
        handleImageUpload(fileInput.files);
      }
    });

    // Click to upload
    uploadZone.addEventListener("click", () => {
      fileInput.click();
    });

    // File input change
    fileInput.addEventListener("change", () => {
      if (fileInput.files.length) {
        handleImageUpload(fileInput.files);
      }
    });
  }

  // Handle the image upload
  async function handleImageUpload(files) {
    // Validate files
    const validFiles = Array.from(files).filter((file) => {
      const isValidType = [
        "image/jpeg",
        "image/jpg",
        "image/png",
        "image/webp",
      ].includes(file.type);
      const isValidSize = file.size <= 20 * 1024 * 1024; // 20MB
      return isValidType && isValidSize;
    });

    if (validFiles.length === 0) {
      showNotification(
        "Ошибка",
        "Выберите изображения в формате JPG, PNG или WEBP размером до 20MB",
        "error"
      );
      return;
    }

    // Create FormData
    const formData = new FormData();
    validFiles.forEach((file) => {
      formData.append("detail_images", file);
    });

    // Show loading state
    const uploadZone = document.getElementById("upload-zone");
    uploadZone.classList.add("uploading");
    const uploadText = uploadZone.querySelector(".upload-text");
    const originalText = uploadText.innerHTML;
    uploadText.innerHTML =
      '<i class="fas fa-spinner fa-spin fa-2x"></i><p>Загрузка изображений...</p>';

    try {
      // Upload the images
      const response = await fetch("/admin/upload-temp", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Ошибка загрузки изображений");
      }

      const result = await response.json();

      if (result.images && result.images.length > 0) {
        // Add new images to the state
        editorState.images = [...editorState.images, ...result.images];

        // Render gallery
        renderGallery();

        showNotification("Успех", "Изображения загружены", "success");
      } else {
        throw new Error("Не удалось загрузить изображения");
      }
    } catch (error) {
      showNotification("Ошибка", error.message, "error");
    } finally {
      // Reset upload zone
      uploadZone.classList.remove("uploading");
      uploadText.innerHTML = originalText;
    }
  }

  // Render image gallery
  function renderGallery() {
    const gallery = document.getElementById("image-gallery");
    if (!gallery) return;

    gallery.innerHTML = "";

    editorState.images.forEach((image, index) => {
      const item = document.createElement("div");
      item.className = "image-item";
      item.draggable = true;
      item.dataset.index = index;

      const img = document.createElement("img");
      img.src = image.path;
      img.alt = `Image ${index + 1}`;

      const actions = document.createElement("div");
      actions.className = "image-actions";

      const removeBtn = document.createElement("button");
      removeBtn.className = "image-action-btn";
      removeBtn.innerHTML = '<i class="fas fa-trash"></i>';
      removeBtn.addEventListener("click", () => removeImage(index));

      const moveUpBtn = document.createElement("button");
      moveUpBtn.className = "image-action-btn";
      moveUpBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
      moveUpBtn.addEventListener("click", () => moveImage(index, "up"));

      const moveDownBtn = document.createElement("button");
      moveDownBtn.className = "image-action-btn";
      moveDownBtn.innerHTML = '<i class="fas fa-arrow-down"></i>';
      moveDownBtn.addEventListener("click", () => moveImage(index, "down"));

      actions.appendChild(moveUpBtn);
      actions.appendChild(moveDownBtn);
      actions.appendChild(removeBtn);

      item.appendChild(img);
      item.appendChild(actions);
      gallery.appendChild(item);

      // Add drag and drop functionality
      setupDragDrop(item);
    });
  }

  // Set up drag and drop for images
  function setupDragDrop(item) {
    item.addEventListener("dragstart", (e) => {
      e.dataTransfer.setData("text/plain", item.dataset.index);
      item.classList.add("dragging");
    });

    item.addEventListener("dragend", () => {
      item.classList.remove("dragging");
    });

    item.addEventListener("dragover", (e) => {
      e.preventDefault();
      item.classList.add("drag-over");
    });

    item.addEventListener("dragleave", () => {
      item.classList.remove("drag-over");
    });

    item.addEventListener("drop", (e) => {
      e.preventDefault();
      item.classList.remove("drag-over");

      const fromIndex = parseInt(e.dataTransfer.getData("text/plain"));
      const toIndex = parseInt(item.dataset.index);

      if (fromIndex !== toIndex) {
        // Reorder images
        const temp = editorState.images[fromIndex];
        editorState.images.splice(fromIndex, 1);
        editorState.images.splice(toIndex, 0, temp);

        // Re-render gallery
        renderGallery();
      }
    });
  }

  // Remove image from gallery
  function removeImage(index) {
    if (confirm("Вы уверены, что хотите удалить это изображение?")) {
      editorState.images.splice(index, 1);
      renderGallery();
    }
  }

  // Move image up or down in the gallery
  function moveImage(index, direction) {
    if (direction === "up" && index > 0) {
      const temp = editorState.images[index];
      editorState.images[index] = editorState.images[index - 1];
      editorState.images[index - 1] = temp;
    } else if (direction === "down" && index < editorState.images.length - 1) {
      const temp = editorState.images[index];
      editorState.images[index] = editorState.images[index + 1];
      editorState.images[index + 1] = temp;
    }

    renderGallery();
  }

  // Load specifications from text
  function loadSpecsFromText(specsText) {
    const specContainer = document.getElementById("specs-container");
    const requiredRows = Array.from(
      specContainer.querySelectorAll(".spec-row")
    ).slice(0, 5);

    // Clear any extra rows first
    const extraRows = Array.from(
      specContainer.querySelectorAll(".spec-row")
    ).slice(5);
    extraRows.forEach((row) => row.remove());

    // Parse specs
    const specs = specsText
      .split("\n")
      .map((line) => {
        const [key, value] = line.split(":").map((s) => s.trim());
        return { key, value };
      })
      .filter((spec) => spec.key && spec.value);

    // Fill required specs first
    specs.slice(0, 5).forEach((spec, index) => {
      if (requiredRows[index]) {
        const keyInput = requiredRows[index].querySelector(".spec-key");
        const valueInput = requiredRows[index].querySelector(".spec-value");

        if (keyInput && valueInput) {
          keyInput.value = spec.key;
          valueInput.value = spec.value;
        }
      }
    });

    // Add additional specs
    specs.slice(5).forEach((spec) => {
      const specRow = document.createElement("div");
      specRow.className = "spec-row";

      const keyInput = document.createElement("input");
      keyInput.type = "text";
      keyInput.className = "form-control spec-key";
      keyInput.value = spec.key;

      const valueInput = document.createElement("input");
      valueInput.type = "text";
      valueInput.className = "form-control spec-value";
      valueInput.value = spec.value;

      const removeBtn = document.createElement("button");
      removeBtn.type = "button";
      removeBtn.className = "btn btn-danger remove-btn";
      removeBtn.innerHTML = '<i class="fas fa-times"></i>';
      removeBtn.addEventListener("click", function () {
        specRow.remove();
      });

      specRow.appendChild(keyInput);
      specRow.appendChild(valueInput);
      specRow.appendChild(removeBtn);

      specContainer.appendChild(specRow);
    });
  }

  // Load features from text
  function loadFeaturesFromText(featuresText) {
    clearFeaturesContainer();

    const sections = featuresText.split("\n\n");
    let mainFeatures = [];
    let additionalFeatures = [];

    if (sections.length >= 2) {
      // Extract main features
      const mainSection = sections[0];
      if (mainSection.toLowerCase().includes("основные")) {
        mainFeatures = mainSection
          .split("\n")
          .slice(1) // Skip the header
          .filter((f) => f.trim());
      }

      // Extract additional features
      const additionalSection = sections[1];
      if (additionalSection.toLowerCase().includes("дополнительные")) {
        additionalFeatures = additionalSection
          .split("\n")
          .slice(1) // Skip the header
          .filter((f) => f.trim());
      }
    } else {
      // Simple split if no clear sections
      const allFeatures = featuresText.split("\n").filter((f) => f.trim());
      mainFeatures = allFeatures.slice(0, Math.min(5, allFeatures.length));
      additionalFeatures = allFeatures.slice(5);
    }

    // Populate features
    populateFeatures("main", mainFeatures);
    populateFeatures("additional", additionalFeatures);
  }

  // Insert content suggestion
  function insertContentSuggestion(content) {
    if (!editorState.descriptionEditor) return;

    const range = editorState.descriptionEditor.getSelection();
    if (range) {
      editorState.descriptionEditor.insertText(
        range.index,
        `\n\n${content}: `,
        "user"
      );
    } else {
      editorState.descriptionEditor.insertText(
        editorState.descriptionEditor.getLength(),
        `\n\n${content}: `,
        "user"
      );
    }

    // Focus the editor
    editorState.descriptionEditor.focus();
  }

  // Switch preview device
  function switchPreviewDevice(device) {
    const previewFrame = document.getElementById("preview-frame");
    const deviceBtns = document.querySelectorAll(".preview-device-btn");

    // Update active button
    deviceBtns.forEach((btn) => {
      btn.classList.toggle("active", btn.dataset.device === device);
    });

    // Update preview frame size
    switch (device) {
      case "mobile":
        previewFrame.style.width = "375px";
        previewFrame.style.height = "667px";
        break;
      case "tablet":
        previewFrame.style.width = "768px";
        previewFrame.style.height = "1024px";
        break;
      default: // desktop
        previewFrame.style.width = "100%";
        previewFrame.style.height = "800px";
        break;
    }

    // Re-generate preview to fit new size
    generatePreview();
  }

  // Generate preview in iframe
  function generatePreview() {
    const previewFrame = document.getElementById("preview-frame");
    if (!previewFrame) return;

    // Get form data
    const formData = new FormData(document.getElementById("car-detail-form"));

    // Collect data from rich text editors and dynamic fields
    if (editorState.descriptionEditor) {
      formData.set("description", editorState.descriptionEditor.root.innerHTML);
    }

    // Collect specs
    const specRows = document.querySelectorAll(".spec-row");
    const specs = Array.from(specRows)
      .map((row) => {
        const key = row.querySelector(".spec-key").value;
        const value = row.querySelector(".spec-value").value;
        return `${key}: ${value}`;
      })
      .join("\n");
    formData.set("specs", specs);

    // Collect features
    const mainFeatures = Array.from(
      document.querySelectorAll("#main-features .feature-row")
    )
      .map((row) => row.querySelector("input").value)
      .filter((text) => text.trim());

    const additionalFeatures = Array.from(
      document.querySelectorAll("#additional-features .feature-row")
    )
      .map((row) => row.querySelector("input").value)
      .filter((text) => text.trim());

    const bestFeatures = [
      "Основные опции",
      ...mainFeatures,
      "",
      "Дополнительные",
      ...additionalFeatures,
    ].join("\n");

    formData.set("best_features", bestFeatures);

    // Set images
    formData.set("images", JSON.stringify(editorState.images));

    // Convert FormData to object
    const data = Object.fromEntries(formData.entries());

    // Generate preview HTML
    fetch("/api/car-details/preview", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    })
      .then((response) => response.json())
      .then((result) => {
        if (result.html) {
          // Write HTML to iframe
          const doc =
            previewFrame.contentDocument || previewFrame.contentWindow.document;
          doc.open();
          doc.write(result.html);
          doc.close();
        } else {
          throw new Error("Не удалось сгенерировать предпросмотр");
        }
      })
      .catch((error) => {
        showNotification("Ошибка", error.message, "error");
      });
  }

  // Save form as draft
  function saveAsDraft() {
    submitForm("draft");
  }

  // Publish car detail
  function publishCarDetail() {
    submitForm("published");
  }

  // Publish and generate HTML
  function publishAndGenerate() {
    submitForm("published", true);
  }

  // Submit form with status
  async function submitForm(status, generateHtml = false) {
    // Validate form
    if (!validateForm()) {
      // Error notification is already shown by validateForm
      return;
    }

    // Get form data
    const formData = new FormData(document.getElementById("car-detail-form"));

    // Collect data from rich text editors and dynamic fields
    if (editorState.descriptionEditor) {
      formData.set("description", editorState.descriptionEditor.root.innerHTML);
    }

    // Collect specs
    const specRows = document.querySelectorAll(".spec-row");
    const specs = Array.from(specRows)
      .map((row) => {
        const key = row.querySelector(".spec-key").value;
        const value = row.querySelector(".spec-value").value;
        return `${key}: ${value}`;
      })
      .join("\n");
    formData.set("specs", specs);

    // Collect features
    const mainFeatures = Array.from(
      document.querySelectorAll("#main-features .feature-row")
    )
      .map((row) => row.querySelector("input").value)
      .filter((text) => text.trim());

    const additionalFeatures = Array.from(
      document.querySelectorAll("#additional-features .feature-row")
    )
      .map((row) => row.querySelector("input").value)
      .filter((text) => text.trim());

    const bestFeatures = [
      "Основные опции",
      ...mainFeatures,
      "",
      "Дополнительные",
      ...additionalFeatures,
    ].join("\n");

    formData.set("best_features", bestFeatures);

    // Set images
    formData.set("images", JSON.stringify(editorState.images));

    // Set status
    formData.set("status", status);

    // Show loading state
    const submitBtn = editorState.isEditing
      ? document.getElementById("publish-btn")
      : document.getElementById("save-draft-btn");

    const originalBtnText = submitBtn.innerHTML;
    submitBtn.innerHTML =
      '<i class="fas fa-spinner fa-spin"></i> Сохранение...';
    submitBtn.disabled = true;

    try {
      // Submit form
      let response;

      if (editorState.isEditing) {
        response = await fetch(`/api/car-details/${editorState.detailId}`, {
          method: "PUT",
          body: formData,
        });
      } else {
        response = await fetch("/api/car-details", {
          method: "POST",
          body: formData,
        });
      }

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Ошибка сохранения");
      }

      const result = await response.json();

      showNotification(
        "Успех",
        editorState.isEditing
          ? "Детальная страница обновлена"
          : "Детальная страница создана",
        "success"
      );

      // If generate HTML was requested
      if (generateHtml && result.id) {
        generateHtmlPageFor(result.id);
      } else {
        // Reset form and editor state
        resetForm();
      }
    } catch (error) {
      showNotification("Ошибка", error.message, "error");
    } finally {
      // Reset button state
      submitBtn.innerHTML = originalBtnText;
      submitBtn.disabled = false;
    }
  }

  // Generate HTML page
  function generateHtmlPage() {
    if (editorState.detailId) {
      generateHtmlPageFor(editorState.detailId);
    } else {
      showNotification(
        "Ошибка",
        "Сохраните детальную страницу перед генерацией HTML",
        "error"
      );
    }
  }

  // Generate HTML page for a specific detail ID
  async function generateHtmlPageFor(detailId) {
    try {
      // Show prompt for output path
      const outputPath = prompt(
        "Введите путь для генерации HTML (относительно public):",
        "cars"
      );

      if (outputPath === null) {
        return; // User cancelled
      }

      // Show loading notification
      showNotification("Информация", "Генерация HTML страницы...", "info");

      console.log("Отправка запроса на генерацию HTML для ID:", detailId);

      // Send request to generate HTML
      const response = await fetch(`/api/car-details/${detailId}/generate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ outputPath }),
      });

      console.log("Получен ответ со статусом:", response.status);

      // For debugging
      const responseText = await response.text();
      console.log("Ответ от сервера:", responseText);

      // Parse the response
      let result;
      try {
        result = JSON.parse(responseText);
      } catch (e) {
        console.error("Ошибка при парсинге JSON ответа:", e);
        throw new Error(
          "Ответ сервера не является корректным JSON: " + responseText
        );
      }

      if (!response.ok) {
        throw new Error(result.error || `Ошибка сервера: ${response.status}`);
      }

      showNotification(
        "Успех",
        `HTML страница создана: ${result.filePath}`,
        "success"
      );

      // Ask if user wants to open the page
      if (confirm("Открыть созданную страницу?")) {
        window.open(result.filePath, "_blank");
      }

      // Reload published pages list if modal is open
      const publishedPagesModal = document.querySelector(
        ".published-pages-modal"
      );
      if (publishedPagesModal) {
        viewPublishedPages();
      }
    } catch (error) {
      console.error("Ошибка генерации HTML:", error);
      showNotification(
        "Ошибка",
        error.message || "Не удалось сгенерировать HTML",
        "error"
      );
    }
  }

  // Validate form
  function validateForm() {
    const requiredFields = ["car-id", "full-title", "price", "moscow-price"];

    for (const field of requiredFields) {
      const input = document.getElementById(field);
      if (!input || !input.value.trim()) {
        // Highlight the field
        if (input) input.classList.add("is-invalid");
        showNotification(
          "Ошибка",
          `Заполните обязательное поле: ${field}`,
          "error"
        );
        navigateToStep("basics");
        return false;
      } else {
        // Remove any previous highlighting
        if (input) input.classList.remove("is-invalid");
      }
    }

    // Check for description
    if (
      editorState.descriptionEditor &&
      editorState.descriptionEditor.getText().trim().length < 10
    ) {
      showNotification("Ошибка", "Добавьте описание автомобиля", "error");
      navigateToStep("description");
      return false;
    }

    // Check for images
    if (editorState.images.length === 0) {
      showNotification("Ошибка", "Добавьте хотя бы одно изображение", "error");
      navigateToStep("gallery");
      return false;
    }

    // Preview step is not required for validation
    return true;
  }

  // Reset form
  function resetForm() {
    document.getElementById("car-detail-form").reset();
    document.getElementById("detail-id").value = "";
    document.getElementById("car-id").value = "";

    // Clear editor
    if (editorState.descriptionEditor) {
      editorState.descriptionEditor.setText("");
    }

    // Clear images
    editorState.images = [];
    renderGallery();

    // Clear features
    clearFeaturesContainer();

    // Reset editor state
    editorState.detailId = null;
    editorState.carId = null;
    editorState.isEditing = false;
    editorState.completedSteps = [];

    // Reset navigation
    navigateToStep("basics");
  }

  // View published car detail pages
  async function viewPublishedPages() {
    try {
      // Show loading notification
      showNotification(
        "Информация",
        "Загрузка списка опубликованных страниц...",
        "info"
      );

      // Fetch published pages
      const response = await fetch("/api/car-details/published");

      if (!response.ok) {
        throw new Error(`Ошибка загрузки страниц: ${response.status}`);
      }

      const publishedPages = await response.json();

      if (!publishedPages || publishedPages.length === 0) {
        showNotification("Информация", "Нет опубликованных страниц", "info");
        return;
      }

      // Create pages listing
      let pageListHtml = '<div class="published-pages-modal">';
      pageListHtml += "<h3>Опубликованные страницы автомобилей</h3>";
      pageListHtml += '<div class="published-pages-list">';

      publishedPages.forEach((page) => {
        pageListHtml += `
                    <div class="published-page-item">
                        <div class="page-info">
                            <div class="page-title">${
                              page.title || "Без названия"
                            }</div>
                            <div class="page-path">${page.filePath}</div>
                        </div>
                        <div class="page-actions">
                            <button class="btn btn-primary btn-sm view-page-btn" data-path="${
                              page.filePath
                            }">
                                <i class="fas fa-external-link-alt"></i> Открыть
                            </button>
                            <button class="btn btn-secondary btn-sm edit-page-btn" data-id="${
                              page.id
                            }">
                                <i class="fas fa-edit"></i> Редактировать
                            </button>
                        </div>
                    </div>
                `;
      });

      pageListHtml += "</div>";
      pageListHtml +=
        '<button class="btn btn-secondary close-modal-btn">Закрыть</button>';
      pageListHtml += "</div>";

      // Create modal overlay
      const modalOverlay = document.createElement("div");
      modalOverlay.className = "modal-overlay";
      modalOverlay.innerHTML = pageListHtml;
      document.body.appendChild(modalOverlay);

      // Add event listeners to buttons
      modalOverlay.querySelectorAll(".view-page-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          window.open(btn.dataset.path, "_blank");
        });
      });

      modalOverlay.querySelectorAll(".edit-page-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          loadDetailForEditing(btn.dataset.id);
          modalOverlay.remove();
        });
      });

      modalOverlay
        .querySelector(".close-modal-btn")
        .addEventListener("click", () => {
          modalOverlay.remove();
        });

      // Add click outside to close
      modalOverlay.addEventListener("click", (event) => {
        if (event.target === modalOverlay) {
          modalOverlay.remove();
        }
      });
    } catch (error) {
      console.error("Error loading published pages:", error);
      showNotification("Ошибка", error.message, "error");
    }
  }

  // Initialize the editor
  initEditor();
});
