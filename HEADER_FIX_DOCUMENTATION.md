# Исправление проблем с шапкой сайта SHMS

## Проблема
Элементы шапки (логотип и навигационные ссылки) "скачут" или смещаются на странице stock.html по сравнению с другими страницами сайта, несмотря на использование общих стилей.

## Выявленные проблемы

### 1. Разная высота шапки в разных CSS файлах
- **hero.css**: `.hero-header-group` имел высоту 48px
- **stock.css**: `.header` имел высоту 110px  
- **mobile-header.css**: `.mobile-header` имел высоту 64px

### 2. Конфликт стилей логотипа
- **hero.css**: `.header__logo-img` имел фиксированные размеры 30px x 100px
- **stock.css**: `.mobile-header__logo-img` имел высоту 80px
- **feedback-mockup.html**: инлайновые стили переопределяли CSS

### 3. Разные медиа-запросы
- Несогласованные точки перехода между мобильной и десктопной версиями
- Разные z-index значения для элементов шапки

### 4. Дублирование стилей
- **stock.css** содержал дублирующиеся стили мобильного хедера
- Конфликтующие правила в разных файлах

## Решение

### 1. Создан единый CSS файл для шапки
**Файл**: `public/css/header-common.css`

Содержит:
- CSS переменные для единообразия размеров
- Общие стили для десктопной и мобильной шапки
- Унифицированные медиа-запросы

### 2. Унифицированы размеры
```css
:root {
  --header-height-desktop: 60px;
  --header-height-mobile: 60px;
  --logo-height-desktop: 30px;  /* Восстановлен изначальный размер */
  --logo-height-mobile: 30px;   /* Восстановлен изначальный размер */
  --header-z-index: 1000;
}
```

### 3. Добавлены специальные стили для страницы stock
```css
/* Специальные стили для анимации логотипа на странице stock */
body.stock-page .header__logo-link {
  margin-left: -35px; /* Сдвигаем логотип влево для анимации */
}

body.stock-page .header__logo-img {
  transform: translateX(35px); /* Компенсируем сдвиг */
  margin-top: 15px;
  transition: transform 0.3s ease;
}

body.stock-page .header__logo-link:hover .header__logo-img {
  transform: translateX(35px) translateY(-2px); /* Анимация при hover */
}
```

### 4. Исправлены все HTML страницы
Добавлен `header-common.css` в:
- `index.html`
- `stock.html` (+ добавлен класс `stock-page` для специальных стилей)
- `contacts.html`
- `order.html`
- `feedback-mockup.html`

### 5. Удалены конфликтующие стили
- Удалены дублирующиеся стили мобильного хедера из `stock.css`
- Исправлены инлайновые стили в `feedback-mockup.html`
- Синхронизированы высоты в медиа-запросах

## Результат

### ✅ Исправлено:
- Унифицирована высота шапки на всех страницах (60px)
- **Восстановлен изначальный размер логотипа (30px высота)**
- Удалены конфликтующие стили
- Синхронизированы z-index значения
- Исправлены медиа-запросы для мобильных устройств
- **Добавлен специальный сдвиг логотипа влево на странице stock для анимации**

### ✅ Теперь единообразно:
- Позиционирование логотипа
- Выравнивание навигационных ссылок
- Отступы и размеры элементов шапки
- Мобильная и десктопная версии

### 🎯 Специальные возможности:
- **На странице stock.html логотип сдвинут влево для корректной работы анимации**
- **Hover-анимация логотипа работает правильно на всех страницах**

## Тестирование

Создана тестовая страница: `public/test-header.html`

### Для проверки:
1. Откройте http://localhost:3000/test-header.html
2. Сравните с другими страницами:
   - http://localhost:3000/stock
   - http://localhost:3000/contacts
   - http://localhost:3000/order
   - http://localhost:3000/

### Проверьте:
- Одинаковая высота шапки на всех страницах
- Логотип имеет одинаковый размер и позицию
- Навигационные ссылки выровнены одинаково
- Мобильная версия работает корректно

## Файлы, которые были изменены

### CSS файлы:
- `public/css/hero.css` - исправлены размеры и высоты
- `public/css/mobile-header.css` - унифицированы размеры
- `public/css/stock.css` - удалены дублирующиеся стили
- `public/css/header-common.css` - **НОВЫЙ** общий файл стилей

### HTML файлы:
- `public/index.html` - добавлен header-common.css, исправлены размеры
- `public/stock.html` - добавлен header-common.css
- `public/contacts.html` - добавлен header-common.css
- `public/order.html` - добавлен header-common.css
- `public/feedback-mockup.html` - добавлен header-common.css, удалены инлайновые стили

### Тестовые файлы:
- `public/test-header.html` - **НОВЫЙ** файл для тестирования

## Рекомендации для будущего

1. **Всегда используйте header-common.css** для стилей шапки
2. **Не добавляйте инлайновые стили** для элементов шапки
3. **Используйте CSS переменные** из header-common.css для размеров
4. **Тестируйте на всех страницах** при изменении стилей шапки

## Порядок подключения CSS файлов

Правильный порядок для всех страниц:
```html
<link rel="stylesheet" href="css/layout.css" />
<link rel="stylesheet" href="css/header-common.css" />
<!-- Остальные CSS файлы -->
<link rel="stylesheet" href="css/mobile-header.css" />
<link rel="stylesheet" href="css/hero.css" />
```

Это обеспечивает правильное наследование стилей и предотвращает конфликты.
