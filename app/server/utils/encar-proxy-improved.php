<?php
// Улучшенный прокси для Encar API - обрабатывает и CSV данные, и изображения
ini_set('memory_limit', '3072M'); // Увеличиваем лимит памяти до 3 GB
ini_set('display_errors', 0); // Отключаем вывод ошибок в продакшене
ini_set('max_execution_time', 600); // Увеличиваем время выполнения скрипта до 10 минут

// Вести лог ошибок для отладки
error_log('Encar proxy called with: ' . print_r($_GET, true));

// В начале файла, после ini_set
error_reporting(E_ALL);
ini_set('display_errors', 1); // Временно включаем вывод ошибок для отладки
ini_set('log_errors', 1);
ini_set('error_log', 'php_errors.log');

// Добавляем функцию для логирования
function debug_log($message, $data = null) {
    $log_message = date('Y-m-d H:i:s') . " - " . $message;
    if ($data !== null) {
        $log_message .= " - Data: " . print_r($data, true);
    }
    error_log($log_message);
}

// Проверка доступности прокси - для отладки
if (isset($_GET['check'])) {
    header('Content-Type: text/plain');
    echo "Proxy server is operational. PHP version: " . PHP_VERSION;
    
    // Проверяем наличие необходимых расширений
    $extensions = [
        'curl' => extension_loaded('curl'),
        'gd' => extension_loaded('gd'),
        'json' => extension_loaded('json')
    ];
    
    echo "\nExtensions: " . json_encode($extensions);
    
    // Проверяем настройки PHP
    $settings = [
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'allow_url_fopen' => ini_get('allow_url_fopen')
    ];
    
    echo "\nSettings: " . json_encode($settings);
    exit;
}

// Проверка наличия необходимых расширений PHP
$missing_extensions = [];
if (!extension_loaded('curl')) $missing_extensions[] = 'curl';
if (!extension_loaded('gd')) $missing_extensions[] = 'gd';

if (!empty($missing_extensions)) {
    header('Content-Type: application/json');
    echo json_encode([
        'error' => 'Missing PHP extensions: ' . implode(', ', $missing_extensions),
        'message' => 'Please install the required PHP extensions. See README-setup.md for instructions.',
        'status' => 'error',
        'mock_data' => true,
        'data' => getMockData()
    ]);
    exit;
}

// Настройки API - пробуем использовать реальный API с ограничениями
$api_host = 'https://autobase-wade.auto-parser.ru';
$api_username = 'admin';
$api_password = 'n2Q8ewyLft9qgPmim5ng';

// Принудительно используем тестовые данные? false - пытаемся использовать API
$force_mock_data = false;

// Дополнительные настройки производительности
$enable_api_cache = true; // Включаем кеширование API-запросов
$api_cache_ttl = 3600; // 1 час - время жизни кеша

// Максимальное время ожидания ответа API в секундах
$api_timeout = 10; // Уменьшаем таймаут до 10 секунд

// Максимальное количество повторных попыток при таймауте
$max_retries = 2;  // Уменьшаем количество попыток

// Определяем размер блока данных (в байтах), который запрашиваем за один раз
$block_size = 100 * 1024; // Уменьшаем размер блока до 100KB

// Количество дней для поиска автомобилей в прошлом (включая сегодня)
$search_days = 30;
$date = date('Y-m-d', strtotime("-{$search_days} days"));

// Используем ли частично полученные данные при таймауте
$use_partial_data = true;

// Логируем начало запроса
error_log('Starting API request with memory limit: ' . ini_get('memory_limit'));
error_log('Max execution time: ' . ini_get('max_execution_time') . ' seconds');
error_log('Searching for cars over ' . $search_days . ' days');

// Определяем тип запроса (данные/изображение)
$request_type = isset($_GET['type']) ? $_GET['type'] : 'data';

if ($request_type === 'image') {
    // Обработка запроса изображения
    $image_url = isset($_GET['url']) ? $_GET['url'] : '';
    if (empty($image_url)) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Image URL is required']);
        exit;
    }
    
    // Проверяем, требуется ли авторизация для этого изображения
    $need_auth = isset($_GET['auth']) && $_GET['auth'] == '1';
    
    // Логируем запрос для отладки
    error_log("Image request: " . $image_url . ", Auth: " . ($need_auth ? 'Yes' : 'No'));
    
    // Очищаем URL от лишних символов
    $image_url = trim($image_url, "'\"[]");
    
    // Проверяем, является ли URL JSON строкой
    if (strpos($image_url, '[') === 0 || strpos($image_url, '{') === 0) {
        try {
            $decoded = json_decode($image_url, true);
            if (is_array($decoded)) {
                // Если это массив, берем первый валидный URL
                foreach ($decoded as $url) {
                    if (is_string($url) && filter_var($url, FILTER_VALIDATE_URL)) {
                        $image_url = $url;
                        break;
                    }
                }
            }
        } catch (Exception $e) {
            error_log("Error decoding JSON image URL: " . $e->getMessage());
        }
    }
    
    // Проверяем валидность URL
    if (!filter_var($image_url, FILTER_VALIDATE_URL)) {
        // Если URL невалидный, проверяем, может это относительный путь
        if (strpos($image_url, '/') === 0) {
            $image_url = 'https://autobase-wade.auto-parser.ru' . $image_url;
        } else {
            // Если это не URL и не относительный путь, используем заглушку
            header('Content-Type: image/jpeg');
            readfile('Img/placeholder-car.jpg');
            exit;
        }
    }
    
    // Проверяем наличие кэшированной версии изображения
    $cache_dir = 'cache/images/';
    if (!is_dir($cache_dir)) {
        mkdir($cache_dir, 0777, true);
    }
    
    // Создаем хеш имени файла из URL и параметра auth
    $cache_key = $image_url . '|auth=' . ($need_auth ? '1' : '0');
    $cache_filename = $cache_dir . md5($cache_key) . '.jpg';
    
    // Проверяем наличие кэшированного файла
    if (file_exists($cache_filename) && (time() - filemtime($cache_filename) < 86400)) { // 24 часа
        header('Content-Type: image/jpeg');
        readfile($cache_filename);
        exit;
    }
    
    // Проверяем известные CDN
    if (strpos($image_url, 'cdn.motor1.com') !== false || 
        strpos($image_url, 'scdn.carimages.ru') !== false || 
        strpos($image_url, 'upload.wikimedia.org') !== false) {
        
        // Пытаемся найти локальное изображение по модели
        $model = extractModelFromUrl($image_url);
        $local_image = getLocalImageByModel($model);
        
        if ($local_image && file_exists($local_image)) {
            header('Content-Type: image/jpeg');
            readfile($local_image);
            exit;
        }
        
        // Если локальное изображение не найдено, используем заглушку
        header('Content-Type: image/jpeg');
        readfile('Img/placeholder-car.jpg');
        exit;
    }
    
    // Список публичных CDN, которые можно использовать напрямую
    $public_cdns = [
        'images.unsplash.com',
        'images.pexels.com',
        'images.wallpaperscraft.com',
        'img.freepik.com',
        'avatars.mds.yandex.net',
        'yastatic.net'
    ];
    
    // Проверяем, является ли URL из публичного CDN
    $url_parts = parse_url($image_url);
    if (isset($url_parts['host'])) {
        foreach ($public_cdns as $cdn) {
            if (strpos($url_parts['host'], $cdn) !== false) {
                // Для публичных CDN используем прямой редирект
                header('Location: ' . $image_url);
                exit;
            }
        }
    }
    
    // Для остальных URL используем прокси
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $image_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        // Добавляем заголовки авторизации, если нужно
        if ($need_auth) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . ENCAR_API_KEY,
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]);
        }
        
        $image_data = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        
        if ($http_code === 200 && $image_data) {
            // Сохраняем в кэш
            file_put_contents($cache_filename, $image_data);
            
            // Отправляем изображение
            header('Content-Type: ' . $content_type);
            echo $image_data;
        } else {
            // В случае ошибки используем заглушку
            header('Content-Type: image/jpeg');
            readfile('Img/placeholder-car.jpg');
        }
        
        curl_close($ch);
    } catch (Exception $e) {
        error_log("Error fetching image: " . $e->getMessage());
        header('Content-Type: image/jpeg');
        readfile('Img/placeholder-car.jpg');
    }
    exit;
} else {
    // Обработка запроса данных
    if ($force_mock_data) {
        error_log("Forcing mock data due to API access issues (file too large)");
        
        // Получаем параметры поиска, если они есть
        $search_brand = isset($_GET['brand']) ? trim($_GET['brand']) : '';
        $search_model = isset($_GET['model']) ? trim($_GET['model']) : '';
        
        error_log("Search params: brand='$search_brand', model='$search_model'");
        
        $mock_data = getMockData();
        
        // Фильтруем данные по бренду и модели, если указаны
        if (!empty($search_brand) || !empty($search_model)) {
            $filtered_data = [];
            
            // Список известных брендов для проверки похожести
            $known_brands = [
                'BMW', 'Audi', 'Mercedes', 'Mercedes-Benz', 'Porsche', 'Ferrari',
                'Lamborghini', 'Bentley', 'Aston Martin', 'Rolls-Royce', 'Tesla', 'Maserati'
            ];
            
            // Если бренд указан с опечаткой, пытаемся найти ближайшее совпадение
            $corrected_brand = $search_brand;
            if (!empty($search_brand)) {
                foreach ($known_brands as $known_brand) {
                    if (stripos($known_brand, $search_brand) !== false || 
                        stripos($search_brand, $known_brand) !== false || 
                        similar_text($known_brand, $search_brand, $percent) > 3 && $percent > 50) {
                        $corrected_brand = $known_brand;
                        error_log("Corrected brand from '$search_brand' to '$corrected_brand'");
                        break;
                    }
                }
            }
            
            foreach ($mock_data as $car) {
                // Проверяем соответствие бренду (с учетом исправленного бренда)
                $match_brand = empty($search_brand) || 
                               stripos($car['brand'], $corrected_brand) !== false || 
                               stripos($corrected_brand, $car['brand']) !== false;
                
                // Проверяем соответствие модели
                $match_model = empty($search_model) || 
                               stripos($car['model'], $search_model) !== false || 
                               similar_text($car['model'], $search_model, $percent) > 3 && $percent > 50;
                
                if ($match_brand && $match_model) {
                    $filtered_data[] = $car;
                }
            }
            
            error_log("Filtered data count: " . count($filtered_data) . " out of " . count($mock_data));
            $mock_data = $filtered_data;
        }
        
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'success',
            'mock_data' => true,
            'message' => 'Using mock data due to API access issues (file too large)',
            'data' => $mock_data
        ]);
        exit;
    }
    
    // Если не используем тестовые данные - обрабатываем запрос к API
    $file_type = isset($_GET['fileType']) ? $_GET['fileType'] : 'active_offer';
    if (!in_array($file_type, ['active_offer', 'removed_offer'])) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Invalid file type']);
        exit;
    }
    
    // Получаем текущую дату или используем указанную
    $date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
        $date = date('Y-m-d');
    }

    // Проверяем, существует ли файл за эту дату, иначе используем вчерашнюю дату
    function encar_csv_exists($date) {
        $url = "https://autobase-wade.auto-parser.ru/encar/{$date}/active_offer.csv";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_USERPWD, "admin:n2Q8ewyLft9qgPmim5ng");
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        return $http_code === 200;
    }

    if (!encar_csv_exists($date)) {
        $date = date('Y-m-d', strtotime('-1 day'));
    }
    
    // Бренд для фильтрации
    $filter_brand = isset($_GET['brand']) ? trim($_GET['brand']) : '';
    
    // Модель для фильтрации
    $filter_model = isset($_GET['model']) ? trim($_GET['model']) : '';
    
    // Лимит записей (по умолчанию 10, максимум 20)
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
    if ($limit <= 0) $limit = 10;
    if ($limit > 20) $limit = 20; // Устанавливаем максимальный лимит для уменьшения размера ответа
    
    // Прямой доступ (для тестирования с реальным API)
    $direct_access = isset($_GET['direct']) && $_GET['direct'] == '1';
    
    // Добавляем функцию для проверки доступности API
    function checkApiAvailability($host, $username, $password) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $host . '/encar/');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_USERPWD, "{$username}:{$password}");
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return [
            'available' => $http_code === 200,
            'http_code' => $http_code
        ];
    }

    // Сначала пытаемся обнаружить доступные эндпоинты
    $api_status = checkApiAvailability($api_host, $api_username, $api_password);
    debug_log("API availability check", $api_status);
    
    if (!$api_status['available']) {
        debug_log("API is not available", $api_status);
        $mock_data = getMockData();
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'API is not available: ' . ($api_status['error'] ?: 'HTTP ' . $api_status['http_code']),
            'api_status' => $api_status,
            'mock_data' => true,
            'data' => $mock_data
        ]);
        exit;
    }
    
    // Если требуется прямой доступ к API
    if ($direct_access) {
        debug_log("Direct API access requested");
        
        // Формируем URL для API с разными вариантами эндпоинтов
        $api_endpoints = [
            "/encar/{$date}/api/vehicles/search",
            "/encar/{$date}/api/v1/vehicles/search",
            "/encar/{$date}/api/cars/search",
            "/encar/{$date}/api/v1/cars/search",
            "/encar/{$date}/vehicles/search",
            "/encar/{$date}/cars/search",
            "/encar/{$date}/api/search",
            "/encar/{$date}/api/v1/search",
            "/encar/{$date}/search"
        ];
        
        $params = [
            'brand' => $filter_brand,
            'model' => $filter_model,
            'limit' => $limit
        ];
        
        $success = false;
        $last_error = null;
        
        foreach ($api_endpoints as $endpoint) {
            $direct_url = $api_host . $endpoint . '?' . http_build_query($params);
            debug_log("Trying API endpoint", $direct_url);
            
            try {
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $direct_url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, $api_timeout);
                curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
                curl_setopt($ch, CURLOPT_USERPWD, "{$api_username}:{$api_password}");
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Accept: application/json',
                    'Content-Type: application/json',
                    'X-Requested-With: XMLHttpRequest',
                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                ]);
                
                // Включаем подробное логирование cURL
                $verbose = fopen('php://temp', 'w+');
                curl_setopt($ch, CURLOPT_VERBOSE, true);
                curl_setopt($ch, CURLOPT_STDERR, $verbose);
                
                // Добавляем проверку SSL
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                
                debug_log("Sending cURL request with options", [
                    'url' => $direct_url,
                    'timeout' => $api_timeout,
                    'auth' => "{$api_username}:{$api_password}",
                    'headers' => [
                        'Accept: application/json',
                        'Content-Type: application/json',
                        'X-Requested-With: XMLHttpRequest',
                        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    ]
                ]);
                
                $result = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $curl_error = curl_error($ch);
                $curl_errno = curl_errno($ch);
                
                // Получаем подробную информацию о запросе
                rewind($verbose);
                $verbose_log = stream_get_contents($verbose);
                
                debug_log("cURL response details", [
                    'endpoint' => $endpoint,
                    'http_code' => $http_code,
                    'curl_error' => $curl_error,
                    'curl_errno' => $curl_errno,
                    'result_length' => strlen($result),
                    'verbose_log' => $verbose_log,
                    'total_time' => curl_getinfo($ch, CURLINFO_TOTAL_TIME),
                    'connect_time' => curl_getinfo($ch, CURLINFO_CONNECT_TIME),
                    'ssl_verify_result' => curl_getinfo($ch, CURLINFO_SSL_VERIFYRESULT),
                    'redirect_count' => curl_getinfo($ch, CURLINFO_REDIRECT_COUNT),
                    'effective_url' => curl_getinfo($ch, CURLINFO_EFFECTIVE_URL)
                ]);
                
                curl_close($ch);
                
                if ($http_code == 200 && !empty($result)) {
                    debug_log("Successfully received JSON data from endpoint: " . $endpoint);
                    $json_data = json_decode($result, true);
                    
                    if ($json_data && (isset($json_data['data']) || isset($json_data['vehicles']))) {
                        debug_log("JSON data parsed successfully");
                        $vehicles = isset($json_data['data']) ? $json_data['data'] : $json_data['vehicles'];
                        $cars = [];
                        
                        foreach ($vehicles as $vehicle) {
                            $car = [
                                'brand' => isset($vehicle['brand']) ? $vehicle['brand'] : (isset($vehicle['make']) ? $vehicle['make'] : 'Unknown'),
                                'model' => isset($vehicle['model']) ? $vehicle['model'] : 'Unknown Model',
                                'year' => isset($vehicle['year']) ? $vehicle['year'] : (isset($vehicle['production_year']) ? $vehicle['production_year'] : '2023'),
                                'power' => isset($vehicle['engine_power']) ? $vehicle['engine_power'] . ' л.с.' : '',
                                'engine' => isset($vehicle['engine_volume']) ? $vehicle['engine_volume'] . 'л' : '',
                                'mileage' => isset($vehicle['mileage']) ? $vehicle['mileage'] : '0',
                                'price' => isset($vehicle['price']) ? '$' . number_format((float)$vehicle['price'], 0, '.', ',') : '',
                                'image_url' => isset($vehicle['image_url']) ? $vehicle['image_url'] : (
                                    isset($vehicle['images'][0]) ? $vehicle['images'][0] : (
                                        isset($vehicle['photo']) ? $vehicle['photo'] : ''
                                    )
                                )
                            ];
                            
                            $cars[] = $car;
                        }
                        
                        header('Content-Type: application/json');
                        echo json_encode([
                            'status' => 'success',
                            'mode' => 'direct_api',
                            'endpoint' => $endpoint,
                            'date' => $date,
                            'count' => count($cars),
                            'data' => $cars
                        ]);
                        exit;
                    }
                }
                
                $last_error = [
                    'endpoint' => $endpoint,
                    'http_code' => $http_code,
                    'error' => $curl_error,
                    'result' => $result
                ];
                
            } catch (Exception $e) {
                $last_error = [
                    'endpoint' => $endpoint,
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ];
            }
        }
        
        // Если ни один эндпоинт не сработал, пробуем получить CSV файл
        debug_log("All API endpoints failed, trying CSV file", [
            'date' => $date
        ]);
        
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "{$api_host}/encar/{$date}/{$file_type}.csv");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, $api_timeout);
            curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
            curl_setopt($ch, CURLOPT_USERPWD, "{$api_username}:{$api_password}");
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $csv_data = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code === 200 && !empty($csv_data)) {
                debug_log("Successfully received CSV data");
                $cars = processCSV($csv_data, $limit, $filter_brand, $filter_model);
                
                header('Content-Type: application/json');
                echo json_encode([
                    'status' => 'success',
                    'mode' => 'csv',
                    'date' => $date,
                    'count' => count($cars),
                    'data' => $cars
                ]);
                exit;
            }
        } catch (Exception $e) {
            debug_log("Error fetching CSV file", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        // Если все попытки не удались, возвращаем тестовые данные
        debug_log("All data fetching attempts failed", $last_error);
        $mock_data = getMockData();
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'All data fetching attempts failed',
            'last_error' => $last_error,
            'mock_data' => true,
            'data' => $mock_data
        ]);
        exit;
    }
    
    // Кеширование данных
    $cache_dir = 'cache/data/';
    if (!is_dir($cache_dir)) {
        mkdir($cache_dir, 0777, true);
    }
    
    // Включаем параметры фильтрации в имя кеша
    $cache_key = $date . '|' . $filter_brand . '|' . $filter_model . '|' . $limit;
    $cache_file = $cache_dir . md5($cache_key) . '.json';
    $cache_ttl = 3600; // 1 час кеширования
    
    // Проверяем наличие кеша
    if (file_exists($cache_file) && (time() - filemtime($cache_file) < $cache_ttl)) {
        $cached_data = file_get_contents($cache_file);
        $data = json_decode($cached_data, true);
        
        // Если есть данные в кеше, возвращаем их
        if ($data && isset($data['data']) && !empty($data['data'])) {
            header('Content-Type: application/json');
            header('X-Cache: HIT');
            
            echo json_encode($data);
            exit;
        }
    }
    
    // Получаем данные с API с лимитом строк
    error_log("Trying to fetch real data from API with limit: {$limit}");
    
    try {
        // Инициализируем переменные для сбора данных
        $all_csv_data = '';
        $retry_count = 0;
        $current_offset = 0;
        $timed_out = false;
        
        // Делаем несколько попыток получить данные блоками
        while ($retry_count < $max_retries) {
            try {
                // Формируем URL с диапазоном байт
                $range_url = "{$api_host}/encar/{$date}/{$file_type}.csv";
                
                // Устанавливаем заголовок Range для получения части файла
                $range_header = "Range: bytes={$current_offset}-" . ($current_offset + $block_size - 1);
                error_log("Fetching data block: {$range_header}");
                
                // Получаем блок данных с API
                $block_data = fetchDataWithRange($range_url, $range_header, $api_username, $api_password, $retry_count);
                
                if (!empty($block_data)) {
                    // Добавляем данные к общему массиву
                    if (empty($all_csv_data)) {
                        // Первый блок - сохраняем целиком
                        $all_csv_data = $block_data;
                    } else {
                        // Для последующих блоков удаляем заголовок CSV, если он есть
                        $first_newline = strpos($block_data, "\n");
                        if ($first_newline !== false) {
                            $all_csv_data .= substr($block_data, $first_newline + 1);
                        } else {
                            $all_csv_data .= $block_data;
                        }
                    }
                    
                    // Переходим к следующему блоку
                    $current_offset += $block_size;
                    
                    // Проверяем, есть ли уже достаточно данных
                    $line_count = substr_count($all_csv_data, "\n");
                    error_log("Collected {$line_count} lines so far, current size: " . strlen($all_csv_data) . " bytes");
                    
                    // Если у нас достаточно строк, завершаем сбор данных
                    if ($line_count > $limit * 2) { // Берем в 2 раза больше лимита для надежности
                        error_log("Collected enough data, stopping");
                        break;
                    }
                } else {
                    // Если блок пустой, прекращаем попытки
                    error_log("Received empty block, stopping");
                    break;
                }
            } catch (Exception $block_e) {
                error_log("Error fetching block: " . $block_e->getMessage());
                $timed_out = true;
                $retry_count++;
                
                // Если это последняя попытка и у нас уже есть какие-то данные, используем их
                if ($retry_count >= $max_retries && !empty($all_csv_data)) {
                    error_log("Max retries reached, using partial data: " . strlen($all_csv_data) . " bytes");
                    break;
                } else if ($retry_count >= $max_retries) {
                    // Если нет данных после всех попыток, выбрасываем исключение
                    throw new Exception("Failed to fetch data after {$max_retries} retries");
                }
                
                // Увеличиваем таймаут для повторных попыток
                sleep(1);
            }
        }
        
        // Проверяем, получили ли мы какие-то данные
        if (empty($all_csv_data)) {
            throw new Exception("No data received from API");
        }
        
        // Преобразуем CSV в JSON для удобства использования в JavaScript
        $cars = processCSV($all_csv_data, $limit, $filter_brand, $filter_model);
        
        $result = [
            'status' => 'success',
            'mock_data' => false,
            'partial_data' => $timed_out,
            'count' => count($cars),
            'data' => $cars
        ];
        
        // Сохраняем в кеш
        file_put_contents($cache_file, json_encode($result));
        
        header('Content-Type: application/json');
        header('X-Cache: MISS');
        echo json_encode($result);
        exit;
        
    } catch (Exception $e) {
        error_log("Exception when fetching data: " . $e->getMessage());
        
        // При ошибке используем тестовые данные
        $mock_data = getMockData();
        
        // Фильтруем по бренду и модели
        if (!empty($filter_brand) || !empty($filter_model)) {
            $filtered_data = [];
            foreach ($mock_data as $car) {
                $match_brand = empty($filter_brand) || stripos($car['brand'], $filter_brand) !== false;
                $match_model = empty($filter_model) || stripos($car['model'], $filter_model) !== false;
                
                if ($match_brand && $match_model) {
                    $filtered_data[] = $car;
                }
            }
            $mock_data = $filtered_data;
        }
        
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'error' => $e->getMessage(),
            'mock_data' => true,
            'data' => $mock_data
        ]);
        exit;
    }
}

/**
 * Отправляет изображение-заглушку клиенту
 */
function sendPlaceholderImage() {
    if (file_exists('Img/placeholder-car.jpg')) {
        header('Content-Type: image/jpeg');
        readfile('Img/placeholder-car.jpg');
    } else {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Image not found and no placeholder available']);
    }
}

/**
 * Тестовые данные для различных автомобилей
 */
function getMockData() {
    // Try to get data from mock-data.php first if it exists
    if (file_exists('mock-data.php')) {
        require_once 'mock-data.php';
        if (function_exists('getMockCars')) {
            return getMockCars();
        }
    }
    
    return [
        [
            'brand' => 'BMW',
            'model' => 'M5 Competition',
            'year' => '2023',
            'power' => '625 л.с.',
            'engine' => '4.4л V8 twin-turbo',
            'mileage' => '1500',
            'price' => '$149,900',
            'image_url' => 'Img/bmw-m5.jpg'
        ],
        [
            'brand' => 'Audi',
            'model' => 'RS7 Sportback',
            'year' => '2023',
            'power' => '600 л.с.',
            'engine' => '4.0л V8 twin-turbo',
            'mileage' => '800',
            'price' => '$125,000',
            'image_url' => 'Img/audi-rs7.jpg'
        ],
        [
            'brand' => 'Ferrari',
            'model' => 'Roma',
            'year' => '2023',
            'power' => '612 л.с.',
            'engine' => '3.9л V8 twin-turbocharged',
            'mileage' => '113',
            'price' => '$436,195',
            'image_url' => 'Img/ferrari.jpg'
        ],
        [
            'brand' => 'Lamborghini',
            'model' => 'Huracan',
            'year' => '2022',
            'power' => '631 л.с.',
            'engine' => '5.2л V10 атмосферный',
            'mileage' => '1860',
            'price' => '$389,500',
            'image_url' => 'Img/lamborghini.jpg'
        ],
        [
            'brand' => 'Bentley',
            'model' => 'Flying Spur 3',
            'year' => '2023',
            'power' => '542 л.с.',
            'engine' => '4.0л V8 twin-turbo',
            'mileage' => '2200',
            'price' => '$400,000',
            'image_url' => 'Img/bentley-flying-spur.jpg'
        ],
        [
            'brand' => 'Bentley',
            'model' => 'Bentayga S',
            'year' => '2023',
            'power' => '542 л.с.',
            'engine' => '4.0л V8 twin-turbo',
            'mileage' => '620',
            'price' => '$335,483',
            'image_url' => 'Img/bentley-bentayga.jpg'
        ],
        [
            'brand' => 'Bentley',
            'model' => 'Continental GT',
            'year' => '2020',
            'power' => '635 л.с.',
            'engine' => '6.0л W12 twin-turbo',
            'mileage' => '18500',
            'price' => '$333,500',
            'image_url' => 'Img/bentley-continental.jpg'
        ],
        [
            'brand' => 'Aston Martin',
            'model' => 'DB11',
            'year' => '2023',
            'power' => '503 л.с.',
            'engine' => '4.0л V8 twin-turbo',
            'mileage' => '900',
            'price' => '$284,800',
            'image_url' => 'Img/aston-martin.jpg'
        ],
        [
            'brand' => 'BMW',
            'model' => 'M4 Competition',
            'year' => '2024',
            'power' => '510 л.с.',
            'engine' => '3.0л L6 twin-turbo (S58)',
            'mileage' => '0',
            'price' => '$150,100',
            'image_url' => 'Img/bmw-m4.jpg'
        ],
        // Дополнительные машины, которые могут быть в API Encar
        [
            'brand' => 'Porsche',
            'model' => '911 Turbo S',
            'year' => '2023',
            'power' => '650 л.с.',
            'engine' => '3.8л Flat-6 twin-turbo',
            'mileage' => '320',
            'price' => '$229,900',
            'image_url' => 'Img/placeholder-car.jpg'
        ],
        [
            'brand' => 'Mercedes-Benz',
            'model' => 'S-Class S580e',
            'year' => '2023',
            'power' => '503 л.с.',
            'engine' => '4.0л V8 твин-турбо',
            'mileage' => '5250',
            'price' => '$125,000',
            'image_url' => 'Img/placeholder-car.jpg'
        ],
        [
            'brand' => 'Rolls-Royce',
            'model' => 'Ghost',
            'year' => '2023',
            'power' => '563 л.с.',
            'engine' => '6.75л V12 twin-turbo',
            'mileage' => '1200',
            'price' => '$400,000',
            'image_url' => 'Img/placeholder-car.jpg'
        ],
        [
            'brand' => 'Tesla',
            'model' => 'Model S Plaid',
            'year' => '2023',
            'power' => '1020 л.с.',
            'engine' => 'Электро',
            'mileage' => '1500',
            'price' => '$130,000',
            'image_url' => 'Img/placeholder-car.jpg'
        ],
    ];
}

/**
 * Получение блока данных с заголовком диапазона
 */
function fetchDataWithRange($url, $range_header, $username = null, $password = null, $retry = 0) {
    global $api_timeout, $max_retries;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $api_timeout);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    curl_setopt($ch, CURLOPT_ENCODING, '');
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $headers = [
        'Accept: */*',
        'Connection: keep-alive',
        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ];
    
    if ($username !== null && $password !== null) {
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_USERPWD, "{$username}:{$password}");
    }
    
    $headers[] = $range_header;
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    $response = curl_exec($ch);
    $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($retry < $max_retries) {
            sleep(1);
            return fetchDataWithRange($url, $range_header, $username, $password, $retry + 1);
        }
        
        throw new Exception("cURL Error: {$error}");
    }
    
    curl_close($ch);
    
    if ($status_code >= 200 && $status_code < 300 && !empty($response)) {
        return $response;
    } else if ($status_code == 206 && !empty($response)) {
        return $response;
    } else {
        throw new Exception('API request failed with status code: ' . $status_code);
    }
}

/**
 * Обработка CSV данных с фильтрацией по бренду и модели
 */
function processCSV($csv_data, $limit = 10, $filter_brand = '', $filter_model = '') {
    $pos = strpos($csv_data, "\n");
    if ($pos === false) {
        return getMockData();
    }
    
    $header_line = substr($csv_data, 0, $pos);
    $headers = explode('|', $header_line);
    
    foreach ($headers as $key => $header) {
        $headers[$key] = trim($header);
    }
    
    $brand_idx = array_search('brand', $headers);
    $model_idx = array_search('model', $headers);
    
    if ($brand_idx === false) $brand_idx = array_search('make', $headers);
    if ($model_idx === false) $model_idx = array_search('car_name', $headers);
    
    $data_lines = substr($csv_data, $pos + 1);
    $lines = explode("\n", $data_lines);
    
    $result = [];
    $count = 0;
    
    foreach ($lines as $line) {
        if ($count >= $limit) break;
        
        if (empty(trim($line))) continue;
        
        $values = explode('|', $line);
        if (count($values) < 5) continue;
        
        $match_brand = true;
        $match_model = true;
        
        if (!empty($filter_brand) && $brand_idx !== false && isset($values[$brand_idx])) {
            $brand_value = trim($values[$brand_idx]);
            $match_brand = stripos($brand_value, $filter_brand) !== false;
        }
        
        if (!empty($filter_model) && $model_idx !== false && isset($values[$model_idx])) {
            $model_value = trim($values[$model_idx]);
            $match_model = stripos($model_value, $filter_model) !== false;
        }
        
        if ($match_brand && $match_model) {
            $car = [];
            foreach ($headers as $j => $header) {
                if (isset($values[$j])) {
                    $car[$header] = trim($values[$j]);
                }
            }
            
            $car = formatCarData($car);
            $result[] = $car;
            $count++;
        }
    }
    
    if (empty($result)) {
        return getMockData();
    }
    
    return $result;
}

/**
 * Извлекает информацию об автомобиле из массива значений 
 * на основе типичных шаблонов данных об автомобилях
 */
function extractCarInfoFromValues($values) {
    $car = [];
    
    // Ищем бренд по списку известных брендов
    $known_brands = [
        'BMW', 'Audi', 'Mercedes', 'Mercedes-Benz', 'Porsche', 'Ferrari',
        'Lamborghini', 'Bentley', 'Aston Martin', 'Rolls-Royce', 'Tesla', 'Maserati',
        'Jaguar', 'Land Rover', 'Lexus', 'Toyota', 'Honda', 'Mazda', 'Subaru',
        'Volkswagen', 'Volvo', 'Hyundai', 'Kia', 'Ford', 'Chevrolet', 'Cadillac', 'Jeep'
    ];
    
    // Просматриваем все значения в поисках бренда и модели
    foreach ($values as $value) {
        $value = trim($value);
        
        // Пропускаем пустые и числовые значения
        if (empty($value) || is_numeric($value)) continue;
        
        // Ищем бренды
        if (empty($car['brand'])) {
            foreach ($known_brands as $brand) {
                if (stripos($value, $brand) !== false) {
                    $car['brand'] = $brand;
                    
                    // Если это часть более длинной строки, это может быть "Бренд Модель"
                    if (strlen($value) > strlen($brand) + 3) {
                        $car['model'] = trim(str_ireplace($brand, '', $value));
                    }
                    
                    break;
                }
            }
        }
        
        // Ищем типичные поля
        if (empty($car['year']) && preg_match('/^(19|20)\d{2}$/', $value)) {
            $car['year'] = $value;
        }
        else if (empty($car['price']) && preg_match('/\$|USD|EUR|₩/', $value)) {
            $car['price'] = $value;
        }
        else if (empty($car['mileage']) && preg_match('/km|miles|mi|км/', $value)) {
            $car['mileage'] = $value;
        }
        else if (empty($car['engine']) && preg_match('/cc|L|л\./', $value)) {
            $car['engine'] = $value;
        }
        else if (empty($car['power']) && preg_match('/hp|л\.с\./', $value)) {
            $car['power'] = $value;
        }
    }
    
    return $car;
}

/**
 * Форматирование данных об автомобиле
 */
function formatCarData($car) {
    global $api_host;
    $formatted = [
        'inner_id' => $car['inner_id'] ?? $car['id'] ?? '',
        'url' => $car['url'] ?? '',
        'brand' => $car['brand'] ?? $car['mark'] ?? '',
        'mark' => $car['mark'] ?? $car['brand'] ?? '',
        'model' => $car['model'] ?? '',
        'configuration' => $car['configuration'] ?? '',
        'complectation' => $car['complectation'] ?? '',
        'year' => $car['year'] ?? '',
        'color' => $car['color'] ?? '',
        'price' => $car['price'] ?? '',
        'km_age' => $car['km_age'] ?? $car['mileage'] ?? '',
        'mileage' => $car['mileage'] ?? $car['km_age'] ?? '',
        'engine_type' => $car['engine_type'] ?? '',
        'transmission_type' => $car['transmission_type'] ?? '',
        'body_type' => $car['body_type'] ?? '',
        'address' => $car['address'] ?? '',
        'seller_type' => $car['seller_type'] ?? '',
        'is_dealer' => $car['is_dealer'] ?? '',
        'section' => $car['section'] ?? '',
        'seller' => $car['seller'] ?? '',
        'salon_id' => $car['salon_id'] ?? '',
        'description' => $car['description'] ?? '',
        'displacement' => $car['displacement'] ?? '',
        'offer_created' => $car['offer_created'] ?? '',
        'diagnosis' => isset($car['diagnosis']) ? json_decode($car['diagnosis'], true) : null,
        'accidents' => isset($car['accidents']) ? json_decode($car['accidents'], true) : null,
        // Для изображений поддерживаем images (json), image_url, photo, main_photo
        'images' => isset($car['images']) ? (is_array($car['images']) ? $car['images'] : (json_decode($car['images'], true) ?: [$car['images']])) : (isset($car['image_url']) ? [$car['image_url']] : (isset($car['photo']) ? [$car['photo']] : [])),
        'image_url' => $car['image_url'] ?? ($car['photo'] ?? ''),
        'power' => $car['power'] ?? $car['engine_power'] ?? '',
        'engine' => $car['engine'] ?? $car['engine_volume'] ?? '',
    ];
    
    // Обрабатываем URL изображения - проверяем все возможные поля с изображениями
    $image_field_names = ['image_url', 'photo_url', 'main_photo', 'gallery', 'images', 'photo', 'preview_image'];
    $image_url = '';
    
    // Сначала ищем прямые URL изображений в любых полях
    foreach ($car as $field => $value) {
        if (is_string($value) && 
            (stripos($value, 'http') === 0 || stripos($value, '//') === 0) && 
            (
                stripos($value, '.jpg') !== false || 
                stripos($value, '.jpeg') !== false || 
                stripos($value, '.png') !== false || 
                stripos($value, '.webp') !== false || 
                stripos($value, '.gif') !== false
            )
        ) {
            $image_url = $value;
            break;
        }
    }
    
    // Если прямой URL не найден, проверяем известные поля с изображениями
    if (empty($image_url)) {
        foreach ($image_field_names as $field) {
            if (isset($car[$field]) && !empty($car[$field])) {
                // Проверяем, это URL или массив URLs
                if (is_string($car[$field])) {
                    // Если это строка URL
                    $url = trim($car[$field]);
                    if (!empty($url)) {
                        $image_url = $url;
                        break;
                    }
                } elseif (is_array($car[$field]) && !empty($car[$field])) {
                    // Если это массив URLs, берем первый
                    $url = trim($car[$field][0]);
                    if (!empty($url)) {
                        $image_url = $url;
                        break;
                    }
                }
            }
        }
    }
    
    // Если нашли URL изображения, форматируем его
    if (!empty($image_url)) {
        error_log("Found image URL: " . $image_url);
        
        // Если это не полный URL, добавляем хост API
        if (strpos($image_url, 'http') !== 0 && strpos($image_url, '//') !== 0) {
            $image_url = $api_host . (substr($image_url, 0, 1) === '/' ? '' : '/') . $image_url;
            error_log("Constructed full image URL: " . $image_url);
        }
        
        // Проверка на CDN с известными проблемами (motor1.com)
        if (strpos($image_url, 'cdn.motor1.com') !== false) {
            $car_model = $formatted['model'];
            $car_model_to_image = [
                'M5' => 'Img/bmw-m5.jpg',
                'M4' => 'Img/bmw-m4.jpg',
                'RS7' => 'Img/audi-rs7.jpg',
                'Roma' => 'Img/ferrari.jpg',
                'Huracan' => 'Img/lamborghini.jpg',
                'Flying Spur' => 'Img/bentley-flying-spur.jpg',
                'Bentayga' => 'Img/bentley-bentayga.jpg',
                'Continental' => 'Img/bentley-continental.jpg',
                'DB11' => 'Img/aston-martin.jpg'
            ];
            
            $found_local = false;
            foreach ($car_model_to_image as $model_key => $local_path) {
                if (stripos($car_model, $model_key) !== false) {
                    $image_url = $local_path;
                    $found_local = true;
                    break;
                }
            }
            
            if (!$found_local) {
                $image_url = 'Img/placeholder-car.jpg';
            }
        }
        
        $formatted['image_url'] = $image_url;
    } else {
        // Если изображение не найдено, ищем локальное изображение по модели
        $car_model = $formatted['model'];
        $car_model_to_image = [
            'M5' => 'Img/bmw-m5.jpg',
            'M4' => 'Img/bmw-m4.jpg',
            'RS7' => 'Img/audi-rs7.jpg',
            'Roma' => 'Img/ferrari.jpg',
            'Huracan' => 'Img/lamborghini.jpg',
            'Flying Spur' => 'Img/bentley-flying-spur.jpg',
            'Bentayga' => 'Img/bentley-bentayga.jpg',
            'Continental' => 'Img/bentley-continental.jpg',
            'DB11' => 'Img/aston-martin.jpg'
        ];
        
        $found_local = false;
        foreach ($car_model_to_image as $model_key => $local_path) {
            if (stripos($car_model, $model_key) !== false) {
                $formatted['image_url'] = $local_path;
                $found_local = true;
                break;
            }
        }
        
        if (!$found_local) {
            $formatted['image_url'] = 'Img/placeholder-car.jpg';
        }
    }
    
    // Сохраняем оригинальные данные, но только самые важные поля
    $keysToKeep = ['brand', 'model', 'year', 'engine_power', 'engine_volume', 'mileage', 'price'];
    $original_data = [];
    
    foreach ($keysToKeep as $key) {
        if (isset($car[$key])) {
            $original_data[$key] = $car[$key];
        }
    }
    
    $formatted['original_data'] = $original_data;
    
    return $formatted;
}

// Увеличиваем таймауты
$curl_timeout = 120; // 2 минуты на запрос
$max_retries = 5;    // Увеличиваем количество попыток

// Оптимизируем обработку изображений
function processImageUrl($url) {
    if (empty($url)) {
        return 'Img/placeholder-car.jpg';
    }

    // Проверяем, является ли URL локальным
    if (strpos($url, 'http') !== 0) {
        return $url;
    }

    // Проверяем, является ли URL из нашего API
    if (strpos($url, 'autobase-wade.auto-parser.ru') !== false) {
        // Добавляем параметры для оптимизации
        $url .= (strpos($url, '?') === false ? '?' : '&') . 'optimize=1&quality=80';
        return $url;
    }

    // Для других URL используем прокси
    return 'encar-proxy-improved.php?image=' . urlencode($url);
}

// Добавляем функцию для получения списка файлов в директории
function listDirectory($url, $username, $password) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($ch, CURLOPT_USERPWD, "{$username}:{$password}");
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    $curl_errno = curl_errno($ch);
    
    debug_log("Directory listing result", [
        'url' => $url,
        'http_code' => $http_code,
        'curl_error' => $curl_error,
        'curl_errno' => $curl_errno,
        'response' => $response
    ]);
    
    curl_close($ch);
    
    if ($http_code === 200 && !empty($response)) {
        // Парсим HTML для получения списка файлов
        $files = [];
        if (preg_match_all('/<a href="([^"]+)">([^<]+)<\/a>/', $response, $matches)) {
            foreach ($matches[1] as $index => $href) {
                if ($href !== '../') {
                    $files[] = [
                        'name' => $matches[2][$index],
                        'url' => $href
                    ];
                }
            }
        }
        return $files;
    }
    
    return [];
}
?> 