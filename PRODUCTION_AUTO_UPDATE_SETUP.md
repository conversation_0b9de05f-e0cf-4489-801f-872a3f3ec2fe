# 🔄 НАСТРОЙКА АВТОМАТИЧЕСКОГО ОБНОВЛЕНИЯ ENCAR ДЛЯ ПРОДАКШЕНА

## 📋 ОБЗОР СИСТЕМЫ

Система автоматического обновления состоит из:

1. **PHP скрипт** (`public/api/auto-setup.php`) - загружает данные с вашего API
2. **Bash скрипт** (`scripts/auto-update-encar.sh`) - управляет процессом обновления
3. **<PERSON>ron задача** - запускает обновление по расписанию
4. **Система логирования** - отслеживает все операции
5. **Резервное копирование** - сохраняет старые данные

## 🚀 БЫСТРАЯ УСТАНОВКА

### Шаг 1: Загрузка файлов на сервер
```bash
# Скопируйте файлы на сервер в директорию /var/www/shms-auto/
scp scripts/auto-update-encar.sh user@server:/var/www/shms-auto/scripts/
scp scripts/setup-cron.sh user@server:/var/www/shms-auto/scripts/
```

### Шаг 2: Установка прав доступа
```bash
# На сервере выполните:
cd /var/www/shms-auto
chmod +x scripts/auto-update-encar.sh
chmod +x scripts/setup-cron.sh
```

### Шаг 3: Автоматическая настройка
```bash
# Запустите скрипт установки:
./scripts/setup-cron.sh install
```

### Шаг 4: Тестирование
```bash
# Протестируйте систему:
./scripts/setup-cron.sh test
```

## 📁 СТРУКТУРА ФАЙЛОВ

```
/var/www/shms-auto/
├── scripts/
│   ├── auto-update-encar.sh      # Основной скрипт обновления
│   └── setup-cron.sh             # Скрипт настройки cron
├── public/api/
│   ├── auto-setup.php             # PHP скрипт загрузки данных
│   └── encar_data/                # Директория с CSV файлами
│       ├── encar_active_YYYY-MM-DD.csv
│       └── encar_active_stats_YYYY-MM-DD.json
├── logs/
│   ├── encar-update.log           # Основной лог
│   └── encar-error.log            # Лог ошибок
└── backups/csv/                   # Резервные копии
    └── encar_backup_YYYYMMDD_HHMMSS.tar.gz
```

## ⚙️ КОНФИГУРАЦИЯ

### Основные настройки в auto-update-encar.sh:

```bash
# Пути
SITE_ROOT="/var/www/shms-auto"
PM2_APP_NAME="shms-auto"

# Уведомления
ADMIN_EMAIL="<EMAIL>"

# Резервное копирование
KEEP_BACKUPS=7  # Дней для хранения бэкапов
```

### Расписание cron:
```bash
# Каждый день в 10:00
0 10 * * * /var/www/shms-auto/scripts/auto-update-encar.sh
```

## 🔧 РУЧНАЯ НАСТРОЙКА

### Если автоматическая установка не работает:

#### 1. Создание директорий:
```bash
mkdir -p /var/www/shms-auto/logs
mkdir -p /var/www/shms-auto/backups/csv
mkdir -p /var/www/shms-auto/scripts
```

#### 2. Установка cron задачи:
```bash
# Откройте crontab
crontab -e

# Добавьте строку:
0 10 * * * /var/www/shms-auto/scripts/auto-update-encar.sh >/dev/null 2>&1
```

#### 3. Настройка прав:
```bash
chmod +x /var/www/shms-auto/scripts/auto-update-encar.sh
chmod 755 /var/www/shms-auto/logs
chmod 755 /var/www/shms-auto/backups
```

## 📊 МОНИТОРИНГ И ЛОГИ

### Проверка статуса:
```bash
# Статус автообновления
./scripts/setup-cron.sh status

# Последние логи
tail -f /var/www/shms-auto/logs/encar-update.log

# Ошибки
tail -f /var/www/shms-auto/logs/encar-error.log
```

### Ручной запуск обновления:
```bash
# Запуск обновления вручную
./scripts/auto-update-encar.sh

# Только загрузка данных (без перезапуска)
php public/api/auto-setup.php update
```

## 🔍 ДИАГНОСТИКА ПРОБЛЕМ

### Проблема: Cron не запускается
**Решение:**
```bash
# Проверьте cron задачи
crontab -l

# Проверьте статус cron службы
systemctl status cron

# Проверьте логи cron
tail -f /var/log/cron
```

### Проблема: PHP скрипт не работает
**Решение:**
```bash
# Проверьте PHP
php -v

# Тестируйте скрипт напрямую
php /var/www/shms-auto/public/api/auto-setup.php update

# Проверьте права доступа
ls -la /var/www/shms-auto/public/api/encar_data/
```

### Проблема: PM2 не перезапускается
**Решение:**
```bash
# Проверьте PM2
pm2 status

# Проверьте имя приложения
pm2 list

# Ручной перезапуск
pm2 reload shms-auto
```

### Проблема: Нет доступа к API
**Решение:**
```bash
# Тестируйте API напрямую
curl -u "admin:n2Q8ewyLft9qgPmim5ng" \
  "https://autobase-wade.auto-parser.ru/encar/$(date '+%Y-%m-%d')/active_offer.csv"

# Проверьте интернет соединение
ping autobase-wade.auto-parser.ru
```

## 📧 УВЕДОМЛЕНИЯ

### Настройка email уведомлений:
```bash
# Установите mail утилиту (если нет)
apt-get install mailutils

# Настройте SMTP в /etc/postfix/main.cf
# Или используйте внешний SMTP сервер
```

### Настройка webhook уведомлений:
```bash
# В auto-update-encar.sh установите:
WEBHOOK_URL="https://your-webhook-url.com/notify"
```

## 🛡️ БЕЗОПАСНОСТЬ

### Защита логов:
```bash
# Ограничьте доступ к логам
chmod 640 /var/www/shms-auto/logs/*.log
chown www-data:www-data /var/www/shms-auto/logs/*.log
```

### Защита скриптов:
```bash
# Ограничьте доступ к скриптам
chmod 750 /var/www/shms-auto/scripts/*.sh
chown root:www-data /var/www/shms-auto/scripts/*.sh
```

## 📈 ОПТИМИЗАЦИЯ

### Для больших файлов:
- Увеличьте `memory_limit` в PHP
- Настройте `max_execution_time`
- Используйте потоковую обработку

### Для частых обновлений:
- Измените расписание cron
- Добавьте проверку изменений
- Настройте инкрементальные обновления

## 🔄 ОБСЛУЖИВАНИЕ

### Еженедельные задачи:
- Проверка логов на ошибки
- Очистка старых резервных копий
- Мониторинг размера файлов

### Ежемесячные задачи:
- Обновление скриптов
- Проверка производительности
- Анализ статистики обновлений

## 📞 ПОДДЕРЖКА

### При проблемах:
1. Проверьте логи: `/var/www/shms-auto/logs/`
2. Запустите диагностику: `./scripts/setup-cron.sh status`
3. Протестируйте вручную: `./scripts/auto-update-encar.sh`

### Контакты:
- **Email**: <EMAIL>
- **Логи**: `/var/www/shms-auto/logs/encar-update.log`

---

## ✅ ЧЕКЛИСТ ГОТОВНОСТИ

- [ ] Файлы скриптов загружены на сервер
- [ ] Установлены права доступа
- [ ] Запущена автоматическая настройка
- [ ] Протестирована система обновления
- [ ] Настроены уведомления
- [ ] Проверены логи
- [ ] Cron задача активна
- [ ] PM2 настроен корректно

**После выполнения всех пунктов система готова к работе!**
