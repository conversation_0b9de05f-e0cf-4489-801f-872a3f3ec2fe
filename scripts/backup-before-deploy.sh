#!/bin/bash

# Скрипт создания бэкапа перед развертыванием SHMS Auto
# Создает полный бэкап приложения, базы данных и загруженных файлов

set -e

echo "💾 Создание бэкапа перед развертыванием"
echo "======================================"

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Функции для вывода
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

# Настройки
APP_DIR="$(cd "$(dirname "$0")/.." && pwd)"
BACKUP_BASE_DIR="$HOME/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="shms-auto-backup-$TIMESTAMP"
BACKUP_DIR="$BACKUP_BASE_DIR/$BACKUP_NAME"

# Максимальное количество бэкапов для хранения
MAX_BACKUPS=10

# Создание директории бэкапов
create_backup_dir() {
    log "Создание директории бэкапов..."
    
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$BACKUP_DIR/app"
    mkdir -p "$BACKUP_DIR/data"
    mkdir -p "$BACKUP_DIR/logs"
    
    log "✅ Директория создана: $BACKUP_DIR"
}

# Бэкап файлов приложения
backup_application() {
    log "Создание бэкапа файлов приложения..."
    
    cd "$APP_DIR"
    
    # Копирование основных файлов приложения
    cp -r server.js package.json ecosystem.config.js "$BACKUP_DIR/app/" 2>/dev/null || true
    cp -r public admin config server "$BACKUP_DIR/app/" 2>/dev/null || true
    cp -r scripts "$BACKUP_DIR/app/" 2>/dev/null || true
    
    # Копирование конфигурационных файлов
    cp .env "$BACKUP_DIR/app/.env" 2>/dev/null || true
    cp .env.example "$BACKUP_DIR/app/.env.example" 2>/dev/null || true
    
    # Создание манифеста бэкапа
    cat > "$BACKUP_DIR/backup-manifest.txt" << EOF
Backup created: $(date)
Application: SHMS Auto
Version: $(git rev-parse HEAD 2>/dev/null || echo "unknown")
Branch: $(git branch --show-current 2>/dev/null || echo "unknown")
Node version: $(node -v 2>/dev/null || echo "unknown")
PM2 status: $(pm2 list 2>/dev/null | grep shms-auto || echo "not running")
EOF
    
    log "✅ Файлы приложения скопированы"
}

# Бэкап базы данных
backup_database() {
    log "Создание бэкапа базы данных..."
    
    local db_path="$APP_DIR/data/stock.db"
    
    if [ -f "$db_path" ]; then
        cp "$db_path" "$BACKUP_DIR/data/stock.db"
        
        # Получение информации о базе данных
        local db_size=$(stat -f%z "$db_path" 2>/dev/null || stat -c%s "$db_path" 2>/dev/null || echo "unknown")
        log "✅ База данных скопирована (размер: $db_size байт)"
        
        # Добавление информации в манифест
        echo "Database size: $db_size bytes" >> "$BACKUP_DIR/backup-manifest.txt"
    else
        warn "База данных не найдена: $db_path"
        echo "Database: not found" >> "$BACKUP_DIR/backup-manifest.txt"
    fi
}

# Бэкап загруженных файлов
backup_uploads() {
    log "Создание бэкапа загруженных файлов..."
    
    local uploads_path="$APP_DIR/data/uploads"
    
    if [ -d "$uploads_path" ]; then
        cp -r "$uploads_path" "$BACKUP_DIR/data/"
        
        # Подсчет файлов и размера
        local file_count=$(find "$uploads_path" -type f | wc -l)
        local total_size=$(du -sh "$uploads_path" 2>/dev/null | cut -f1 || echo "unknown")
        
        log "✅ Загруженные файлы скопированы ($file_count файлов, $total_size)"
        
        # Добавление информации в манифест
        echo "Uploads: $file_count files, $total_size" >> "$BACKUP_DIR/backup-manifest.txt"
    else
        warn "Директория uploads не найдена: $uploads_path"
        echo "Uploads: not found" >> "$BACKUP_DIR/backup-manifest.txt"
    fi
}

# Бэкап логов
backup_logs() {
    log "Создание бэкапа логов..."
    
    local logs_path="$APP_DIR/logs"
    
    if [ -d "$logs_path" ]; then
        # Копируем только последние логи (не старше 7 дней)
        find "$logs_path" -name "*.log" -mtime -7 -exec cp {} "$BACKUP_DIR/logs/" \; 2>/dev/null || true
        
        local log_count=$(find "$BACKUP_DIR/logs" -name "*.log" | wc -l)
        log "✅ Логи скопированы ($log_count файлов)"
        
        echo "Logs: $log_count files" >> "$BACKUP_DIR/backup-manifest.txt"
    else
        warn "Директория logs не найдена: $logs_path"
        echo "Logs: not found" >> "$BACKUP_DIR/backup-manifest.txt"
    fi
}

# Создание архива бэкапа
create_archive() {
    log "Создание архива бэкапа..."
    
    cd "$BACKUP_BASE_DIR"
    
    # Создание tar.gz архива
    tar -czf "${BACKUP_NAME}.tar.gz" "$BACKUP_NAME"
    
    # Проверка создания архива
    if [ -f "${BACKUP_NAME}.tar.gz" ]; then
        local archive_size=$(du -sh "${BACKUP_NAME}.tar.gz" | cut -f1)
        log "✅ Архив создан: ${BACKUP_NAME}.tar.gz ($archive_size)"
        
        # Удаление временной директории
        rm -rf "$BACKUP_NAME"
        
        echo "Archive: ${BACKUP_NAME}.tar.gz ($archive_size)" >> "${BACKUP_NAME}.tar.gz.manifest"
    else
        error "Не удалось создать архив"
    fi
}

# Очистка старых бэкапов
cleanup_old_backups() {
    log "Очистка старых бэкапов..."
    
    cd "$BACKUP_BASE_DIR"
    
    # Подсчет текущих бэкапов
    local backup_count=$(find . -name "shms-auto-backup-*.tar.gz" | wc -l)
    
    if [ "$backup_count" -gt "$MAX_BACKUPS" ]; then
        local to_delete=$((backup_count - MAX_BACKUPS))
        log "Найдено $backup_count бэкапов, удаляем $to_delete старых..."
        
        # Удаление самых старых бэкапов
        find . -name "shms-auto-backup-*.tar.gz" -type f -printf '%T@ %p\n' | \
        sort -n | head -n "$to_delete" | cut -d' ' -f2- | \
        while read -r file; do
            log "Удаление старого бэкапа: $file"
            rm -f "$file"
            rm -f "${file}.manifest" 2>/dev/null || true
        done
        
        log "✅ Старые бэкапы очищены"
    else
        log "✅ Очистка не требуется ($backup_count/$MAX_BACKUPS бэкапов)"
    fi
}

# Проверка доступного места
check_disk_space() {
    log "Проверка доступного места на диске..."
    
    local available_space=$(df -h "$BACKUP_BASE_DIR" | awk 'NR==2 {print $4}')
    local used_percent=$(df -h "$BACKUP_BASE_DIR" | awk 'NR==2 {print $5}' | sed 's/%//')
    
    log "Доступно места: $available_space (использовано: ${used_percent}%)"
    
    if [ "$used_percent" -gt 90 ]; then
        warn "Мало места на диске! Рассмотрите очистку старых бэкапов."
    fi
}

# Основная функция
main() {
    log "Начало создания бэкапа..."
    
    # Переход в директорию приложения
    cd "$APP_DIR"
    
    # Проверки
    check_disk_space
    
    # Создание бэкапа
    create_backup_dir
    backup_application
    backup_database
    backup_uploads
    backup_logs
    create_archive
    cleanup_old_backups
    
    log "✅ Бэкап создан успешно!"
    
    echo ""
    echo "🎉 Бэкап SHMS Auto завершен!"
    echo "============================"
    echo "📁 Архив: $BACKUP_BASE_DIR/${BACKUP_NAME}.tar.gz"
    echo "📅 Время создания: $(date)"
    echo "📊 Всего бэкапов: $(find "$BACKUP_BASE_DIR" -name "shms-auto-backup-*.tar.gz" | wc -l)"
    echo ""
    echo "💡 Для восстановления используйте:"
    echo "   tar -xzf $BACKUP_BASE_DIR/${BACKUP_NAME}.tar.gz"
    echo "   ./scripts/rollback.sh"
}

# Обработка сигналов
trap 'error "Создание бэкапа прервано"' INT TERM

# Запуск
main "$@"
