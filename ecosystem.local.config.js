module.exports = {
  apps: [{
    name: 'shms-auto-local',
    script: 'server.local.js',
    
    // Настройки окружения для локальной разработки
    env: {
      NODE_ENV: 'development',
      PORT: 3000,
      HOST: 'localhost'
    },
    
    // Настройки PM2 для разработки
    instances: 1,
    exec_mode: 'fork',
    
    // Автоперезапуск при изменениях (для разработки)
    watch: true,
    watch_delay: 1000,
    ignore_watch: [
      'node_modules', 
      'logs', 
      'data/uploads',
      'data/cache',
      'data/sessions',
      '.git',
      '*.log'
    ],
    
    // Логирование для разработки
    log_file: './logs/local-combined.log',
    out_file: './logs/local-out.log',
    error_file: './logs/local-error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    
    // Автоперезапуск при ошибках
    autorestart: true,
    max_restarts: 5,
    min_uptime: '5s',
    
    // Ограничения ресурсов (более мягкие для разработки)
    max_memory_restart: '200M',
    
    // Переменные окружения из файла
    env_file: '.env',
    
    // Настройки для разработки
    merge_logs: true,
    time: true,
    
    // Дополнительные настройки для разработки
    kill_timeout: 3000,
    listen_timeout: 3000,
    
    // Переменные для отладки
    node_args: '--inspect=0.0.0.0:9229'
  }]
};
