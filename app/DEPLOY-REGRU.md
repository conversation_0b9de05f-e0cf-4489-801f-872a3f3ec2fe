# 🚀 Развертывание SHMS Авто на Рег.ру

## 📋 Пошаговая инструкция

### 1. Заказ VPS на Рег.ру

1. **Войдите в личный кабинет Рег.ру**
   - Перейдите на https://www.reg.ru
   - Войдите в личный кабинет

2. **Закажите VPS сервер**
   - Перейдите в раздел "Серверы" → "VPS"
   - Выберите конфигурацию:
     - **ОС**: Ubuntu 20.04 LTS
     - **RAM**: минимум 2GB
     - **Диск**: минимум 20GB SSD
     - **CPU**: 1-2 ядра
   - Оформите заказ

3. **Получите данные доступа**
   - IP адрес сервера
   - Логин: root
   - Пароль (придет на email)

### 2. Подключение к серверу

```bash
# Подключение по SSH
ssh root@ваш-ip-адрес

# Введите пароль, который пришел на email
```

### 3. Подготовка сервера

```bash
# Обновление системы
apt update && apt upgrade -y

# Установка необходимых пакетов
apt install -y curl wget git nginx

# Установка Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs

# Проверка установки
node -v
npm -v

# Установка PM2
npm install -g pm2

# Создание пользователя для приложения (рекомендуется)
adduser shmsauto
usermod -aG sudo shmsauto
su - shmsauto
```

### 4. Загрузка проекта

```bash
# Переход в домашнюю директорию
cd /home/<USER>

# Клонирование проекта (если есть Git репозиторий)
git clone https://github.com/ваш-username/shms-auto.git
cd shms-auto

# ИЛИ загрузка архива проекта
# wget https://ваш-сайт.com/shms-auto.zip
# unzip shms-auto.zip
# cd shms-auto
```

**Если у вас нет Git репозитория, загрузите файлы через SFTP:**

1. Используйте FileZilla или WinSCP
2. Подключитесь к серверу по SFTP
3. Загрузите все файлы проекта в `/home/<USER>/shms-auto/`

### 5. Настройка проекта

```bash
# Установка зависимостей
npm install

# Копирование и настройка .env
cp .env.example .env
nano .env
```

**Настройте .env файл:**
```env
NODE_ENV=production
PORT=3001
HOST=0.0.0.0
DOMAIN=shms-auto.ru
SITE_URL=https://shms-auto.ru

# Почта Yandex (уже настроена)
MAIL_HOST=smtp.yandex.ru
MAIL_PORT=465
MAIL_SECURE=true
MAIL_USER=<EMAIL>
MAIL_PASS=SHMSAutogroup777!

CONTACT_EMAIL=<EMAIL>
MANAGER_EMAIL=<EMAIL>

# Сгенерируйте секретный ключ
SESSION_SECRET=shms-auto-2024-super-secret-key-kJ8mN2pQ5rT9wX3zA6bC1dF4gH7jK0lM

HTTPS_REDIRECT=true
SECURE_COOKIES=true
```

### 6. Запуск автоматического развертывания

```bash
# Сделать скрипт исполняемым
chmod +x deploy-regru.sh

# Запустить развертывание
./deploy-regru.sh
```

Скрипт автоматически:
- ✅ Проверит Node.js и PM2
- ✅ Установит зависимости
- ✅ Создаст необходимые директории
- ✅ Протестирует приложение
- ✅ Запустит через PM2
- ✅ Настроит автозапуск

### 7. Настройка Nginx

```bash
# Создание конфигурации Nginx
sudo nano /etc/nginx/sites-available/shms-auto
```

**Содержимое файла:**
```nginx
server {
    listen 80;
    server_name shms-auto.ru www.shms-auto.ru;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    location /uploads/ {
        alias /home/<USER>/shms-auto/data/uploads/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
}
```

**Активация конфигурации:**
```bash
# Создание символической ссылки
sudo ln -s /etc/nginx/sites-available/shms-auto /etc/nginx/sites-enabled/

# Удаление дефолтной конфигурации
sudo rm /etc/nginx/sites-enabled/default

# Проверка конфигурации
sudo nginx -t

# Перезапуск Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### 8. Настройка домена в Рег.ру

1. **В личном кабинете Рег.ру:**
   - Перейдите в "Домены" → "Управление DNS"
   - Найдите домен shms-auto.ru

2. **Настройте A-записи:**
   ```
   Тип: A
   Имя: @
   Значение: ваш-ip-адрес-сервера
   
   Тип: A
   Имя: www
   Значение: ваш-ip-адрес-сервера
   ```

3. **Сохраните изменения**
   - DNS изменения могут занять до 24 часов

### 9. Установка SSL сертификата

```bash
# Установка Certbot
sudo apt install certbot python3-certbot-nginx

# Получение SSL сертификата
sudo certbot --nginx -d shms-auto.ru -d www.shms-auto.ru

# Автоматическое обновление
sudo crontab -e
# Добавьте строку:
0 12 * * * /usr/bin/certbot renew --quiet
```

### 10. Проверка работы

```bash
# Проверка статуса PM2
pm2 list

# Проверка логов
pm2 logs shms-auto

# Проверка здоровья приложения
npm run health-check

# Тест почты
npm run test-mail
```

**Проверьте в браузере:**
- http://ваш-ip:3001 - должен работать
- http://shms-auto.ru - после настройки DNS
- https://shms-auto.ru - после установки SSL

### 11. Настройка мониторинга

```bash
# Настройка PM2 мониторинга
pm2 install pm2-server-monit

# Просмотр мониторинга
pm2 monit
```

### 12. Резервное копирование

```bash
# Создание скрипта бэкапа
nano backup.sh
```

**Содержимое backup.sh:**
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/backups"
PROJECT_DIR="/home/<USER>/shms-auto"

mkdir -p $BACKUP_DIR

# Бэкап базы данных
cp $PROJECT_DIR/data/stock.db $BACKUP_DIR/stock_$DATE.db

# Бэкап загруженных файлов
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz -C $PROJECT_DIR/data uploads

# Удаление старых бэкапов (старше 30 дней)
find $BACKUP_DIR -name "*.db" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

```bash
# Сделать исполняемым
chmod +x backup.sh

# Добавить в cron (ежедневно в 3:00)
crontab -e
# Добавить: 0 3 * * * /home/<USER>/backup.sh
```

## 🎉 Готово!

Ваш сайт SHMS Авто теперь работает на:
- **Локально**: http://ваш-ip:3001
- **Домен**: https://shms-auto.ru

## 📞 Поддержка

Если что-то не работает:

1. **Проверьте логи:**
   ```bash
   pm2 logs shms-auto
   sudo tail -f /var/log/nginx/error.log
   ```

2. **Перезапустите сервисы:**
   ```bash
   pm2 restart shms-auto
   sudo systemctl restart nginx
   ```

3. **Проверьте статус:**
   ```bash
   npm run health-check
   sudo systemctl status nginx
   ```

Все функции вашего сайта (каталог авто, админка, заявки, отзывы) будут работать полностью!
