<?php
/**
 * 🧪 Тест-скрипт для проверки функций фильтрации автомобилей
 * 
 * Этот скрипт тестирует:
 * 1. Фильтрацию забронированных автомобилей (999999/333333 вон)
 * 2. Исключение бюджетных брендов из премиум категорий
 * 3. Функцию determineCarCategory()
 * 4. Функцию convertKRWToUSD()
 */

// Подключаем основной файл API для доступа к функциям
require_once 'encar-proxy.php';

// Устанавливаем заголовки для JSON ответа
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обрабатываем OPTIONS запрос
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

/**
 * Функция для логирования результатов тестов
 */
function logTestResult($testName, $passed, $message = '') {
    $status = $passed ? '✅ ПРОЙДЕН' : '❌ ПРОВАЛЕН';
    $result = [
        'test' => $testName,
        'status' => $status,
        'passed' => $passed,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    return $result;
}

/**
 * Тест 1: Проверка фильтрации забронированных цен в convertKRWToUSD
 */
function testReservedPriceFiltering() {
    $results = [];
    
    // Тестовые данные
    $testPrices = [999999, 333333, 1000, 5000, 10000];
    $mockRates = ['KRW_to_RUB' => 0.075, 'USD_rate' => 95];
    
    foreach ($testPrices as $price) {
        $convertedPrice = convertKRWToUSD($price, $mockRates);
        
        if ($price === 999999 || $price === 333333) {
            // Забронированные цены должны возвращать 0
            $passed = ($convertedPrice === 0);
            $results[] = logTestResult(
                "Забронированная цена {$price}",
                $passed,
                $passed ? "Корректно исключена" : "Ошибка: цена не исключена, результат: {$convertedPrice}"
            );
        } else {
            // Обычные цены должны конвертироваться
            $passed = ($convertedPrice > 0);
            $results[] = logTestResult(
                "Обычная цена {$price}",
                $passed,
                $passed ? "Корректно конвертирована: {$convertedPrice} USD" : "Ошибка: цена не конвертирована"
            );
        }
    }
    
    return $results;
}

/**
 * Тест 2: Проверка исключения бюджетных брендов из премиум категорий
 */
function testBudgetBrandExclusion() {
    $results = [];
    
    // Бюджетные бренды, которые должны быть исключены
    $budgetBrands = ['Kia', 'Hyundai', 'Chevrolet', 'Daewoo'];
    
    // Премиум категории
    $premiumCategories = ['business', 'sport', 'suv'];
    
    foreach ($budgetBrands as $brand) {
        foreach ($premiumCategories as $category) {
            // Тестируем различные модели
            $testModels = ['Sportage', 'Sorento', 'Telluride', 'Palisade', 'Santa Fe', 'Corvette', 'Camaro'];
            
            foreach ($testModels as $model) {
                $detectedCategory = determineCarCategory($brand, $model);
                
                $shouldBeExcluded = in_array($category, $premiumCategories);
                $isExcluded = ($detectedCategory !== $category);
                
                if ($shouldBeExcluded) {
                    $passed = $isExcluded;
                    $results[] = logTestResult(
                        "Исключение {$brand} {$model} из {$category}",
                        $passed,
                        $passed ? "Корректно исключен" : "Ошибка: бюджетный бренд попал в премиум категорию"
                    );
                }
            }
        }
    }
    
    return $results;
}

/**
 * Тест 3: Проверка корректности определения премиум категорий
 */
function testPremiumCategoryDetection() {
    $results = [];
    
    // Премиум бренды, которые должны попадать в категории
    $premiumTests = [
        ['BMW', 'M3', 'sport'],
        ['Mercedes-Benz', 'AMG GT', 'sport'],
        ['BMW', 'X5', 'suv'],
        ['Audi', 'Q7', 'suv'],
        ['Rolls-Royce', 'Phantom', 'business'],
        ['Bentley', 'Flying Spur', 'business'],
        ['Ferrari', '458', 'sport'],
        ['Lamborghini', 'Aventador', 'sport'],
        ['Porsche', 'Cayenne', 'suv']
    ];
    
    foreach ($premiumTests as $test) {
        list($brand, $model, $expectedCategory) = $test;
        $detectedCategory = determineCarCategory($brand, $model);
        
        $passed = ($detectedCategory === $expectedCategory);
        $results[] = logTestResult(
            "Премиум {$brand} {$model} → {$expectedCategory}",
            $passed,
            $passed ? "Корректно определена категория" : "Ошибка: ожидалось '{$expectedCategory}', получено '{$detectedCategory}'"
        );
    }
    
    return $results;
}

/**
 * Тест 4: Проверка обработки некорректных данных
 */
function testInvalidDataHandling() {
    $results = [];
    $mockRates = ['KRW_to_RUB' => 0.075, 'USD_rate' => 95];
    
    // Тест некорректных цен
    $invalidPrices = [0, -1000, null, '', 'abc', 999999999];
    
    foreach ($invalidPrices as $price) {
        $convertedPrice = convertKRWToUSD($price, $mockRates);
        
        // Все некорректные цены должны возвращать 0
        $passed = ($convertedPrice === 0);
        $priceStr = is_null($price) ? 'null' : (is_string($price) ? "'{$price}'" : $price);
        $results[] = logTestResult(
            "Некорректная цена {$priceStr}",
            $passed,
            $passed ? "Корректно обработана" : "Ошибка: некорректная цена не обработана, результат: {$convertedPrice}"
        );
    }
    
    // Тест некорректных брендов/моделей
    $invalidBrands = ['', null, 'UnknownBrand123'];
    
    foreach ($invalidBrands as $brand) {
        $category = determineCarCategory($brand, 'TestModel');
        
        // Неизвестные бренды должны возвращать пустую строку
        $passed = ($category === '');
        $brandStr = is_null($brand) ? 'null' : "'{$brand}'";
        $results[] = logTestResult(
            "Неизвестный бренд {$brandStr}",
            $passed,
            $passed ? "Корректно обработан" : "Ошибка: неизвестный бренд вернул категорию '{$category}'"
        );
    }
    
    return $results;
}

/**
 * Основная функция выполнения всех тестов
 */
function runAllTests() {
    $allResults = [];
    $summary = [
        'total' => 0,
        'passed' => 0,
        'failed' => 0,
        'success_rate' => 0
    ];
    
    // Выполняем все тесты
    $testSuites = [
        'Фильтрация забронированных цен' => testReservedPriceFiltering(),
        'Исключение бюджетных брендов' => testBudgetBrandExclusion(),
        'Определение премиум категорий' => testPremiumCategoryDetection(),
        'Обработка некорректных данных' => testInvalidDataHandling()
    ];
    
    foreach ($testSuites as $suiteName => $results) {
        $allResults[$suiteName] = $results;
        
        foreach ($results as $result) {
            $summary['total']++;
            if ($result['passed']) {
                $summary['passed']++;
            } else {
                $summary['failed']++;
            }
        }
    }
    
    // Вычисляем процент успешности
    $summary['success_rate'] = $summary['total'] > 0 ? 
        round(($summary['passed'] / $summary['total']) * 100, 1) : 0;
    
    return [
        'summary' => $summary,
        'results' => $allResults,
        'timestamp' => date('Y-m-d H:i:s'),
        'php_version' => PHP_VERSION,
        'memory_usage' => memory_get_usage(true)
    ];
}

// Выполняем тесты и возвращаем результат
try {
    $testResults = runAllTests();
    
    // Добавляем информацию о системе
    $testResults['system_info'] = [
        'php_version' => PHP_VERSION,
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'server_time' => date('Y-m-d H:i:s'),
        'timezone' => date_default_timezone_get()
    ];
    
    echo json_encode($testResults, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'message' => 'Ошибка выполнения тестов: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}
?>
