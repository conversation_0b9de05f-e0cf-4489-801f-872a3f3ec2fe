document.addEventListener("DOMContentLoaded", () => {
  // Элементы DOM
  const carSelect = document.getElementById("select-car");
  const loadCarBtn = document.getElementById("load-car-btn");
  const carDetailForm = document.getElementById("car-detail-form");
  const submitDetailBtn = document.getElementById("submit-detail-btn");
  const cancelDetailBtn = document.getElementById("cancel-detail-btn");
  const previewBtn = document.getElementById("preview-btn");
  const detailsContainer = document.getElementById("details-container");
  const detailsCounter = document.getElementById("details-counter");
  const formTabs = document.querySelectorAll(".form-tab");
  const formTabContents = document.querySelectorAll(".form-tab-content");
  const previewModal = document.getElementById("preview-modal");
  const previewContainer = document.getElementById("preview-container");
  const modalClose = document.querySelector(".modal-close");
  const uploadedImagesContainer = document.getElementById("uploaded-images");
  const imagesInput = document.getElementById("images");
  const imageUpload = document.getElementById("image-upload");
  const uploadError = document.getElementById("upload-error");

  // Переменные состояния
  let isEditing = false;
  let currentCarId = null;
  let currentDetailId = null;
  let uploadedImages = [];
  let selectedColors = [];

  // Инициализация
  init();

  // Инициализация приложения
  function init() {
    // Добавляем стили для кнопки открытия страницы
    const styleEl = document.createElement("style");
    styleEl.textContent = `
            .detail-open-page-btn {
                background-color: #28a745;
                color: white;
            }
            .detail-open-page-btn:hover {
                background-color: #218838;
            }
        `;
    document.head.appendChild(styleEl);

    loadCars();
    loadCarDetails();
    setupEventListeners();
  }

  // Настройка прослушивателей событий
  function setupEventListeners() {
    // Загрузка данных автомобиля
    loadCarBtn.addEventListener("click", loadSelectedCar);

    // Отправка формы
    carDetailForm.addEventListener("submit", handleFormSubmit);

    // Кнопка отмены
    cancelDetailBtn.addEventListener("click", resetForm);

    // Кнопка предпросмотра
    previewBtn.addEventListener("click", handlePreview);

    // Закрытие модального окна
    modalClose.addEventListener("click", () => {
      previewModal.style.display = "none";
    });

    // Закрытие модального окна при клике вне его содержимого
    window.addEventListener("click", (e) => {
      if (e.target === previewModal) {
        previewModal.style.display = "none";
      }
    });

    // Переключение вкладок формы
    formTabs.forEach((tab) => {
      tab.addEventListener("click", () => {
        const tabId = tab.getAttribute("data-tab");
        formTabs.forEach((t) => t.classList.remove("active"));
        formTabContents.forEach((c) => c.classList.remove("active"));
        tab.classList.add("active");
        document.getElementById(tabId).classList.add("active");
      });
    });

    // Загрузка изображений
    if (imageUpload) {
      console.log("Инициализация загрузки изображений");
      imageUpload.addEventListener("change", handleImageUpload);
    } else {
      console.error("Элемент imageUpload не найден");
    }

    // Проверим все элементы загрузки изображений
    document.querySelectorAll(".image-upload").forEach((upload, index) => {
      console.log(`Найден элемент загрузки #${index}`);
      upload.addEventListener("change", handleImageUpload);
    });
  }

  // Загрузка списка автомобилей
  async function loadCars() {
    try {
      const response = await fetch("/api/cars");
      const cars = await response.json();

      carSelect.innerHTML =
        '<option value="">-- Выберите автомобиль --</option>';
      cars.forEach((car) => {
        const option = document.createElement("option");
        option.value = car.id;
        option.textContent = car.title;
        carSelect.appendChild(option);
      });
    } catch (error) {
      showNotification(
        "Ошибка",
        "Не удалось загрузить список автомобилей",
        "error"
      );
    }
  }

  // Загрузка всех детальных страниц
  async function loadCarDetails() {
    try {
      const response = await fetch("/api/car-details");
      const details = await response.json();

      console.log("Получены данные из БД:", details);

      detailsContainer.innerHTML = "";
      if (details.length === 0) {
        detailsContainer.innerHTML =
          '<div class="no-details">Нет созданных детальных страниц</div>';
      } else {
        details.forEach((detail) => {
          detailsContainer.appendChild(createDetailCard(detail));
        });
      }

      updateDetailsCounter(details.length);
    } catch (error) {
      console.error("Ошибка при загрузке детальных страниц:", error);
      showNotification(
        "Ошибка",
        "Не удалось загрузить детальные страницы",
        "error"
      );
    }
  }

  // Создание карточки для детальной страницы
  function createDetailCard(detail) {
    console.log(
      "Создание карточки для detail:",
      detail.id,
      "HTML страница:",
      detail.html_page
    );

    const card = document.createElement("div");
    card.className = "detail-card";
    card.setAttribute("data-detail-id", detail.id);

    const image = document.createElement("img");
    image.className = "detail-card-image";
    image.src = detail.car_image || "../PNG/SHMS_Logo_Black.png";
    image.alt = detail.car_title;

    const info = document.createElement("div");
    info.className = "detail-card-info";

    const title = document.createElement("div");
    title.className = "detail-card-title";
    title.textContent = detail.full_title;

    const subtitle = document.createElement("div");
    subtitle.className = "detail-card-subtitle";
    subtitle.textContent = detail.car_title;

    info.appendChild(title);
    info.appendChild(subtitle);

    const price = document.createElement("div");
    price.className = "detail-card-price";
    price.textContent = detail.price;

    const actions = document.createElement("div");
    actions.className = "detail-card-actions";

    // Проверяем, есть ли информация о сгенерированной странице
    if (detail.html_page) {
      console.log(
        'Создаем кнопку "Открыть страницу" для:',
        detail.id,
        "Путь:",
        detail.html_page
      );

      const openPageBtn = document.createElement("button");
      openPageBtn.className = "detail-action-btn detail-open-page-btn";
      openPageBtn.innerHTML =
        '<i class="fas fa-external-link-alt"></i> Открыть страницу';
      openPageBtn.setAttribute("data-path", detail.html_page);
      openPageBtn.addEventListener("click", function () {
        const path = this.getAttribute("data-path");
        console.log("Открываем страницу по пути:", path);
        window.open(path, "_blank");
      });
      actions.appendChild(openPageBtn);
    } else {
      console.log("HTML страница не найдена для ID:", detail.id);
    }

    const editBtn = document.createElement("button");
    editBtn.className = "detail-action-btn detail-edit-btn";
    editBtn.innerHTML = '<i class="fas fa-edit"></i> Изменить';
    editBtn.addEventListener("click", () => editDetail(detail.id));

    const deleteBtn = document.createElement("button");
    deleteBtn.className = "detail-action-btn detail-delete-btn";
    deleteBtn.innerHTML = '<i class="fas fa-trash"></i> Удалить';
    deleteBtn.addEventListener("click", () => deleteDetail(detail.id, card));

    const previewBtn = document.createElement("button");
    previewBtn.className = "detail-action-btn detail-preview-btn";
    previewBtn.innerHTML = '<i class="fas fa-eye"></i> Просмотр';
    previewBtn.addEventListener("click", () => previewDetail(detail));

    const generateHtmlBtn = document.createElement("button");
    generateHtmlBtn.className = "detail-action-btn detail-generate-btn";
    generateHtmlBtn.innerHTML = '<i class="fas fa-code"></i> Создать HTML';
    generateHtmlBtn.addEventListener("click", () =>
      generateHtmlPage(detail.id)
    );

    actions.appendChild(editBtn);
    actions.appendChild(deleteBtn);
    actions.appendChild(previewBtn);
    actions.appendChild(generateHtmlBtn);

    card.appendChild(image);
    card.appendChild(info);
    card.appendChild(price);
    card.appendChild(actions);

    return card;
  }

  // Загрузка выбранного автомобиля
  async function loadSelectedCar() {
    const carId = carSelect.value;
    if (!carId) {
      showNotification("Ошибка", "Выберите автомобиль", "error");
      return;
    }

    try {
      // Проверяем, есть ли уже детальная информация для этого автомобиля
      const detailResponse = await fetch(`/api/car-details/car/${carId}`);
      const detailData = await detailResponse.json();

      if (detailData) {
        // Если есть, загружаем её для редактирования
        showNotification(
          "Информация",
          "Для этого автомобиля уже есть детальная страница",
          "info"
        );
        editDetail(detailData.id);
        return;
      }

      // Получаем данные автомобиля
      const carResponse = await fetch(`/api/cars/${carId}`);
      const carData = await carResponse.json();

      // Заполняем форму данными
      resetForm();
      document.getElementById("car-id").value = carId;

      // Формируем полное название с годом и моделью
      let fullTitle = carData.title;
      if (!fullTitle.includes("202")) {
        // Если в названии ещё нет года, добавляем текущий
        const currentYear = new Date().getFullYear();
        fullTitle = `${currentYear} ${fullTitle}`;
      }
      document.getElementById("full-title").value = fullTitle;

      // Устанавливаем цены
      // Если цена в числовом формате, добавляем $ знак
      let priceValue = carData.price;
      if (!isNaN(parseFloat(priceValue)) && !priceValue.includes("$")) {
        priceValue = `$${priceValue}`;
      }
      document.getElementById("price").value = priceValue;

      // Для цены в Москве добавляем небольшую надбавку
      let moscowPrice = parseFloat(carData.price.replace(/[$,]/g, ""));
      if (!isNaN(moscowPrice)) {
        // Увеличиваем цену на 1-3%
        const markup = 1 + (Math.random() * 2 + 1) / 100;
        moscowPrice = Math.round(moscowPrice * markup);
        document.getElementById(
          "moscow-price"
        ).value = `$${moscowPrice.toLocaleString("en-US")}`;
      } else {
        document.getElementById("moscow-price").value = priceValue;
      }

      // Собираем данные из доступных полей
      const power = carData.power || "";
      const mileage = carData.mileage || "";
      const bodyType = carData.body_type || "";
      const engine = carData.engine || "";
      const transmission = carData.transmission || "";

      // Загружаем цвета интерьера из карточки
      let interiorColors = [];
      try {
        if (carData.interior_colors) {
          // Пробуем распарсить как JSON
          interiorColors = JSON.parse(carData.interior_colors);
        }
      } catch (e) {
        console.error("Ошибка при парсинге JSON цветов интерьера:", e);
        // Если не удалось распарсить как JSON, проверяем старые форматы
        if (
          carData.interior_colors &&
          typeof carData.interior_colors === "string"
        ) {
          const colorsStr = carData.interior_colors;
          if (colorsStr.includes("dot--") || colorsStr.includes("dot dot-")) {
            // Обрабатываем старый формат
            const colors = [];
            if (colorsStr.includes("dot dot-")) {
              // Формат "dot dot-color, dot dot-color2"
              const colorParts = colorsStr.split(", ");
              colorParts.forEach((part) => {
                const colorName = part.replace("dot dot-", "").trim();
                colors.push({ name: colorName, hex: getColorHex(colorName) });
              });
            } else if (colorsStr.includes("dot--")) {
              // Формат "dot--color,dot--color2"
              const colorParts = colorsStr.split(",");
              colorParts.forEach((part) => {
                const colorName = part.replace("dot--", "").trim();
                colors.push({ name: colorName, hex: getColorHex(colorName) });
              });
            }
            interiorColors = colors;
          }
        }
      }

      // Если удалось получить цвета, загружаем их в форму
      if (interiorColors && interiorColors.length > 0) {
        // Очищаем текущие выбранные цвета
        selectedColors = [];

        // Добавляем каждый цвет в массив выбранных цветов
        interiorColors.forEach((color) => {
          selectedColors.push({
            id: "color-" + Date.now() + Math.random().toString(36).substr(2, 5),
            hex: color.hex || "#000000",
            name: color.name || "Черный",
          });
        });

        // Обновляем отображение и скрытое поле
        renderSelectedColors();
        updateInteriorColorsValue();
      }

      // Извлекаем цвет экстерьера из названия, если есть
      let exteriorColor = "";
      const colorKeywords = [
        "black",
        "white",
        "red",
        "blue",
        "silver",
        "gray",
        "grey",
        "green",
        "yellow",
        "orange",
        "brown",
        "gold",
        "bronze",
      ];
      const titleWords = carData.title.toLowerCase().split(" ");
      for (const word of titleWords) {
        if (colorKeywords.includes(word)) {
          exteriorColor = word.charAt(0).toUpperCase() + word.slice(1);
          break;
        }
      }

      // Генерируем описание с учетом доступных данных
      const engineDesc = engine ? `**${engine}**` : "**Мощный двигатель**";
      const powerDesc = power ? `**${power}**` : "**Впечатляющая мощность**";

      const description = `**${fullTitle}** — это квинтэссенция скорости, роскоши и передовых технологий.
Этот автомобиль создан для тех, кто требует от жизни больше: больше мощности, больше эмоций, больше свободы.

${engineDesc} развивает ${powerDesc} и впечатляющий крутящий момент, обеспечивая захватывающую динамику и непревзойденную производительность.

**Двигатель:** ${engine || "Уточняется"}
**Мощность:** ${power || "Уточняется"}

Не следуй за скоростью — будь ею!`;

      document.getElementById("description").value = description;

      // Генерируем характеристики на основе доступных данных
      const specs = `Привод: ${
        bodyType.includes("AWD") || bodyType.includes("4WD")
          ? "AWD"
          : "Уточняется"
      }
Двигатель: ${engine || "Уточняется"}
Мощность: ${power || "Уточняется"}
Трансмиссия: ${transmission || "Уточняется"}
Пробег: ${mileage || "Уточняется"}
Цвет экстерьера: ${exteriorColor || "Уточняется"}`;

      document.getElementById("specs").value = specs;

      // Генерируем лучшие особенности для разных типов автомобилей, разделяя их на основные и дополнительные
      // Определяем тип автомобиля по названию
      const isSUV =
        carData.title.toLowerCase().includes("suv") ||
        bodyType.toLowerCase().includes("suv") ||
        carData.title.match(/x[0-9]/i) || // для BMW X5, X7 и т.д.
        carData.title.match(/q[0-9]/i) || // для Audi Q5, Q7 и т.д.
        ["cayenne", "urus", "bentayga", "cullinan"].some((model) =>
          carData.title.toLowerCase().includes(model)
        );

      const isSports =
        carData.title.toLowerCase().includes("sport") ||
        (power && parseInt(power.replace(/[^0-9]/g, "")) > 500) ||
        ["911", "gt", "amg", "ferrari", "lamborghini", "corvette"].some(
          (model) => carData.title.toLowerCase().includes(model)
        );

      const isLuxury = [
        "s-class",
        "s class",
        "maybach",
        "bentley",
        "rolls",
        "phantom",
      ].some((model) => carData.title.toLowerCase().includes(model));

      // Основные опции
      let mainFeatures = [];

      if (engine) {
        mainFeatures.push(`${engine}`);
      } else if (power) {
        mainFeatures.push(`Двигатель ${power}`);
      } else {
        mainFeatures.push("Мощный двигатель");
      }

      // Добавляем полный привод, если это указано в типе кузова
      if (
        bodyType.toLowerCase().includes("awd") ||
        bodyType.toLowerCase().includes("4wd")
      ) {
        mainFeatures.push("Полный привод quattro с активным дифференциалом");
      }

      // Добавляем информацию о разгоне
      mainFeatures.push("Разгон 0-100 км/ч — 3,4 секунды");

      // Добавляем специфичные основные опции в зависимости от типа
      if (isSports) {
        mainFeatures = mainFeatures.concat([
          "Керамические тормоза (опция)",
          "Динамическая подвеска RS",
          "Матрикс LED-фары с лазерной технологией",
          "Спортивная выпускная система RS",
        ]);
      } else if (isSUV) {
        mainFeatures = mainFeatures.concat([
          "Адаптивная пневмоподвеска",
          "Матрикс LED-фары",
          "Система полного привода",
          "Спортивный пакет S-Line",
        ]);
      } else if (isLuxury) {
        mainFeatures = mainFeatures.concat([
          "Адаптивная пневмоподвеска",
          "Digital Matrix LED-фары",
          "Система ночного видения",
          "Звукоизолирующие стекла",
        ]);
      } else {
        mainFeatures = mainFeatures.concat([
          "Современная система безопасности",
          "LED-фары",
          "Электронные системы стабилизации",
          "Адаптивный круиз-контроль",
        ]);
      }

      // Дополнительные опции
      let additionalFeatures = [
        "Бесключевой доступ и запуск",
        "Панорамная крыша",
        "Вентилируемые и массажные сиденья",
        "Обивка салона из кожи Valcona и алькантары",
        "Беспроводная зарядка и MMI touch response",
        "Аудиосистема Bang & Olufsen 3D Premium",
      ];

      // Объединяем основные и дополнительные опции с заголовками
      const bestFeatures =
        "Основные опции\n" +
        [...new Set(mainFeatures)].join("\n") +
        "\n\nДополнительные\n" +
        [...new Set(additionalFeatures)].join("\n");

      document.getElementById("best-features").value = bestFeatures;

      // Показываем кнопку отмены
      cancelDetailBtn.style.display = "inline-block";

      currentCarId = carId;
      isEditing = false;

      showNotification("Успех", "Данные автомобиля загружены", "success");
    } catch (error) {
      console.error("Ошибка при загрузке данных автомобиля:", error);
      showNotification(
        "Ошибка",
        "Не удалось загрузить данные автомобиля",
        "error"
      );
    }
  }

  // Функция для получения HEX-кода цвета по его названию
  function getColorHex(colorName) {
    const colorMap = {
      black: "#000000",
      white: "#ffffff",
      red: "#d20000",
      blue: "#0066cc",
      orange: "#ff7700",
      brown: "#8B4513",
      beige: "#F5F5DC",
      gray: "#808080",
      grey: "#808080",
      silver: "#C0C0C0",
      gold: "#FFD700",
      green: "#008000",
    };

    return colorMap[colorName.toLowerCase()] || "#000000";
  }

  // Отправка формы
  async function handleFormSubmit(e) {
    e.preventDefault();

    // Сбросим предыдущие ошибки
    if (uploadError) {
      uploadError.style.display = "none";
      uploadError.textContent = "";
    }

    const formData = new FormData(carDetailForm);

    // Проверяем обязательные поля
    const requiredFields = [
      "car_id",
      "full_title",
      "price",
      "moscow_price",
      "description",
      "specs",
      "best_features",
    ];
    for (const field of requiredFields) {
      if (!formData.get(field)) {
        showNotification("Ошибка", `Заполните поле ${field}`, "error");
        return;
      }
    }

    try {
      // Проверим значение в поле изображений
      if (imagesInput && imagesInput.value) {
        console.log("Изображения из формы:", imagesInput.value);
        // Парсим JSON строку с изображениями, проверяем что это массив
        try {
          const images = JSON.parse(imagesInput.value);
          if (!Array.isArray(images)) {
            throw new Error("Неверный формат данных для изображений");
          }

          if (images.length === 0) {
            console.warn("Предупреждение: не выбрано ни одного изображения");
          }
        } catch (error) {
          console.error("Ошибка при парсинге JSON изображений:", error);
          showNotification(
            "Ошибка",
            "Неверный формат данных для изображений",
            "error"
          );
          return;
        }
      } else {
        console.warn("Предупреждение: поле изображений пусто");
      }

      // Обновляем UI - показываем индикатор загрузки
      submitDetailBtn.disabled = true;
      submitDetailBtn.classList.add("btn-loading");
      const originalBtnText = submitDetailBtn.innerHTML;
      submitDetailBtn.innerHTML =
        '<i class="fas fa-spinner fa-spin"></i> Сохранение...';

      console.log("Отправка формы:", isEditing ? "обновление" : "создание");

      let response;
      if (isEditing) {
        response = await fetch(`/api/car-details/${currentDetailId}`, {
          method: "PUT",
          body: formData,
        });
      } else {
        response = await fetch("/api/car-details", {
          method: "POST",
          body: formData,
        });
      }

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Ошибка сервера");
      }

      const result = await response.json();

      showNotification(
        "Успех",
        isEditing
          ? "Детальная страница обновлена"
          : "Детальная страница создана",
        "success"
      );
      resetForm();
      loadCarDetails();
    } catch (error) {
      console.error("Ошибка при отправке формы:", error);
      showNotification(
        "Ошибка",
        error.message || "Не удалось отправить форму",
        "error"
      );
      if (uploadError) {
        uploadError.textContent = error.message || "Не удалось отправить форму";
        uploadError.style.display = "block";
      }
    } finally {
      // Восстанавливаем состояние кнопки отправки
      submitDetailBtn.disabled = false;
      submitDetailBtn.classList.remove("btn-loading");
      if (originalBtnText) {
        submitDetailBtn.innerHTML = originalBtnText;
      } else {
        submitDetailBtn.innerHTML = isEditing
          ? '<i class="fas fa-save"></i> Обновить'
          : '<i class="fas fa-save"></i> Сохранить';
      }
    }
  }

  // Редактирование детальной страницы
  async function editDetail(id) {
    try {
      const response = await fetch(`/admin-stock/car-details/${id}`);
      const detail = await response.json();

      // Заполняем форму данными
      resetForm();
      document.getElementById("detail-id").value = detail.id;
      document.getElementById("car-id").value = detail.car_id;
      document.getElementById("full-title").value = detail.full_title;
      document.getElementById("price").value = detail.price;
      document.getElementById("moscow-price").value = detail.moscow_price;
      document.getElementById("description").value = detail.description;
      document.getElementById("specs").value = detail.specs;
      document.getElementById("best-features").value = detail.best_features;
      document.getElementById("similar-cars").value = detail.similar_cars || "";

      // Загружаем изображения
      uploadedImages = [];
      if (detail.images) {
        try {
          uploadedImages = JSON.parse(detail.images);
          renderUploadedImages();
        } catch (e) {
          console.error("Ошибка при парсинге JSON изображений:", e);
        }
      }

      // Обновляем скрытое поле с изображениями
      imagesInput.value = JSON.stringify(uploadedImages);

      // Обновляем UI
      submitDetailBtn.innerHTML = '<i class="fas fa-save"></i> Обновить';
      cancelDetailBtn.style.display = "inline-block";

      // Выбираем автомобиль в селекте
      carSelect.value = detail.car_id;

      // Обновляем состояние
      currentDetailId = detail.id;
      currentCarId = detail.car_id;
      isEditing = true;

      showNotification(
        "Информация",
        "Загружены данные для редактирования",
        "info"
      );
    } catch (error) {
      showNotification(
        "Ошибка",
        "Не удалось загрузить данные для редактирования",
        "error"
      );
    }
  }

  // Удаление детальной страницы
  async function deleteDetail(id, card) {
    if (!confirm("Вы уверены, что хотите удалить эту детальную страницу?")) {
      return;
    }

    try {
      const response = await fetch(`/api/car-details/${id}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (response.ok) {
        card.remove();
        updateDetailsCounter(document.querySelectorAll(".detail-card").length);
        showNotification("Успех", "Детальная страница удалена", "success");

        // Если редактировалась удаленная запись
        if (isEditing && currentDetailId === id) {
          resetForm();
        }
      } else {
        showNotification(
          "Ошибка",
          result.error || "Не удалось удалить страницу",
          "error"
        );
      }
    } catch (error) {
      showNotification("Ошибка", "Не удалось удалить страницу", "error");
    }
  }

  // Предпросмотр детальной страницы
  async function previewDetail(detail) {
    try {
      const response = await fetch("/api/car-details/preview", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(detail),
      });

      const result = await response.json();

      if (response.ok) {
        previewContainer.innerHTML = result.html;
        previewModal.style.display = "block";

        // Инициализируем слайдер в предпросмотре
        const slides = previewContainer.querySelectorAll(".slider-img");
        const prevBtn = previewContainer.querySelector("#sliderPrev");
        const nextBtn = previewContainer.querySelector("#sliderNext");
        let current = 0;

        function showSlide(idx) {
          slides.forEach((img, i) => {
            img.classList.toggle("active", i === idx);
            img.style.display = i === idx ? "block" : "none";
          });
        }

        function nextSlide() {
          current = (current + 1) % slides.length;
          showSlide(current);
        }

        function prevSlide() {
          current = (current - 1 + slides.length) % slides.length;
          showSlide(current);
        }

        // Показываем первый слайд
        if (slides.length > 0) {
          showSlide(0);
        }

        // Прикрепляем обработчики событий
        if (prevBtn) {
          prevBtn.addEventListener("click", function () {
            prevSlide();
          });
        }

        if (nextBtn) {
          nextBtn.addEventListener("click", function () {
            nextSlide();
          });
        }

        // Инициализируем табы в предпросмотре
        const tabs = previewContainer.querySelectorAll(".car-detail__tab");
        tabs.forEach((tab) => {
          tab.addEventListener("click", function (e) {
            e.preventDefault();

            // Удаляем класс active у всех табов
            tabs.forEach((t) => t.classList.remove("active"));

            // Добавляем класс active к нажатому табу
            this.classList.add("active");

            // Прокрутка к нужному разделу
            const targetId = this.getAttribute("href");
            const targetElement = previewContainer.querySelector(targetId);
            if (targetElement) {
              previewContainer.scrollTop = targetElement.offsetTop - 100;
            }
          });
        });
      } else {
        showNotification(
          "Ошибка",
          result.error || "Не удалось сгенерировать предпросмотр",
          "error"
        );
      }
    } catch (error) {
      showNotification(
        "Ошибка",
        "Не удалось сгенерировать предпросмотр",
        "error"
      );
    }
  }

  // Предпросмотр текущей формы
  async function handlePreview() {
    // Собираем данные формы
    const formData = new FormData(carDetailForm);
    const data = Object.fromEntries(formData.entries());

    try {
      const response = await fetch("/api/car-details/preview", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok) {
        previewContainer.innerHTML = result.html;
        previewModal.style.display = "block";

        // Инициализируем слайдер в предпросмотре
        const slides = previewContainer.querySelectorAll(".slider-img");
        const prevBtn = previewContainer.querySelector("#sliderPrev");
        const nextBtn = previewContainer.querySelector("#sliderNext");
        let current = 0;

        function showSlide(idx) {
          slides.forEach((img, i) => {
            img.classList.toggle("active", i === idx);
            img.style.display = i === idx ? "block" : "none";
          });
        }

        function nextSlide() {
          current = (current + 1) % slides.length;
          showSlide(current);
        }

        function prevSlide() {
          current = (current - 1 + slides.length) % slides.length;
          showSlide(current);
        }

        // Показываем первый слайд
        if (slides.length > 0) {
          showSlide(0);
        }

        // Прикрепляем обработчики событий
        if (prevBtn) {
          prevBtn.addEventListener("click", function () {
            prevSlide();
          });
        }

        if (nextBtn) {
          nextBtn.addEventListener("click", function () {
            nextSlide();
          });
        }

        // Инициализируем табы в предпросмотре
        const tabs = previewContainer.querySelectorAll(".car-detail__tab");
        tabs.forEach((tab) => {
          tab.addEventListener("click", function (e) {
            e.preventDefault();

            // Удаляем класс active у всех табов
            tabs.forEach((t) => t.classList.remove("active"));

            // Добавляем класс active к нажатому табу
            this.classList.add("active");

            // Прокрутка к нужному разделу
            const targetId = this.getAttribute("href");
            const targetElement = previewContainer.querySelector(targetId);
            if (targetElement) {
              previewContainer.scrollTop = targetElement.offsetTop - 100;
            }
          });
        });
      } else {
        showNotification(
          "Ошибка",
          result.error || "Не удалось сгенерировать предпросмотр",
          "error"
        );
      }
    } catch (error) {
      showNotification(
        "Ошибка",
        "Не удалось сгенерировать предпросмотр",
        "error"
      );
    }
  }

  // Обработка загрузки изображений
  function handleImageUpload(e) {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    console.log("Загрузка изображений:", files.length);

    // Сбросим предыдущие ошибки
    if (uploadError) {
      uploadError.style.display = "none";
      uploadError.textContent = "";
    }

    // Проверка размера и типа файлов
    const maxSize = 20 * 1024 * 1024; // 20MB
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      if (file.size > maxSize) {
        const errorMsg = `Файл "${file.name}" слишком большой. Максимальный размер: 20MB`;
        console.error(errorMsg);
        if (uploadError) {
          uploadError.textContent = errorMsg;
          uploadError.style.display = "block";
        }
        return;
      }

      if (!allowedTypes.includes(file.type)) {
        const errorMsg = `Файл "${file.name}" имеет неподдерживаемый формат. Поддерживаемые форматы: JPG, PNG, WEBP`;
        console.error(errorMsg);
        if (uploadError) {
          uploadError.textContent = errorMsg;
          uploadError.style.display = "block";
        }
        return;
      }
    }

    // Создаем FormData для загрузки файлов
    const formData = new FormData();
    for (let i = 0; i < files.length; i++) {
      formData.append("detail_images", files[i]);
      console.log("Добавлен файл:", files[i].name);
    }

    // Обновляем UI
    const uploadBtn = e.target
      .closest(".image-upload-container")
      .querySelector(".upload-placeholder");
    if (uploadBtn) {
      uploadBtn.innerHTML =
        '<i class="fas fa-spinner fa-spin"></i><span>Загрузка...</span>';
    }

    // Временно отключаем кнопку загрузки
    e.target.disabled = true;

    // Тестовый запрос - проверяем доступность маршрута загрузки
    fetch("/admin/upload-test", {
      method: "GET",
    })
      .then((response) => {
        if (!response.ok) {
          console.error(
            "Маршрут загрузки недоступен. Статус:",
            response.status
          );
          throw new Error(
            `Маршрут загрузки недоступен. Статус: ${response.status}`
          );
        }
        console.log("Маршрут загрузки доступен, начинаем загрузку файлов");

        // Основной запрос загрузки файлов
        return fetch("/admin/upload-temp", {
          method: "POST",
          body: formData,
        });
      })
      .then((response) => {
        console.log("Статус ответа:", response.status);
        console.log("URL ответа:", response.url);

        if (!response.ok) {
          console.error("Ошибка от сервера. Статус:", response.status);
          return response.text().then((text) => {
            try {
              const json = JSON.parse(text);
              throw new Error(json.error || `Ошибка HTTP: ${response.status}`);
            } catch (e) {
              throw new Error(`Ошибка HTTP: ${response.status}. ${text}`);
            }
          });
        }

        return response.json();
      })
      .then((result) => {
        console.log("Результат загрузки:", result);
        if (result.images && result.images.length > 0) {
          // Добавляем новые изображения в массив
          uploadedImages = [...uploadedImages, ...result.images];

          // Обновляем скрытое поле с изображениями
          imagesInput.value = JSON.stringify(uploadedImages);

          // Отображаем изображения
          renderUploadedImages();

          showNotification("Успех", "Изображения загружены", "success");
        } else {
          showNotification(
            "Ошибка",
            "Не удалось загрузить изображения",
            "error"
          );
          if (uploadError) {
            uploadError.textContent = "Не удалось загрузить изображения";
            uploadError.style.display = "block";
          }
        }
      })
      .catch((error) => {
        console.error("Ошибка при загрузке изображений:", error);
        showNotification("Ошибка", error.message, "error");
        if (uploadError) {
          uploadError.textContent = `Ошибка: ${error.message}`;
          uploadError.style.display = "block";
        }
      })
      .finally(() => {
        // Включаем кнопку загрузки
        e.target.disabled = false;
        // Восстанавливаем текст на кнопке
        if (uploadBtn) {
          uploadBtn.innerHTML =
            '<i class="fas fa-plus"></i><span>Добавить изображения</span>';
        }
        // Очищаем поле выбора файлов
        e.target.value = "";
      });
  }

  // Отображение загруженных изображений
  function renderUploadedImages() {
    uploadedImagesContainer.innerHTML = "";

    if (uploadedImages.length === 0) {
      return;
    }

    uploadedImages.forEach((image, index) => {
      const container = document.createElement("div");
      container.className = "uploaded-image-container";

      const img = document.createElement("img");
      img.className = "uploaded-image";
      img.src = image.path;
      img.alt = `Изображение ${index + 1}`;

      const actions = document.createElement("div");
      actions.className = "uploaded-image-actions";

      const deleteBtn = document.createElement("button");
      deleteBtn.className = "image-action-btn";
      deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
      deleteBtn.addEventListener("click", () => {
        uploadedImages = uploadedImages.filter((_, i) => i !== index);
        imagesInput.value = JSON.stringify(uploadedImages);
        renderUploadedImages();
      });

      actions.appendChild(deleteBtn);
      container.appendChild(img);
      container.appendChild(actions);
      uploadedImagesContainer.appendChild(container);
    });
  }

  // Сброс формы
  function resetForm() {
    carDetailForm.reset();
    document.getElementById("detail-id").value = "";
    document.getElementById("car-id").value = "";
    uploadedImages = [];
    imagesInput.value = "[]";
    renderUploadedImages();
    submitDetailBtn.innerHTML = '<i class="fas fa-save"></i> Сохранить';
    cancelDetailBtn.style.display = "none";
    currentDetailId = null;
    currentCarId = null;
    isEditing = false;

    // Сбрасываем активную вкладку
    formTabs[0].click();
  }

  // Обновление счетчика детальных страниц
  function updateDetailsCounter(count) {
    const counterValue = detailsCounter.querySelector(".counter-value");
    counterValue.textContent = count.toString();
  }

  // Показ уведомления
  function showNotification(title, message, type = "info") {
    const notification = document.getElementById("notification");
    const notificationTitle = notification.querySelector(".notification-title");
    const notificationText = notification.querySelector(".notification-text");
    const notificationClose = notification.querySelector(".notification-close");

    notificationTitle.textContent = title;
    notificationText.textContent = message;
    notification.className = `notification notification--${type} notification--visible`;

    const hideNotification = () => {
      notification.classList.remove("notification--visible");
    };

    notificationClose.addEventListener("click", hideNotification);

    // Автоматически скрыть через 5 секунд
    setTimeout(hideNotification, 5000);
  }

  // Генерация HTML-страницы для детальной страницы
  async function generateHtmlPage(id) {
    try {
      // Запрашиваем у пользователя путь для генерации (опционально)
      let outputPath = "cars";
      const outputModal = document.createElement("div");
      outputModal.className = "modal";
      outputModal.style.display = "block";

      outputModal.innerHTML = `
                <div class="modal-content" style="max-width: 500px;">
                    <span class="modal-close">&times;</span>
                    <h3>Создание HTML страницы</h3>
                    <div style="margin: 20px 0;">
                        <label>Путь к директории (относительно public):</label>
                        <input type="text" id="output-path" value="cars" style="width: 100%; margin-top: 10px; padding: 8px;">
                    </div>
                    <div style="text-align: right;">
                        <button id="cancel-gen-btn" class="detail-action-btn detail-delete-btn">Отмена</button>
                        <button id="confirm-gen-btn" class="detail-action-btn detail-generate-btn">Создать</button>
                    </div>
                </div>
            `;

      document.body.appendChild(outputModal);

      // Обработчики кнопок
      return new Promise((resolve, reject) => {
        const closeBtn = outputModal.querySelector(".modal-close");
        const cancelBtn = outputModal.querySelector("#cancel-gen-btn");
        const confirmBtn = outputModal.querySelector("#confirm-gen-btn");
        const pathInput = outputModal.querySelector("#output-path");

        // Закрытие модального окна
        const closeModal = () => {
          outputModal.style.display = "none";
          document.body.removeChild(outputModal);
        };

        closeBtn.addEventListener("click", () => {
          closeModal();
          reject(new Error("Отменено пользователем"));
        });

        cancelBtn.addEventListener("click", () => {
          closeModal();
          reject(new Error("Отменено пользователем"));
        });

        confirmBtn.addEventListener("click", () => {
          outputPath = pathInput.value.trim();
          closeModal();
          resolve(outputPath);
        });
      })
        .then(async (outputPath) => {
          // Показываем уведомление о начале генерации
          showNotification("Информация", "Генерация HTML страницы...", "info");

          // Отправляем запрос на генерацию HTML
          const response = await fetch(`/api/car-details/${id}/generate`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ outputPath }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Ошибка сервера");
          }

          const result = await response.json();
          console.log("Ответ от сервера после генерации HTML:", result);

          // Показываем уведомление об успешной генерации
          showNotification(
            "Успех",
            `HTML страница создана: ${result.filePath}`,
            "success"
          );

          // Спрашиваем, хочет ли пользователь открыть созданную страницу
          const openModal = document.createElement("div");
          openModal.className = "modal";
          openModal.style.display = "block";

          openModal.innerHTML = `
                    <div class="modal-content" style="max-width: 500px;">
                        <span class="modal-close">&times;</span>
                        <h3>HTML страница создана</h3>
                        <div style="margin: 20px 0;">
                            <p>Страница успешно создана по пути: ${result.filePath}</p>
                        </div>
                        <div style="text-align: right;">
                            <button id="no-open-btn" class="detail-action-btn detail-delete-btn">Закрыть</button>
                            <button id="yes-open-btn" class="detail-action-btn detail-preview-btn">Открыть страницу</button>
                        </div>
                    </div>
                `;

          document.body.appendChild(openModal);

          // Обработчики кнопок
          const closeOpenBtn = openModal.querySelector(".modal-close");
          const noOpenBtn = openModal.querySelector("#no-open-btn");
          const yesOpenBtn = openModal.querySelector("#yes-open-btn");

          // Закрытие модального окна
          const closeOpenModal = () => {
            openModal.style.display = "none";
            document.body.removeChild(openModal);
          };

          closeOpenBtn.addEventListener("click", closeOpenModal);
          noOpenBtn.addEventListener("click", closeOpenModal);

          yesOpenBtn.addEventListener("click", () => {
            closeOpenModal();
            window.open(result.filePath, "_blank");
          });

          // Добавляем кнопку "Открыть" в карточку детали
          const detailCard = Array.from(
            document.querySelectorAll(".detail-card")
          ).find((card) => {
            const detailId = card.getAttribute("data-detail-id") || "";
            return detailId === id;
          });

          console.log(
            "Найдена карточка детали:",
            detailCard ? "Да" : "Нет",
            "ID:",
            id
          );

          if (detailCard) {
            // Проверяем, существует ли уже кнопка открытия страницы
            const existingOpenBtn = detailCard.querySelector(
              ".detail-open-page-btn"
            );
            console.log(
              "Существующая кнопка открытия:",
              existingOpenBtn ? "Да" : "Нет"
            );

            if (!existingOpenBtn) {
              const actionsContainer = detailCard.querySelector(
                ".detail-card-actions"
              );
              console.log(
                "Контейнер действий:",
                actionsContainer ? "Найден" : "Не найден"
              );

              if (actionsContainer) {
                const openPageBtn = document.createElement("button");
                openPageBtn.className =
                  "detail-action-btn detail-open-page-btn";
                openPageBtn.innerHTML =
                  '<i class="fas fa-external-link-alt"></i> Открыть страницу';
                openPageBtn.setAttribute("data-path", result.filePath);
                console.log("Устанавливаем путь кнопки:", result.filePath);

                openPageBtn.addEventListener("click", function () {
                  const path = this.getAttribute("data-path");
                  console.log("Открываем страницу по пути:", path);
                  window.open(path, "_blank");
                });

                // Добавляем кнопку в начало списка действий
                if (actionsContainer.firstChild) {
                  actionsContainer.insertBefore(
                    openPageBtn,
                    actionsContainer.firstChild
                  );
                } else {
                  actionsContainer.appendChild(openPageBtn);
                }
                console.log('Кнопка "Открыть страницу" добавлена');
              }
            } else {
              // Обновляем путь к файлу
              existingOpenBtn.setAttribute("data-path", result.filePath);
              console.log(
                "Обновлен путь существующей кнопки:",
                result.filePath
              );
            }
          }
        })
        .catch((error) => {
          if (error.message !== "Отменено пользователем") {
            console.error("Ошибка при генерации HTML страницы:", error);
            showNotification(
              "Ошибка",
              error.message || "Не удалось сгенерировать HTML страницу",
              "error"
            );
          }
        });
    } catch (error) {
      console.error("Ошибка при генерации HTML страницы:", error);
      showNotification(
        "Ошибка",
        error.message || "Не удалось сгенерировать HTML страницу",
        "error"
      );
    }
  }
});
