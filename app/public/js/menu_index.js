document.addEventListener('DOMContentLoaded', function() {
    const burger = document.getElementById('burgerBtn');
    const menu = document.getElementById('dropdownMenu');
    const hero = document.querySelector('.hero-stock') || document.querySelector('.hero');
    const body = document.body;
    const mobileHeader = document.querySelector('.mobile-header');
    const mobileLogo = document.querySelector('.mobile-header__logo-img');
  
    function closeMenu() {
      menu.classList.remove('active');
      burger.classList.remove('open');
      body.classList.remove('menu-open');
    }
  
    function openMenu() {
      menu.classList.add('active');
      burger.classList.add('open');
      body.classList.add('menu-open');
    }
  
    burger.addEventListener('click', function(e) {
      e.stopPropagation();
      if (menu.classList.contains('active')) {
        closeMenu();
      } else {
        openMenu();
      }
    });
  
    // Закрытие по клику вне меню и вне кнопки
    document.addEventListener('click', function(e) {
      if (menu.classList.contains('active') && !menu.contains(e.target) && e.target !== burger) {
        closeMenu();
      }
    });
  
    // Навигация по меню
    const menuLinks = menu.querySelectorAll('a');
    menuLinks.forEach(link => {
      link.addEventListener('click', function() {
        closeMenu();
      });
    });
  
    // Меняем цвет хедера при скролле
    function updateHeader() {
      if (!hero) return;
      const heroRect = hero.getBoundingClientRect();
      if (heroRect.bottom <= 0) {
        mobileHeader.style.background = '#fff';
        burger.classList.add('dark');
        if (mobileLogo) {
          mobileLogo.style.filter = 'brightness(0)';
        }
      } else {
        mobileHeader.style.background = 'transparent';
        burger.classList.remove('dark');
        if (mobileLogo) {
          mobileLogo.style.filter = 'none';
        }
      }
    }
  
    window.addEventListener('scroll', updateHeader);
    updateHeader();
  }); 