/* === Footer Styles === */
.footer {
  background: #000000;
  color: #fff;
  padding: 3rem 2vw 1rem 2vw;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 2rem;
}

.footer__info {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  padding-bottom: 1.5rem;
}

.footer__left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.footer__logo {
  margin-bottom: 1rem;
}

.footer__logo-img {
  height: 80px;
  width: auto;
  display: block;
  margin-right: 0;
}

.footer__address {
  margin: 0;
  padding: 0;
  line-height: 1.5;
}

.footer__right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.footer__nav {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.footer__nav a {
  color: #fff;
  font-weight: 700;
  font-size: 1.1rem;
  transition: color 0.2s;
  text-decoration: none;
}

.footer__nav a:hover {
  color: #b2c9d6;
}

.footer__copyright {
  width: 100%;
  color: #aaa;
  font-size: 1rem;
  margin-top: 1.5rem;
}

/* Реквизиты блок */
.footer__requisites {
  background: #181b1d;
  color: #a5c3d7;
  border-radius: 14px;
  padding: 1.5rem 2rem 1.2rem 2rem;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  font-size: 1.08rem;
  width: 100%;
}

.footer__requisites-title {
  font-weight: 700;
  color: #fff;
  font-size: 1.18rem;
  margin-bottom: 0.7em;
  letter-spacing: 0.01em;
  text-align: right;
}

.footer__requisites-info {
  line-height: 1.5;
  font-size: 1.05rem;
  text-align: right;
}

/* Desktop Styles */
@media (min-width: 601px) {
  .footer__info {
    display: grid;
    grid-template-columns: 1fr auto;
    grid-template-areas: "left right";
    align-items: flex-start;
    width: 100%;
    padding-bottom: 2rem;
    gap: 0 4rem;
  }
  
  .footer__left {
    grid-area: left;
  }
  
  .footer__right {
    grid-area: right;
    max-width: 540px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
  
  .footer__nav {
    justify-content: flex-end;
    margin-bottom: 0.5rem;
  }

  .footer__requisites {
    width: 350px;
    max-width: 350px;
    margin-left: 1rem;
    margin-right: 1rem;
    margin-top: 2.5rem;
  }
}

/* Mobile Styles */
@media (max-width: 600px) {
  .footer {
    padding: 2rem 15px 1rem 15px;
  }
  
  .footer__info {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .footer__left {
    width: 100%;
    align-items: center;
    text-align: center;
  }
  
  .footer__logo {
    margin-bottom: 15px;
  }
  
  .footer__logo-img {
    max-width: 100px;
    margin: 0 auto;
  }
  
  .footer__address {
    font-size: 14px;
  }
  
  .footer__right {
    width: 100%;
    margin-top: 1.5rem;
    align-items: center;
    text-align: center;
  }
  
  .footer__nav {
    display: none !important; /* Hide navigation links on mobile */
  }
  
  .footer__requisites {
    width: 100%;
    padding: 1.2rem 1.5rem 1rem 1.5rem;
    text-align: center;
  }
  
  .footer__requisites-title {
    font-size: 16px;
    margin-bottom: 8px;
    text-align: center;
  }

  .footer__requisites-info {
    font-size: 14px;
    text-align: center;
  }
}

/* Tablet Styles */
@media (max-width: 700px) and (min-width: 601px) {
  .footer__requisites {
    padding: 1.2rem 1.5rem;
  }
  
  .footer__nav {
    gap: 1.5rem;
  }
  
  .footer__nav a {
    font-size: 1rem;
  }
} 