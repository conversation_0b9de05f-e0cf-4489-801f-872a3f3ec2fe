/* Modern car detail styles that match the reference design */
:root {
  --primary-bg: #ffffff;
  --text-color: #111111;
  --heading-color: #000000;
  --accent-color: #0d6efd;
  --border-radius: 8px;
  --section-spacing: 60px;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  color: var(--text-color);
  background-color: var(--primary-bg);
  line-height: 1.6;
}

/* Main layout improvements */
.car-detail__headline-row {
  max-width: 1140px;
  margin: 40px auto 30px;
  padding: 0 24px;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.car-detail__title {
  font-size: 2.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 6px;
  color: var(--heading-color);
}

.car-detail__price-label {
  font-size: 1rem;
  font-weight: 400;
  color: #555;
}

.car-detail__price {
  font-weight: 600;
  color: var(--heading-color);
}

/* Buy button styling */
.car-detail__buy-btn {
  background-color: var(--heading-color);
  color: white;
  padding: 18px 50px;
  border-radius: var(--border-radius);
  font-weight: 700;
  font-size: 1.2rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.2s;
  display: inline-block;
  text-align: center;
  text-decoration: none;
  min-width: 180px;
}

.car-detail__info-buy .car-detail__buy-btn {
  width: 100%;
  margin-top: 20px;
}

.car-detail__buy-btn:hover {
  background-color: #333;
  transform: translateY(-2px);
}

/* Gallery improvements */
.car-detail__gallery {
  max-width: 1140px;
  margin: 0 auto 40px;
  padding: 0 24px;
}

.car-detail__desc-gallery,
.car-detail__specs-gallery {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  gap: 18px;
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
}
.car-detail__desc-gallery {
  margin-top: 30px;
}

.car-detail__desc-gallery img,
.car-detail__specs-gallery img {
  width: 48%;
  height: 280px;
  object-fit: fill;
  margin: 0;
  padding: 0;
  border: 0;
  display: block;
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

/* Description and info section */
.car-detail__desc-row {
  max-width: 1140px;
  margin: 0 auto var(--section-spacing);
  padding: 0 24px;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 50px;
}

.car-detail__desc-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: var(--heading-color);
}

.car-detail__desc-text {
  font-size: 1.05rem;
  line-height: 1.7;
  color: #333;
  margin-bottom: 30px;
}

/* Info column styling */
.car-detail__info-col {
  background-color: #f9f9f9;
  border-radius: var(--border-radius);
  padding: 30px;
  height: fit-content;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.05);
}

.car-detail__info-price {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 5px;
  color: var(--heading-color);
}

.car-detail__info-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 25px;
}

.car-detail__info-blocks {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.car-detail__info-block {
  display: flex;
  align-items: center;
}

.car-detail__info-block-icon {
  width: 45px;
  height: 45px;
  background-color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.car-detail__info-block-icon img {
  width: 24px;
  height: auto;
}

.car-detail__info-block-text {
  font-size: 1.05rem;
  font-weight: 600;
}

.car-detail__info-block-label {
  display: block;
  font-size: 0.85rem;
  font-weight: 400;
  color: #666;
}

.car-detail__priority {
  background-color: white;
  padding: 20px;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  line-height: 1.5;
  color: #555;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.car-detail__priority b {
  display: block;
  margin-bottom: 5px;
  color: var(--heading-color);
}

/* Specs section */
.car-detail__specs-section {
  max-width: 1140px;
  margin: 0 auto var(--section-spacing);
  padding: 0 24px;
}

.car-detail__specs-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 30px;
  color: var(--heading-color);
}

.car-detail__specs-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: flex-start;
}

.specs-table-container {
  width: 100%;
  height: 100%;
}

.car-detail__specs-table {
  width: 100%;
  border-collapse: collapse;
}

.car-detail__specs-table td {
  padding: 16px 0;
  border-bottom: 1px solid #eee;
}

.car-detail__specs-table tr:last-child td {
  border-bottom: none;
}

.car-detail__specs-table td:first-child {
  font-weight: 500;
  color: #555;
}

.car-detail__specs-table td:last-child {
  text-align: right;
  font-weight: 600;
}

/* Best features section */
.car-detail__best-section {
  max-width: 1140px;
  margin: 0 auto var(--section-spacing);
  padding: 0 24px;
}

.car-detail__best-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 30px;
  color: var(--heading-color);
}

.car-detail__best-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

.car-detail__best-column h4 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
}

.car-detail__best-column ul {
  list-style-type: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.car-detail__best-column ul li {
  position: relative;
  padding: 0 !important;
  margin-bottom: 12px;
  font-size: 1rem;
  line-height: 1.5;
  list-style: none !important;
  list-style-type: none !important;
  display: block;
}

.car-detail__best-column ul li::marker,
.car-detail__best-column ul li::before {
  display: none !important;
  content: none !important;
}

/* Contact section */
.car-detail__contact-section {
  max-width: 1140px;
  margin: 60px auto var(--section-spacing);
  padding: 40px 60px;
  background-color: transparent;
  border-radius: var(--border-radius);
  text-align: center;
  position: relative;
  box-shadow: none;
  cursor: default;
  border-top: 1px solid #eee;
}

.car-detail__contact-section:hover .car-detail__contact-icon svg {
  transform: translateX(5px);
}

.car-detail__contact-icon {
  margin-bottom: 20px;
  cursor: pointer;
  display: inline-block;
  background-color: #000000;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.car-detail__contact-icon svg {
  transition: transform 0.3s ease;
}

.car-detail__contact-icon svg path {
  fill: #ffffff;
}

.car-detail__contact-label {
  font-size: 1rem;
  color: #666;
  margin-bottom: 10px;
  opacity: 0.8;
}

.car-detail__contact-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--heading-color);
  line-height: 1.3;
}

/* Tabs Navigation */
.car-detail__tabs {
  max-width: 1140px;
  margin: 30px auto 40px;
  display: flex;
  justify-content: center;
  gap: 50px;
}

.car-detail__tab {
  font-size: 1.1rem;
  font-weight: 600;
  color: #555;
  position: relative;
  transition: color 0.3s;
}

.car-detail__tab::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--heading-color);
  transform: scaleX(0);
  transition: transform 0.3s;
  transform-origin: center;
}

.car-detail__tab:hover,
.car-detail__tab.active {
  color: var(--heading-color);
}

.car-detail__tab:hover::after,
.car-detail__tab.active::after {
  transform: scaleX(1);
}

/* Interior colors dots */
.dot {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 900px) {
  .car-detail__desc-row {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .car-detail__specs-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .car-detail__best-columns {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .car-detail__headline-row {
    flex-direction: column;
    gap: 20px;
  }

  .car-detail__buy-btn {
    margin-left: 0;
    width: 100%;
  }

  .car-detail__tabs {
    gap: 30px;
  }
}

@media (max-width: 600px) {
  .car-detail__title {
    font-size: 2rem;
  }

  .car-detail__desc-gallery img,
  .car-detail__specs-gallery img {
    width: 100%;
    height: 250px;
    margin-bottom: 10px;
  }
  .car-detail__desc-gallery,
  .car-detail__specs-gallery {
    flex-direction: column;
  }

  .car-detail__contact-section {
    padding: 30px 20px;
  }

  .car-detail__contact-title {
    font-size: 1.7rem;
  }

  .car-detail__buy-btn {
    padding: 12px 30px;
    width: 100%;
    box-sizing: border-box;
  }
}
