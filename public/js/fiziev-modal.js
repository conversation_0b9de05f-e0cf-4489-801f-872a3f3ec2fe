// Модальное окно промо-страницы Физиева
class FizievModal {
  constructor() {
    this.modal = null;
    this.isOpen = false;
    this.init();
  }

  init() {
    // Проверяем URL параметр для автоматического открытия
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('fiziev-modal') === 'true') {
      this.open();
      // Убираем параметр из URL без перезагрузки
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }

  createModal() {
    const modalHTML = `
      <div id="fiziev-modal" class="fiziev-modal-overlay">
        <div class="fiziev-modal-container">
          <button class="fiziev-modal-close" onclick="fizievModal.close()">&times;</button>
          
          <div class="fiziev-modal-content">
            <div class="fiziev-logo">
              <div class="fiziev-logo-text">SHMS</div>
            </div>

            <h1 class="fiziev-title">
              ДОБРО ПОЖАЛОВАТЬ В SHMS<br>
              ЛУЧШИЙ СЕРВИС АВТОПОДБОРА!
            </h1>

            <div class="fiziev-main-content">
              <div class="fiziev-left-section">
                <p class="fiziev-welcome-text">
                  Ты перешёл по ссылке от <strong>Рафаэля Физиева</strong> — значит, ты уже победил.
                </p>

                <p class="fiziev-promo-intro">Специально для тебя — промокод ATAMAN:</p>

                <div class="fiziev-promo-code">ATAMAN</div>

                <button class="fiziev-copy-button" onclick="fizievModal.copyPromocode()">
                  Скопировать промокод
                </button>
              </div>

              <div class="fiziev-right-section">
                <h3 class="fiziev-gifts-title">ТО в подарок:</h3>
                <ul class="fiziev-gifts-list">
                  <li><span class="fiziev-icon">🛢️</span> Замена масла</li>
                  <li><span class="fiziev-icon">🔧</span> Замена воздушных и масляных фильтров</li>
                  <li><span class="fiziev-icon">🛑</span> Замена тормозных колодок</li>
                </ul>
                
                <p class="fiziev-champion-text">
                  Пройди путь чемпиона — выбери авто без лишних затрат и забот.
                </p>
              </div>
            </div>

            <div class="fiziev-footer">
              <p class="fiziev-final-text">
                Привоз авто из Кореи и Европы — <span class="fiziev-highlight">БЕЗ КОМИССИИ</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
    this.modal = document.getElementById('fiziev-modal');
    this.addEventListeners();
  }

  addEventListeners() {
    // Закрытие по клику на backdrop
    this.modal.addEventListener('click', (e) => {
      if (e.target === this.modal) {
        this.close();
      }
    });

    // Закрытие по Escape
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isOpen) {
        this.close();
      }
    });
  }

  open() {
    if (!this.modal) {
      this.createModal();
    }
    
    this.modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
    this.isOpen = true;
    
    // Анимация появления
    setTimeout(() => {
      this.modal.classList.add('fiziev-modal-show');
    }, 10);
  }

  close() {
    if (!this.modal || !this.isOpen) return;
    
    this.modal.classList.remove('fiziev-modal-show');
    document.body.style.overflow = '';
    this.isOpen = false;
    
    setTimeout(() => {
      this.modal.style.display = 'none';
    }, 300);
  }

  async copyPromocode() {
    const promoCode = 'ATAMAN';
    const button = document.querySelector('.fiziev-copy-button');

    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(promoCode);
      } else {
        // Fallback для старых браузеров
        const textArea = document.createElement('textarea');
        textArea.value = promoCode;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        textArea.remove();
      }

      // Показываем успешное копирование
      button.textContent = 'Скопировано!';
      button.classList.add('fiziev-success');

      setTimeout(() => {
        button.textContent = 'Скопировать промокод';
        button.classList.remove('fiziev-success');
      }, 2000);

    } catch (err) {
      console.error('Ошибка копирования: ', err);
      button.textContent = 'Ошибка копирования';

      setTimeout(() => {
        button.textContent = 'Скопировать промокод';
      }, 2000);
    }
  }
}

// Инициализация модального окна
let fizievModal;
document.addEventListener('DOMContentLoaded', () => {
  fizievModal = new FizievModal();
});

// Экспорт для использования в других скриптах
window.fizievModal = fizievModal;

// Глобальная функция для открытия модального окна
window.openFizievModal = function() {
  if (window.fizievModal) {
    window.fizievModal.open();
  } else {
    // Если модальное окно еще не инициализировано, создаем его
    window.fizievModal = new FizievModal();
    window.fizievModal.open();
  }
};
