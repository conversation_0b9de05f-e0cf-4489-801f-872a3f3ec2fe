#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const args = process.argv.slice(2);
const mode = args[0];

if (!mode || !['local', 'production'].includes(mode)) {
  console.log('🔧 Переключатель режимов SHMS Авто');
  console.log('');
  console.log('Использование:');
  console.log('  node switch-mode.js local      - переключить на локальную разработку');
  console.log('  node switch-mode.js production - переключить на продакшн');
  console.log('');
  console.log('Текущие файлы:');
  
  if (fs.existsSync('.env')) {
    const envContent = fs.readFileSync('.env', 'utf8');
    const nodeEnv = envContent.match(/NODE_ENV=(.+)/);
    if (nodeEnv) {
      console.log(`  .env: ${nodeEnv[1]}`);
    }
  }
  
  if (fs.existsSync('.env.production')) {
    console.log('  .env.production: существует');
  }
  
  process.exit(1);
}

function backupFile(filename) {
  if (fs.existsSync(filename)) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `${filename}.backup-${timestamp}`;
    fs.copyFileSync(filename, backupName);
    console.log(`📦 Создан бэкап: ${backupName}`);
  }
}

function switchToLocal() {
  console.log('🔧 Переключение на локальную разработку...');
  
  // Создаем бэкап текущего .env
  backupFile('.env');
  
  // Создаем локальный .env
  const localEnv = `# Локальная разработка
NODE_ENV=development

# Сервер
PORT=3000
HOST=localhost

# База данных
DB_PATH=./data/stock.db

# Настройки загрузки
UPLOAD_DIR=./data/uploads
MAX_FILE_SIZE=10485760

# Настройки почты для разработки (заглушки)
MAIL_HOST=localhost
MAIL_PORT=1025
MAIL_USER=test@localhost
MAIL_PASS=test
MAIL_FROM=test@localhost
MAIL_SECURE=false

# Безопасность для разработки
SESSION_SECRET=local-development-secret-key
SECURE_COOKIES=false
HTTPS_REDIRECT=false

# Домен и URL для локальной разработки
DOMAIN=localhost
PROTOCOL=http
SITE_URL=http://localhost:3000

# Логирование
LOG_LEVEL=debug

# Email для получения заявок (для тестирования)
MANAGER_EMAIL=test@localhost
CONTACT_EMAIL=test@localhost`;

  fs.writeFileSync('.env', localEnv);
  console.log('✅ Создан .env для локальной разработки');
  console.log('');
  console.log('🚀 Для запуска используйте:');
  console.log('   npm run local');
  console.log('   npm run dev:local  (с автоперезагрузкой)');
}

function switchToProduction() {
  console.log('🚀 Переключение на продакшн...');
  
  if (!fs.existsSync('.env.production')) {
    console.error('❌ Файл .env.production не найден!');
    process.exit(1);
  }
  
  // Создаем бэкап текущего .env
  backupFile('.env');
  
  // Копируем продакшн настройки
  fs.copyFileSync('.env.production', '.env');
  console.log('✅ Скопированы настройки из .env.production');
  console.log('');
  console.log('🚀 Для запуска используйте:');
  console.log('   npm start');
  console.log('   npm run production');
  console.log('   npm run pm2');
}

try {
  if (mode === 'local') {
    switchToLocal();
  } else if (mode === 'production') {
    switchToProduction();
  }
  
  console.log('');
  console.log('✅ Переключение завершено!');
} catch (error) {
  console.error('❌ Ошибка при переключении:', error.message);
  process.exit(1);
}
