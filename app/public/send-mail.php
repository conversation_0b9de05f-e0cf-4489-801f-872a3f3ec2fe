<?php
/**
 * Простая отправка почты через SMTP для Windows
 * Использует встроенные функции PHP без внешних библиотек
 */

function sendEmailSMTP($to, $subject, $message, $config) {
    // Настройки SMTP
    $smtp_host = $config['smtp_host'];
    $smtp_port = $config['smtp_port'];
    $smtp_user = $config['smtp_user'];
    $smtp_pass = $config['smtp_pass'];
    $from_email = $config['from_email'];
    $from_name = $config['from_name'];
    
    // Создаем заголовки
    $headers = "From: $from_name <$from_email>\r\n";
    $headers .= "Reply-To: $from_email\r\n";
    $headers .= "Content-Type: text/plain; charset=utf-8\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
    
    // Настройка ini для SMTP (временно)
    $original_smtp = ini_get('SMTP');
    $original_smtp_port = ini_get('smtp_port');
    $original_sendmail_from = ini_get('sendmail_from');
    
    // Устанавливаем настройки SMTP
    ini_set('SMTP', $smtp_host);
    ini_set('smtp_port', $smtp_port);
    ini_set('sendmail_from', $from_email);
    
    // Пытаемся отправить письмо
    $result = mail($to, $subject, $message, $headers);
    
    // Восстанавливаем оригинальные настройки
    ini_set('SMTP', $original_smtp);
    ini_set('smtp_port', $original_smtp_port);
    ini_set('sendmail_from', $original_sendmail_from);
    
    return $result;
}

function sendEmailCurl($to, $subject, $message, $config) {
    // Альтернативный способ через cURL и внешний API
    // Можно использовать SendGrid, Mailgun или другие сервисы
    
    // Для демонстрации используем простой webhook
    $webhook_url = "https://formspree.io/f/your-form-id"; // Замените на ваш webhook
    
    $data = [
        'to' => $to,
        'subject' => $subject,
        'message' => $message,
        'from' => $config['from_email']
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $webhook_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return $http_code >= 200 && $http_code < 300;
}

function sendEmailFile($to, $subject, $message, $config) {
    // Сохраняем письма в файл для локальной разработки
    $log_dir = __DIR__ . '/mail_logs';
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $filename = $log_dir . '/mail_' . date('Y-m-d_H-i-s') . '_' . uniqid() . '.txt';
    
    $email_content = "=== EMAIL LOG ===\n";
    $email_content .= "Date: " . date('Y-m-d H:i:s') . "\n";
    $email_content .= "To: $to\n";
    $email_content .= "Subject: $subject\n";
    $email_content .= "From: {$config['from_name']} <{$config['from_email']}>\n";
    $email_content .= "==================\n\n";
    $email_content .= $message . "\n\n";
    $email_content .= "==================\n";
    
    $result = file_put_contents($filename, $email_content);
    
    if ($result !== false) {
        error_log("Email saved to file: $filename");
        return true;
    }
    
    return false;
}

// Основная функция отправки
function sendEmail($to, $subject, $message, $config, $method = 'file') {
    error_log("Attempting to send email via method: $method");
    
    switch ($method) {
        case 'smtp':
            return sendEmailSMTP($to, $subject, $message, $config);
        case 'curl':
            return sendEmailCurl($to, $subject, $message, $config);
        case 'file':
        default:
            return sendEmailFile($to, $subject, $message, $config);
    }
}

// Тестовая функция
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    // Тест отправки
    $config = [
        'smtp_host' => 'smtp.yandex.ru',
        'smtp_port' => 465,
        'smtp_user' => '<EMAIL>',
        'smtp_pass' => 'SHMSAutogroup777!',
        'from_email' => '<EMAIL>',
        'from_name' => 'SHMS Авто Test'
    ];
    
    $result = sendEmail(
        '<EMAIL>',
        'Тест отправки почты',
        'Это тестовое сообщение для проверки работы почты.',
        $config,
        'file' // Используем file для локальной разработки
    );
    
    echo $result ? "Email sent successfully!" : "Failed to send email.";
}
?>
