const sqlite3 = require("sqlite3").verbose();
const db = new sqlite3.Database("./admin/js/stock.db");

console.log("Структура таблицы car_details:");
db.all("PRAGMA table_info(car_details)", (err, rows) => {
  if (err) {
    console.error("Ошибка:", err);
    return;
  }

  rows.forEach((col) => {
    console.log(
      `${col.name}: ${col.type} ${col.notnull ? "NOT NULL" : ""} ${
        col.dflt_value ? "DEFAULT " + col.dflt_value : ""
      }`
    );
  });

  db.close();
});
