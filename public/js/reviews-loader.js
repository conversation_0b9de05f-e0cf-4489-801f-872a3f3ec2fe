/**
 * SHMS Auto - Reviews Loader
 * Загружает отзывы из API и отображает их на главной странице
 */

document.addEventListener('DOMContentLoaded', function() {
    // Загружаем отзывы с сервера
    loadReviewsFromAPI();
    
    // Проверяем наличие параметра review в URL
    checkReviewInUrl();
});

/**
 * Загружает отзывы из API и отображает их на странице
 */
function loadReviewsFromAPI() {
    fetch('/api/reviews')
        .then(response => response.json())
        .then(reviews => {
            if (reviews.length === 0) {
                console.log('Нет доступных отзывов');
                return;
            }
            
            // Сортируем отзывы по дате (новые сначала)
            reviews.sort((a, b) => new Date(b.date) - new Date(a.date));
            
            // Ограничиваем количество отзывов для отображения на главной странице
            const displayReviews = reviews.slice(0, 3);
            
            // Получаем контейнер для отзывов
            const reviewsList = document.querySelector('.reviews__list');
            
            // Если контейнер найден, заполняем его отзывами
            if (reviewsList) {
                reviewsList.innerHTML = '';
                
                displayReviews.forEach(review => {
                    const reviewElement = createReviewElement(review);
                    reviewsList.appendChild(reviewElement);
                });
            }
            
            // Обновляем список отзывов в модальном окне
            updateReviewsModal(reviews);
        })
        .catch(error => {
            console.error('Ошибка при загрузке отзывов:', error);
        });
}

/**
 * Создает элемент отзыва для главной страницы
 * @param {Object} review - Данные отзыва
 * @returns {HTMLElement} - DOM-элемент отзыва
 */
function createReviewElement(review) {
    const reviewElement = document.createElement('div');
    reviewElement.className = 'review';
    reviewElement.id = `review-${review.id}`;
    reviewElement.style.backgroundImage = `url('${review.image}')`;
    
    // Форматируем дату
    const dateObj = new Date(review.date);
    const formattedDate = dateObj.toLocaleDateString('ru-RU', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
    });
    
    // Создаем содержимое отзыва
    reviewElement.innerHTML = `
        <div class="review__gradient">
            <div class="review__date">${formattedDate}</div>
            <button class="review__arrow" aria-label="Подробнее">
                <svg width="48" height="36" viewBox="0 0 48 36" fill="none">
                    <rect x="0" y="0" width="48" height="36" rx="12" fill="black" />
                    <path d="M14 18H34" stroke="white" stroke-width="3.5" stroke-linecap="round" />
                    <path d="M29.5 13.5L34 18L29.5 22.5" stroke="white" stroke-width="3.5" stroke-linecap="round"
                        stroke-linejoin="round" />
                </svg>
            </button>
            <div class="review__content">
                <div class="review__title">${review.title}</div>
                <div class="review__desc">${getShortDescription(review.content)}</div>
            </div>
        </div>
    `;
    
    // Добавляем обработчик клика
    reviewElement.addEventListener('click', function() {
        openReviewModal(review);
    });
    
    return reviewElement;
}

/**
 * Получает краткое описание из содержания отзыва
 * @param {string} content - HTML-содержание отзыва
 * @returns {string} - Краткое описание
 */
function getShortDescription(content) {
    // Удаляем HTML-теги
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;
    const textContent = tempDiv.textContent || tempDiv.innerText || '';
    
    // Ограничиваем длину
    return textContent.length > 120 
        ? textContent.substring(0, 120) + '...' 
        : textContent;
}

/**
 * Обновляет содержимое модального окна со списком отзывов
 * @param {Array} reviews - Список отзывов
 */
function updateReviewsModal(reviews) {
    const reviewsModalList = document.querySelector('.reviews-modal__list');
    
    if (reviewsModalList) {
        reviewsModalList.innerHTML = '';
        
        reviews.forEach(review => {
            const reviewItem = document.createElement('div');
            reviewItem.className = 'reviews-modal__item';
            reviewItem.setAttribute('data-review-id', review.id);
            
            // Форматируем дату
            const dateObj = new Date(review.date);
            const formattedDate = dateObj.toLocaleDateString('ru-RU', {
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            });
            
            reviewItem.innerHTML = `
                <img src="${review.image}" alt="${review.title}" class="reviews-modal__item-img">
                <div class="reviews-modal__item-content">
                    <div class="reviews-modal__item-title">${review.title}</div>
                    <div class="reviews-modal__item-date">${formattedDate}</div>
                </div>
            `;
            
            // Добавляем обработчик клика
            reviewItem.addEventListener('click', function() {
                // Закрываем модальное окно со списком отзывов
                document.getElementById('reviewsModal').classList.remove('active');
                
                // Открываем модальное окно с отзывом
                openReviewModal(review);
            });
            
            reviewsModalList.appendChild(reviewItem);
        });
    }
}

/**
 * Открывает модальное окно с отзывом
 * @param {Object} review - Данные отзыва
 */
function openReviewModal(review) {
    // Проверяем, существует ли модальное окно для этого отзыва
    let modalId = `review-article-${review.id}`;
    let modal = document.getElementById(modalId);
    
    // Если модальное окно не существует, создаем его
    if (!modal) {
        modal = createReviewModal(review);
        document.body.appendChild(modal);
    }
    
    // Открываем модальное окно
    modal.classList.add('active');
}

/**
 * Создает модальное окно для отзыва
 * @param {Object} review - Данные отзыва
 * @returns {HTMLElement} - DOM-элемент модального окна
 */
function createReviewModal(review) {
    const modal = document.createElement('div');
    modal.className = 'review-article-modal';
    modal.id = `review-article-${review.id}`;
    
    // Форматируем дату
    const dateObj = new Date(review.date);
    const formattedDate = dateObj.toLocaleDateString('ru-RU', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
    });
    
    modal.innerHTML = `
        <div class="review-article-modal__content">
            <span class="review-article-modal__close">&times;</span>
            <div class="review-article-modal__header">
                <img src="${review.image}" alt="${review.title}" class="review-article-modal__image">
                <div>
                    <h2 class="review-article-modal__title">${review.title}</h2>
                    <div class="review-article-modal__date">${formattedDate}</div>
                    ${review.author ? `<div class="review-article-modal__author">Автор: ${review.author}</div>` : ''}
                    ${review.carModel ? `<div class="review-article-modal__car">Автомобиль: ${review.carModel}</div>` : ''}
                </div>
            </div>
            <div class="review-article-modal__body">
                ${review.content}
            </div>
        </div>
    `;
    
    // Добавляем обработчики событий
    const closeBtn = modal.querySelector('.review-article-modal__close');
    closeBtn.addEventListener('click', function() {
        modal.classList.remove('active');
    });
    
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.classList.remove('active');
        }
    });
    
    return modal;
}

/**
 * Проверяет наличие параметра review в URL и открывает соответствующий отзыв
 */
function checkReviewInUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    const reviewId = urlParams.get('review');
    
    if (reviewId) {
        // Загружаем отзыв по ID
        fetch(`/api/reviews/${reviewId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Отзыв не найден');
                }
                return response.json();
            })
            .then(review => {
                // Открываем модальное окно с отзывом
                setTimeout(() => {
                    openReviewModal(review);
                }, 500);
            })
            .catch(error => {
                console.error('Ошибка при загрузке отзыва:', error);
            });
    }
} 