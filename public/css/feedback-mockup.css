/* ====== Общие стили ====== */
html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: "Inter", Arial, sans-serif;
  background: #fff;
  color: #222;
  box-sizing: border-box;
}

body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ====== Main Content ====== */
.main {
  margin-top: 120px;
  flex: 1 0 auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* ====== Стили для главного заголовка ====== */
.feedback__main-title {
  font-size: 2.8rem;
  font-weight: 700;
  margin: 2rem 0 1.5rem 0;
  text-align: center;
  color: #222;
}

.feedback {
  margin-top: 60px;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2vw 48px 2vw;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.feedback__title {
  margin-top: 60px;
  font-size: 2.8rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-align: center;
}
.feedback__subtitle {
  font-size: 1.15rem;
  color: #666;
  margin-bottom: 2.5rem;
  text-align: center;
}

.feedback__tips {
  display: flex;
  justify-content: center;
  align-items: stretch;
  gap: 2.5rem;
  background: none;
  margin-bottom: 2.5rem;
  width: 100%;
  max-width: 1400px;
}
.feedback__tip {
  background: #f2e6d9;
  border-radius: 16px;
  padding: 2.2rem 2.5rem 2.2rem 2.5rem;
  flex: 1 1 0;
  display: flex;
  align-items: flex-start;
  gap: 1.2rem;
  min-width: 220px;
  box-sizing: border-box;
  font-size: 1.18rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}
.feedback__check {
  color: #bfa76a;
  font-size: 2rem;
  margin-top: 0.2em;
  flex-shrink: 0;
}
.feedback__tip p {
  margin: 0;
  color: #444;
  font-size: 1.13rem;
  line-height: 1.6;
}

/* ====== Feedback Form ====== */
.feedback__form {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  padding: 2.5rem 2.5rem 2rem 2.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}
.feedback__form-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-align: center;
}
.feedback__form-group {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
}
.feedback__form-group label {
  font-size: 1.08rem;
  color: #222;
  font-weight: 500;
}
.feedback__form-group input,
.feedback__form-group select,
.feedback__form-group textarea {
  padding: 0.7rem 1rem;
  border: none;
  border-bottom: none;
  background: #fafafa;
  font-size: 1.08rem;
  border-radius: 4px 4px 0 0;
  outline: none;
  transition: border-color 0.2s;
  position: relative;
}

/* Создаем узкую линию под полем ввода с отступом от левого края */
.feedback__form-group {
  position: relative;
}

.feedback__form-group::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 2rem;
  height: 2px;
  background: #000;
  transition: all 0.3s ease;
}
.feedback__form-group input:focus,
.feedback__form-group select:focus,
.feedback__form-group textarea:focus {
  border-bottom: none;
}

/* Изменение цвета линии при фокусе */
.feedback__form-group:focus-within::after {
  background: #6f9aab;
  left: 0;
  right: 1.5rem;
}
.feedback__form-group textarea {
  resize: vertical;
  min-height: 60px;
  max-height: 200px;
}
.feedback__submit {
  margin-top: 1.5rem;
  padding: 0.9rem 0;
  background: #000;
  color: #fff;
  font-size: 1.18rem;
  font-weight: 600;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: background 0.2s;
}
.feedback__submit:hover {
  background: #222;
}

/* ====== Стили для промокода ====== */
.promo-indicator {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Стили для поля промокода */
#promo {
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

#promo.valid {
  border-color: #28a745 !important;
  box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.25);
}

#promo.invalid {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
}

/* ====== Стили для поля URL автомобиля (скрытое от пользователей) ====== */
#carUrlGroup {
  display: none !important; /* Гарантированно скрываем от пользователей */
}

#carUrl {
  background-color: #f5f5f5 !important;
  color: #666 !important;
  cursor: not-allowed !important;
  border: 1px solid #ddd !important;
  font-family: monospace;
  font-size: 0.9rem !important;
}

#carUrl:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(111, 154, 171, 0.3) !important;
}

#carUrlGroup small {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
  display: block;
  font-style: italic;
}

/* ====== Адаптивность ====== */
@media (max-width: 1100px) {
  .feedback__tips {
    flex-direction: column;
    gap: 1.5rem;
  }
  .feedback__tip {
    min-width: 0;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .main {
    margin-top: 0.5rem !important;
  }
  .main {
    padding: 0 0.5rem;
  }
  .feedback__form {
    padding: 1.2rem 0.7rem;
  }
  .feedback__main-title {
    font-size: 2.2rem;
    margin: 1.5rem 0 1rem 0;
  }
  .feedback__title {
    font-size: 2rem;
  }
  .feedback__form-title {
    font-size: 1.2rem;
  }
}

/* Mobile styles for feedback form - comprehensive version */
@media (max-width: 600px) {
  html,
  body {
    width: 100vw !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
    overflow-y: auto !important;
    height: 100% !important;
  }
  body {
    min-width: 100vw !important;
    box-sizing: border-box !important;
    min-height: 100vh !important;
  }
  .main {
    width: 100vw !important;
    max-width: 100vw !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
  }
  .feedback {
    width: 100vw !important;
    max-width: 100vw !important;
    padding: 0 0 30px 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;
  }
  .feedback__main-title {
    font-size: 28px;
    margin: 15px 0 20px 0;
    text-align: center;
    padding: 0 10px;
  }

  .feedback__title {
    font-size: 24px;
    margin-top: 10px;
    text-align: center;
  }

  .feedback__subtitle {
    font-size: 16px;
    margin-bottom: 20px;
    text-align: center;
  }

  .feedback__form-title {
    font-size: 18px;
    margin-bottom: 15px;
    text-align: center;
  }

  .feedback__form {
    width: 100vw !important;
    max-width: 100vw !important;
    padding: 10px 0.5rem !important;
    border-radius: 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;
  }

  .feedback__form-group {
    margin-bottom: 8px;
    width: 100%;
    transition: transform 0.2s;
  }

  /* Активное состояние группы формы */
  .feedback__form-group.active {
    transform: translateY(-2px);
  }

  .feedback__form-group label {
    font-size: 14px;
    margin-bottom: 4px;
    font-weight: 600;
    transition: color 0.2s;
  }

  /* Цвет лейбла при фокусе на поле */
  .feedback__form-group.active label {
    color: #6f9aab;
  }

  .feedback__form-group input,
  .feedback__form-group select,
  .feedback__form-group textarea {
    padding: 12px;
    font-size: 16px; /* Larger font size for better touch interaction */
    border-radius: 8px 8px 0 0;
    border: none;
    border-bottom: none;
    background-color: #fff;
    height: auto;
    -webkit-appearance: none; /* Remove default iOS styles */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: border-color 0.2s, box-shadow 0.2s;
    position: relative;
  }

  /* Узкая линия под полем ввода для мобильных - с отступом от левого края */
  .feedback__form-group {
    position: relative;
  }

  .feedback__form-group::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 1rem;
    height: 2px;
    background: #000;
    transition: all 0.3s ease;
  }

  /* Стили для полей в фокусе */
  .feedback__form-group input:focus,
  .feedback__form-group select:focus,
  .feedback__form-group textarea:focus {
    border-color: transparent;
    box-shadow: 0 2px 8px rgba(111, 154, 171, 0.1);
    outline: none;
  }

  /* Изменение цвета линии при фокусе для мобильных */
  .feedback__form-group:focus-within::after {
    background: #6f9aab;
    left: 0;
    right: 0.7rem;
  }

  /* Стили для невалидных полей */
  .feedback__form-group input:invalid:not(:placeholder-shown),
  .feedback__form-group select:invalid:not(:placeholder-shown),
  .feedback__form-group textarea:invalid:not(:placeholder-shown) {
    border-color: #e53e3e;
  }

  /* Fix for select dropdowns on mobile */
  .feedback__form-group select {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 36px;
  }

  /* Special styling for the checkbox label to ensure it's readable */
  .checkbox-label {
    font-size: 13px;
    line-height: 1.3;
    display: flex;
    align-items: flex-start;
    margin-top: 10px;
    margin-bottom: 5px;
    padding: 0 5px;
  }

  .checkbox-label input[type="checkbox"] {
    margin-right: 8px;
    min-width: 20px;
    height: 20px;
    margin-top: 2px;
  }

  /* Submit button styling */
  .feedback__submit {
    padding: 14px;
    font-size: 16px;
    border-radius: 8px;
    margin-top: 15px;
    background-color: #000;
    color: #fff;
    font-weight: 600;
    transition: background-color 0.2s, transform 0.2s;
  }

  .feedback__submit:hover,
  .feedback__submit:active {
    background-color: #333;
  }

  /* Анимация нажатия на кнопку */
  .feedback__submit:active {
    transform: scale(0.98);
  }
}
