#!/bin/bash

# Простой скрипт запуска SHMS Авто на сервере
echo "🚀 Запуск SHMS Авто..."

# Остановка старых процессов
echo "Остановка старых процессов..."
pm2 delete shms-auto 2>/dev/null || true
pkill -f "node server.js" 2>/dev/null || true

# Установка зависимостей
echo "Установка зависимостей..."
npm install

# Создание необходимых папок
echo "Создание папок..."
mkdir -p logs
mkdir -p data/uploads
mkdir -p data/cache

# Установка прав
chmod 755 data/uploads
chmod 755 logs
chmod +x deploy-regru.sh 2>/dev/null || true
chmod +x start-server.sh

# Запуск через PM2
echo "Запуск приложения..."
pm2 start ecosystem.config.js --env production

# Сохранение конфигурации PM2
pm2 save

echo "✅ Готово! Проверьте статус: pm2 list"
echo "📋 Логи: pm2 logs shms-auto"
echo "🌐 Сайт должен работать на вашем домене"
