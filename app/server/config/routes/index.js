const express = require("express");
const router = express.Router();
const path = require("path");
const fs = require("fs");

// Заглушка для маршрутов админки
router.get("/admin-test", (req, res) => {
  res.json({ message: "Маршрутизатор админки работает!" });
});

// Перенаправляем все запросы на основные маршруты
router.all("*", (req, res, next) => {
  try {
    // Пробуем найти основной маршрутизатор
    const mainRouter = require("../routes");

    // Если нашли, передаем запрос ему
    console.log("Перенаправление запроса на основной маршрутизатор:", req.url);
    return mainRouter(req, res, next);
  } catch (e) {
    console.error("Ошибка при перенаправлении на основной маршрутизатор:", e);
    next();
  }
});

module.exports = router;
