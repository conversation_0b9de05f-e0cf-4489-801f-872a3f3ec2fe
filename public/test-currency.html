<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест новой системы курсов валют</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            font-weight: bold;
        }
        .error {
            background: #ffe7e7;
            border-color: #ffb3b3;
            color: #d00;
        }
        .success {
            background: #e7ffe7;
            border-color: #b3ffb3;
            color: #0a0;
        }
        .debug-info {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Тест новой системы курсов валют</h1>
        
        <div class="test-section">
            <h3>📊 Информация о курсах</h3>
            <button onclick="loadRatesInfo()">Загрузить информацию о курсах</button>
            <div id="rates-info" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>💱 Тест конвертации KRW → USD</h3>
            <div class="input-group">
                <label for="krw-amount">Цена в корейских вонах (как на сайте):</label>
                <input type="number" id="krw-amount" placeholder="Например: 3500" value="3500">
            </div>
            <button onclick="testKRWToUSD()">Рассчитать цену в долларах</button>
            <div id="krw-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🚗 Тест расчета цены автомобиля</h3>
            <div class="input-group">
                <label for="car-price">Цена автомобиля (как на Encar):</label>
                <input type="number" id="car-price" placeholder="Например: 2800" value="2800">
            </div>
            <button onclick="testCarPrice()">Рассчитать итоговую цену</button>
            <div id="car-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔧 Отладочная информация</h3>
            <button onclick="showDebugInfo()">Показать отладочную информацию</button>
            <div id="debug-info" class="debug-info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🌐 Тест API</h3>
            <button onclick="testCBRAPI()">Тест API ЦБ РФ</button>
            <button onclick="testExchangeAPI()">Тест API обменника</button>
            <div id="api-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script src="js/currency-converter.js"></script>
    <script>
        async function loadRatesInfo() {
            const resultDiv = document.getElementById('rates-info');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Загрузка...';
            
            try {
                const rates = await window.currencyConverter.getRates();
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>Текущие курсы:</strong><br>
                    • Корейская вона к рублю: ${rates.KRW_to_RUB}<br>
                    • Курсы доллара: ${JSON.stringify(rates.USD_rates, null, 2)}
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Ошибка: ${error.message}`;
            }
        }

        async function testKRWToUSD() {
            const krwAmount = document.getElementById('krw-amount').value;
            const resultDiv = document.getElementById('krw-result');
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Расчет...';
            
            try {
                const usdAmount = await window.currencyConverter.convertKRWToUSD(krwAmount * 10000);
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>Результат конвертации:</strong><br>
                    ${krwAmount} (сайт) → ${krwAmount * 10000} KRW → ${usdAmount.toFixed(2)} USD
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Ошибка: ${error.message}`;
            }
        }

        async function testCarPrice() {
            const carPrice = document.getElementById('car-price').value;
            const resultDiv = document.getElementById('car-result');
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Расчет...';
            
            try {
                const finalPrice = await window.currencyConverter.calculateCarPrice(carPrice);
                const formattedPrice = window.currencyConverter.formatUSDPrice(finalPrice);
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>Итоговая цена автомобиля:</strong><br>
                    Цена на сайте: ${carPrice}<br>
                    Реальная цена: ${carPrice * 10000} KRW<br>
                    Цена в долларах: ${formattedPrice}
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Ошибка: ${error.message}`;
            }
        }

        async function showDebugInfo() {
            const debugDiv = document.getElementById('debug-info');
            debugDiv.style.display = 'block';
            debugDiv.innerHTML = 'Загрузка отладочной информации...';
            
            try {
                const debugInfo = await window.currencyConverter.getDebugInfo();
                debugDiv.innerHTML = JSON.stringify(debugInfo, null, 2);
            } catch (error) {
                debugDiv.innerHTML = `Ошибка: ${error.message}`;
            }
        }

        async function testCBRAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Тестирование API ЦБ РФ...';
            
            try {
                const response = await fetch('/api/cbr-rates.php');
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>API ЦБ РФ:</strong><br>
                    ${JSON.stringify(data, null, 2)}
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Ошибка API ЦБ РФ: ${error.message}`;
            }
        }

        async function testExchangeAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Тестирование API обменника...';
            
            try {
                const response = await fetch('/api/exchange-rates.php');
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>API обменника:</strong><br>
                    ${JSON.stringify(data, null, 2)}
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Ошибка API обменника: ${error.message}`;
            }
        }

        // Автоматически загружаем информацию о курсах при загрузке страницы
        window.addEventListener('load', () => {
            setTimeout(loadRatesInfo, 1000);
        });
    </script>
</body>
</html>
