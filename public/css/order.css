/* === Общие стили и сброс === */
@font-face {
  font-family: "Inter";
  src: url("/assets/fonts/Inter-VariableFont_opsz,wght.ttf") format("truetype");
  font-weight: 100 900;
  font-style: normal;
}

@font-face {
  font-family: "Inter";
  src: url("/assets/fonts/Inter-Italic-VariableFont_opsz,wght.ttf")
    format("truetype");
  font-weight: 100 900;
  font-style: italic;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
body {
  font-family: "Inter", Arial, sans-serif;
  background: #fff;
  color: #111;
  min-height: 100vh;
  padding: 0;
  overflow-x: hidden;
}
a {
  color: inherit;
  text-decoration: none;
}
img {
  max-width: 100%;
  display: block;
}

/* === Header === */
.hero-header-group {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 110px;
  padding: 0 2vw;
  background: #fff;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
  position: relative;
}

.header__center-group {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2.5rem;
}

.header__logo-link {
  display: flex;
  align-items: center;
  margin-right: 2.5rem;
}

.header__logo-img {
  height: 95px;
  width: auto;
  display: block;
  background: #fff;
  vertical-align: middle;
  align-self: center;
}

.header__nav {
  display: flex;
  align-items: center;
  gap: 3.5rem;
}

.header__back-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #333;
  text-decoration: none;
  font-size: 1rem;
  transition: all 0.3s ease;
  padding: 0 12px;
  border-radius: 4px;
  height: 48px;
  line-height: 48px;
  position: static;
  background: none;
  z-index: 2;
}

.header__back-link:hover {
  color: #000;
  background: rgba(0, 0, 0, 0.05);
  transform: translateX(-2px);
}

.header__nav-link {
  font-size: 1.1rem;
  color: #111;
  transition: color 0.2s;
  line-height: 95px;
  display: flex;
  align-items: center;
}

.header__nav-link.active {
  color: #000;
  text-decoration: none;
}

.header__nav-link:hover {
  color: #6f9aab;
}

/* === Hero Section === */
.hero-stock {
  background: #fff;
  position: relative;
  padding-bottom: 0;
}

.hero-stock--custom {
  background-color: #f5f5f5;
  min-height: 250px;
  padding: 40px 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.hero-stock--custom::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(
    circle at right,
    rgba(224, 229, 232, 0.95) 0%,
    rgba(224, 229, 232, 0.9) 10%,
    rgba(224, 229, 232, 0.7) 30%,
    rgba(224, 229, 232, 0.4) 60%,
    rgba(224, 229, 232, 0) 80%
  );
  background-size: 100% 100%;
  background-position: 100% 0%;
  background-repeat: no-repeat;
  z-index: 1;
}

.hero-stock__visual {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.hero-stock__center {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 800px;
  padding: 20px;
  width: 100%;
}

.hero-stock__title-main {
  font-size: 36px;
  font-weight: 500;
  margin-bottom: 40px;
  text-align: center;
}

/* === Search Container === */
.search-container {
  display: flex;
  width: 100%;
  max-width: 600px;
  margin-bottom: 20px;
}

.search-input {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid #ccc;
  border-radius: 4px 0 0 4px;
  font-size: 16px;
}

.search-button {
  padding: 12px 25px;
  background-color: #000;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  font-size: 16px;
}

.search-button:hover {
  background-color: #333;
}

.search-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid white;
  border-radius: 50%;
  margin-right: 8px;
  position: relative;
}

.search-icon::after {
  content: "";
  position: absolute;
  width: 2px;
  height: 8px;
  background: white;
  bottom: -5px;
  right: -2px;
  transform: rotate(45deg);
}

.gradient-oval {
  display: none;
}

@keyframes pulse {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

/* === Order Cards === */
.order-content {
  padding: 40px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.order-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.order-card {
  position: relative;
  border: 1px solid #eaeaea;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
  background-color: white;
}

.order-card-clickable {
  cursor: pointer;
}

.order-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
  border-color: #ddd;
}

.order-card-clickable:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
  border-color: #ccc;
}

.order-card-image {
  height: 350px;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.order-card-image img {
  width: 100%;
  height: 100%;
  object-fit: fill;
  transition: transform 0.3s;
}

/* Резервные фоновые изображения для карточек по маркам */
.order-card[data-brand="BMW"] .order-card-image {
  background-image: url("/assets/img/cars/bmw-m5.jpg");
  background-size: cover;
  background-position: center;
}

.order-card[data-brand="Bentley"] .order-card-image {
  background-image: url("/assets/img/cars/bentley-continental.jpg");
  background-size: cover;
  background-position: center;
}

.order-card[data-brand="Aston Martin"] .order-card-image {
  background-image: url("/assets/img/cars/aston-martin.jpg");
  background-size: cover;
  background-position: center;
}

.order-card[data-brand="Audi"] .order-card-image {
  background-image: url("/assets/img/cars/audi-rs7.jpg");
  background-size: cover;
  background-position: center;
}

.order-card[data-brand="Ferrari"] .order-card-image {
  background-image: url("/assets/img/cars/ferrari.jpg");
  background-size: cover;
  background-position: center;
}

.order-card[data-brand="Lamborghini"] .order-card-image {
  background-image: url("/assets/img/cars/lamborghini.jpg");
  background-size: cover;
  background-position: center;
}

.order-card:hover .order-card-image img {
  transform: scale(1.05);
}

.order-card-clickable:hover .order-card-img img {
  transform: scale(1.08);
}

/* Стили для ссылки на источник */
.car-source-link {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.95);
  color: #666;
  padding: 6px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 15;
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  backdrop-filter: blur(5px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.car-source-link:hover {
  background: rgba(255, 255, 255, 1);
  color: #333;
  border-color: rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.car-source-link svg {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

.order-card-content {
  padding: 20px;
}

.order-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.order-card-logo {
  width: 50px;
  height: 50px;
  margin-right: 15px;
  flex-shrink: 0;
}

.order-card-logo img {
  width: 100%;
  height: 100%;
  object-fit: fill;
}

.order-card-title {
  margin: 0;
  font-size: 20px;
  line-height: 1.3;
  font-weight: 500;
}

.order-card-title--small {
  font-size: 18px;
}

.order-card-specs {
  margin-bottom: 15px;
}

.order-card-spec {
  font-size: 14px;
  color: #777;
  margin-bottom: 5px;
}

.order-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.order-card-mileage {
  font-size: 14px;
  color: #666;
}

.order-card-price {
  font-size: 18px;
  font-weight: 600;
}

/* Стили для неактивных (проданных) авто */
.order-card--inactive {
  opacity: 0.8;
}

.order-card-sold-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(255, 59, 48, 0.9);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  z-index: 1;
}

/* === Mobile Header === */
.mobile-header {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: #fff;
  z-index: 1000;
  padding: 0 20px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
}

.mobile-header__logo {
  position: static;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.mobile-header__logo-img {
  height: 80px;
  width: auto;
}

/* Пустой раздел для отступа */
.mobile-header-spacer {
  display: none;
  height: 80px;
  width: 100%;
}

/* === Mobile Menu === */
.burger {
  display: none;
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: none;
  border: none;
  cursor: pointer;
  gap: 6px;
  z-index: 1001;
}

.burger span {
  display: block;
  width: 32px;
  height: 4px;
  background: #111;
  border-radius: 2px;
  transition: transform 0.3s cubic-bezier(0.4, 1.3, 0.6, 1), opacity 0.25s;
}

.burger.open span:nth-child(1) {
  transform: translateY(10px) rotate(45deg);
}

.burger.open span:nth-child(2) {
  opacity: 0;
}

.burger.open span:nth-child(3) {
  transform: translateY(-10px) rotate(-45deg);
}

.burger.dark span {
  background: #111;
}

.dropdown-menu {
  position: fixed;
  top: 64px;
  left: 16px;
  min-width: 260px;
  width: auto;
  height: auto;
  max-height: 90vh;
  background: #111;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
  padding: 32px 36px 24px 36px;
  z-index: 1500;
  display: none;
  flex-direction: column;
  gap: 18px;
  opacity: 0;
  pointer-events: none;
  transform: translateY(-20px) scale(0.98);
  transition: opacity 0.28s cubic-bezier(0.4, 1.3, 0.6, 1),
    transform 0.28s cubic-bezier(0.4, 1.3, 0.6, 1);
  overflow-y: auto;
}

.dropdown-menu.active {
  display: flex;
  opacity: 1;
  pointer-events: auto;
  transform: translateY(0) scale(1);
}

.dropdown-menu a {
  color: #fff;
  font-size: 1.15rem;
  text-decoration: none;
  transition: color 0.18s;
  font-family: inherit;
  padding: 5px 0;
}

.dropdown-menu a:hover,
.dropdown-menu a.active {
  color: #b2c9d6;
}

/* === Media Queries === */
@media (max-width: 900px) {
  .header__nav {
    gap: 2rem;
  }

  .header__logo-img {
    height: 70px;
  }

  .order-cards {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .hero-stock__title-main {
    font-size: 2.8rem;
  }
}

@media (max-width: 1200px) {
  .hero-stock__title-main {
    font-size: 2.8rem;
  }

  .hero-stock--custom::before {
    background-size: 120% 120%;
  }
}

@media (max-width: 768px) {
  .mobile-header {
    display: flex;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
  }

  .mobile-header-spacer {
    display: block;
    height: 80px;
  }

  .hero-header-group {
    display: none;
  }

  .dropdown-menu {
    display: flex;
  }

  .order-cards {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }

  .hero-stock--custom::before {
    background-size: 150% 150%;
    background-position: 80% 0%;
  }

  .header__back-link {
    display: none;
  }

  .hero-stock__title-main {
    font-size: 28px;
  }

  .search-container {
    flex-direction: column;
  }

  .search-input,
  .search-button {
    width: 100%;
    border-radius: 4px;
    margin-bottom: 10px;
  }
}

@media (max-width: 600px) {
  .hero-stock__title-main {
    font-size: 2rem;
  }

  .search-container {
    flex-direction: column;
  }

  .search-input {
    padding: 15px 20px;
    font-size: 0.9rem;
  }

  .search-button {
    padding: 12px;
  }

  .order-cards {
    grid-template-columns: 1fr;
    max-width: 400px;
  }

  /* Hide navigation links except logo on mobile */
  .header__nav-link:not(.header__logo-link) {
    display: none !important;
  }

  /* Футер стили перенесены в footer.css */
}

@media (max-width: 480px) {
  .order-cards {
    grid-template-columns: 1fr;
  }

  .order-card-image {
    height: 180px;
  }
}

@media (max-width: 700px) {
  .hero-stock__title-main {
    font-size: 2.1rem;
    margin-bottom: 1.2rem;
  }
  .search-container {
    max-width: 98%;
    margin: 0 auto;
  }

  .search-button {
    display: none;
  }

  .search-input {
    padding: 14px 20px;
    font-size: 1rem;
  }

  .order-cards {
    gap: 1.2rem;
    margin-top: 1.5rem;
  }

  .footer__requisites {
    text-align: center;
    padding: 15px 0;
  }

  .footer__requisites-title {
    font-size: 15px;
    margin-bottom: 8px;
  }

  .order-card-name {
    font-size: 0.95rem;
  }
}

/* API Integration Styles */
.loading,
.no-cars {
  width: 100%;
  padding: 40px;
  text-align: center;
  font-size: 18px;
  color: #333;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  margin: 20px 0;
}

.loading {
  position: relative;
}

.loading:before {
  content: "";
  display: block;
  width: 30px;
  height: 30px;
  border: 3px solid #ccc;
  border-top-color: #333;
  border-radius: 50%;
  margin: 0 auto 15px;
  animation: spin 1s infinite linear;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Стили для подсказки поиска */
.search-tip {
  font-size: 14px;
  color: #444;
  text-align: center;
  margin-top: 12px;
  margin-bottom: 0;
  background-color: #f0f5f9;
  padding: 10px 15px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.search-tip b {
  color: #222;
}

.search-tip .examples {
  font-style: italic;
  color: #555;
}

/* Стили для индикатора загрузки */
.loading {
  text-align: center;
  padding: 30px;
  font-size: 18px;
  color: #333;
  margin: 20px auto;
}

/* Стили для сообщения об ошибке */
.error {
  text-align: center;
  padding: 30px;
  font-size: 18px;
  color: #cc0000;
  margin: 20px auto;
}

/* Стили для сообщения о отсутствии результатов */
.no-cars {
  text-align: center;
  padding: 30px;
  font-size: 18px;
  color: #666;
  margin: 20px auto;
}

@media (min-width: 601px) {
  .footer__info {
    display: grid;
    grid-template-columns: 1fr auto;
    grid-template-areas: "left right";
    align-items: flex-start;
    width: 100%;
    padding-bottom: 2rem;
    gap: 0 2.5rem;
  }
  .footer__left {
    grid-area: left;
  }
  .footer__right {
    grid-area: right;
    max-width: 420px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
  .footer__nav {
    justify-content: flex-end;
    margin-bottom: 1.5rem;
  }
  .footer__requisites {
    width: 100%;
  }
}

@media (max-width: 600px) {
  .footer__right {
    width: 100%;
    margin-top: 1.5rem;
    align-items: center;
    text-align: center;
  }
  .footer__nav {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    width: 100%;
  }
  .footer__requisites {
    width: 100%;
    padding: 1.2rem 1.5rem 1rem 1.5rem;
  }
}

@media (min-width: 769px) {
  .mobile-header,
  .mobile-header-spacer,
  .burger,
  .dropdown-menu {
    display: none !important;
  }
}

/* Encar Integration Styles */
.order-card-details {
  margin-top: 15px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 5px;
}

.order-card-details pre {
  font-family: inherit;
  margin: 5px 0;
  white-space: pre-wrap;
  font-size: 14px;
}

.order-card-details-btn {
  display: inline-block;
  padding: 8px 15px;
  background-color: #000;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.order-card-details-btn:hover {
  background-color: #333;
}

/* === Стили для фильтров === */
.filter-container {
  max-width: 600px;
  margin: 15px auto 0;
  width: 100%;
}

.filter-dropdown {
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  width: 100%;
}

.filter-summary {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  user-select: none;
}

.filter-arrow {
  font-size: 12px;
  transition: transform 0.3s ease;
}

details[open] .filter-arrow {
  transform: rotate(180deg);
}

.filter-options {
  padding: 15px;
  border-top: 1px solid #eee;
}

.filter-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.filter-group {
  flex: 1;
  min-width: 200px;
}

.filter-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #555;
}

.filter-select {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.range-filter {
  display: flex;
  gap: 10px;
}

.range-filter .filter-select {
  flex: 1;
}

/* Improved loading animation */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.loading::after {
  content: "";
  width: 40px;
  height: 40px;
  margin-top: 20px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.load-more-btn {
  display: block;
  margin: 30px auto;
  padding: 12px 30px;
  background-color: #000;
  color: #fff;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.load-more-btn:hover {
  background-color: #333;
}

.retry-button {
  display: inline-block;
  margin-top: 10px;
  padding: 8px 20px;
  background-color: #000;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.clear-search {
  display: inline-block;
  margin-top: 10px;
  padding: 8px 20px;
  background-color: #000;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* === 4-уровневая система категорий === */
/* Скрываем кнопку "Рекомендуемые" */
.category-tab[data-category="featured"] {
  display: none !important;
}

/* 🚀 НОВЫЕ СТИЛИ: Индикатор загрузки всех автомобилей категории */
.loading-all-category {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid #000000;
  border-radius: 15px;
  padding: 40px;
  margin: 60px auto;
  max-width: 600px;
  box-shadow: 0 10px 30px rgba(0, 123, 255, 0.1);
  text-align: center;
}

.loading-all-category h3 {
  color: #000000;
  margin: 20px 0 10px 0;
  font-size: 24px;
  font-weight: 600;
}

.loading-all-category p {
  color: #6c757d;
  margin: 10px 0 30px 0;
  font-size: 16px;
  line-height: 1.5;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 6px solid #e9ecef;
  border-top: 6px solid #000000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px auto;
}

.loading-progress {
  margin-top: 20px;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background-color: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 10px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #000000 0%, #3f3f3f 100%);
  border-radius: 10px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  color: #495057;
  font-size: 14px;
  font-weight: 500;
}

/* Убираем стандартный ::after для loading-all-category */
.loading-all-category::after {
  display: none;
}

/* Добавляем анимацию spin если её нет */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
