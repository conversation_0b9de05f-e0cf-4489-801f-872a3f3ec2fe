// Функция для загрузки данных об автомобилях
async function loadCars() {
    try {
        const response = await fetch('/data/cars.json');
        if (!response.ok) throw new Error('Не удалось загрузить cars.json');
        const cars = await response.json();
        console.log('Загружено авто из JSON:', cars.length);
        return cars;
    } catch (error) {
        console.error('Ошибка при загрузке cars.json:', error);
        return [];
    }
}

// Функция для форматирования цены
function formatPrice(price) {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(price);
}

// Функция для форматирования пробега
function formatMileage(km) {
    return km === 0 ? 'Без пробега' : new Intl.NumberFormat('ru-RU').format(km) + ' км';
}

// Функция для создания карточки автомобиля
function createCarCard(car) {
    const card = document.createElement('div');
    card.className = 'order-card';
    card.setAttribute('data-brand', car.mark);
    
    // Получаем первое изображение из массива или используем заглушку
    let imageUrl = '../Img/placeholder-car.jpg';
    if (car.images) {
        try {
            // Handle double-encoded JSON string
            const imagesStr = car.images.replace(/^"|"$/g, ''); // Remove outer quotes
            const imagesArray = JSON.parse(imagesStr);
            if (Array.isArray(imagesArray) && imagesArray.length > 0) {
                // Remove extra quotes from each URL
                imageUrl = imagesArray[0].replace(/^"|"$/g, '');
            }
        } catch (e) {
            console.error('Error parsing images:', e);
        }
    } else if (car.image_url) {
        imageUrl = car.image_url;
    } else if (car.image) {
        imageUrl = car.image;
    }
    
    // Маппинг названий марок к файлам логотипов
    const logoMap = {
        'Ferrari': 'ferrari-logo.png',
        'Lamborghini': 'lamborghini-logo.png',
        'Bentley': 'bentley-logo.png',
        'Aston Martin': 'aston-martin-logo.png',
        'BMW': 'bmw-logo.png',
        'Audi': 'audi-logo.png',
        'Rolls-Royce': 'default-logo.png',
        'Mercedes-Benz': 'default-logo.png',
        'Land Rover': 'default-logo.png',
        'Porsche': 'default-logo.png'
    };
    
    const logoFile = logoMap[car.mark] || 'default-logo.png';
    
    card.innerHTML = `
        <div class="order-card-image">
            <img src="${imageUrl}" alt="${car.mark} ${car.model}" onerror="this.src='../Img/placeholder-car.jpg'">
        </div>
        <div class="order-card-content">
            <div class="order-card-header">
                <div class="order-card-logo">
                    <img src="../PNG/${logoFile}" alt="${car.mark}" onerror="this.src='../PNG/default-logo.png'">
                </div>
                <h3 class="order-card-title order-card-title--small">${car.mark} ${car.model} ${car.year}</h3>
            </div>
            <div class="order-card-specs">
                <div class="order-card-spec">
                    ${car.power ? car.power + ' л.с. • ' : ''}
                    ${car.engine_volume ? car.engine_volume + 'л ' : ''}
                    ${car.fuel_type || ''}
                </div>
            </div>
            <div class="order-card-footer">
                <div class="order-card-mileage">Пробег ${car.km_age || '—'} км</div>
                <div class="order-card-price">${formatPrice(car.price)}</div>
            </div>
        </div>
    `;
    
    return card;
}

// Функция для отображения автомобилей
async function displayCars(searchQuery = '') {
    console.log('displayCars вызвана с запросом:', searchQuery);
    
    const cardsContainer = document.querySelector('.order-cards');
    console.log('Контейнер для карточек:', cardsContainer);
    
    if (!cardsContainer) {
        console.error('Контейнер .order-cards не найден!');
        return;
    }
    
    // Показываем индикатор загрузки
    cardsContainer.innerHTML = '<div class="loading">Загрузка автомобилей...</div>';
    
    try {
        let cars = await loadCars();
        if (!Array.isArray(cars)) {
            console.warn('loadCars вернул не массив, используем пустой массив');
            cars = [];
        }
        console.log('Загружено автомобилей:', cars.length);
        
        // Фильтруем автомобили по поисковому запросу
        const filteredCars = Array.isArray(cars) ? (
            searchQuery 
                ? cars.filter(car => {
                    const searchStr = `${car.mark} ${car.model}`.toLowerCase();
                    return searchStr.includes(searchQuery.toLowerCase());
                })
                : cars
        ) : [];
        
        console.log('Отфильтровано автомобилей:', filteredCars.length);
        
        // Очищаем контейнер
        cardsContainer.innerHTML = '';
        
        if (filteredCars.length === 0) {
            cardsContainer.innerHTML = '<div class="no-cars">Автомобили не найдены</div>';
            return;
        }
        
        // Добавляем карточки автомобилей
        filteredCars.forEach(car => {
            const card = createCarCard(car);
            console.log('Создана карточка для:', car.mark, car.model);
            cardsContainer.appendChild(card);
        });
        
    } catch (error) {
        console.error('Ошибка при загрузке автомобилей:', error);
        cardsContainer.innerHTML = '<div class="error">Произошла ошибка при загрузке автомобилей</div>';
    }
}

// Инициализация при загрузке страницы
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOMContentLoaded сработал');
    
    // Отображаем все автомобили при загрузке
    displayCars();
    
    // Обработка поиска
    const searchInput = document.querySelector('.search-input');
    const searchButton = document.querySelector('#search-btn');
    
    console.log('Поле поиска:', searchInput);
    console.log('Кнопка поиска:', searchButton);
    
    if (searchInput && searchButton) {
        // Поиск по клику на кнопку
        searchButton.addEventListener('click', () => {
            console.log('Клик по кнопке поиска');
            displayCars(searchInput.value);
        });
        
        // Поиск по Enter
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                console.log('Нажат Enter в поле поиска');
                displayCars(searchInput.value);
            }
        });
    } else {
        console.error('Не найдены элементы поиска!');
    }
}); 