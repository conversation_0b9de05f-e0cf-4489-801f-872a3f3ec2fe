const fs = require('fs');
const https = require('https');
const path = require('path');

// Пути к файлам
const dataFilePath = path.join(__dirname, '../public/data/cars.json');
const configPath = path.join(__dirname, '../.env.json');
const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

// Строго определённые авто для поиска (с пробегом)
const carsToFind = [
  { mark: "Lamborghini", model: "Huracan Technica", year: "2023", km_age: "860" },
  { mark: "Ferrari", model: "Roma", year: "2023", km_age: "113" },
  { mark: "BMW", model: "M5", year: "2025", km_age: "0" },
  { mark: "Audi", model: "RS7 4.0", year: "2023", km_age: "1620" },
  { mark: "Aston Martin", model: "DB11 4.0", year: "2023", km_age: "900" },
  { mark: "BMW", model: "M4 Competition", year: "2024", km_age: "0" },
  { mark: "Bentley", model: "Flying Spur 3", year: "2023", km_age: "2200" },
  { mark: "Bentley", model: "Continental GT", year: "2020", km_age: "19500" },
  { mark: "Bentley", model: "Bentayga S", year: "2023", km_age: "620" },
  { mark: "Mercedes-Benz", model: "GLS600 Maybach", year: "2022", km_age: "8620" },
  { mark: "Rolls-Royce", model: "Cullinan Black Badge", year: "2021", km_age: "5200" },
  { mark: "Land Rover", model: "Range Rover P530", year: "2024", km_age: "620" },
  { mark: "Porsche", model: "Macan 2.9 GTS", year: "2022", km_age: "620" }
];

// Берем вчерашнюю дату для гарантированного получения данных
const date = new Date();
date.setDate(date.getDate() - 1);
const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

// Возвращаем поиск по всем типам объявлений
const urls = [
  `https://autobase-wade.auto-parser.ru/encar/${formattedDate}/active_offer.csv`,
  `https://autobase-wade.auto-parser.ru/encar/${formattedDate}/inactive_offer.csv`
];

console.log('Будет искать по этим файлам:', urls);

// Базовая авторизация
const auth = Buffer.from(`${config.username}:${config.password}`).toString('base64');

let headers = null;
let buffer = '';
let lineCount = 0;
const foundCars = {};
let currentUrlIndex = 0;

function isTargetCar(car) {
  const mark = (car.mark || '').toLowerCase();
  const model = (car.model || '').toLowerCase();
  
  return carsToFind.find(target => {
    const targetMark = target.mark.toLowerCase();
    const targetModel = target.model.toLowerCase();
    
    // Разбиваем название модели на ключевые слова
    const modelKeywords = targetModel.split(' ').filter(word => word.length > 1);
    
    // Проверяем марку и хотя бы одно ключевое слово модели
    const markMatch = mark.includes(targetMark);
    const modelMatch = modelKeywords.some(keyword => model.includes(keyword));
    
    if (markMatch && modelMatch) {
      console.log(`Найдено совпадение: ${car.mark} ${car.model} (${car.year})`);
      return true;
    }
    return false;
  });
}

function saveData() {
  const dataDir = path.dirname(dataFilePath);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  
  // Берем только первые 13 найденных машин
  const carsArr = Object.values(foundCars).slice(0, 13);
  fs.writeFileSync(dataFilePath, JSON.stringify(carsArr, null, 2), 'utf8');
  console.log(`Готово! Сохранено ${carsArr.length} авто в ${dataFilePath}`);
}

function processCSV(url, cb) {
  headers = null;
  buffer = '';
  lineCount = 0;
  console.log('Загружаю:', url);
  
  const req = https.request(url, {
    method: 'GET',
    headers: { 'Authorization': `Basic ${auth}` }
  }, (res) => {
    console.log(`Статус ответа: ${res.statusCode}`);
    
    res.on('data', (chunk) => {
      // Если уже нашли 13 машин, прерываем чтение
      if (Object.keys(foundCars).length >= 13) {
        req.destroy();
        cb();
        return;
      }
      
      buffer += chunk.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop();
      
      for (let i = 0; i < lines.length; i++) {
        lineCount++;
        if (lineCount === 1) {
          headers = lines[i].split('|');
          continue;
        }
        
        const row = lines[i].split('|');
        if (row.length < 5) continue;
        
        const car = {};
        headers.forEach((header, index) => {
          if (index < row.length) {
            car[header] = row[index] || '';
          }
        });
        
        if (!car.mark || !car.model) continue;
        
        const target = isTargetCar(car);
        if (target && !foundCars[target.mark + target.model]) {
          foundCars[target.mark + target.model] = car;
          console.log(`Найдено: ${car.mark} ${car.model} (${car.year}) пробег ${car.km_age}`);
          
          // Если нашли 13 машин, прерываем поиск
          if (Object.keys(foundCars).length >= 13) {
            req.destroy();
            cb();
            return;
          }
        }
      }
    });
    
    res.on('end', () => cb());
    res.on('error', (error) => { console.error('Ошибка при получении данных:', error.message); cb(); });
  });
  
  req.on('error', (error) => { console.error('Ошибка при запросе:', error.message); cb(); });
  req.end();
}

function next() {
  if (Object.keys(foundCars).length >= 13 || currentUrlIndex >= urls.length) {
    saveData();
    return;
  }
  processCSV(urls[currentUrlIndex++], next);
}

next();

// Фильтруем уникальные модели
const uniqueModels = new Set();
const filteredCars = Object.values(foundCars).filter(car => {
  const modelKey = `${car.mark} ${car.model}`;
  if (uniqueModels.has(modelKey)) {
    return false;
  }
  uniqueModels.add(modelKey);
  return true;
});

// Сохраняем отфильтрованные автомобили
fs.writeFileSync(dataFilePath, JSON.stringify(filteredCars, null, 2), 'utf8');
console.log(`Готово! Сохранено ${filteredCars.length} уникальных авто в ${dataFilePath}`); 