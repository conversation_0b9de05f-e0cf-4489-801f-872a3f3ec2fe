<?php
// Скрипт для анализа CSV файлов

header('Content-Type: text/html; charset=utf-8');
echo "<h1>Анализ CSV файлов</h1>";

$dataDir = __DIR__ . '/encar_data/';
$csvFiles = glob($dataDir . '*.csv');

if (empty($csvFiles)) {
    echo "<p>CSV файлы не найдены в директории $dataDir</p>";
    exit;
}

// Выбираем файл для анализа
$fileToAnalyze = $csvFiles[0]; // Берем первый найденный файл

echo "<h2>Анализ файла: " . basename($fileToAnalyze) . "</h2>";
echo "<p>Размер файла: " . round(filesize($fileToAnalyze) / 1024 / 1024, 2) . " МБ</p>";

// Пытаемся прочитать заголовки и первые строки
$handle = fopen($fileToAnalyze, 'r');
if (!$handle) {
    echo "<p>Не удалось открыть файл.</p>";
    exit;
}

// Читаем заголовки
$headers = fgetcsv($handle, 0, "|");
if (!$headers) {
    echo "<p>Не удалось прочитать заголовки.</p>";
    fclose($handle);
    exit;
}

// Выводим заголовки
echo "<h3>Заголовки CSV (всего: " . count($headers) . ")</h3>";
echo "<pre>";
print_r($headers);
echo "</pre>";

// Находим важные для нас столбцы
$importantColumns = [
    'mark', 'brand', 'model', 'year', 'mileage', 
    'transmission', 'transmission_type', 
    'body_type', 'fuel_type', 'engine_type', 'displacement',
    'horsepower', 'price'
];

echo "<h3>Важные столбцы и их индексы</h3>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Название в коде</th><th>Найден в CSV?</th><th>Индекс</th></tr>";

foreach ($importantColumns as $column) {
    $index = array_search($column, $headers);
    echo "<tr>";
    echo "<td>$column</td>";
    echo "<td>" . ($index !== false ? 'Да' : 'Нет') . "</td>";
    echo "<td>" . ($index !== false ? $index : '-') . "</td>";
    echo "</tr>";
}
echo "</table>";

// Проверяем другие возможные имена столбцов
echo "<h3>Поиск альтернативных имен столбцов</h3>";
$possibleAlternatives = [
    'марка' => 'mark',
    'бренд' => 'brand',
    'модель' => 'model',
    'год' => 'year', 
    'пробег' => 'mileage',
    'КПП' => 'transmission',
    'тип_кпп' => 'transmission_type',
    'кузов' => 'body_type',
    'топливо' => 'fuel_type',
    'двигатель' => 'engine_type',
    'объем' => 'displacement',
    'мощность' => 'horsepower',
    'цена' => 'price'
];

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Возможное имя</th><th>Имя в коде</th><th>Найден в CSV?</th><th>Индекс</th></tr>";

foreach ($possibleAlternatives as $alternative => $codeColumn) {
    $index = array_search($alternative, array_map('strtolower', $headers));
    echo "<tr>";
    echo "<td>$alternative</td>";
    echo "<td>$codeColumn</td>";
    echo "<td>" . ($index !== false ? 'Да' : 'Нет') . "</td>";
    echo "<td>" . ($index !== false ? $index : '-') . "</td>";
    echo "</tr>";
}
echo "</table>";

// Читаем первые 3 строки данных
echo "<h3>Первые 3 строки данных</h3>";
echo "<table border='1' cellpadding='5'>";

// Выводим заголовки таблицы
echo "<tr>";
foreach ($headers as $header) {
    echo "<th>" . htmlspecialchars($header) . "</th>";
}
echo "</tr>";

// Читаем и выводим данные
$count = 0;
while (($data = fgetcsv($handle, 0, "|")) !== FALSE && $count < 3) {
    echo "<tr>";
    foreach ($data as $key => $value) {
        // Для каждого значения выводим его и стлобец
        echo "<td title='" . htmlspecialchars($headers[$key]) . "'>" . htmlspecialchars($value) . "</td>";
    }
    echo "</tr>";
    $count++;
}
echo "</table>";

// Создаем словарь столбцов
echo "<h3>Словарь столбцов</h3>";
echo "<p>Этот словарь поможет вам сопоставить имена столбцов с нужными в коде:</p>";

echo "<pre>";
echo "// Словарь столбцов\n";
echo "\$columnMap = [\n";
foreach ($headers as $index => $header) {
    $suggested = "";
    foreach ($importantColumns as $column) {
        if (stripos($header, $column) !== false || stripos($column, $header) !== false) {
            $suggested = $column;
            break;
        }
    }
    echo "    '$header' => '" . ($suggested ?: "unknown") . "', // индекс $index\n";
}
echo "];\n";
echo "</pre>";

fclose($handle);

// Добавляем совет по очистке кэша
echo "<h3>Решение проблем с кэшем</h3>";
echo "<p>Добавьте параметр <code>no_cache=1</code> к URL запроса API для обхода кэша:</p>";
echo "<code>http://example.com/api/encar-proxy.php?no_cache=1</code>";
echo "<p>Также можно очистить директорию кэша вручную.</p>"; 