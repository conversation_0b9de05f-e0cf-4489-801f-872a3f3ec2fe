# 🚀 Локальный сервер SHMS Auto

## 📋 Быстрый старт

### 1. Установка зависимостей
```bash
npm install
```

### 2. Запуск локального сервера
```bash
# Обычный запуск
npm run local

# С автоперезагрузкой при изменениях
npm run dev:local

# Альтернативный способ
npm run start:local
```

### 3. Открыть в браузере
```
http://localhost:3000
```

## 🔧 Конфигурация

### Файлы конфигурации:
- **`.env.local`** - настройки для локальной разработки
- **`server-local.js`** - локальный сервер
- **`.env`** - настройки для продакшена
- **`server.js`** - продакшн сервер

### Основные различия локального и продакшн серверов:

| Параметр | Локальный | Продакшн |
|----------|-----------|----------|
| NODE_ENV | development | production |
| HOST | localhost | 0.0.0.0 |
| SITE_URL | http://localhost:3000 | https://shms-auto.ru |
| HTTPS | ❌ | ✅ |
| Кеширование | ❌ | ✅ |
| Сессии | В памяти | В файлах |
| Логирование | Подробное | Стандартное |

## 📡 Доступные маршруты

### Публичные страницы:
- **Главная**: http://localhost:3000/
- **Авто в наличии**: http://localhost:3000/stock
- **Авто под заказ**: http://localhost:3000/order
- **Контакты**: http://localhost:3000/contacts
- **Промо Физиева**: http://localhost:3000/Fiziyev

### Админ-панель:
- **Вход**: http://localhost:3000/admin/login.html
- **Панель**: http://localhost:3000/admin

### API:
- **Тест**: http://localhost:3000/test
- **Поиск авто**: http://localhost:3000/api/encar
- **Отправка заявок**: http://localhost:3000/api/send-review

## 🔐 Вход в админку

**Логин:** `Admin`  
**Пароль:** `Admin123!@#`  
**Email:** `<EMAIL>`

## 🛠️ Команды разработки

```bash
# Запуск локального сервера
npm run local

# Запуск с автоперезагрузкой
npm run dev:local

# Запуск продакшн сервера
npm start

# Создание администратора
npm run create-admin

# Проверка здоровья сервера
npm run health-check

# Тестирование почты
npm run test-mail

# Переключение режимов
npm run switch:local      # Переключить на локальный
npm run switch:production # Переключить на продакшн
npm run switch:help       # Помощь по переключению
```

## 📁 Структура проекта

```
shms-auto123/
├── .env                 # Продакшн конфигурация
├── .env.local          # Локальная конфигурация
├── server.js           # Продакшн сервер
├── server-local.js     # Локальный сервер
├── public/             # Статические файлы сайта
├── admin/              # Файлы админ-панели
├── data/               # База данных и загрузки
│   ├── stock.db        # База данных SQLite
│   └── uploads/        # Загруженные файлы
├── server/             # Серверная логика
│   ├── controllers/    # Контроллеры
│   ├── models/         # Модели данных
│   └── middleware/     # Промежуточное ПО
└── scripts/            # Утилиты и скрипты
```

## 🔄 Переключение между режимами

### Автоматическое переключение:
```bash
# На локальную разработку
npm run switch:local

# На продакшн
npm run switch:production
```

### Ручное переключение:
1. Скопировать нужный .env файл:
   ```bash
   # Для локальной разработки
   cp .env.local .env
   
   # Для продакшена
   cp .env.production .env  # если есть
   ```

2. Запустить соответствующий сервер:
   ```bash
   # Локальный
   npm run local
   
   # Продакшн
   npm start
   ```

## 🐛 Отладка

### Логи сервера:
- Все запросы логируются в консоль
- Подробная информация о body и query параметрах
- Ошибки выводятся с полными деталями

### Проверка работы:
```bash
# Тест API
curl http://localhost:3000/test

# Проверка главной страницы
curl http://localhost:3000/

# Проверка админки
curl http://localhost:3000/admin
```

## 📧 Настройка почты

Почтовые настройки берутся из `.env.local`:
```env
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=SHMSAutogroup777!
```

Тест отправки почты:
```bash
npm run test-mail
```

## 🚨 Решение проблем

### Сервер не запускается:
1. Проверить установку зависимостей: `npm install`
2. Проверить порт 3000: `netstat -an | findstr :3000`
3. Проверить файл `.env.local`

### Админка не работает:
1. Создать администратора: `npm run create-admin`
2. Проверить базу данных: `ls -la data/stock.db`
3. Проверить сессии в логах

### API не отвечает:
1. Проверить PHP сервер (если используется)
2. Проверить файл `public/api/encar-proxy.php`
3. Проверить логи в консоли

## 📞 Поддержка

При возникновении проблем:
1. Проверить логи в консоли
2. Открыть Developer Tools в браузере (F12)
3. Проверить Network tab для API запросов
