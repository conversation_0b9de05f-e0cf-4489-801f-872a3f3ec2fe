const fs = require('fs');
const path = require('path');
const db = require('./database');
const cheerio = require('cheerio');

const htmlPath = path.join(__dirname, '../public/stock.html');
const imgDir = path.join(__dirname, '../public/uploads');

if (!fs.existsSync(imgDir)) fs.mkdirSync(imgDir);

const html = fs.readFileSync(htmlPath, 'utf8');
const $ = cheerio.load(html);

$('.car-card').each((i, el) => {
    const title = $(el).find('.car-card__title').text().trim();
    const subtitle = $(el).find('.car-card__subtitle').text().trim();
    const imgSrc = $(el).find('.car-card__image img').attr('src');
    const power = $(el).find('.car-card__spec-main').first().text().trim();
    const mileage = $(el).find('.car-card__spec-main').last().text().trim();
    const body_type = $(el).find('.car-card__info-list div').eq(0).text().replace('Тип кузова', '').trim();
    const engine = $(el).find('.car-card__info-list div').eq(1).text().replace('Двигатель', '').trim();
    const transmission = $(el).find('.car-card__info-list div').eq(2).text().replace('Трансмиссия', '').trim();
    
    // Преобразуем формат цветов из "dot dot-color" или "dot--color" в JSON
    let colorsArray = [];
    const colorDots = $(el).find('.car-card__info-list div').eq(3).find('.dot');
    
    if (colorDots.length > 0) {
        // Получаем цвета из HTML-элементов
        colorDots.each((i, dot) => {
            const className = $(dot).attr('class') || '';
            const colorClass = className.split(' ').find(c => c.startsWith('dot--'));
            if (colorClass) {
                const colorName = colorClass.replace('dot--', '');
                let hexColor = '#000000'; // Черный по умолчанию
                
                // Преобразуем название цвета в HEX
                switch(colorName.toLowerCase()) {
                    case 'black': hexColor = '#000000'; break;
                    case 'white': hexColor = '#ffffff'; break;
                    case 'orange': hexColor = '#ff7700'; break;
                    case 'red': hexColor = '#d20000'; break;
                    case 'blue': hexColor = '#0066cc'; break;
                    case 'brown': hexColor = '#8B4513'; break;
                    case 'beige': hexColor = '#F5F5DC'; break;
                    case 'gray': hexColor = '#808080'; break;
                    default: hexColor = '#000000';
                }
                
                colorsArray.push({
                    hex: hexColor,
                    name: colorName
                });
            }
        });
    } else {
        // Если на странице цвета в текстовом формате "Цвета: dot dot-black, dot dot-orange"
        const colorText = $(el).find('.car-card__info-list div').eq(3).text().replace('Цвета:', '').trim();
        if (colorText) {
            const colorParts = colorText.split(', ');
            colorParts.forEach(part => {
                let colorName = part.trim();
                if (colorName.startsWith('dot dot-')) {
                    colorName = colorName.replace('dot dot-', '');
                } else if (colorName.startsWith('dot--')) {
                    colorName = colorName.replace('dot--', '');
                }
                
                let hexColor = '#000000'; // Черный по умолчанию
                
                // Преобразуем название цвета в HEX
                switch(colorName.toLowerCase()) {
                    case 'black': hexColor = '#000000'; break;
                    case 'white': hexColor = '#ffffff'; break;
                    case 'orange': hexColor = '#ff7700'; break;
                    case 'red': hexColor = '#d20000'; break;
                    case 'blue': hexColor = '#0066cc'; break;
                    case 'brown': hexColor = '#8B4513'; break;
                    case 'beige': hexColor = '#F5F5DC'; break;
                    case 'gray': hexColor = '#808080'; break;
                    default: hexColor = '#000000';
                }
                
                colorsArray.push({
                    hex: hexColor,
                    name: colorName
                });
            });
        }
    }
    
    // Преобразуем массив цветов в JSON строку
    const interior_colors = JSON.stringify(colorsArray);
    
    const consumption = $(el).find('.car-card__info-list div').eq(4).text().replace('Расход', '').trim();
    const capacity = $(el).find('.car-card__info-list div').eq(5).text().replace('Вместимость', '').trim();
    const price = $(el).find('.car-card__price').text().trim();

    // Копируем картинку в uploads
    let image_path = '';
    if (imgSrc) {
        const srcPath = path.join(__dirname, '../', imgSrc);
        const ext = path.extname(srcPath);
        const destName = `imported_${Date.now()}_${i}${ext}`;
        const destPath = path.join(imgDir, destName);
        if (fs.existsSync(srcPath)) {
            fs.copyFileSync(srcPath, destPath);
            image_path = 'public/uploads/' + destName;
        }
    }

    db.run(
        `INSERT INTO stock_cars (title, subtitle, image_path, power, mileage, body_type, engine, transmission, interior_colors, consumption, capacity, price)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [title, subtitle, image_path, power, mileage, body_type, engine, transmission, interior_colors, consumption, capacity, price],
        function(err) {
            if (err) console.error('Ошибка при импорте:', err);
        }
    );
});

console.log('Импорт завершён!'); 