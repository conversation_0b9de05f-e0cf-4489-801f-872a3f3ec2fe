{"ChevroletGMDaewoo": {"Matiz": 178, "Spark": 2036, "Lacetti": 63, "Aveo": 175, "Cruze": 680, "Tosca": 7, "Alpheon": 158, "Orlando": 680, "Malibu": 1020, "labo": 146, "damas": 165, "Captiva": 198, "Prince": 1, "Trax": 943, "Impala": 132, "Acadia": 6, "Espero": 4, "G2X": 12, "Equinox": 73, "Bolt EV": 62, "Trailblazer": 329, "Traverse": 90, "Colorado": 353, "Camaro": 57, "Winstorm": 15, "볼트 EUV": 22, "Tahoe": 16, "Gentra": 8, "Magnus": 1, "VERITAS": 3, "Statesman": 3, "Tico": 4, "VOLT": 2, "Lemans": 1, "Leganza": 1, "Rezzo": 1, "Super Salon": 1}, "Hyundai": {"Grandeur": 8795, "Sonata": 4390, "AVANTE": 5536, "Equus": 791, "Tucson": 2658, "Santafe": 4317, "Starex": 2857, "Traget XG": 6, "i30": 346, "Marcia": 3, "Genesis": 1602, "Accent": 473, "Verna": 15, "Dynasty": 13, "Terracan": 30, "Tuscani": 14, "i40": 254, "Veracruz": 125, "Veloster": 366, "Elantra": 3, "Galloper": 44, "Excel": 6, "Aslan": 130, "Ioniq": 193, "Maxcruz": 255, "Pony": 4, "Venue": 328, "Kona": 1312, "Casper": 655, "Nexo": 180, "Staria": 1254, "Palisade": 2675, "Ioniq5": 515, "Solati": 126, "Tiburon": 5, "Ioniq6": 143, "ST1": 3, "Santamo": 3, "Atoz": 4, "Lavita": 2, "Click": 2, "BlueOn": 1}, "Renault-KoreaSamsung": {"SM7": 480, "SM5 ": 1047, "SM3": 803, "Twizy": 21, "QM5 ": 192, "QM3": 452, "SM6": 1360, "QM6": 1523, "XM3": 485, "Master": 127, "Arkana": 37, "Captur": 22, "Cilo": 52, "Grand Koleos": 61, "Zoe": 9}, "Kia": {"morning": 3101, "LOTZE": 32, "OPIRUS": 131, "Porte": 136, "Carens": 292, "Canival": 7459, "pride": 262, "Sportage": 2598, "Spectra": 3, "Soul": 342, "K5/Optima": 3488, "K7/Cadenza": 3018, "Sephia": 1, "Sorento": 3844, "RAY": 2789, "K3": 2243, "Potentia": 8, "K9/Quoris": 1206, "Mohave": 1303, "Enterprise": 4, "Stonic": 207, "Bongo III Minibus": 5, "Niro": 816, "Seltos": 1178, "Stinger": 541, "EV6": 506, "K8": 1058, "Parktown": 1, "Credos": 2, "Cerato": 1, "Elan": 1, "Avella": 1, "EV3": 45, "EV9": 59, "Regal": 1, "Visto": 1, "Retona": 1, "Besta": 1, "Capital": 1}, "Volvo": {"S80": 40, "C30": 10, "S40": 7, "XC90": 185, "XC60": 247, "940": 2, "S60": 97, "V40": 47, "V60": 85, "S70": 1, "S90": 177, "V90": 20, "XC40": 126, "850": 2, "C40": 20, "XC70": 14, "960": 1, "V50": 1, "C70": 1, "740": 1}, "KG_Mobility_Ssangyong": {"Chairman": 221, "Rexton": 2346, "KORANDO": 1580, "Rodius": 11, "Kyron": 10, "Musso": 5, "TIBOLI": 1704, "Torres": 318, "Actyon": 92, "Istana": 1}, "Saab": {"9-3": 11, "900": 3, "9000": 3, "9-5": 4}, "Lincoln": {"MKS": 38, "MKZ": 100, "Town Car": 8, "MKX": 31, "MKC": 43, "Continental": 38, "Navigator": 38, "Aviator": 97, "Corsair": 32, "Nautilus": 37}, "Chrysler": {"Sebring": 3, "300C": 59, "Pacifica": 2, "Crossfire": 6, "200": 18, "Grand Voyager": 7, "PT Crusier": 6, "Prowler": 1}, "Ford": {"Taurus": 54, "Focus": 14, "Fusion": 14, "Escape": 14, "Explorer": 417, "Mustang": 248, "F150": 71, "F350": 5, "E-Series": 6, "Ranger": 66, "Transit": 14, "Expedition": 19, "Mondeo": 53, "Kuga": 13, "Freestyle": 1, "F250": 4, "Bronco": 17, "Econoline": 1}, "Audi": {"A6": 934, "A4": 225, "A3": 119, "A8": 171, "A5": 191, "S4": 21, "Q7": 227, "Q5": 212, "S6": 17, "A7": 282, "TT": 27, "S7": 13, "Q2": 20, "RS7": 16, "Q3": 85, "R8": 49, "e-tron": 62, "Q8": 112, "RS e-tron GT": 3, "RSQ8": 14, "S8": 10, "S3": 4, "TTS": 11, "S5": 3, "SQ5": 40, "e-tron GT": 10, "Q8 e-tron": 13, "Q4 e-tron": 33, "RS3": 4, "RS5": 16, "Allroad Quattro": 2, "A1": 7, "SQ8": 4}, "BMW": {"5-Series": 3004, "7-Series": 745, "3-Series": 1108, "Gran Turismo": 742, "X3": 594, "1-Series": 341, "X5": 897, "Z4": 103, "X1": 228, "i3": 26, "2-Series": 154, "4-Series": 410, "6-Series": 87, "X6": 628, "Z3": 7, "X6M": 52, "M6": 14, "M3": 72, "M4": 113, "8-Series": 65, "X4": 500, "M5": 67, "i5": 17, "M2": 39, "i8": 30, "X7": 435, "X4M": 25, "M8": 10, "X2 (F39)": 52, "X5M": 25, "XM": 17, "i4": 83, "iX3": 34, "iX": 14, "M Coupe/Roadster": 1, "Z8": 1, "X3M": 14, "i7": 10, "1M": 3, "iX1": 3}, "Mercedes-Benz": {"E-Class": 3280, "B-Class": 65, "CLS-Class": 599, "My B": 6, "S-Class": 1942, "R-Class": 5, "C-Class": 954, "CL-Class": 25, "SLK-Class": 58, "GLK-Class": 64, "M-class": 65, "CLK-Class": 6, "A-Class": 422, "GLA-Class": 249, "CLA-Class": 286, "SL-Class": 61, "SEL/SEC": 14, "GLE-Class": 683, "GLC-Class": 715, "EQA": 50, "GL-Class": 7, "Sprinter": 56, "GLS-Class": 216, "SLC-Class": 22, "GLB-Class": 167, "G-Class": 263, "EQB": 52, "Others": 1, "V-Class": 22, "EQE": 122, "AMG GT": 220, "EQS": 75, "SLS AMG": 5, "190-Class": 2, "EQC": 11, "CLE-Class": 48}, "Mini": {"Cooper": 958, "Clubman": 300, "Countryman": 513, "Coupe": 26, "Paceman": 15, "Cooper Convertible": 142, "Rover Mini": 4, "Roadster": 13}, "Peugeot": {"308": 60, "307": 1, "207": 21, "407": 1, "508": 67, "2008": 104, "206": 7, "3008": 88, "5008": 63, "208": 25, "Expert": 2, "408": 13, "RCZ": 6}, "Infiniti": {"G": 57, "M": 22, "FX": 23, "Q50": 80, "Q70": 16, "EX": 7, "QX50": 33, "Q30": 31, "QX70": 8, "QX60": 46, "Q60": 3, "QX30": 7, "QX": 2, "JX": 4, "QX80": 2}, "Nissan": {"Altima": 102, "Cube": 45, "Murano": 15, "Leaf": 15, "Maxima": 25, "Frontier": 3, "Pathfinder": 10, "370Z": 11, "Skyline": 2, "Rogue": 3, "Elgrand": 1, "300ZX": 1, "Juke": 15, "March": 3, "350Z": 1, "Figaro": 1, "Quest": 1, "X-Trail": 13, "Qashqai": 4, "GT-R": 1}, "Honda": {"Legend": 18, "Civic": 21, "Accord": 130, "Pilot": 58, "CR-V": 66, "Odyssey": 66, "CR-Z": 2, "Crosstour": 3, "Insight": 3, "S2000": 2, "HR-V": 4, "N-BOX": 1, "S660": 2}, "Others": {"Others": 71}, "Volkswagen": {"EOS": 3, "Beatle": 58, "Passat": 159, "Golf": 260, "CC": 78, "Touareg": 87, "Polo": 33, "Phaeton": 13, "Scirocco": 18, "Arteon": 118, "Tiguan": 393, "Jetta": 111, "T-Roc": 32, "ID.4": 34, "ID.5": 1, "Atlas": 2}, "Citroen-DS": {"DS3": 21, "C4 Picasso": 36, "C3 Aircross": 9, "C4 SpaceTourer": 4, "C4 CACTUS": 29, "DS5": 5, "C5 Aircross": 8, "DS7": 10, "DS4": 5}, "Lexus": {"ES": 381, "IS": 73, "LS": 117, "CT200h": 31, "NX": 132, "RC": 6, "UX": 52, "GS": 22, "RX": 107, "SC": 6, "RZ": 3, "LM": 8, "LC": 6, "LX": 2}, "Cadillac": {"CTS": 54, "SRX": 13, "ATS": 20, "Escalade": 171, "Concour": 3, "CT6": 59, "XT5": 34, "XT4": 8, "CTS-V": 4, "STS": 1, "XT6": 16, "CT5": 7, "CT4": 4, "Lyriq": 1, "Fleetwood": 1, "DeVille": 1, "SeVille": 1, "DTS": 2}, "Toyota": {"bB": 9, "Camry": 192, "Venza": 1, "Prius": 115, "Crown": 14, "Estima": 2, "Sienna": 137, "AYGO": 2, "86": 20, "Celica": 2, "Tundra": 8, "FJ Cruiser": 14, "Tacoma": 4, "Supra": 4, "Yaris(Vitz)": 1, "RAV4": 107, "Alphard": 37, "Avalon": 17, "Highlander": 10, "Soarer": 1, "Corolla": 1, "iQ": 1, "WiLL": 1, "MR-S": 1, "Aristo": 1, "Wish": 1, "Mark2": 1, "Noah": 1}, "Jaguar": {"XF": 141, "X-TYPE": 5, "S-TYPE": 5, "XJ": 92, "XE": 87, "XJ-6": 7, "Daimler": 5, "F-PACE": 81, "XK8": 1, "E-PACE": 29, "XJ-8": 5, "F-TYPE": 57, "XKR": 3, "Others": 1, "I-PACE": 2, "XJR": 1, "XK": 1}, "Jeep": {"Compass": 73, "Cherokee": 323, "Renegade": 238, "Wrangler": 389, "Commander": 3, "Gladiator": 70, "Avenger": 2}, "DFSK": {"C31": 1, "Fengon ix5": 2, "C35": 9, "C32": 1}, "Smart": {"Fortwo": 43, "Forfour": 3, "Roadster": 1}, "Acura": {"TL": 4}, "Land Rover": {"Freelander": 9, "Discovery": 308, "Discovery Sport": 376, "Range Rover Evoque": 251, "Range Rover": 336, "Range Rover Sport": 247, "Range Rover Velar": 100, "Defender": 91}, "Mazda": {"Mazda 3": 2, "MX-5 Miata": 11, "Eunons": 1, "RX-7": 1}, "Suzuki": {"Alto Lapin": 6, "HUSTLER": 11, "ALTO": 1, "Jimny": 9, "Ignis": 1, "Cappucino": 1}, "Fiat": {"Feelmont": 5, "500X": 27, "500": 56, "124": 3, "Ducato": 1}, "Subaru": {"R1": 1, "Legacy": 2, "Impreza": 5, "Forester": 4, "Outback": 2, "Levorg": 1}, "Dodge": {"Intrepid": 1, "Durango": 4, "Dakoda": 7, "Ram Van": 2, "Ram Pick Up": 45, "Charger": 3, "Challenger": 15, "Nitro": 3, "Caliber": 5, "Others": 1}, "Mercury": {"Sable": 1}, "Porsche": {"Cayenne": 530, "Panamera": 410, "Cayman": 14, "Boxster": 64, "Macan": 178, "718": 214, "911": 232, "Taycan": 138}, "Chevrolet": {"Blazer": 2, "Express Van": 76, "Surburban": 16, "Others": 2, "Camaro": 5, "Silverado": 6, "Corvette": 16, "Chevy Van": 7, "Tahoe": 2, "Astro Van": 5, "Avalanche": 3, "Colorado": 2, "Tracker": 1}, "Genesis": {"G80": 3366, "EQ900": 748, "G70": 978, "G90": 928, "GV80": 1454, "GV70": 943, "GV60": 76}, "Daihatsu": {"Copen": 13, "Mira": 2, "Tanto": 1, "Mira Gino": 1, "MATERIA": 1}, "Hummer": {"H3": 13, "H2": 31, "H1": 1}, "Maserati": {"Ghibli": 217, "Coupe": 2, "Quattroporte": 135, "Levante": 148, "GranTurismo": 16, "Gran Cabrio": 5, "MC20": 2, "Grecale": 8, "Spyder": 1}, "Bentley": {"Continental": 113, "Flying Spur": 104, "Bentayga": 52, "Mulsanne": 10, "Brooklands": 1}, "Mitsubishi": {"Lancer Evolution": 2}, "Mitsuoka": {"Galue": 4, "Le-seyde": 1}, "Tesla": {"Model 3": 411, "Model Y": 275, "Model S": 29, "Model X": 25, "Cybertruck": 2}, "Polestar": {"Polestar 2": 45, "Polestar 4": 2}, "GMC": {"Canyon": 1, "Savana": 11, "Sierra": 23, "Yukon": 2, "Hummer EV": 3, "Vandura": 1}, "Maybach": {"62": 5, "57": 3, "57s": 3, "62s": 4}, "Lotus": {"Elise": 2, "Evora": 6, "Exige": 2, "Emira": 2, "Eletre": 1}, "Astonmartin": {"Vantage": 13, "Rapide": 11, "DB9": 3, "DB11": 10, "DBX": 10, "Vanquish": 6}, "Ferrari": {"California": 23, "599": 2, "FF": 2, "458": 23, "F12 Berlinetta": 4, "488": 26, "GTC4 Lusso": 1, "296": 11, "Roma": 13, "812": 10, "F8": 15, "SF90": 8, "F430": 8, "Portofino": 20, "360": 2, "612": 4, "F355": 2, "308": 1, "328": 1, "Purosangue": 1, "575M": 2}, "Rolls-Royce": {"Ghost": 59, "Wraith": 23, "Dawn": 11, "Cullinan": 41, "Phantom": 15, "Spectre": 2}, "Mclaren": {"650S": 10, "570S": 15, "600LT": 5, "720S": 15, "Artura": 5, "750S": 4, "765LT": 2, "675LT": 1, "GT": 1, "Senna": 1, "570GT": 1}, "Lamborghini": {"Huracan": 38, "Gallardo": 11, "Aventador": 19, "Urus": 45, "Murcielago": 1}, "etc": {"Others": 17}, "Baic Yinxiang": {"CK MINI Truck": 3, "CK Mini Van": 3, "Kenbo 600": 3}, "Alfa Romeo": {"Brera": 1}, "Opel": {"SpeedSter": 1}, "Ineos": {"Grenadier": 1}}