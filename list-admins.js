const sqlite3 = require("sqlite3").verbose();
const path = require("path");

console.log("👥 Список администраторов SHMS Auto");
console.log("==================================\n");

// Путь к базе данных
const dbPath = path.join(__dirname, "data/stock.db");

// Подключаемся к базе данных
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error("❌ Ошибка подключения к базе данных:", err.message);
    process.exit(1);
  }
});

// Функция получения списка администраторов
function getAdmins() {
  return new Promise((resolve, reject) => {
    const sql = `
            SELECT id, username, email, full_name, is_active, created_at, last_login
            FROM admins 
            ORDER BY created_at DESC
        `;

    db.all(sql, [], (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows || []);
      }
    });
  });
}

// Функция получения статистики
function getStats() {
  return new Promise((resolve, reject) => {
    const sql = `
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
                SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive
            FROM admins
        `;

    db.get(sql, [], (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

// Основная функция
async function main() {
  try {
    // Получаем статистику
    const stats = await getStats();

    console.log("📊 Статистика:");
    console.log(`   Всего администраторов: ${stats.total}`);
    console.log(`   Активных: ${stats.active}`);
    console.log(`   Неактивных: ${stats.inactive}\n`);

    if (stats.total === 0) {
      console.log("❌ В системе нет администраторов");
      console.log("💡 Создайте администратора командой: node create-admin.js");
      return;
    }

    // Получаем список администраторов
    const admins = await getAdmins();

    console.log("👥 Список администраторов:\n");

    admins.forEach((admin, index) => {
      const status = admin.is_active ? "🟢 Активен" : "🔴 Неактивен";
      const createdAt = new Date(admin.created_at).toLocaleString("ru-RU");
      const lastLogin = admin.last_login
        ? new Date(admin.last_login).toLocaleString("ru-RU")
        : "Никогда";

      console.log(`${index + 1}. ${admin.username} (ID: ${admin.id})`);
      if (admin.full_name) {
        console.log(`   👤 Имя: ${admin.full_name}`);
      }
      console.log(`   📊 Статус: ${status}`);
      console.log(`   📅 Создан: ${createdAt}`);
      console.log(`   🕐 Последний вход: ${lastLogin}`);
      console.log("   " + "─".repeat(50));
    });

    console.log("\n💡 Доступные команды:");
    console.log("   node create-admin.js  - создать администратора");
    console.log(
      "   node delete-admin.js  - удалить/деактивировать администратора"
    );
    console.log("   node list-admins.js   - показать этот список");
  } catch (error) {
    console.error("\n❌ Ошибка:", error.message);
  } finally {
    db.close();
  }
}

// Запуск
main();
