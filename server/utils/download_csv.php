<?php
// Отключаем вывод ошибок
error_reporting(0);
ini_set('display_errors', 0);

// Настройки API
$api_host = 'https://autobase-wade.auto-parser.ru';
$api_username = 'admin';
$api_password = 'n2Q8ewyLft9qgPmim5ng';

// URL файла
$url = "{$api_host}/encar/2024-06-06/active_offer.csv";

// Инициализируем cURL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
curl_setopt($ch, CURLOPT_USERPWD, "{$api_username}:{$api_password}");
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

// Устанавливаем заголовок Range для получения первых 50KB
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Range: bytes=0-50000']);

// Выполняем запрос
$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

// Закрываем соединение
curl_close($ch);

// Проверяем результат
if ($http_code === 206 && !empty($response)) {
    // Сохраняем в файл
    file_put_contents('first50k.csv', $response);
    echo "Successfully downloaded first 50KB to first50k.csv\n";
} else {
    echo "Error: HTTP code {$http_code}\n";
    if (!empty($error)) {
        echo "cURL error: {$error}\n";
    }
} 