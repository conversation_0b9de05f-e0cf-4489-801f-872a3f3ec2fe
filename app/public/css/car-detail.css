/* === Общие стили и сброс === */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
body {
  font-family: "Inter", Arial, sans-serif;
  background: #fff;
  color: #111;
  min-height: 100vh;
  padding: 0;
  overflow-x: hidden;
}
a {
  color: inherit;
  text-decoration: none;
}
img {
  max-width: 100%;
  display: block;
}

/* === Header === */
.hero-header-group {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 110px;
  padding: 0 2vw;
  background: #fff;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
  position: relative;
}

.header__center-group {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2.5rem;
}

.header__logo-link {
  display: flex;
  align-items: center;
  margin-right: 2.5rem;
}

.header__logo-img {
  height: 95px;
  width: auto;
  display: block;
  background: #fff;
  vertical-align: middle;
  align-self: center;
}

.header__nav {
  display: flex;
  align-items: center;
  gap: 3.5rem;
}

.header__back-link {
  position: absolute;
  left: 2vw;
  top: 50%;
  transform: translateY(-50%);
  z-index: 4;
  font-size: 1.2rem;
  font-weight: 700;
  color: #111;
  text-decoration: none;
}

.header__nav-link {
  font-weight: 700;
  font-size: 1.1rem;
  color: #111;
  transition: color 0.2s;
  line-height: 95px;
  display: flex;
  align-items: center;
}

.header__nav-link.active {
  color: #000;
  text-decoration: none;
  font-weight: 700;
}

.header__nav-link:hover {
  color: #6f9aab;
}

/* === Hero Section === */
.hero-stock {
  background: #fff;
  position: relative;
  padding-bottom: 0;
}

.hero-stock--custom {
  background-color: #f9f9f9;
  position: relative;
  padding: 3rem 0 4rem 0;
  min-height: 350px;
}

.hero-stock--custom::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(
    circle at right,
    rgba(224, 229, 232, 0.95) 0%,
    rgba(224, 229, 232, 0.9) 10%,
    rgba(224, 229, 232, 0.7) 30%,
    rgba(224, 229, 232, 0.4) 60%,
    rgba(224, 229, 232, 0) 80%
  );
  background-size: 100% 100%;
  background-position: 100% 0%;
  background-repeat: no-repeat;
  z-index: 1;
}

.hero-stock__visual {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 2vw;
  position: relative;
  z-index: 2;
}

.hero-stock__center {
  text-align: center;
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
  position: relative;
  z-index: 5;
}

.hero-stock__title-main {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 2rem;
  text-align: center;
  color: #111;
  position: relative;
  z-index: 5;
}

/* === Базовые стили и сброс === */
body,
.car-detail {
  font-family: "Inter", Arial, sans-serif;
  background: #fff;
  color: #111;
}

.car-detail {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  background: #fff;
}

/* === Tabs === */
.car-detail__tabs {
  display: flex;
  justify-content: center;
  gap: 3.5rem;
  margin: 32px 0 0 0;
  font-size: 1.13rem;
  font-weight: 700;
  background: #fff;
  border: none;
}
.car-detail__tab {
  color: #222;
  padding: 0 0 8px 0;
  position: relative;
  cursor: pointer;
  transition: color 0.2s;
  text-decoration: none;
}
.car-detail__tab::after {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 3px;
  background: #111;
  border-radius: 2px;
  transform: scaleX(0);
  transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}
.car-detail__tab.active::after,
.car-detail__tab:hover::after {
  transform: scaleX(1);
}
.car-detail__tab.active,
.car-detail__tab:hover {
  color: #111;
}

/* === Заголовок и кнопка === */
.car-detail__headline-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  max-width: 1200px;
  margin: 48px auto 0 auto;
  padding: 0 20px;
}
.car-detail__title {
  font-size: 2.4rem;
  font-weight: 800;
  margin: 0;
  line-height: 1.1;
}
.car-detail__price-label {
  color: #666;
  font-size: 0.9rem;
  margin-top: 6px;
  display: block;
}
.car-detail__price {
  font-size: 1.1rem;
  font-weight: 600;
  color: #222;
  margin-left: 5px;
}
.car-detail__buy-btn {
  background: #000;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.7em 2.2em;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  margin-left: 32px;
}
.car-detail__buy-btn:hover {
  background: #333;
}

/* === Галерея === */
.car-detail__gallery {
  display: flex;
  justify-content: center;
  margin: 32px auto 0 auto;
  max-width: 1200px;
  padding: 0 20px;
}
.slider {
  position: relative;
  width: 100%;
  height: 500px; /* Fixed height for slider container */
  background-color: #f9f9f9;
  border-radius: 6px;
  overflow: hidden;
}
.car-detail__main-img {
  width: 100%;
  height: 100%;
  object-fit: fill;
  border-radius: 0;
  background: #fff;
  display: none;
  margin: 0 auto;
  position: absolute; /* Position absolute to overlay */
  top: 0;
  left: 0;
}
.car-detail__main-img.active {
  display: block;
}

/* === Описание и инфо-блок === */
.car-detail__desc-row {
  display: flex;
  align-items: flex-start;
  gap: 48px;
  max-width: 1200px;
  margin: 48px auto 0 auto;
  padding: 0 20px;
}
.car-detail__desc-col {
  flex: 2;
}
.car-detail__desc-title {
  font-size: 1.7rem;
  font-weight: 800;
  margin-bottom: 24px;
}
.car-detail__desc-text {
  font-size: 1rem;
  margin-bottom: 18px;
  color: #333;
  line-height: 1.5;
}
.car-detail__desc-list {
  margin-bottom: 18px;
  padding-left: 1.5rem;
  color: #444;
  font-size: 1.08rem;
}
.car-detail__desc-gallery {
  display: flex;
  justify-content: center;
  gap: 18px;
  margin-top: 24px;
  width: 100%;
}
.car-detail__desc-img {
  width: 48%;
  height: 320px;
  object-fit: cover;
  border-radius: 6px;
  display: block;
  margin: 0;
  padding: 0;
}

.car-detail__info-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 18px;
}
.car-detail__info-price {
  font-size: 1.8rem;
  font-weight: 800;
  margin-bottom: 0;
}
.car-detail__info-label {
  color: #888;
  font-size: 0.9rem;
  margin-bottom: 20px;
}
.car-detail__info-blocks {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
  width: 100%;
}
.car-detail__info-block {
  background: #f6f6f6;
  border-radius: 6px;
  padding: 14px 18px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 1rem;
  font-weight: 600;
  position: relative;
  padding-left: 54px; /* Additional padding to accommodate icon */
  flex-direction: column;
  align-items: flex-start;
}
.car-detail__info-block-icon {
  width: 28px;
  height: 28px;
  position: absolute;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}
.car-detail__info-block-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.car-detail__info-block-label {
  color: #888;
  font-weight: 400;
  font-size: 0.9rem;
  display: block;
  margin-top: 4px;
}
.car-detail__priority {
  background: #f8f8f8;
  border-radius: 6px;
  padding: 20px;
  font-size: 0.9rem;
  color: #333;
  margin-bottom: 20px;
}
.car-detail__priority b {
  display: block;
  margin-bottom: 6px;
  color: #000;
  font-weight: 800;
}
.car-detail__info-buy {
  margin-top: 20px;
  width: 100%;
}
.car-detail__info-buy .car-detail__buy-btn {
  width: 100%;
  margin-left: 0;
  padding: 0.9em 0;
  font-size: 1.1rem;
}

/* === Характеристики === */
.car-detail__specs-section {
  max-width: 1200px !important;
  margin: 64px auto 0 auto !important;
  padding: 0 20px !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
}
.car-detail__specs-title {
  font-size: 1.7rem;
  font-weight: 800;
  margin-bottom: 32px;
}
.car-detail__specs-table {
  width: 100% !important;
  max-width: 100% !important;
  border-collapse: collapse !important;
  margin-bottom: 32px !important;
  table-layout: fixed !important;
  border-spacing: 0 !important;
  box-sizing: border-box !important;
}
.car-detail__specs-table td {
  padding: 12px 0 !important;
  font-size: 1rem;
  border-bottom: 1px solid #eee;
  line-height: 1.5;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: normal !important;
  vertical-align: top !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}
.car-detail__specs-table tr:last-child td {
  border-bottom: none;
}
.car-detail__specs-table td:first-child {
  font-weight: 400;
  color: #666;
  width: 40% !important;
  padding-right: 20px !important;
  box-sizing: border-box !important;
}
.car-detail__specs-table td:last-child {
  text-align: right !important;
  color: #111;
  font-weight: 600;
  width: 60% !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  padding-left: 20px !important;
  max-width: 60% !important;
  box-sizing: border-box !important;
  hyphens: auto !important;
}
.car-detail__specs-gallery {
  display: flex;
  justify-content: center;
  gap: 18px;
  margin-top: 24px;
  max-width: 100% !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
}
.car-detail__specs-img {
  width: 48%;
  height: 280px;
  object-fit: fill;
  border-radius: 6px;
  display: block;
  margin: 0;
  padding: 0;
}

/* === Лучшее в авто === */
.car-detail__best-section {
  max-width: 1200px;
  margin: 64px auto 0 auto;
  padding: 0 20px;
}
.car-detail__best-title {
  font-size: 1.7rem;
  font-weight: 800;
  margin-bottom: 32px;
}
.car-detail__best-columns {
  display: flex;
  gap: 48px;
}
.car-detail__best-column {
  flex: 1;
}
.car-detail__best-column h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #111;
}
.car-detail__best-columns ul {
  list-style: disc inside;
  font-size: 1rem;
  color: #333;
  line-height: 1.7;
  padding-left: 5px;
}

/* === Похожие предложения === */
.car-detail__similar-section {
  max-width: 1200px;
  margin: 64px auto 0 auto;
  padding: 0 20px;
}
.car-detail__similar-title {
  font-size: 1.7rem;
  font-weight: 800;
  margin-bottom: 32px;
}
.car-detail__similar-list {
  display: flex;
  gap: 24px;
  overflow-x: auto;
}
.car-detail__similar-card {
  min-width: 280px;
  max-width: 350px;
  background: #f8f8f8;
  border-radius: 6px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}
.car-detail__similar-img {
  width: 100%;
  height: 180px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 12px;
}
.car-detail__similar-info {
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 5px;
}
.car-detail__similar-model {
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 5px;
}
.car-detail__similar-meta {
  color: #666;
  font-size: 0.85rem;
}
.car-detail__similar-price {
  margin-top: 10px;
  font-weight: 700;
  font-size: 1.1rem;
  color: #111;
  text-align: right;
}

/* === Контакты === */
.car-detail__contact-section {
  max-width: 1200px;
  margin: 80px auto 0 auto;
  text-align: center;
  padding: 0 20px 40px 20px;
  background-color: transparent;
}
.car-detail__contact-icon {
  width: 60px;
  height: 60px;
  background: #000;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  margin: 0 auto 18px auto;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.car-detail__contact-icon svg {
  transition: transform 0.3s ease;
  width: 24px;
  height: 12px;
}

.car-detail__contact-icon:hover {
  transform: translateX(5px);
}

.car-detail__contact-icon svg path {
  fill: #fff;
}

.car-detail__contact-label {
  color: #666;
  font-size: 1rem;
  margin-bottom: 12px;
  opacity: 0.8;
}
.car-detail__contact-title {
  font-size: 1.8rem;
  font-weight: 800;
  margin-bottom: 0;
  line-height: 1.3;
  color: #000;
}

/* Цветовые точки */
.dot {
  display: inline-block;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  margin-right: 4px;
  border: 1px solid #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
.dot--red {
  background: #b92626;
}
.dot--black {
  background: #1e1f1e;
}
.dot--white {
  background: #ffffff;
  border: 1px solid #ddd;
}
.dot--silver {
  background: #c0c0c0;
}
.dot--blue {
  background: #1a4f95;
}
.dot--brown {
  background: #8b4513;
}
.dot--beige {
  background: #f5f5dc;
  border: 1px solid #ddd;
}
.dot--green {
  background: #1e7a46;
}
.dot--orange {
  background: #ff6700;
}
.dot--yellow {
  background: #ffcc00;
}
.dot--gray {
  background: #808080;
}
.dot--grey {
  background: #808080;
}
.dot--gold {
  background: #ffd700;
}
.dot--purple {
  background: #800080;
}

/* Слайдер стрелки */
.slider-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  border: none;
  font-size: 1.8rem;
  padding: 0.2em 0.5em;
  border-radius: 50%;
  cursor: pointer;
  z-index: 2;
  transition: background 0.2s;
}
.slider-arrow--left {
  left: 10px;
}
.slider-arrow--right {
  right: 10px;
}
.slider-arrow:hover {
  background: rgba(0, 0, 0, 0.7);
}
.slider {
  position: relative;
}

/* Модальное окно для изображений */
.img-modal {
  display: none;
  position: fixed;
  z-index: 100;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  justify-content: center;
  align-items: center;
}
.img-modal__content {
  max-width: 90%;
  max-height: 90%;
  margin: auto;
}
.img-modal__close {
  position: absolute;
  top: 20px;
  right: 30px;
  color: #f1f1f1;
  font-size: 40px;
  font-weight: bold;
  cursor: pointer;
  z-index: 101;
}
.img-modal__close:hover {
  color: #fff;
  text-decoration: none;
}

/* Эффект перехода для изображений */
.slider-img {
  transition: opacity 0.3s ease;
  opacity: 0;
}
.slider-img.active.fade {
  opacity: 1;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* === Mobile Header === */
.mobile-header {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: #fff;
  z-index: 1000;
  padding: 0 20px;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
}

.mobile-header__logo {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.mobile-header__logo-img {
  height: 80px;
  width: auto;
}

.mobile-header-spacer {
  display: none;
  height: 80px;
  width: 100%;
}

/* Адаптивность и медиа-запросы */
@media (max-width: 1200px) {
  .car-detail__headline-row,
  .car-detail__desc-row,
  .car-detail__specs-section,
  .car-detail__best-section,
  .car-detail__similar-section,
  .car-detail__contact-section {
    padding: 0 20px;
  }
}

@media (max-width: 900px) {
  .car-detail__desc-row {
    flex-direction: column;
  }
  .car-detail__specs-gallery {
    flex-direction: column;
  }
  .car-detail__best-columns {
    flex-direction: column;
    gap: 30px;
  }
  .car-detail__similar-card {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .mobile-header {
    display: flex;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
  }

  .mobile-header-spacer {
    display: block;
    height: 80px;
  }

  .hero-header-group {
    display: none;
  }

  .dropdown-menu {
    display: flex;
  }
}

@media (max-width: 600px) {
  .car-detail__headline-row {
    flex-direction: column;
    gap: 15px;
  }
  .car-detail__title {
    font-size: 1.7rem;
  }
  .car-detail__desc-title,
  .car-detail__specs-title,
  .car-detail__best-title,
  .car-detail__similar-title,
  .car-detail__contact-title {
    font-size: 1.5rem;
  }
  .car-detail__main-img {
    height: 250px;
  }
  .car-detail__desc-gallery,
  .car-detail__specs-gallery {
    flex-direction: column;
  }
  .car-detail__desc-img,
  .car-detail__specs-img {
    width: 100%;
    height: 250px;
  }
  .slider {
    height: 250px;
  }
  .car-detail__buy-btn {
    padding: 12px 30px;
    width: 100%;
  }
  .car-detail__info-blocks {
    gap: 8px;
  }
  .car-detail__info-block {
    padding: 12px 15px 12px 50px;
  }
  .car-detail__info-block-icon {
    width: 22px;
    height: 22px;
  }
}

/* Бургер-меню */
.burger {
  display: block;
  width: 30px;
  height: 20px;
  position: relative;
  cursor: pointer;
  border: none;
  background: transparent;
  z-index: 999;
}

.burger span {
  display: block;
  position: absolute;
  height: 2px;
  width: 100%;
  background: #111;
  opacity: 1;
  left: 0;
  transform: rotate(0deg);
  transition: 0.25s ease-in-out;
}

.burger span:nth-child(1) {
  top: 0px;
}
.burger span:nth-child(2) {
  top: 9px;
}
.burger span:nth-child(3) {
  top: 18px;
}

.burger.open span:nth-child(1) {
  top: 9px;
  transform: rotate(135deg);
}

.burger.open span:nth-child(2) {
  opacity: 0;
  left: -60px;
}

.burger.open span:nth-child(3) {
  top: 9px;
  transform: rotate(-135deg);
}

.burger.dark span {
  background: #fff;
}
