const bcrypt = require('bcrypt');
const crypto = require('crypto');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class SecurityUtils {
    constructor() {
        this.dbPath = path.join(__dirname, '../../data/stock.db');
        this.maxLoginAttempts = 5;
        this.lockoutDuration = 15 * 60 * 1000; // 15 минут
        this.cleanupInterval = 60 * 60 * 1000; // 1 час
        
        // Запускаем очистку старых записей
        this.startCleanupTimer();
    }

    // Получение соединения с базой данных
    getDatabase() {
        return new sqlite3.Database(this.dbPath, (err) => {
            if (err) {
                console.error('Ошибка подключения к базе данных:', err.message);
            }
        });
    }

    // Хеширование пароля
    async hashPassword(password) {
        try {
            const saltRounds = 12;
            return await bcrypt.hash(password, saltRounds);
        } catch (error) {
            throw new Error('Ошибка хеширования пароля: ' + error.message);
        }
    }

    // Проверка пароля
    async verifyPassword(password, hash) {
        try {
            return await bcrypt.compare(password, hash);
        } catch (error) {
            throw new Error('Ошибка проверки пароля: ' + error.message);
        }
    }

    // Генерация безопасного токена
    generateSecureToken(length = 32) {
        return crypto.randomBytes(length).toString('hex');
    }

    // Валидация пароля
    validatePassword(password) {
        const errors = [];

        if (!password || password.length < 8) {
            errors.push('Пароль должен содержать минимум 8 символов');
        }

        if (!/[a-z]/.test(password)) {
            errors.push('Пароль должен содержать строчные буквы');
        }

        if (!/[A-Z]/.test(password)) {
            errors.push('Пароль должен содержать заглавные буквы');
        }

        if (!/\d/.test(password)) {
            errors.push('Пароль должен содержать цифры');
        }

        if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            errors.push('Пароль должен содержать специальные символы');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    // Валидация email
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Валидация логина
    validateUsername(username) {
        if (!username || username.length < 3) {
            return { isValid: false, error: 'Логин должен содержать минимум 3 символа' };
        }

        if (username.length > 50) {
            return { isValid: false, error: 'Логин не должен превышать 50 символов' };
        }

        if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
            return { isValid: false, error: 'Логин может содержать только буквы, цифры, дефис и подчеркивание' };
        }

        return { isValid: true };
    }

    // Запись попытки входа
    async recordLoginAttempt(ipAddress, username, success) {
        return new Promise((resolve, reject) => {
            const db = this.getDatabase();
            const sql = `
                INSERT INTO login_attempts (ip_address, username, success, created_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            `;

            db.run(sql, [ipAddress, username, success ? 1 : 0], (err) => {
                db.close();
                if (err) {
                    console.error('Ошибка записи попытки входа:', err.message);
                    return reject(err);
                }
                resolve();
            });
        });
    }

    // Проверка блокировки IP
    async isIpBlocked(ipAddress) {
        return new Promise((resolve, reject) => {
            const db = this.getDatabase();
            const timeLimit = new Date(Date.now() - this.lockoutDuration).toISOString();
            
            const sql = `
                SELECT COUNT(*) as attempts
                FROM login_attempts 
                WHERE ip_address = ? 
                AND success = 0 
                AND created_at > ?
            `;

            db.get(sql, [ipAddress, timeLimit], (err, row) => {
                db.close();
                if (err) {
                    console.error('Ошибка проверки блокировки IP:', err.message);
                    return reject(err);
                }
                
                const isBlocked = row.attempts >= this.maxLoginAttempts;
                resolve(isBlocked);
            });
        });
    }

    // Получение количества неудачных попыток
    async getFailedAttempts(ipAddress) {
        return new Promise((resolve, reject) => {
            const db = this.getDatabase();
            const timeLimit = new Date(Date.now() - this.lockoutDuration).toISOString();
            
            const sql = `
                SELECT COUNT(*) as attempts
                FROM login_attempts 
                WHERE ip_address = ? 
                AND success = 0 
                AND created_at > ?
            `;

            db.get(sql, [ipAddress, timeLimit], (err, row) => {
                db.close();
                if (err) {
                    console.error('Ошибка получения количества попыток:', err.message);
                    return reject(err);
                }
                resolve(row.attempts || 0);
            });
        });
    }

    // Очистка успешных попыток входа для IP
    async clearSuccessfulAttempts(ipAddress) {
        return new Promise((resolve, reject) => {
            const db = this.getDatabase();
            const sql = `
                DELETE FROM login_attempts 
                WHERE ip_address = ? AND success = 0
            `;

            db.run(sql, [ipAddress], (err) => {
                db.close();
                if (err) {
                    console.error('Ошибка очистки попыток входа:', err.message);
                    return reject(err);
                }
                resolve();
            });
        });
    }

    // Очистка старых записей попыток входа
    async cleanupOldAttempts() {
        return new Promise((resolve, reject) => {
            const db = this.getDatabase();
            const timeLimit = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(); // 24 часа
            
            const sql = `
                DELETE FROM login_attempts 
                WHERE created_at < ?
            `;

            db.run(sql, [timeLimit], function(err) {
                db.close();
                if (err) {
                    console.error('Ошибка очистки старых попыток:', err.message);
                    return reject(err);
                }
                console.log(`Очищено ${this.changes} старых записей попыток входа`);
                resolve(this.changes);
            });
        });
    }

    // Запуск таймера очистки
    startCleanupTimer() {
        setInterval(async () => {
            try {
                await this.cleanupOldAttempts();
            } catch (error) {
                console.error('Ошибка при автоматической очистке:', error.message);
            }
        }, this.cleanupInterval);
    }

    // Санитизация входных данных
    sanitizeInput(input) {
        if (typeof input !== 'string') {
            return input;
        }
        
        return input
            .trim()
            .replace(/[<>]/g, '') // Удаляем потенциально опасные символы
            .substring(0, 1000); // Ограничиваем длину
    }

    // Получение IP адреса из запроса
    getClientIp(req) {
        return req.ip || 
               req.connection.remoteAddress || 
               req.socket.remoteAddress ||
               (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
               '127.0.0.1';
    }

    // Генерация CSRF токена
    generateCSRFToken() {
        return this.generateSecureToken(32);
    }

    // Проверка CSRF токена
    verifyCSRFToken(sessionToken, requestToken) {
        if (!sessionToken || !requestToken) {
            return false;
        }
        return sessionToken === requestToken;
    }
}

module.exports = new SecurityUtils();
