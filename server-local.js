// Локальный сервер для разработки
require('dotenv').config({ path: '.env.local' });

const express = require("express");
const path = require("path");
const session = require("express-session");
const fs = require("fs");

const { sendContactForm } = require("./config/mail-config");

// Импорт системы авторизации
const authController = require("./server/controllers/authController");
const authMiddleware = require("./server/middleware/auth");

// Добавляем node-fetch для API запросов
let fetch;
(async () => {
  try {
    const fetchModule = await import("node-fetch");
    fetch = fetchModule.default;
  } catch (error) {
    console.warn(
      "node-fetch не найден, API encar может не работать:",
      error.message
    );
  }
})();

const app = express();

// Настройки сервера
const port = process.env.PORT || 3000;
const host = process.env.HOST || "localhost";
const domain = process.env.DOMAIN || "localhost";
const site_url = process.env.SITE_URL || "http://localhost:3000";
const isProduction = false; // Всегда false для локального сервера

console.log("🔧 Запуск локального сервера для разработки");
console.log("====================================");
console.log(`🌐 Режим: development`);
console.log(`📡 Адрес: ${site_url}`);
console.log(`🔗 Домен: ${domain}`);
console.log("====================================\n");

try {
  // Загрузка маршрутов админки
  console.log("Загрузка модулей маршрутизаторов...");

  let adminRoutes;
  try {
    adminRoutes = require("./server/config/routes.js");
    console.log("✅ Найден модуль маршрутизатора server/config/routes.js");
  } catch (routeError) {
    try {
      adminRoutes = require("./server/config/routes/index.js");
      console.log("✅ Найден модуль маршрутизатора server/config/routes/index.js");
    } catch (indexError) {
      console.log("⚠️ Модуль маршрутизатора не найден, создаем заглушку");
      adminRoutes = require("express").Router();
      adminRoutes.stack = [];
    }
  }

  const uploadRoutes = require("./admin/js/upload-routes");
  console.log("✅ Модули маршрутизаторов загружены успешно");

  // Middleware для парсинга JSON и URL-encoded данных
  app.use(express.json({ limit: "50mb" }));
  app.use(express.urlencoded({ extended: true, limit: "50mb" }));

  // CORS для локальной разработки
  app.use((req, res, next) => {
    res.header("Access-Control-Allow-Origin", "*");
    res.header(
      "Access-Control-Allow-Methods",
      "GET, POST, PUT, DELETE, OPTIONS"
    );
    res.header(
      "Access-Control-Allow-Headers",
      "Origin, X-Requested-With, Content-Type, Accept, Authorization"
    );

    if (req.method === "OPTIONS") {
      res.sendStatus(200);
    } else {
      next();
    }
  });

  // Настройка сессий (упрощенная для локальной разработки)
  const sessionConfig = {
    secret: process.env.SESSION_SECRET || "local-dev-secret",
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: false, // HTTP для локальной разработки
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000, // 24 часа
    },
  };

  app.use(session(sessionConfig));
  console.log("🗄️ Используется память для хранения сессий (локальная разработка)");

  // Упрощенные заголовки безопасности для разработки
  app.use((req, res, next) => {
    res.setHeader("X-Content-Type-Options", "nosniff");
    res.setHeader("X-Frame-Options", "SAMEORIGIN");
    next();
  });

  // Подробное логирование для разработки
  app.use((req, res, next) => {
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
    if (req.body && Object.keys(req.body).length > 0) {
      console.log("📦 Body:", JSON.stringify(req.body, null, 2));
    }
    if (req.query && Object.keys(req.query).length > 0) {
      console.log("🔍 Query:", JSON.stringify(req.query, null, 2));
    }
    next();
  });

  // Убедимся, что директория uploads существует
  const uploadsDir = path.join(__dirname, "data", "uploads");
  if (!fs.existsSync(uploadsDir)) {
    console.log("📁 Создание директории uploads...");
    fs.mkdirSync(uploadsDir, { recursive: true });
    console.log("✅ Директория uploads создана:", uploadsDir);
  } else {
    console.log("✅ Директория uploads существует:", uploadsDir);
  }

  // Специальные заголовки для изображений логотипов
  app.use('/assets/img/logos', (req, res, next) => {
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    next();
  });

  // Раздача статических файлов из директории public
  app.use(
    express.static("public", {
      dotfiles: "allow",
      etag: false,
      index: false,
      maxAge: 0, // Отключаем кеширование для разработки
      setHeaders: (res, path) => {
        if (path.includes('/logos/')) {
          res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
          res.setHeader('Pragma', 'no-cache');
          res.setHeader('Expires', '0');
        }
      }
    })
  );

  // Раздача статических файлов из директории admin
  app.use(
    "/admin",
    express.static("admin", {
      dotfiles: "allow",
      etag: true,
      index: false,
      maxAge: 0, // Отключаем кеширование для разработки
    })
  );

  // Раздача загруженных файлов
  app.use(
    "/uploads",
    express.static("data/uploads", {
      dotfiles: "allow",
      etag: true,
      index: false,
      maxAge: 0, // Отключаем кеширование для разработки
    })
  );

  // Простой тестовый маршрут
  app.get("/test", (req, res) => {
    res.json({
      message: "🚀 Локальный сервер SHMS Авто работает!",
      mode: "development",
      timestamp: new Date().toISOString(),
      site_url: site_url,
    });
  });

  // API курсов валют для локальной разработки
  app.get("/api/exchange-rates.php", (req, res) => {
    console.log("💰 Запрос курсов валют (локальная заглушка)");

    // Заглушка с тестовыми курсами
    const mockRates = {
      success: true,
      KRW_to_RUB: 0.075, // 1 корейская вона = 0.075 рубля
      USD_rates: {
        usdtrub: {
          sell: 95.50,
          buy: 94.50
        }
      },
      updated: new Date().toISOString(),
      source: "local_mock"
    };

    res.json(mockRates);
  });

  // Маршруты авторизации
  app.post("/api/auth/login", authController.login);
  app.post("/api/auth/logout", authController.logout);
  app.get("/api/auth/check", authController.checkAuth);
  app.post("/api/auth/forgot-password", authController.forgotPassword);
  app.post("/api/auth/reset-password", authController.resetPassword);
  app.post("/api/auth/change-password", authController.changePassword);
  console.log("🔐 Маршруты авторизации подключены");

  // Маршруты загрузки файлов
  app.use("/admin", uploadRoutes);
  console.log("📤 Маршруты загрузки файлов подключены");

  // Подключение маршрутов админки
  app.use("/api", adminRoutes);
  app.use("/admin", adminRoutes);
  console.log("🔧 Маршруты админки подключены");

  // Маршрут для главной страницы
  app.get("/", (req, res) => {
    console.log("🏠 Запрос главной страницы");
    res.sendFile(path.join(__dirname, "public", "index.html"));
  });

  // Маршрут для страницы stock
  app.get("/stock", (req, res) => {
    console.log("🚗 Запрос страницы stock");
    res.sendFile(path.join(__dirname, "public", "stock.html"));
  });

  // Маршрут для страницы order
  app.get("/order", (req, res) => {
    console.log("📋 Запрос страницы order");
    res.sendFile(path.join(__dirname, "public", "order.html"));
  });

  // Маршрут для страницы contacts
  app.get("/contacts", (req, res) => {
    console.log("📞 Запрос страницы contacts");
    res.sendFile(path.join(__dirname, "public", "contacts.html"));
  });

  // Маршрут для промо-страницы Физиева
  app.get("/Fiziev", (req, res) => {
    console.log("🎯 Запрос промо-страницы Fiziev");
    res.sendFile(path.join(__dirname, "public", "Fiziev.html"));
  });

  app.get("/fiziev", (req, res) => {
    res.redirect("/Fiziev");
  });

  // Обратная совместимость со старым названием
  app.get("/Fiziyev", (req, res) => {
    console.log("🎯 Запрос старого URL Fiziyev (redirect)");
    res.redirect("/Fiziev");
  });

  app.get("/fiziyev", (req, res) => {
    console.log("🎯 Запрос старого URL fiziyev (redirect)");
    res.redirect("/Fiziev");
  });

  // Маршрут для админ-панели
  app.get("/admin", (req, res) => {
    console.log("🔧 Запрос админ-панели");
    console.log("🔍 Проверка сессии:", {
      hasSession: !!req.session,
      adminId: req.session?.adminId,
    });

    if (req.session && req.session.adminId) {
      console.log("✅ Пользователь авторизован, отправляем админку");
      res.sendFile(path.join(__dirname, "admin", "index.html"));
    } else {
      console.log("❌ Пользователь не авторизован, перенаправляем на логин");
      res.redirect("/admin/login.html");
    }
  });

  // Защищенные маршруты админки
  app.get("/admin/dashboard", authMiddleware.requireAuth, (req, res) => {
    res.sendFile(path.join(__dirname, "admin", "index.html"));
  });

  // Маршруты для страниц авторизации
  app.get("/admin/login.html", (req, res) => {
    res.sendFile(path.join(__dirname, "admin", "login.html"));
  });

  app.get("/admin/forgot-password.html", (req, res) => {
    res.sendFile(path.join(__dirname, "admin", "forgot-password.html"));
  });

  app.get("/admin/reset-password.html", (req, res) => {
    res.sendFile(path.join(__dirname, "admin", "reset-password.html"));
  });

  // Маршрут для детального просмотра автомобиля
  app.get("/car-details/:id", (req, res) => {
    const id = req.params.id;
    console.log("🚗 Запрос детальной страницы автомобиля ID:", id);
    res.sendFile(path.join(__dirname, "public", "car-detail.html"));
  });

  // API маршрут для поиска автомобилей
  app.get("/api/encar", async (req, res) => {
    try {
      if (!fetch) {
        console.error("node-fetch не доступен");
        return res.status(500).json({ error: "API временно недоступен" });
      }

      const {
        fileType = "active_offer",
        date,
        brand = "",
        model = "",
        limit = 100,
        offset = 0,
        direct = 1,
      } = req.query;

      if (!date) {
        return res.status(400).json({ error: "Параметр date обязателен" });
      }

      console.log("🔍 API encar - получены параметры:", {
        fileType,
        date,
        brand,
        model,
        limit,
        offset,
      });

      // Логируем ВСЕ параметры запроса для отладки фильтров
      console.log("🔍 ВСЕ параметры запроса:", req.query);

      // Формируем URL для запроса к PHP серверу
      const phpServerUrl = `http://localhost:8000/api/fast-encar.php`;
      const allParams = new URLSearchParams(req.query);
      const apiUrl = `${phpServerUrl}?${allParams.toString()}`;
      console.log("🔄 Проксирование запроса к PHP серверу:", apiUrl);

      // Создаем AbortController для timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000);

      const response = await fetch(apiUrl, {
        signal: controller.signal,
        headers: {
          "User-Agent": "SHMS-Auto-Local-Server/1.0",
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        console.error("❌ Ошибка PHP API:", response.status, response.statusText);
        return res.status(502).json({
          error: "Ошибка PHP API",
          status: response.status,
          statusText: response.statusText,
        });
      }

      // Проверяем Content-Type
      const contentType = response.headers.get('content-type');
      console.log("🔍 Content-Type от PHP API:", contentType);

      // Пытаемся получить текст ответа
      const text = await response.text();

      // Проверяем, является ли ответ JSON (независимо от Content-Type)
      let data;
      try {
        data = JSON.parse(text);
        console.log("✅ JSON успешно распарсен, несмотря на Content-Type:", contentType);
      } catch (parseError) {
        console.error("❌ PHP API вернул не JSON:", text.substring(0, 200));
        return res.status(502).json({
          error: "PHP API returned non-JSON content",
          contentType: contentType,
          preview: text.substring(0, 200)
        });
      }
      console.log(
        "✅ Получен ответ от PHP API, количество записей:",
        Array.isArray(data) ? data.length : "не массив"
      );

      res.json(data);
    } catch (error) {
      console.error("❌ Ошибка API encar:", error.message);
      console.error("❌ Тип ошибки:", error.name);
      console.error("❌ Stack trace:", error.stack);

      let errorMessage = "Ошибка сервера при обработке запроса";
      if (error.name === 'AbortError') {
        errorMessage = "Превышено время ожидания ответа от API";
      }

      res.status(500).json({
        error: errorMessage,
        details: error.message,
        type: error.name,
      });
    }
  });

  // API для получения отзывов
  app.get("/api/reviews", async (req, res) => {
    try {
      console.log("📋 Запрос списка отзывов");

      // Возвращаем пустой массив отзывов (заглушка)
      // В будущем здесь будет загрузка из базы данных
      const reviews = [];

      res.json(reviews);
    } catch (error) {
      console.error("❌ Ошибка при получении отзывов:", error);
      res.status(500).json({ error: "Ошибка сервера" });
    }
  });

  // API для получения отзыва по ID
  app.get("/api/reviews/:id", async (req, res) => {
    try {
      const reviewId = req.params.id;
      console.log("📋 Запрос отзыва по ID:", reviewId);

      // Заглушка - отзыв не найден
      res.status(404).json({ error: "Отзыв не найден" });
    } catch (error) {
      console.error("❌ Ошибка при получении отзыва:", error);
      res.status(500).json({ error: "Ошибка сервера" });
    }
  });

  // 🚀 НОВЫЙ API маршрут для быстрого поиска автомобилей с загрузкой всех категорий
  app.get("/api/fast-encar", async (req, res) => {
    try {
      // Проверяем доступность fetch
      if (!fetch) {
        console.error("node-fetch не доступен");
        return res.status(500).json({ error: "API временно недоступен" });
      }

      const {
        date = new Date().toISOString().split('T')[0],
        limit = 20,
        offset = 0,
        priority_sort = "false",
        load_all_category = "false",
        category = "",
        brand = "",
        model = "",
        year_min = "",
        year_max = "",
        price_min = "",
        price_max = "",
        cars_only = "false"
      } = req.query;

      console.log("🚀 API fast-encar - получены параметры:", {
        date,
        limit,
        offset,
        priority_sort,
        load_all_category,
        category,
        brand,
        model,
        cars_only
      });

      // Формируем URL для запроса к PHP серверу (fast-encar.php)
      const phpServerUrl = `http://localhost:8000/api/fast-encar.php`;
      const allParams = new URLSearchParams(req.query);
      const apiUrl = `${phpServerUrl}?${allParams.toString()}`;
      console.log("🔄 Проксирование запроса к fast-encar PHP серверу:", apiUrl);

      const response = await fetch(apiUrl, {
        timeout: 60000, // Увеличиваем таймаут для загрузки больших объемов
        headers: {
          "User-Agent": "SHMS-Auto-Local-Server/1.0",
        },
      });

      if (!response.ok) {
        console.error("❌ Ошибка fast-encar PHP API:", response.status, response.statusText);
        return res.status(502).json({
          error: "Ошибка fast-encar PHP API",
          status: response.status,
          statusText: response.statusText,
        });
      }

      const data = await response.json();

      // Логируем результат
      if (data.success) {
        console.log("✅ Получен ответ от fast-encar PHP API:", {
          total: data.total,
          count: data.count,
          load_all_category: data.load_all_category,
          category: data.category,
          priorities_loaded: data.priorities_loaded
        });
      } else {
        console.log("⚠️ fast-encar API вернул ошибку:", data.error);
      }

      // Добавляем CORS заголовки
      res.header("Access-Control-Allow-Origin", "*");
      res.header(
        "Access-Control-Allow-Methods",
        "GET, POST, PUT, DELETE, OPTIONS"
      );
      res.header(
        "Access-Control-Allow-Headers",
        "Origin, X-Requested-With, Content-Type, Accept, Authorization"
      );

      // Если запрашивается только массив автомобилей (для совместимости)
      if (cars_only === 'true' && data.success && data.data) {
        res.json(data.data);
      } else {
        res.json(data);
      }
    } catch (error) {
      console.error("❌ Ошибка API fast-encar:", error.message);
      res.status(500).json({
        error: "Ошибка сервера при обработке запроса fast-encar",
        details: error.message,
      });
    }
  });

  // Обработка отзывов и заявок с сайта
  app.post("/api/send-review", async (req, res) => {
    try {
      const formData = req.body;
      console.log("📧 Получена заявка:", formData);

      if (!formData.name && !formData.contact && !formData.message) {
        return res.status(400).json({
          error:
            "Необходимо заполнить хотя бы одно из полей: имя, контакт или сообщение",
        });
      }

      if (formData.promo) {
        console.log("🎫 Промокод:", formData.promo);
        if (formData.promoValid) {
          console.log("✅ Промокод ВАЛИДНЫЙ:", formData.promoCode);
        } else {
          console.log("❌ Промокод НЕВАЛИДНЫЙ");
        }
      }

      // Отправка email
      await sendContactForm(formData);

      console.log("✅ Заявка успешно отправлена:", {
        name: formData.name,
        contact: formData.contact || formData.phone || formData.email,
        formType: formData.formType || "review",
      });

      let message = "Ваше сообщение успешно отправлено! Мы свяжемся с вами в ближайшее время.";

      if (formData.promoValid && formData.promoCode === "ATAMAN") {
        message += " 🎉 Промокод ATAMAN успешно применен!";
      }

      res.json({
        success: true,
        message: message,
        promoApplied: formData.promoValid || false,
      });
    } catch (error) {
      console.error("❌ Ошибка при обработке заявки:", error);
      res.status(500).json({
        error: "Ошибка сервера при обработке заявки",
        details: error.message,
      });
    }
  });

  // Обработка 404 ошибок
  app.use((req, res) => {
    console.log("❌ 404 - Страница не найдена:", req.url);
    res.status(404).json({
      error: "Страница не найдена",
      url: req.url,
      method: req.method,
    });
  });

  // Обработка ошибок
  app.use((err, req, res, next) => {
    console.error("❌ Ошибка сервера:", err);
    res.status(500).json({
      error: "Внутренняя ошибка сервера",
      details: err.message,
    });
  });

  // Запуск сервера
  app.listen(port, host, () => {
    console.log("\n=================================");
    console.log("🚀 ЛОКАЛЬНЫЙ СЕРВЕР ЗАПУЩЕН!");
    console.log("🌐 Режим: development");
    console.log(`📡 Адрес: http://${host}:${port}`);
    console.log(`🔗 Публичный URL: ${site_url}`);
    console.log("📋 Доступные маршруты:");
    console.log("   - Главная страница: /");
    console.log("   - Авто в наличии: /stock");
    console.log("   - Авто под заказ: /order");
    console.log("   - Контакты: /contacts");
    console.log("   - Промо-страница Физиева: /Fiziev");
    console.log("   - Админ-панель: /admin");
    console.log("   - Загруженные файлы: /uploads");
    console.log("   - API тест: /test");
    console.log("   - API заявки: /api/send-review");
    console.log("   - API поиск авто: /api/encar");
    console.log("   - API быстрый поиск: /api/fast-encar");
    console.log("=================================");
  });

} catch (error) {
  console.error("❌ Критическая ошибка при запуске сервера:", error);
  process.exit(1);
}
