const express = require("express");
const path = require("path");
const session = require("express-session");
const fs = require("fs");
require("dotenv").config();

// Импорт хранилища сессий для продакшена
const FileStore = require('session-file-store')(session);

const { sendContactForm } = require("./config/mail-config");

// Импорт системы авторизации
const authController = require("./server/controllers/authController");
const authMiddleware = require("./server/middleware/auth");

// Добавляем node-fetch для API запросов
let fetch;
(async () => {
  try {
    const fetchModule = await import("node-fetch");
    fetch = fetchModule.default;
  } catch (error) {
    console.warn(
      "node-fetch не найден, API encar может не работать:",
      error.message
    );
  }
})();
const app = express();

// Настройки сервера из переменных окружения
const port = process.env.PORT || 3000;
const host = process.env.HOST || "0.0.0.0";
const domain = process.env.DOMAIN || "localhost";
const site_url = process.env.SITE_URL || "http://localhost";
const isProduction = process.env.NODE_ENV === "production";

try {
  // Загрузка маршрутов админки
  console.log("Загрузка модулей маршрутизаторов...");

  // Проверяем существование файла в новой структуре
  let adminRoutes;
  try {
    // Пробуем загрузить из новой директории server/config/routes.js
    adminRoutes = require("./server/config/routes.js");
    console.log("Найден модуль маршрутизатора server/config/routes.js");
  } catch (routeError) {
    try {
      // Пробуем загрузить из server/config/routes/index.js
      adminRoutes = require("./server/config/routes/index.js");
      console.log("Найден модуль маршрутизатора server/config/routes/index.js");
    } catch (indexError) {
      console.log(
        "Модуль маршрутизатора server/config не найден, создаем заглушку"
      );
      // Создаем простую заглушку для маршрутов
      adminRoutes = require("express").Router();
      adminRoutes.stack = [];
    }
  }

  const uploadRoutes = require("./admin/js/upload-routes");
  console.log("Модули маршрутизаторов загружены успешно");

  // Middleware для парсинга JSON и URL-encoded данных с увеличенным лимитом
  app.use(express.json({ limit: "50mb" }));
  app.use(express.urlencoded({ extended: true, limit: "50mb" }));

  // CORS для продакшн
  app.use((req, res, next) => {
    res.header("Access-Control-Allow-Origin", "*");
    res.header(
      "Access-Control-Allow-Methods",
      "GET, POST, PUT, DELETE, OPTIONS"
    );
    res.header(
      "Access-Control-Allow-Headers",
      "Origin, X-Requested-With, Content-Type, Accept, Authorization"
    );

    if (req.method === "OPTIONS") {
      res.sendStatus(200);
    } else {
      next();
    }
  });

  // Настройка сессий
  const sessionConfig = {
    secret: process.env.SESSION_SECRET || "your-secret-key",
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: isProduction && process.env.SECURE_COOKIES === "true", // true для HTTPS в продакшене
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000, // 24 часа
    },
  };

  // В продакшене используем файловое хранилище сессий
  if (isProduction) {
    sessionConfig.store = new FileStore({
      path: path.join(__dirname, 'data', 'sessions'),
      ttl: 24 * 60 * 60, // 24 часа в секундах
      retries: 3,
      logFn: function(message) {
        console.log('📁 Session store:', message);
      }
    });
    console.log("🗄️ Используется файловое хранилище сессий");
  } else {
    console.log("🗄️ Используется память для хранения сессий");
  }

  app.use(session(sessionConfig));

  // Безопасные заголовки для продакшена
  if (isProduction) {
    app.use((req, res, next) => {
      // HTTPS редирект
      if (
        process.env.HTTPS_REDIRECT === "true" &&
        req.header("x-forwarded-proto") !== "https"
      ) {
        return res.redirect(`https://${req.header("host")}${req.url}`);
      }

      // Безопасные заголовки
      res.setHeader("X-Content-Type-Options", "nosniff");
      res.setHeader("X-Frame-Options", "DENY");
      res.setHeader("X-XSS-Protection", "1; mode=block");
      res.setHeader("Referrer-Policy", "strict-origin-when-cross-origin");
      res.setHeader(
        "Permissions-Policy",
        "geolocation=(), microphone=(), camera=()"
      );

      next();
    });
  } else {
    // Упрощенные заголовки безопасности для разработки
    app.use((req, res, next) => {
      res.setHeader("X-Content-Type-Options", "nosniff");
      res.setHeader("X-Frame-Options", "SAMEORIGIN");
      next();
    });
  }

  // Подробное логирование для продакшн
  app.use((req, res, next) => {
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
    if (req.body && Object.keys(req.body).length > 0) {
      console.log("📦 Body:", JSON.stringify(req.body, null, 2));
    }
    if (req.query && Object.keys(req.query).length > 0) {
      console.log("🔍 Query:", JSON.stringify(req.query, null, 2));
    }
    next();
  });

  // Логирование исходящих ответов
  app.use((req, res, next) => {
    const oldSend = res.send;
    res.send = function (data) {
      console.log(
        `📤 Отправка ответа [${res.statusCode}] для ${req.method} ${req.url}`
      );
      return oldSend.apply(res, arguments);
    };
    next();
  });

  // Убедимся, что директория uploads существует
  const uploadsDir = path.join(__dirname, "data", "uploads");
  if (!fs.existsSync(uploadsDir)) {
    console.log("📁 Создание директории uploads...");
    fs.mkdirSync(uploadsDir, { recursive: true });
    console.log("✅ Директория uploads создана:", uploadsDir);
  } else {
    console.log("✅ Директория uploads существует:", uploadsDir);
  }

  // Раздача статических файлов из директории public
  app.use(
    express.static("public", {
      dotfiles: "allow",
      etag: true,
      index: false,
      maxAge: isProduction ? "1d" : 0, // Отключаем кеширование для разработки
    })
  );

  // Раздача статических файлов из директории admin
  app.use(
    "/admin",
    express.static("admin", {
      dotfiles: "allow",
      etag: true,
      index: false,
      maxAge: isProduction ? "1d" : 0, // Отключаем кеширование для разработки
    })
  );

  // Раздача загруженных файлов из новой директории data/uploads
  app.use(
    "/uploads",
    express.static("data/uploads", {
      dotfiles: "allow",
      etag: true,
      index: false,
      maxAge: isProduction ? "1d" : 0, // Отключаем кеширование для разработки
    })
  );

  // Простой тестовый маршрут
  app.get("/test", (req, res) => {
    res.json({
      message: "🚀 Сервер SHMS Авто работает!",
      mode: process.env.NODE_ENV || "development",
      timestamp: new Date().toISOString(),
      site_url: site_url,
    });
  });

  // API курсов валют
  app.get("/api/exchange-rates.php", async (req, res) => {
    console.log("💰 Запрос курсов валют");

    if (isProduction) {
      try {
        // В продакшене пытаемся получить реальные курсы
        const phpServerUrl = `${site_url}/api/exchange-rates.php`;
        console.log("🔄 Проксирование запроса к PHP серверу:", phpServerUrl);

        const response = await fetch(phpServerUrl, {
          timeout: 10000,
          headers: {
            "User-Agent": "SHMS-Auto-Server/1.0",
          },
        });

        if (response.ok) {
          const data = await response.json();
          console.log("✅ Получены курсы валют от PHP API");
          res.json(data);
          return;
        }
      } catch (error) {
        console.warn("⚠️ Ошибка получения курсов от PHP API:", error.message);
      }
    }

    // Заглушка с тестовыми курсами (для разработки или fallback)
    const mockRates = {
      success: true,
      KRW_to_RUB: 0.075, // 1 корейская вона = 0.075 рубля
      USD_rates: {
        usdtrub: {
          sell: 95.50,
          buy: 94.50
        }
      },
      updated: new Date().toISOString(),
      source: isProduction ? "fallback_mock" : "local_mock"
    };

    res.json(mockRates);
  });

  // Маршруты авторизации (упрощенные для совместимости)
  app.post("/api/auth/login", authController.login);
  app.post("/api/auth/logout", authController.logout);
  app.get("/api/auth/check", authController.checkAuth);
  app.post("/api/auth/forgot-password", authController.forgotPassword);
  app.post("/api/auth/reset-password", authController.resetPassword);
  app.post("/api/auth/change-password", authController.changePassword);
  console.log("🔐 Маршруты авторизации подключены");

  // Маршруты загрузки файлов (приоритетнее других маршрутов)
  app.use("/admin", uploadRoutes);
  console.log("📤 Маршруты загрузки файлов подключены");

  // Выводим все маршруты для загрузки
  console.log("📋 Доступные маршруты загрузки:");
  uploadRoutes.stack.forEach((r) => {
    if (r.route && r.route.path) {
      console.log(
        `   ${r.route.stack[0].method.toUpperCase()} /admin${r.route.path}`
      );
    }
  });

  // Подключение маршрутов админки stock
  app.use("/api", adminRoutes); // Подключаем к /api для совместимости со старым кодом
  app.use("/admin", adminRoutes);
  console.log("🔧 Маршруты админки подключены");

  // Логирование зарегистрированных маршрутов
  console.log("📋 Зарегистрированные маршруты:");
  adminRoutes.stack.forEach((r) => {
    if (r.route && r.route.path) {
      console.log(
        `   ${Object.keys(r.route.methods).join(",").toUpperCase()} ${
          r.route.path
        }`
      );
    }
  });

  // Маршрут для главной страницы
  app.get("/", (req, res) => {
    console.log("🏠 Запрос главной страницы");
    res.sendFile(path.join(__dirname, "public", "index.html"), (err) => {
      if (err && !res.headersSent) {
        console.error("❌ Ошибка при отправке index.html:", err);
        res.status(500).send("Ошибка при загрузке страницы");
      }
    });
  });

  // Маршрут для страницы stock
  app.get("/stock", (req, res) => {
    console.log("🚗 Запрос страницы stock");
    res.sendFile(path.join(__dirname, "public", "stock.html"), (err) => {
      if (err && !res.headersSent) {
        console.error("❌ Ошибка при отправке stock.html:", err);
        res.status(500).send("Ошибка при загрузке страницы");
      }
    });
  });

  // Маршрут для страницы order
  app.get("/order", (req, res) => {
    console.log("📋 Запрос страницы order");
    res.sendFile(path.join(__dirname, "public", "order.html"), (err) => {
      if (err && !res.headersSent) {
        console.error("❌ Ошибка при отправке order.html:", err);
        res.status(500).send("Ошибка при загрузке страницы");
      }
    });
  });

  // Маршрут для страницы contacts
  app.get("/contacts", (req, res) => {
    console.log("📞 Запрос страницы contacts");
    res.sendFile(path.join(__dirname, "public", "contacts.html"), (err) => {
      if (err && !res.headersSent) {
        console.error("❌ Ошибка при отправке contacts.html:", err);
        res.status(500).send("Ошибка при загрузке страницы");
      }
    });
  });

  // Маршруты для промо-страницы Физиева
  app.get("/Fiziev", (req, res) => {
    console.log("🎯 Запрос промо-страницы Fiziev");
    res.sendFile(path.join(__dirname, "public", "Fiziev.html"), (err) => {
      if (err && !res.headersSent) {
        console.error("❌ Ошибка при отправке Fiziev.html:", err);
        res.status(500).send("Ошибка при загрузке промо-страницы");
      }
    });
  });

  app.get("/fiziev", (req, res) => {
    console.log("🎯 Запрос промо-страницы fiziev (redirect)");
    res.redirect("/Fiziev");
  });

  // Обратная совместимость со старым названием
  app.get("/Fiziyev", (req, res) => {
    console.log("🎯 Запрос старого URL Fiziyev (redirect)");
    res.redirect("/Fiziev");
  });

  app.get("/fiziyev", (req, res) => {
    console.log("🎯 Запрос старого URL fiziyev (redirect)");
    res.redirect("/Fiziev");
  });

  // Маршрут для админ-панели (перенаправление на логин)
  app.get("/admin", (req, res) => {
    console.log("🔧 Запрос админ-панели");
    console.log("🔍 Проверка сессии:", {
      hasSession: !!req.session,
      adminId: req.session?.adminId,
      isProduction: isProduction
    });

    // Проверяем авторизацию независимо от режима
    if (req.session && req.session.adminId) {
      console.log("✅ Пользователь авторизован, отправляем админку");
      res.sendFile(path.join(__dirname, "admin", "index.html"));
    } else {
      console.log("❌ Пользователь не авторизован, перенаправляем на логин");
      res.redirect("/admin/login.html");
    }
  });

  // Защищенные маршруты админки
  app.get("/admin/dashboard", authMiddleware.requireAuth, (req, res) => {
    res.sendFile(path.join(__dirname, "admin", "index.html"));
  });

  // Маршруты для страниц авторизации (без защиты)
  app.get("/admin/login.html", (req, res) => {
    res.sendFile(path.join(__dirname, "admin", "login.html"));
  });

  app.get("/admin/forgot-password.html", (req, res) => {
    res.sendFile(path.join(__dirname, "admin", "forgot-password.html"));
  });

  app.get("/admin/reset-password.html", (req, res) => {
    res.sendFile(path.join(__dirname, "admin", "reset-password.html"));
  });

  // Маршрут для страницы детального просмотра автомобиля
  app.get("/car-details/:id", (req, res) => {
    const id = req.params.id;
    console.log("🚗 Запрос детальной страницы автомобиля ID:", id);
    res.sendFile(path.join(__dirname, "public", "car-detail.html"));
  });

  // API маршрут для поиска автомобилей
  app.get("/api/encar", async (req, res) => {
    try {
      // Проверяем доступность fetch
      if (!fetch) {
        console.error("node-fetch не доступен");
        return res.status(500).json({ error: "API временно недоступен" });
      }

      const {
        fileType = "active_offer",
        date,
        brand = "",
        model = "",
        limit = 100,
        offset = 0,
        direct = 1,
      } = req.query;

      if (!date) {
        return res.status(400).json({ error: "Параметр date обязателен" });
      }

      console.log("🔍 API encar - получены параметры:", {
        fileType,
        date,
        brand,
        model,
        limit,
        offset,
      });

      // Формируем URL для запроса к PHP серверу (encar-proxy.php)
      const phpServerUrl = isProduction
        ? `${site_url}/api/encar-proxy.php`
        : `http://localhost:8000/api/encar-proxy.php`;
      const allParams = new URLSearchParams(req.query);
      const apiUrl = `${phpServerUrl}?${allParams.toString()}`;
      console.log("🔄 Проксирование запроса к PHP серверу:", apiUrl);

      const response = await fetch(apiUrl, {
        timeout: 30000,
        headers: {
          "User-Agent": isProduction ? "SHMS-Auto-Server/1.0" : "SHMS-Auto-Local-Server/1.0",
        },
      });

      if (!response.ok) {
        console.error("❌ Ошибка PHP API:", response.status, response.statusText);
        return res.status(502).json({
          error: "Ошибка PHP API",
          status: response.status,
          statusText: response.statusText,
        });
      }

      const data = await response.json();
      console.log(
        "✅ Получен ответ от PHP API, количество записей:",
        Array.isArray(data) ? data.length : "не массив"
      );

      // Добавляем CORS заголовки
      res.header("Access-Control-Allow-Origin", "*");
      res.header(
        "Access-Control-Allow-Methods",
        "GET, POST, PUT, DELETE, OPTIONS"
      );
      res.header(
        "Access-Control-Allow-Headers",
        "Origin, X-Requested-With, Content-Type, Accept, Authorization"
      );

      res.json(data);
    } catch (error) {
      console.error("❌ Ошибка API encar:", error.message);
      res.status(500).json({
        error: "Ошибка сервера при обработке запроса",
        details: isProduction ? undefined : error.message,
      });
    }
  });

  // 🚀 НОВЫЙ API маршрут для быстрого поиска автомобилей с загрузкой всех категорий
  app.get("/api/fast-encar", async (req, res) => {
    try {
      // Проверяем доступность fetch
      if (!fetch) {
        console.error("node-fetch не доступен");
        return res.status(500).json({ error: "API временно недоступен" });
      }

      const {
        date = new Date().toISOString().split('T')[0],
        limit = 20,
        offset = 0,
        priority_sort = "false",
        load_all_category = "false",
        category = "",
        brand = "",
        model = "",
        year_min = "",
        year_max = "",
        price_min = "",
        price_max = "",
        cars_only = "false"
      } = req.query;

      console.log("🚀 API fast-encar - получены параметры:", {
        date,
        limit,
        offset,
        priority_sort,
        load_all_category,
        category,
        brand,
        model,
        cars_only
      });

      // Формируем URL для запроса к PHP серверу (fast-encar.php)
      const phpServerUrl = isProduction
        ? `${site_url}/api/fast-encar.php`
        : `http://localhost:8000/api/fast-encar.php`;
      const allParams = new URLSearchParams(req.query);
      const apiUrl = `${phpServerUrl}?${allParams.toString()}`;
      console.log("🔄 Проксирование запроса к fast-encar PHP серверу:", apiUrl);

      const response = await fetch(apiUrl, {
        timeout: 60000, // Увеличиваем таймаут для загрузки больших объемов
        headers: {
          "User-Agent": isProduction ? "SHMS-Auto-Server/1.0" : "SHMS-Auto-Local-Server/1.0",
        },
      });

      if (!response.ok) {
        console.error("❌ Ошибка fast-encar PHP API:", response.status, response.statusText);
        return res.status(502).json({
          error: "Ошибка fast-encar PHP API",
          status: response.status,
          statusText: response.statusText,
        });
      }

      const data = await response.json();

      // Логируем результат
      if (data.success) {
        console.log("✅ Получен ответ от fast-encar PHP API:", {
          total: data.total,
          count: data.count,
          load_all_category: data.load_all_category,
          category: data.category,
          priorities_loaded: data.priorities_loaded
        });
      } else {
        console.log("⚠️ fast-encar API вернул ошибку:", data.error);
      }

      // Добавляем CORS заголовки
      res.header("Access-Control-Allow-Origin", "*");
      res.header(
        "Access-Control-Allow-Methods",
        "GET, POST, PUT, DELETE, OPTIONS"
      );
      res.header(
        "Access-Control-Allow-Headers",
        "Origin, X-Requested-With, Content-Type, Accept, Authorization"
      );

      // Всегда возвращаем полный объект ответа
      res.json(data);
    } catch (error) {
      console.error("❌ Ошибка API fast-encar:", error.message);
      res.status(500).json({
        error: "Ошибка сервера при обработке запроса fast-encar",
        details: isProduction ? undefined : error.message,
      });
    }
  });

  // Обработка отзывов и заявок с сайта
  app.post("/api/send-review", async (req, res) => {
    try {
      const formData = req.body;
      console.log("📧 Получена заявка:", formData);

      // Валидация обязательных полей
      if (!formData.name && !formData.contact && !formData.message) {
        return res.status(400).json({
          error:
            "Необходимо заполнить хотя бы одно из полей: имя, контакт или сообщение",
        });
      }

      // Обработка промокода
      if (formData.promo) {
        console.log("🎫 Промокод:", formData.promo);
        if (formData.promoValid) {
          console.log("✅ Промокод ВАЛИДНЫЙ:", formData.promoCode);
        } else {
          console.log("❌ Промокод НЕВАЛИДНЫЙ");
        }
      }

      // Отправка email через новую систему
      await sendContactForm(formData);

      console.log("✅ Заявка успешно отправлена:", {
        name: formData.name,
        contact: formData.contact || formData.phone || formData.email,
        formType: formData.formType || "review",
      });

      // Формируем сообщение ответа
      let message = "Ваше сообщение успешно отправлено! Мы свяжемся с вами в ближайшее время.";

      if (formData.promoValid && formData.promoCode === "ATAMAN") {
        message += " 🎉 Промокод ATAMAN успешно применен!";
      }

      res.json({
        success: true,
        message: message,
        promoApplied: formData.promoValid || false,
      });
    } catch (error) {
      console.error("❌ Ошибка отправки заявки:", error);
      res.status(500).json({
        error:
          "Ошибка отправки сообщения. Попробуйте позже или свяжитесь с нами по телефону.",
        details: isProduction ? undefined : error.message,
      });
    }
  });

  // Обработка 404 ошибок
  app.use((req, res) => {
    console.log("❌ 404 - Страница не найдена:", req.url);
    res.status(404).send("Страница не найдена");
  });

  // Обработка ошибок
  app.use((err, req, res, next) => {
    console.error("❌ Ошибка обработки запроса:", err);
    res.status(500).send("Ошибка сервера: " + err.message);
  });

  // Запуск сервера
  app.listen(port, host, () => {
    console.log("=================================");
    console.log(`🚀 СЕРВЕР SHMS Авто запущен!`);
    console.log(`🌐 Режим: ${process.env.NODE_ENV || "development"}`);
    console.log(`📡 Адрес: http://${host}:${port}`);
    if (process.env.SITE_URL) {
      console.log(`🔗 Публичный URL: ${process.env.SITE_URL}`);
    }
    console.log("📋 Доступные маршруты:");
    console.log("   - Главная страница: /");
    console.log("   - Авто в наличии: /stock");
    console.log("   - Авто под заказ: /order");
    console.log("   - Контакты: /contacts");
    console.log("   - Промо-страница Физиева: /Fiziev");
    console.log("   - Админ-панель: /admin");
    console.log("   - Загруженные файлы: /uploads");
    console.log("   - API тест: /test");
    console.log("   - API заявки: /api/send-review");
    console.log("   - API поиск авто: /api/encar");
    console.log("   - API быстрый поиск: /api/fast-encar");
    if (!isProduction) {
      console.log("🔧 Особенности разработки:");
      console.log("   - Отключено кеширование статических файлов");
      console.log("   - Подробное логирование всех запросов");
      console.log("   - CORS разрешен для всех источников");
    }
    console.log("=================================");
  });
} catch (error) {
  console.error("💥 КРИТИЧЕСКАЯ ОШИБКА при запуске сервера:", error);
}
