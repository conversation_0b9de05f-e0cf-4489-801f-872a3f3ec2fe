const sqlite3 = require('sqlite3').verbose();
const path = require('path');

console.log('🔧 Исправление схемы базы данных');
console.log('================================\n');

// Путь к базе данных
const dbPath = path.join(__dirname, 'data/stock.db');

// Подключаемся к базе данных
const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('❌ Ошибка подключения к базе данных:', err.message);
        process.exit(1);
    }
    console.log('✅ Подключение к базе данных установлено\n');
});

// Функция для выполнения SQL запроса
function runQuery(sql, description) {
    return new Promise((resolve, reject) => {
        db.run(sql, (err) => {
            if (err) {
                console.error(`❌ Ошибка при ${description}:`, err.message);
                reject(err);
            } else {
                console.log(`✅ ${description} выполнено успешно`);
                resolve();
            }
        });
    });
}

// Функция для получения информации о таблице
function getTableInfo(tableName) {
    return new Promise((resolve, reject) => {
        db.all(`PRAGMA table_info(${tableName})`, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve(rows);
            }
        });
    });
}

// Основная функция
async function fixDatabase() {
    try {
        console.log('🔍 Проверка текущей схемы таблицы admins...');
        
        // Получаем информацию о таблице
        const tableInfo = await getTableInfo('admins');
        
        if (tableInfo.length === 0) {
            console.log('❌ Таблица admins не существует');
            console.log('💡 Запустите: node create-admin.js для создания таблицы');
            return;
        }

        console.log('📋 Текущая схема таблицы admins:');
        tableInfo.forEach(column => {
            const notNull = column.notnull ? 'NOT NULL' : 'NULL';
            const pk = column.pk ? 'PRIMARY KEY' : '';
            console.log(`   ${column.name}: ${column.type} ${notNull} ${pk}`.trim());
        });

        // Проверяем, есть ли проблема с email
        const emailColumn = tableInfo.find(col => col.name === 'email');
        if (emailColumn && emailColumn.notnull === 1) {
            console.log('\n⚠️  Найдена проблема: поле email обязательное (NOT NULL)');
            console.log('🔧 Исправляем схему...\n');

            // Создаем новую таблицу с правильной схемой
            const createNewTable = `
                CREATE TABLE admins_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(100),
                    password_hash VARCHAR(255) NOT NULL,
                    full_name VARCHAR(100),
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    last_login DATETIME,
                    reset_token VARCHAR(255),
                    reset_token_expires DATETIME
                );
            `;

            await runQuery(createNewTable, 'создание новой таблицы admins_new');

            // Копируем данные из старой таблицы
            const copyData = `
                INSERT INTO admins_new (id, username, email, password_hash, full_name, is_active, created_at, last_login, reset_token, reset_token_expires)
                SELECT id, username, email, password_hash, full_name, is_active, created_at, last_login, reset_token, reset_token_expires
                FROM admins;
            `;

            await runQuery(copyData, 'копирование данных');

            // Удаляем старую таблицу
            await runQuery('DROP TABLE admins;', 'удаление старой таблицы');

            // Переименовываем новую таблицу
            await runQuery('ALTER TABLE admins_new RENAME TO admins;', 'переименование таблицы');

            console.log('\n✅ Схема базы данных успешно исправлена!');
            console.log('📋 Теперь поле email необязательное');

        } else if (!emailColumn) {
            console.log('\n✅ Поле email отсутствует - это нормально');
        } else {
            console.log('\n✅ Поле email уже необязательное - исправление не требуется');
        }

        console.log('\n🎉 База данных готова к работе!');
        console.log('💡 Теперь можете создавать администраторов: node create-admin.js');

    } catch (error) {
        console.error('\n❌ Ошибка при исправлении базы данных:', error.message);
        process.exit(1);
    } finally {
        db.close((err) => {
            if (err) {
                console.error('❌ Ошибка при закрытии базы данных:', err.message);
            } else {
                console.log('\n🔒 Соединение с базой данных закрыто');
            }
        });
    }
}

// Запуск
fixDatabase();
