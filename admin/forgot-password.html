<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Восстановление пароля | SHMS Auto</title>
    <link rel="stylesheet" href="css/login.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="icon" href="../public/favicon.ico" type="image/x-icon">
</head>
<body>
    <div class="login-container">
        <div class="login-background">
            <div class="login-overlay"></div>
        </div>
        
        <div class="login-content">
            <div class="login-card">
                <div class="login-header">
                    <div class="login-logo">
                        <i class="fas fa-key"></i>
                        <h1>SHMS Auto</h1>
                    </div>
                    <h2>Восстановление пароля</h2>
                    <p>Введите ваш email адрес для получения ссылки восстановления пароля</p>
                </div>

                <form id="forgotPasswordForm" class="login-form">
                    <div class="form-group">
                        <label for="email">
                            <i class="fas fa-envelope"></i>
                            Email адрес
                        </label>
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            required 
                            autocomplete="email"
                            placeholder="Введите ваш email"
                        >
                        <div class="field-error" id="email-error"></div>
                    </div>

                    <button type="submit" class="login-button" id="submitButton">
                        <span class="button-text">Отправить ссылку</span>
                        <span class="button-loader" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                    </button>

                    <div class="form-message" id="formMessage"></div>
                </form>

                <div class="login-footer">
                    <p>
                        <a href="login.html" class="forgot-password">
                            <i class="fas fa-arrow-left"></i>
                            Вернуться к входу
                        </a>
                    </p>
                    <div class="security-info">
                        <small>
                            <i class="fas fa-info-circle"></i>
                            Ссылка для восстановления будет действительна в течение 1 часа
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Уведомления -->
    <div id="notification" class="notification">
        <div class="notification-content">
            <div class="notification-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="notification-message">
                <div class="notification-title"></div>
                <div class="notification-text"></div>
            </div>
            <button class="notification-close">&times;</button>
        </div>
    </div>

    <script>
        // Система восстановления пароля
        class ForgotPasswordSystem {
            constructor() {
                this.form = document.getElementById('forgotPasswordForm');
                this.emailInput = document.getElementById('email');
                this.submitButton = document.getElementById('submitButton');
                this.formMessage = document.getElementById('formMessage');
                this.notification = document.getElementById('notification');
                
                this.init();
            }

            init() {
                this.bindEvents();
                this.emailInput.focus();
            }

            bindEvents() {
                this.form.addEventListener('submit', (e) => this.handleSubmit(e));
                this.emailInput.addEventListener('input', () => this.validateEmail());
                
                const notificationClose = this.notification.querySelector('.notification-close');
                if (notificationClose) {
                    notificationClose.addEventListener('click', () => this.hideNotification());
                }
            }

            async handleSubmit(e) {
                e.preventDefault();
                
                this.clearErrors();
                
                if (!this.validateForm()) {
                    return;
                }
                
                this.setLoading(true);
                
                try {
                    const formData = new FormData(this.form);
                    const requestData = {
                        email: formData.get('email').trim()
                    };
                    
                    const response = await fetch('/api/auth/forgot-password', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        credentials: 'include',
                        body: JSON.stringify(requestData)
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        this.showMessage(data.message, 'success');
                        this.form.reset();
                    } else {
                        this.showMessage(data.error || 'Произошла ошибка при отправке запроса', 'error');
                    }
                    
                } catch (error) {
                    console.error('Ошибка при восстановлении пароля:', error);
                    this.showMessage('Ошибка соединения с сервером. Попробуйте позже.', 'error');
                } finally {
                    this.setLoading(false);
                }
            }

            validateForm() {
                const email = this.emailInput.value.trim();
                
                if (!email) {
                    this.showFieldError('email', 'Email обязателен для заполнения');
                    return false;
                }
                
                if (!this.isValidEmail(email)) {
                    this.showFieldError('email', 'Введите корректный email адрес');
                    return false;
                }
                
                return true;
            }

            validateEmail() {
                const email = this.emailInput.value.trim();
                this.clearFieldError('email');
                
                if (email && !this.isValidEmail(email)) {
                    this.showFieldError('email', 'Введите корректный email адрес');
                    return false;
                }
                
                return true;
            }

            isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            showFieldError(fieldName, message) {
                const errorElement = document.getElementById(`${fieldName}-error`);
                const fieldElement = document.getElementById(fieldName);
                
                if (errorElement) {
                    errorElement.textContent = message;
                }
                
                if (fieldElement) {
                    fieldElement.style.borderColor = 'var(--danger)';
                }
            }

            clearFieldError(fieldName) {
                const errorElement = document.getElementById(`${fieldName}-error`);
                const fieldElement = document.getElementById(fieldName);
                
                if (errorElement) {
                    errorElement.textContent = '';
                }
                
                if (fieldElement) {
                    fieldElement.style.borderColor = '';
                }
            }

            clearErrors() {
                this.clearFieldError('email');
                this.hideMessage();
            }

            showMessage(message, type = 'error') {
                this.formMessage.textContent = message;
                this.formMessage.className = `form-message ${type}`;
                this.formMessage.style.display = 'block';
            }

            hideMessage() {
                this.formMessage.style.display = 'none';
            }

            hideNotification() {
                this.notification.classList.remove('show');
            }

            setLoading(loading) {
                const buttonText = this.submitButton.querySelector('.button-text');
                const buttonLoader = this.submitButton.querySelector('.button-loader');
                
                if (loading) {
                    buttonText.style.display = 'none';
                    buttonLoader.style.display = 'inline-block';
                    this.submitButton.disabled = true;
                } else {
                    buttonText.style.display = 'inline-block';
                    buttonLoader.style.display = 'none';
                    this.submitButton.disabled = false;
                }
            }
        }

        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', () => {
            new ForgotPasswordSystem();
        });
    </script>
</body>
</html>
