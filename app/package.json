{"name": "shms-car-dealership", "version": "1.0.0", "description": "Car dealership website with admin panel", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "production": "NODE_ENV=production node server.js", "pm2": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop shms-auto", "pm2:restart": "pm2 restart shms-auto", "pm2:reload": "pm2 reload shms-auto", "pm2:logs": "pm2 logs shms-auto", "pm2:monit": "pm2 monit", "health-check": "node scripts/health-check.js", "backup-db": "node scripts/backup-db.js", "test-mail": "node scripts/test-mail.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"cheerio": "^1.0.0-rc.12", "dotenv": "^16.5.0", "express": "^4.18.2", "express-session": "^1.18.1", "fs": "0.0.1-security", "multer": "^1.4.5-lts.1", "node-fetch": "^2.6.7", "nodemailer": "^7.0.3", "path": "^0.12.7", "sqlite3": "^5.1.6"}, "devDependencies": {"nodemon": "^3.0.1", "pm2": "^5.3.0"}}