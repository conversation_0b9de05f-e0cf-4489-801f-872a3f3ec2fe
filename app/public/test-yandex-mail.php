<?php
/**
 * Тест подключения к Яндекс почте
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== ТЕСТ ПОДКЛЮЧЕНИЯ К ЯНДЕКС ПОЧТЕ ===\n\n";

// Настройки
$config = [
    'smtp_host' => 'smtp.yandex.ru',
    'smtp_port' => 465,
    'smtp_user' => '<EMAIL>',
    'smtp_pass' => 'SHMSAutogroup777!',
    'from_email' => '<EMAIL>',
    'from_name' => 'SHMS Авто Test'
];

echo "Настройки:\n";
echo "SMTP Host: {$config['smtp_host']}\n";
echo "SMTP Port: {$config['smtp_port']}\n";
echo "Email: {$config['smtp_user']}\n";
echo "Password: " . str_repeat('*', strlen($config['smtp_pass'])) . "\n\n";

// Тест 1: Проверка функции mail()
echo "=== ТЕСТ 1: Функция mail() ===\n";
echo "Функция mail() доступна: " . (function_exists('mail') ? 'ДА' : 'НЕТ') . "\n";

// Тест 2: Проверка настроек PHP
echo "\n=== ТЕСТ 2: Настройки PHP ===\n";
echo "SMTP: " . ini_get('SMTP') . "\n";
echo "smtp_port: " . ini_get('smtp_port') . "\n";
echo "sendmail_from: " . ini_get('sendmail_from') . "\n";

// Тест 3: Проверка подключения к SMTP серверу
echo "\n=== ТЕСТ 3: Подключение к SMTP ===\n";

$socket = @fsockopen('ssl://' . $config['smtp_host'], $config['smtp_port'], $errno, $errstr, 10);
if ($socket) {
    echo "✅ Подключение к {$config['smtp_host']}:{$config['smtp_port']} успешно!\n";
    
    // Читаем приветствие сервера
    $response = fgets($socket, 512);
    echo "Ответ сервера: " . trim($response) . "\n";
    
    fclose($socket);
} else {
    echo "❌ Ошибка подключения: $errstr ($errno)\n";
}

// Тест 4: Попытка отправки через встроенную функцию mail()
echo "\n=== ТЕСТ 4: Отправка через mail() ===\n";

// Временно устанавливаем настройки SMTP
$original_smtp = ini_get('SMTP');
$original_smtp_port = ini_get('smtp_port');
$original_sendmail_from = ini_get('sendmail_from');

ini_set('SMTP', $config['smtp_host']);
ini_set('smtp_port', $config['smtp_port']);
ini_set('sendmail_from', $config['from_email']);

$to = $config['smtp_user'];
$subject = 'Тест отправки с SHMS Авто';
$message = "Это тестовое сообщение отправлено " . date('Y-m-d H:i:s');
$headers = "From: {$config['from_name']} <{$config['from_email']}>\r\n";
$headers .= "Content-Type: text/plain; charset=utf-8\r\n";

echo "Отправляем письмо на: $to\n";
echo "Тема: $subject\n";

$result = @mail($to, $subject, $message, $headers);

echo "Результат отправки: " . ($result ? '✅ УСПЕХ' : '❌ ОШИБКА') . "\n";

if (!$result) {
    $error = error_get_last();
    if ($error) {
        echo "Последняя ошибка: " . $error['message'] . "\n";
    }
}

// Восстанавливаем настройки
ini_set('SMTP', $original_smtp);
ini_set('smtp_port', $original_smtp_port);
ini_set('sendmail_from', $original_sendmail_from);

// Тест 5: Проверка через cURL (альтернативный способ)
echo "\n=== ТЕСТ 5: Проверка cURL ===\n";
echo "cURL доступен: " . (function_exists('curl_init') ? 'ДА' : 'НЕТ') . "\n";

if (function_exists('curl_init')) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://smtp.yandex.ru:465');
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    
    $result = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "cURL ошибка: $error\n";
    } else {
        echo "cURL подключение: ✅ УСПЕХ (код: $http_code)\n";
    }
}

echo "\n=== РЕКОМЕНДАЦИИ ===\n";
echo "1. Для локальной разработки используйте сохранение в файлы\n";
echo "2. Для продакшена настройте SMTP на хостинге\n";
echo "3. Проверьте, что в Яндекс почте включены пароли приложений\n";
echo "4. Возможно потребуется двухфакторная аутентификация\n";

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?>
