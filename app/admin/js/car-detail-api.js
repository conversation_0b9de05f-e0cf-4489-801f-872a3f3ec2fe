/**
 * Утилита для генерации HTML страниц автомобилей
 */

const fs = require("fs");
const path = require("path");

/**
 * Генерирует HTML-страницу для детальной информации об автомобиле
 * @param {Object} detail - Объект с детальной информацией
 * @param {Array} images - Массив изображений
 * @param {string} outputPath - Путь для сохранения (относительно public)
 * @returns {Object} Результат генерации с путем к файлу
 */
function generateCarDetailHTML(detail, images, outputPath) {
  try {
    console.log("Начало генерации HTML для:", detail.car_title);

    // Загружаем шаблон
    const templatePath = path.join(
      __dirname,
      "../templates/car-detail-template.html"
    );
    let template = fs.readFileSync(templatePath, "utf8");

    // Подготавливаем данные для шаблона
    const mainImage =
      images.length > 0 ? images[0].path : "/PNG/SHMS_Logo_Black.png";
    const additionalImages = images.slice(1, 5);

    // Форматируем описание
    const formatDescription = (text) => {
      if (!text) return "";
      return text
        .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
        .replace(/\n/g, "<br>");
    };

    const description = formatDescription(detail.description || "");

    // Генерируем HTML для характеристик
    const specificationsHtml = detail.specs
      ? detail.specs
          .split("\n")
          .filter((spec) => spec.trim())
          .map((spec) => {
            const [label, value] = spec.split(":").map((s) => s.trim());
            if (label && value) {
              // Handle color values specially
              if (label.toLowerCase().includes("цвет") && value.includes("#")) {
                const colors = value.split(",").map((color) => color.trim());
                const colorDots = colors
                  .map((color) => {
                    const colorCode = color.match(/#[0-9A-Fa-f]{6}/);
                    return colorCode
                      ? `<span class="color-dot" style="background-color: ${colorCode[0]}"></span>`
                      : "";
                  })
                  .join("");
                return `<div class="spec-row">
                      <span class="spec-label">${label}</span>
                      <span class="spec-value">
                        <div class="color-dots">${colorDots}</div>
                      </span>
                    </div>`;
              }
              return `<div class="spec-row">
                    <span class="spec-label">${label}</span>
                    <span class="spec-value">${value}</span>
                  </div>`;
            }
            return "";
          })
          .join("")
      : "";

    // Обработка функций - используем новые поля main_features и additional_features
    const mainFeaturesArray = detail.main_features
      ? detail.main_features.split("\n").filter((feature) => feature.trim())
      : [];
    const additionalFeaturesArray = detail.additional_features
      ? detail.additional_features
          .split("\n")
          .filter((feature) => feature.trim())
      : [];

    // Fallback к старому полю best_features если новых полей нет
    if (
      mainFeaturesArray.length === 0 &&
      additionalFeaturesArray.length === 0 &&
      detail.best_features
    ) {
      const allFeatures = detail.best_features
        .split("\n")
        .filter((feature) => feature.trim());
      const midPoint = Math.ceil(allFeatures.length / 2);
      mainFeaturesArray.push(...allFeatures.slice(0, midPoint));
      additionalFeaturesArray.push(...allFeatures.slice(midPoint));
    }

    const mainFeatures = mainFeaturesArray
      .map((feature) => `<li>${formatDescription(feature)}</li>`)
      .join("");
    const additionalFeatures = additionalFeaturesArray
      .map((feature) => `<li>${formatDescription(feature)}</li>`)
      .join("");

    // Разделяем дополнительные изображения на две группы
    const descriptionImages = additionalImages.slice(0, 2); // 2-е и 3-е изображения для описания
    const specsImages = additionalImages.slice(2, 4); // 4-е и 5-е изображения для характеристик

    // Генерируем HTML для изображений описания
    const descriptionImagesHtml = descriptionImages
      .map(
        (img, index) =>
          `<div class="additional-image" onclick="openFullscreen(${index + 1})">
         <img src="${img.path}" alt="${detail.full_title || detail.car_title}">
       </div>`
      )
      .join("");

    // Генерируем HTML для изображений характеристик
    const specsImagesHtml = specsImages
      .map(
        (img, index) =>
          `<div class="additional-image" onclick="openFullscreen(${index + 3})">
         <img src="${img.path}" alt="${detail.full_title || detail.car_title}">
       </div>`
      )
      .join("");

    // Создаем имя файла на основе названия машины
    const safeTitle = detail.car_title
      .toLowerCase()
      .replace(/\s+/g, "-") // Заменяем пробелы на дефисы
      .replace(/[^\w\-]+/g, "") // Удаляем все не-буквы, не-цифры и не-дефисы
      .replace(/\-\-+/g, "-") // Заменяем множественные дефисы на один
      .replace(/^-+/, "") // Удаляем дефисы в начале
      .replace(/-+$/, ""); // Удаляем дефисы в конце

    // Определяем путь к файлу
    const fileName = `${safeTitle}.html`;
    const basePath = path.join(__dirname, "../../public");
    const defaultOutputDir = "cars";
    const outputDir = outputPath || defaultOutputDir;

    // Создаем директорию, если она не существует
    const fullOutputDir = path.join(basePath, outputDir);
    if (!fs.existsSync(fullOutputDir)) {
      fs.mkdirSync(fullOutputDir, { recursive: true });
    }

    // Заменяем плейсхолдеры в шаблоне
    const allImages = images.slice(0, 5);
    const imageArray = allImages.map((img) => img.path);

    template = template
      .replace(
        /\{\{FULL_TITLE\}\}/g,
        detail.full_title || detail.car_title || "Название автомобиля"
      )
      .replace(
        /\{\{CAR_SUBTITLE\}\}/g,
        `Цена в Москве: ${detail.moscow_price || "По запросу"}`
      )
      .replace(/\{\{PRICE\}\}/g, detail.price || "Цена по запросу")
      .replace(
        /\{\{MOSCOW_PRICE\}\}/g,
        `Цена в Москве по всем документам: ${
          detail.moscow_price || "По запросу"
        }`
      )
      .replace(/\{\{MAIN_IMAGE\}\}/g, mainImage)
      .replace(/\{\{DESCRIPTION\}\}/g, description)
      .replace(/\{\{DESCRIPTION_IMAGES\}\}/g, descriptionImagesHtml)
      .replace(/\{\{SPECS_IMAGES\}\}/g, specsImagesHtml)
      .replace(/\{\{CAR_TYPE\}\}/g, "Универсал")
      .replace(/\{\{POWER\}\}/g, "Мощность")
      .replace(/\{\{MILEAGE\}\}/g, "Пробег")
      .replace(/\{\{SPECIFICATIONS\}\}/g, specificationsHtml)
      .replace(/\{\{MAIN_FEATURES\}\}/g, mainFeatures)
      .replace(/\{\{ADDITIONAL_FEATURES\}\}/g, additionalFeatures)
      .replace(
        /\{\{IMAGE_ARRAY\}\}/g,
        imageArray.map((url) => `"${url}"`).join(", ")
      );
    // Записываем файл
    const filePath = path.join(fullOutputDir, fileName);
    fs.writeFileSync(filePath, template, "utf8");

    const relativePath = `/${outputDir}/${fileName}`;
    console.log(`Файл HTML создан по пути: ${filePath}`);

    return {
      success: true,
      filePath: relativePath,
      fullPath: filePath,
    };
  } catch (error) {
    console.error("Ошибка при генерации HTML:", error);
    throw error;
  }
}

module.exports = {
  generateCarDetailHTML,
};
