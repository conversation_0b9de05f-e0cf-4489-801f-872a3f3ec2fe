# ⚡ Быстрое развертывание SHMS Auto на Рег.ру

## 🎯 Экспресс-развертывание (30 минут)

### 1. Заказ VPS (5 минут)
- [ ] Войти в личный кабинет Рег.ру
- [ ] Заказать VPS: Ubuntu 20.04, 2GB RAM, 20GB SSD
- [ ] Получить IP и пароль root
- [ ] Записать данные: IP = _________, пароль = _________

### 2. Подключение к серверу (2 минуты)
```bash
ssh root@ваш-ip-адрес
```

### 3. Автоматическая установка (10 минут)
```bash
# Скопируйте и выполните весь блок одной командой:
apt update && apt upgrade -y && \
apt install -y curl git nginx certbot python3-certbot-nginx htop && \
curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
apt-get install -y nodejs && \
npm install -g pm2 && \
adduser shmsauto --disabled-password --gecos "" && \
usermod -aG sudo shmsauto && \
echo "✅ Базовая установка завершена"
```

### 4. Загрузка проекта (5 минут)
```bash
# Переключение на пользователя shmsauto
su - shmsauto
cd /home/<USER>

# Создание директории проекта
mkdir shms-auto && cd shms-auto

# Если есть Git репозиторий:
# git clone https://github.com/ваш-username/shms-auto.git .

# Если загружаете через SFTP - используйте FileZilla/WinSCP
# Хост: ваш-ip, Пользователь: shmsauto, Порт: 22

# Если нет Git - загрузите файлы через SFTP в /home/<USER>/shms-auto/
```

### 5. Настройка проекта (3 минуты)
```bash
# Установка зависимостей
npm install --production

# Настройка конфигурации
cp .env.example .env
nano .env

# ОБЯЗАТЕЛЬНО измените в .env:
# DOMAIN=ваш-домен.ru
# SITE_URL=https://ваш-домен.ru
# CONTACT_EMAIL=ваша-почта@gmail.com
# MANAGER_EMAIL=ваша-почта@gmail.com

# Создание директорий
mkdir -p logs data/uploads data/cache
chmod 755 data/uploads logs
```

### 6. Запуск приложения (2 минуты)
```bash
# Запуск через PM2
pm2 start ecosystem.config.js --env production

# Настройка автозапуска
pm2 startup
# Выполните команду, которую выдаст PM2
pm2 save

# Проверка
pm2 list
curl http://localhost:3001/test
```

### 7. Настройка Nginx (3 минуты)
```bash
# ЗАМЕНИТЕ ваш-домен.ru на ваш реальный домен!
sudo tee /etc/nginx/sites-available/shms-auto > /dev/null <<EOF
server {
    listen 80;
    server_name ваш-домен.ru www.ваш-домен.ru;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    location /uploads/ {
        alias /home/<USER>/shms-auto/data/uploads/;
        expires 30d;
    }
}
EOF

sudo ln -s /etc/nginx/sites-available/shms-auto /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t && sudo systemctl restart nginx
```

### 8. Настройка DNS в Рег.ру (2 минуты)
- [ ] Личный кабинет → Домены → Управление DNS
- [ ] A-запись: @ → ваш-ip-адрес
- [ ] A-запись: www → ваш-ip-адрес
- [ ] Сохранить изменения

### 9. SSL сертификат (3 минуты)
```bash
# ЗАМЕНИТЕ ваш-домен.ru на ваш реальный домен!
sudo certbot --nginx -d ваш-домен.ru -d www.ваш-домен.ru

# Следуйте инструкциям:
# 1. Введите email для уведомлений
# 2. Согласитесь с условиями (Y)
# 3. Выберите редирект на HTTPS (2)

# Настройка автообновления
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

## ✅ Проверка работы

```bash
# Статус приложения
pm2 list

# Проверка здоровья
npm run health-check

# Тест почты
npm run test-mail
```

**В браузере:**
- ✅ http://ваш-ip:3001/test (прямой доступ к Node.js)
- ✅ https://ваш-домен.ru (через Nginx с SSL)
- ✅ https://ваш-домен.ru/admin (админ-панель)
- ✅ https://ваш-домен.ru/stock (каталог автомобилей)

## 🚨 Если что-то не работает

```bash
# Логи приложения
pm2 logs shms-auto

# Логи Nginx
sudo tail -f /var/log/nginx/error.log

# Перезапуск всего
pm2 restart shms-auto
sudo systemctl restart nginx
```

## 📱 Полезные команды

```bash
# Управление PM2
pm2 list                 # список процессов
pm2 restart shms-auto    # перезапуск
pm2 logs shms-auto       # логи
pm2 monit               # мониторинг

# Управление Nginx
sudo systemctl status nginx    # статус
sudo systemctl restart nginx   # перезапуск
sudo nginx -t                 # проверка конфигурации

# Проверка портов
netstat -tlnp | grep 3001     # Node.js
netstat -tlnp | grep 80       # Nginx
```

## 🎉 Готово!

**Время развертывания: ~30 минут**

Ваш сайт работает на https://ваш-домен.ru со всеми функциями:
- ✅ Каталог автомобилей
- ✅ Админ-панель
- ✅ Формы заявок
- ✅ Почтовые уведомления
- ✅ Загрузка изображений
- ✅ SSL сертификат
