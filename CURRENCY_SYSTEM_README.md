# 💰 Новая система курсов валют SHMS Auto

## 📋 Обзор

Новая система курсов валют использует:
- **Курс корейской воны к рублям** от ЦБ РФ
- **Курс доллара** из вашего файла `rates.json`
- **Автоматическое кэширование** для оптимизации производительности

## 🔧 Компоненты системы

### 1. API для получения курсов ЦБ РФ
**Файл:** `public/api/cbr-rates.php`
- Получает курс корейской воны к рублям от ЦБ РФ
- Кэширует данные на 1 час
- Имеет fallback курс при недоступности API

### 2. Единый API курсов валют
**Файл:** `public/api/exchange-rates.php`
- Объединяет курс воны (ЦБ РФ) и курс доллара (rates.json)
- Кэширует данные на 30 минут
- Возвращает все необходимые курсы в одном запросе

### 3. JavaScript библиотека конвертации
**Файл:** `public/js/currency-converter.js`
- Класс `CurrencyConverter` для работы с курсами
- Автоматическое кэширование в браузере
- Методы для конвертации валют и расчета цен

## 📊 Новая формула расчета цен

### Старая формула:
```
USD = (цена_в_вонах × 10,000) ÷ 1320
```

### Новая формула:
```
1. Реальная цена в вонах = цена_на_сайте × 10,000
2. Цена в рублях = реальная_цена_в_вонах × курс_воны_к_рублю_ЦБ
3. Цена в долларах = цена_в_рублях ÷ курс_доллара_из_rates.json
```

## 🚀 Использование

### В JavaScript коде:

```javascript
// Получение курсов
const rates = await window.currencyConverter.getRates();

// Расчет цены автомобиля
const usdPrice = await window.currencyConverter.calculateCarPrice(koreanWonPrice);

// Форматирование цены
const formattedPrice = window.currencyConverter.formatUSDPrice(usdPrice);

// Конвертация валют
const rubAmount = await window.currencyConverter.convertKRWToRUB(krwAmount);
const usdAmount = await window.currencyConverter.convertRUBToUSD(rubAmount);
```

### В HTML файлах:

```html
<!-- Подключение библиотеки -->
<script src="js/currency-converter.js"></script>
<script src="js/your-script.js"></script>
```

## 📁 Измененные файлы

### JavaScript файлы:
- `public/js/currency-converter.js` - новая библиотека
- `public/js/order-encar.js` - обновлен для асинхронных цен
- `public/js/stock.js` - обновлен formatPrice()
- `public/js/premium-cars.js` - обновлен formatPrice()
- `public/js/index.js` - обновлен formatPrice()

### HTML файлы:
- `public/order.html` - добавлено подключение currency-converter.js
- `public/stock.html` - добавлено подключение currency-converter.js
- `public/index.html` - добавлено подключение currency-converter.js

### API файлы:
- `public/api/cbr-rates.php` - новый API ЦБ РФ
- `public/api/exchange-rates.php` - новый единый API курсов

### Тестовые файлы:
- `public/test-currency.html` - страница для тестирования системы

## 🧪 Тестирование

### 1. Тестовая страница
Откройте `https://shms-auto.ru/test-currency.html` для тестирования всех функций.

### 2. Проверка API
```bash
# Тест API ЦБ РФ
curl https://shms-auto.ru/api/cbr-rates.php

# Тест единого API
curl https://shms-auto.ru/api/exchange-rates.php
```

### 3. Консоль браузера
```javascript
// Проверка работы конвертера
await window.currencyConverter.getDebugInfo();

// Тест расчета цены
await window.currencyConverter.calculateCarPrice(3500);
```

## 📂 Структура кэша

### Серверный кэш:
- `data/cache/cbr_rates.json` - курсы ЦБ РФ (1 час)
- `data/cache/exchange_rates.json` - объединенные курсы (30 минут)

### Браузерный кэш:
- Курсы кэшируются в памяти на 30 минут
- Автоматическое обновление при истечении срока

## ⚠️ Важные моменты

### 1. Асинхронность
Все функции расчета цен теперь асинхронные. Используйте `await`:

```javascript
// Правильно
const price = await formatPrice(carPrice);

// Неправильно
const price = formatPrice(carPrice); // Вернет Promise
```

### 2. Fallback механизмы
- При недоступности ЦБ РФ используется курс 0.075 руб/вона
- При недоступности rates.json используется курс 95 руб/доллар
- При ошибках возвращается к старой формуле расчета

### 3. Логирование
Все операции логируются в консоль браузера с префиксами:
- `💰` - операции с курсами
- `💱` - конвертация валют
- `🚗` - расчет цен автомобилей
- `✅` - успешные операции
- `❌` - ошибки

## 🔄 Обновление rates.json

Система автоматически подхватывает изменения в `public/rates.json`. 
Поддерживаемые форматы курсов доллара:
- `usdtrub` - доллар к рублю
- `usda7a5` - доллар к A7A5
- `usdta7a5` - доллар к TA7A5

## 📞 Поддержка

При возникновении проблем:
1. Проверьте консоль браузера на ошибки
2. Откройте тестовую страницу `/test-currency.html`
3. Проверьте доступность API `/api/exchange-rates.php`
4. Убедитесь, что файл `rates.json` актуален
