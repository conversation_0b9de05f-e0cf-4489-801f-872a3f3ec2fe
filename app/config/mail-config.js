const nodemailer = require('nodemailer');
require('dotenv').config();

// Конфигурация почты для Рег.ру
const mailConfig = {
  host: process.env.MAIL_HOST || 'mail.hosting.reg.ru',
  port: parseInt(process.env.MAIL_PORT) || 465,
  secure: process.env.MAIL_SECURE === 'true', // true для 465, false для других портов
  auth: {
    user: process.env.MAIL_USER || '<EMAIL>',
    pass: process.env.MAIL_PASS || 'your_password_here'
  },
  // Дополнительные настройки для Рег.ру
  tls: {
    rejectUnauthorized: false // Может потребоваться для некоторых серверов
  }
};

// Создание транспортера
const createTransporter = () => {
  try {
    const transporter = nodemailer.createTransporter(mailConfig);
    
    // Проверка подключения
    transporter.verify((error, success) => {
      if (error) {
        console.error('Ошибка настройки почты:', error);
      } else {
        console.log('Почтовый сервер готов к отправке сообщений');
      }
    });
    
    return transporter;
  } catch (error) {
    console.error('Ошибка создания почтового транспортера:', error);
    return null;
  }
};

// Функция отправки email
const sendEmail = async (options) => {
  const transporter = createTransporter();
  
  if (!transporter) {
    throw new Error('Не удалось создать почтовый транспортер');
  }

  const mailOptions = {
    from: `"SHMS Авто" <${process.env.MAIL_USER}>`,
    to: options.to || process.env.CONTACT_EMAIL,
    subject: options.subject || 'Новое сообщение с сайта',
    text: options.text,
    html: options.html,
    ...options
  };

  try {
    const result = await transporter.sendMail(mailOptions);
    console.log('Email отправлен успешно:', result.messageId);
    return result;
  } catch (error) {
    console.error('Ошибка отправки email:', error);
    throw error;
  }
};

// Функция отправки уведомления о новой заявке
const sendContactForm = async (formData) => {
  const { name, contact, message, region, carType, model, phone, email, contactMethod, formType } = formData;
  
  const emailText = `
Новая заявка с сайта SHMS Авто

Тип формы: ${formType || 'Обратная связь'}
Имя: ${name || 'Не указано'}
Регион: ${region || 'Не указан'}
Тип авто: ${carType || 'Не указан'}
Модель: ${model || 'Не указана'}
Телефон: ${phone || 'Не указан'}
Email: ${email || 'Не указан'}
Предпочитаемый способ связи: ${contactMethod || contact || 'Не указан'}
Сообщение: ${message || 'Не указано'}

Дата: ${new Date().toLocaleString('ru-RU')}
  `;

  const emailHtml = `
    <h2>Новая заявка с сайта SHMS Авто</h2>
    <table style="border-collapse: collapse; width: 100%;">
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>Тип формы:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${formType || 'Обратная связь'}</td></tr>
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>Имя:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${name || 'Не указано'}</td></tr>
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>Регион:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${region || 'Не указан'}</td></tr>
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>Тип авто:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${carType || 'Не указан'}</td></tr>
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>Модель:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${model || 'Не указана'}</td></tr>
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>Телефон:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${phone || 'Не указан'}</td></tr>
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>Email:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${email || 'Не указан'}</td></tr>
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>Способ связи:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${contactMethod || contact || 'Не указан'}</td></tr>
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>Сообщение:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${message || 'Не указано'}</td></tr>
      <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>Дата:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">${new Date().toLocaleString('ru-RU')}</td></tr>
    </table>
  `;

  return await sendEmail({
    to: process.env.MANAGER_EMAIL,
    subject: `Новая заявка с сайта SHMS Авто - ${formType || 'Обратная связь'}`,
    text: emailText,
    html: emailHtml
  });
};

module.exports = {
  mailConfig,
  createTransporter,
  sendEmail,
  sendContactForm
};
