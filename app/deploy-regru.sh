#!/bin/bash

# Скрипт развертывания SHMS Авто на Рег.ру
# Автор: SHMS Auto Team
# Дата: $(date +%Y-%m-%d)

set -e  # Остановка при ошибке

echo "🚀 Развертывание SHMS Авто на Рег.ру"
echo "===================================="

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Функция для вывода сообщений
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

# Проверка Node.js
check_nodejs() {
    log "Проверка Node.js..."
    if ! command -v node &> /dev/null; then
        error "Node.js не установлен! Установите Node.js 16+ перед продолжением."
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 16 ]; then
        error "Требуется Node.js версии 16 или выше. Текущая версия: $(node -v)"
    fi
    
    log "✅ Node.js $(node -v) установлен"
}

# Проверка PM2
check_pm2() {
    log "Проверка PM2..."
    if ! command -v pm2 &> /dev/null; then
        log "Установка PM2..."
        npm install -g pm2
    fi
    log "✅ PM2 готов"
}

# Установка зависимостей
install_dependencies() {
    log "Установка зависимостей..."
    npm install --production
    log "✅ Зависимости установлены"
}

# Создание необходимых директорий
create_directories() {
    log "Создание директорий..."
    
    mkdir -p logs
    mkdir -p data/uploads
    mkdir -p data/cache
    
    # Установка прав доступа
    chmod 755 data/uploads
    chmod 755 data/cache
    chmod 755 logs
    
    if [ -f "data/stock.db" ]; then
        chmod 644 data/stock.db
    fi
    
    log "✅ Директории созданы"
}

# Проверка конфигурации
check_config() {
    log "Проверка конфигурации..."
    
    if [ ! -f ".env" ]; then
        warn ".env файл не найден, создаем из шаблона..."
        cp .env.example .env
        warn "⚠️  ВАЖНО: Отредактируйте .env файл с вашими настройками!"
        return 1
    fi
    
    # Проверка обязательных переменных
    required_vars=("MAIL_USER" "MAIL_PASS" "CONTACT_EMAIL" "SESSION_SECRET")
    missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" .env || grep -q "^$var=.*замените\|^$var=.*your_\|^$var=$" .env; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        error "Не настроены переменные в .env: ${missing_vars[*]}"
    fi
    
    log "✅ Конфигурация проверена"
}

# Тестирование приложения
test_application() {
    log "Тестирование приложения..."
    
    # Тест запуска
    timeout 10s npm run production &
    SERVER_PID=$!
    sleep 5
    
    if kill -0 $SERVER_PID 2>/dev/null; then
        log "✅ Сервер запускается успешно"
        kill $SERVER_PID
        wait $SERVER_PID 2>/dev/null || true
    else
        error "Сервер не запускается"
    fi
    
    # Тест почты
    if command -v node &> /dev/null; then
        log "Тестирование почты..."
        if node scripts/test-mail.js; then
            log "✅ Почта настроена корректно"
        else
            warn "⚠️  Проблемы с настройкой почты"
        fi
    fi
}

# Запуск через PM2
start_pm2() {
    log "Запуск через PM2..."
    
    # Остановка существующих процессов
    pm2 delete shms-auto 2>/dev/null || true
    
    # Запуск нового процесса
    pm2 start ecosystem.config.js --env production
    
    # Сохранение конфигурации PM2
    pm2 save
    
    # Настройка автозапуска
    pm2 startup
    
    log "✅ Приложение запущено через PM2"
}

# Настройка Nginx (опционально)
setup_nginx() {
    if command -v nginx &> /dev/null; then
        log "Настройка Nginx..."
        
        # Создание конфигурации Nginx
        cat > /tmp/shms-auto-nginx.conf << 'EOF'
server {
    listen 80;
    server_name shms-auto.ru www.shms-auto.ru;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    location /uploads/ {
        alias $(pwd)/data/uploads/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    location /robots.txt {
        alias $(pwd)/public/robots.txt;
    }
    
    location /sitemap.xml {
        alias $(pwd)/public/sitemap.xml;
    }
}
EOF
        
        log "Конфигурация Nginx создана в /tmp/shms-auto-nginx.conf"
        log "Скопируйте её в /etc/nginx/sites-available/ и активируйте"
    else
        warn "Nginx не установлен, пропускаем настройку"
    fi
}

# Финальная проверка
final_check() {
    log "Финальная проверка..."
    
    sleep 3
    
    # Проверка статуса PM2
    if pm2 list | grep -q "shms-auto.*online"; then
        log "✅ Приложение работает"
    else
        error "Приложение не запущено"
    fi
    
    # Проверка доступности
    if curl -f http://localhost:3001/test >/dev/null 2>&1; then
        log "✅ Сервер отвечает на запросы"
    else
        warn "⚠️  Сервер не отвечает на localhost:3001"
    fi
    
    log "✅ Развертывание завершено успешно!"
}

# Вывод информации
show_info() {
    echo ""
    echo "🎉 SHMS Авто успешно развернут!"
    echo "================================"
    echo "📡 Локальный адрес: http://localhost:3001"
    echo "🌐 Домен: https://shms-auto.ru (после настройки DNS)"
    echo ""
    echo "📋 Полезные команды:"
    echo "  pm2 list              - список процессов"
    echo "  pm2 logs shms-auto    - просмотр логов"
    echo "  pm2 restart shms-auto - перезапуск"
    echo "  pm2 stop shms-auto    - остановка"
    echo "  npm run health-check  - проверка здоровья"
    echo ""
    echo "📁 Важные файлы:"
    echo "  .env                  - настройки"
    echo "  logs/                 - логи приложения"
    echo "  data/stock.db         - база данных"
    echo "  data/uploads/         - загруженные файлы"
    echo ""
    echo "🔧 Следующие шаги:"
    echo "  1. Настройте DNS для домена shms-auto.ru"
    echo "  2. Установите SSL сертификат"
    echo "  3. Настройте Nginx (опционально)"
    echo "  4. Настройте резервное копирование"
}

# Основная функция
main() {
    log "Начало развертывания..."
    
    check_nodejs
    check_pm2
    install_dependencies
    create_directories
    
    if ! check_config; then
        error "Настройте .env файл и запустите скрипт снова"
    fi
    
    test_application
    start_pm2
    setup_nginx
    final_check
    show_info
}

# Запуск основной функции
main "$@"
