<!DOCTYPE html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{FULL_TITLE}} | SHMS Auto</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        line-height: 1.6;
        color: #333;
        background: #fff;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      /* Header Section */
      .car-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 30px;
        gap: 20px;
      }

      .car-title-section {
        flex: 1;
      }

      .car-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: #000;
        margin-bottom: 8px;
        line-height: 1.2;
      }

      .car-subtitle {
        font-size: 1rem;
        color: #666;
        font-weight: 400;
      }

      .car-price-section {
        text-align: right;
      }

      .car-price {
        font-size: 2rem;
        font-weight: 700;
        color: #000;
        margin-bottom: 10px;
      }

      .moscow-price {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 15px;
      }

      .buy-button {
        background: #000;
        color: #fff;
        padding: 12px 30px;
        border: none;
        border-radius: 25px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .buy-button:hover {
        background: #333;
        transform: translateY(-2px);
      }

      /* Main Image Gallery */
      .main-gallery {
        position: relative;
        margin-bottom: 40px;
      }

      .main-image-container {
        position: relative;
        width: 100%;
        height: 500px;
        border-radius: 12px;
        overflow: hidden;
        background: #f5f5f5;
      }

      .main-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        cursor: pointer;
      }

      .gallery-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(0, 0, 0, 0.7);
        color: white;
        border: none;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        transition: all 0.3s ease;
      }

      .gallery-nav:hover {
        background: rgba(0, 0, 0, 0.9);
        transform: translateY(-50%) scale(1.1);
      }

      .gallery-nav.prev {
        left: 20px;
      }

      .gallery-nav.next {
        right: 20px;
      }

      .close-fullscreen {
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        border: none;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        cursor: pointer;
        font-size: 1.5rem;
        z-index: 1001;
        display: none;
      }

      /* Content Section */
      .content-section {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 40px;
        margin-bottom: 40px;
      }

      .description-section h2 {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 20px;
        color: #000;
      }

      .description-text {
        font-size: 1rem;
        line-height: 1.8;
        color: #333;
        margin-bottom: 30px;
      }

      .description-text strong {
        font-weight: 700;
        color: #000;
      }

      .additional-images {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin-top: 20px;
      }

      .additional-image {
        width: 100%;
        height: 200px;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
      }

      .additional-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .additional-image:hover img {
        transform: scale(1.05);
      }

      /* Sidebar */
      .sidebar {
        background: #f8f9fa;
        padding: 25px;
        border-radius: 12px;
        height: fit-content;
      }

      .sidebar-price {
        font-size: 1.8rem;
        font-weight: 700;
        color: #000;
        margin-bottom: 5px;
      }

      .sidebar-moscow-price {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 25px;
      }

      .info-section {
        margin-bottom: 25px;
      }

      .info-section h3 {
        font-size: 1rem;
        font-weight: 600;
        color: #000;
        margin-bottom: 10px;
      }

      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 0.9rem;
      }

      .info-icon {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        background: #e9ecef;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
      }

      .priority-section {
        background: #fff;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
      }

      .priority-section h3 {
        margin-bottom: 10px;
      }

      .priority-text {
        font-size: 0.85rem;
        color: #666;
        line-height: 1.5;
      }

      /* Specifications Table */
      .specs-section {
        margin-bottom: 40px;
      }

      .specs-section h2 {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 25px;
        color: #000;
      }

      .specs-table {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        background: #f8f9fa;
        padding: 25px;
        border-radius: 12px;
      }

      .spec-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #e9ecef;
      }

      .spec-row:last-child {
        border-bottom: none;
      }

      .spec-label {
        font-weight: 500;
        color: #666;
      }

      .spec-value {
        font-weight: 600;
        color: #000;
        text-align: right;
      }

      .color-dots {
        display: flex;
        gap: 5px;
        align-items: center;
      }

      .color-dot {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        border: 2px solid #fff;
        box-shadow: 0 0 0 1px #ddd;
      }

      /* Best Features Section */
      .best-features-section {
        margin-bottom: 40px;
      }

      .best-features-section h2 {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 25px;
        color: #000;
      }

      .features-grid {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        width: 100%;
        gap: 40px;
      }

      .features-column {
        display: flex;
        flex-direction: column;
        flex: 0 0 auto;
        max-width: 45%;
      }

      .features-column:first-child {
        text-align: left;
        margin-left: 0;
        padding-left: 0;
      }

      .features-column:first-child h3 {
        text-align: left;
        margin-left: 0;
      }

      .features-column:last-child {
        text-align: right;
        margin-right: 0;
        padding-right: 0;
      }

      .features-column:last-child h3 {
        text-align: right;
        margin-right: 0;
      }

      .features-column h3 {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 15px;
        margin-top: 0;
        color: #000;
        line-height: 1.4;
      }

      .features-list {
        list-style: none;
      }

      .features-list li {
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
        font-size: 0.95rem;
        line-height: 1.5;
      }

      .features-list li:last-child {
        border-bottom: none;
      }

      .features-list li strong {
        font-weight: 700;
        color: #000;
      }

      /* Fullscreen Modal */
      .fullscreen-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.95);
        z-index: 1000;
        display: none;
        align-items: center;
        justify-content: center;
      }

      .fullscreen-image {
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
      }

      /* Mobile Header Styles */
      .mobile-header {
        display: flex;
        align-items: center;
        justify-content: center;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 64px;
        background: #fff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
        padding: 0;
        z-index: 100;
      }

      .mobile-header__logo {
        position: static;
        margin: 0 auto;
        width: auto;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        text-decoration: none;
      }

      .mobile-header__logo-img {
        height: 38px;
        width: auto;
        object-fit: contain;
      }

      .mobile-header-spacer {
        display: none;
        height: 64px;
        width: 100%;
      }

      /* Burger Menu */
      .burger {
        position: absolute;
        top: 50%;
        left: 20px;
        width: 32px;
        height: 32px;
        transform: translateY(-50%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        background: none;
        border: none;
        z-index: 2000;
        cursor: pointer;
        gap: 4px;
      }

      .burger span {
        display: block;
        width: 22px;
        height: 2px;
        background: #000;
        border-radius: 0;
        transition: transform 0.3s cubic-bezier(0.4, 1.3, 0.6, 1), opacity 0.25s;
      }

      .burger.open span:nth-child(1) {
        transform: translateY(6px) rotate(45deg);
      }
      .burger.open span:nth-child(2) {
        opacity: 0;
      }
      .burger.open span:nth-child(3) {
        transform: translateY(-6px) rotate(-45deg);
      }

      /* Dropdown Menu */
      .dropdown-menu {
        position: fixed;
        top: 60px;
        left: 0;
        width: 100%;
        background: #fff;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 20px;
        z-index: 1000;
        display: none;
        flex-direction: column;
        gap: 15px;
        opacity: 0;
        transform: translateY(-10px);
        transition: opacity 0.2s, transform 0.2s;
      }

      .dropdown-menu.active {
        display: flex;
        opacity: 1;
        transform: translateY(0);
      }

      .dropdown-menu a {
        color: #111;
        font-size: 1.1rem;
        text-decoration: none;
        padding: 10px 15px;
        border-radius: 5px;
        transition: background 0.2s;
      }

      .dropdown-menu a:hover {
        background: #f0f0f0;
      }

      /* Hero Section Styles */
      .hero-stock {
        position: relative;
        width: 100%;
        background: #fff;
      }

      .hero-header-group {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 2rem;
        background: #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 100;
        height: 48px;
        min-height: 48px;
      }

      .header__left {
        display: flex;
        align-items: center;
      }

      .header__back-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #333;
        text-decoration: none;
        font-size: 1rem;
        transition: all 0.3s ease;
        padding: 0;
        border-radius: 4px;
        height: 48px;
        line-height: 48px;
      }

      .header__back-link:hover {
        color: #000;
        background: rgba(0, 0, 0, 0.05);
        transform: translateX(-2px);
      }

      .header__back-link svg {
        width: 24px;
        height: 24px;
        transition: transform 0.3s ease;
      }

      .header__back-link:hover svg {
        transform: translateX(-2px);
      }

      .header__center-group {
        flex: 1;
        display: flex;
        justify-content: center;
      }

      .header__nav {
        display: flex;
        align-items: center;
        gap: 2rem;
      }

      .header__center-group {
        flex: 1;
        display: flex;
        justify-content: center;
      }

      .header__logo-link {
        font-family: "Inter", Arial, sans-serif;
        font-size: 1.5rem;
        font-weight: 400;
        letter-spacing: 4px;
        color: #000;
        text-transform: uppercase;
        text-decoration: none;
        margin-right: 2rem;
        transition: all 0.3s ease;
        position: relative;
        line-height: 48px;
        padding: 0;
      }

      .header__logo-img {
        height: 30px !important;
        width: 100px !important;
        object-fit: contain;
        display: block;
      }

      .header__logo-link {
        font-family: "Inter", Arial, sans-serif;
        font-size: 1.5rem;
        font-weight: 400;
        letter-spacing: 4px;
        color: #000;
        text-transform: uppercase;
        text-decoration: none;
        margin-right: 2rem;
        transition: all 0.3s ease;
        position: relative;
        line-height: 48px;
        padding: 0;
      }

      .header__logo-link:hover {
        transform: translateY(-2px);
        color: #333;
      }

      .header__nav-links {
        display: flex;
        gap: 1.5rem;
      }

      .header__nav-link {
        color: #333;
        text-decoration: none;
        font-size: 1rem;
        transition: all 0.3s ease;
        position: relative;
        padding: 0 0.5rem;
        font-weight: 400;
        line-height: 48px;
        height: 48px;
        display: flex;
        align-items: center;
      }

      .header__nav-link:hover {
        color: #000;
        transform: translateY(-2px);
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .container {
          padding: 15px;
        }

        .car-header {
          flex-direction: column;
          text-align: center;
        }

        .car-title {
          font-size: 2rem;
        }

        .content-section {
          grid-template-columns: 1fr;
          gap: 30px;
        }

        .specs-table {
          grid-template-columns: 1fr;
          gap: 15px;
        }

        .features-grid {
          flex-direction: column;
          gap: 30px;
        }

        .features-column {
          max-width: 100%;
        }

        .features-column:first-child,
        .features-column:last-child {
          text-align: left;
        }

        .features-column:first-child h3,
        .features-column:last-child h3 {
          text-align: left;
        }

        .additional-images {
          grid-template-columns: 1fr;
        }

        .main-image-container {
          height: 300px;
        }

        .burger {
          display: flex;
        }

        .mobile-header {
          display: flex;
        }

        .mobile-header-spacer {
          display: block;
        }

        .hero-header-group {
          display: none;
        }
      }

      /* Desktop styles */
      @media (min-width: 769px) {
        .mobile-header,
        .mobile-header-spacer,
        .burger,
        .dropdown-menu {
          display: none !important;
        }

        .hero-header-group {
          display: flex !important;
        }

        /* Logo should be visible on all devices for car detail pages */
      }

      /* Footer Styles */
      .footer {
        background: #000000;
        color: #fff;
        padding: 3rem 2vw 1rem 2vw;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-top: 2rem;
      }

      .footer__info {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: flex-start;
        width: 100%;
        padding-bottom: 1.5rem;
      }

      .footer__left {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }

      .footer__logo {
        margin-bottom: 1rem;
      }

      .footer__logo-img {
        height: 80px;
        width: auto;
        display: block;
        margin-right: 0;
      }

      .footer__address {
        margin: 0;
        padding: 0;
        line-height: 1.5;
      }

      .footer__right {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
      }

      .footer__nav {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 2rem;
        margin-bottom: 1.5rem;
      }

      .footer__nav a {
        color: #fff;
        font-weight: 700;
        font-size: 1.1rem;
        transition: color 0.2s;
        text-decoration: none;
      }

      .footer__nav a:hover {
        color: #b2c9d6;
      }

      .footer__copyright {
        width: 100%;
        color: #aaa;
        font-size: 1rem;
        margin-top: 1.5rem;
      }

      .footer__requisites {
        background: #181b1d;
        color: #a5c3d7;
        border-radius: 14px;
        padding: 1.5rem 2rem 1.2rem 2rem;
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
        font-size: 1.08rem;
        width: 100%;
      }

      .footer__requisites-title {
        font-weight: 700;
        color: #fff;
        font-size: 1.18rem;
        margin-bottom: 0.7em;
        letter-spacing: 0.01em;
        text-align: right;
      }

      .footer__requisites-info {
        line-height: 1.5;
        font-size: 1.05rem;
        text-align: right;
      }

      /* Footer Desktop Styles */
      @media (min-width: 601px) {
        .footer__info {
          display: grid;
          grid-template-columns: 1fr auto;
          grid-template-areas: "left right";
          align-items: flex-start;
          width: 100%;
          padding-bottom: 2rem;
          gap: 0 4rem;
        }

        .footer__left {
          grid-area: left;
        }

        .footer__right {
          grid-area: right;
          max-width: 540px;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
        }

        .footer__nav {
          justify-content: flex-end;
          margin-bottom: 0.5rem;
        }

        .footer__requisites {
          width: 350px;
          max-width: 350px;
          margin-left: 1rem;
          margin-right: 1rem;
          margin-top: 2.5rem;
        }
      }

      /* Footer Mobile Styles */
      @media (max-width: 600px) {
        .footer {
          padding: 2rem 15px 1rem 15px;
        }

        .footer__info {
          flex-direction: column;
          gap: 1.5rem;
        }

        .footer__left {
          width: 100%;
          align-items: center;
          text-align: center;
        }

        .footer__logo {
          margin-bottom: 15px;
        }

        .footer__logo-img {
          max-width: 100px;
          margin: 0 auto;
        }

        .footer__address {
          font-size: 14px;
        }

        .footer__right {
          width: 100%;
          margin-top: 1.5rem;
          align-items: center;
          text-align: center;
        }

        .footer__nav {
          display: none !important;
        }

        .footer__requisites {
          width: 100%;
          padding: 1.2rem 1.5rem 1rem 1.5rem;
          text-align: center;
        }

        .footer__requisites-title {
          font-size: 16px;
          margin-bottom: 8px;
          text-align: center;
        }

        .footer__requisites-info {
          font-size: 14px;
          text-align: center;
        }
      }
    </style>
  </head>
  <body>
    <!-- Mobile Header -->
    <header class="mobile-header">
      <button class="burger" id="burgerBtn" aria-label="Открыть меню">
        <span></span>
        <span></span>
        <span></span>
      </button>
      <a href="/index.html" class="mobile-header__logo">
        <img src="/Img/logo.png" alt="SHMS" class="mobile-header__logo-img" />
      </a>
    </header>

    <!-- Пустой раздел для отступа -->
    <div class="mobile-header-spacer"></div>

    <!-- Mobile Menu -->
    <nav class="dropdown-menu" id="dropdownMenu">
      <a href="/stock.html">Авто в наличии</a>
      <a href="/order.html">Авто под заказ</a>
      <a href="/contacts.html">Наши контакты</a>
      <a href="/feedback-mockup.html">Обратная связь</a>
    </nav>

    <!-- Hero / Title Section -->
    <section class="hero-stock">
      <div class="hero-header-group">
        <div class="header__left">
          <a
            href="#"
            class="header__back-link"
            onclick="window.history.back(); return false;"
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19 12H5M5 12L12 19M5 12L12 5"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            Назад
          </a>
        </div>
        <div class="header__center-group">
          <nav class="header__nav">
            <a href="/index.html" class="header__logo-link"
              ><img src="/Img/logo.png" alt="SHMS" class="header__logo-img"
            /></a>
            <div class="header__nav-links">
              <a href="/stock.html" class="header__nav-link">Авто в наличии</a>
              <a href="/order.html" class="header__nav-link">Заказать авто</a>
              <a href="/contacts.html" class="header__nav-link"
                >Наши контакты</a
              >
              <a href="/feedback-mockup.html" class="header__nav-link"
                >Обратная связь</a
              >
            </div>
          </nav>
        </div>
      </div>
    </section>

    <div class="container">
      <!-- Header -->
      <div class="car-header">
        <div class="car-title-section">
          <h1 class="car-title">{{FULL_TITLE}}</h1>
          <p class="car-subtitle">{{CAR_SUBTITLE}}</p>
        </div>
        <div class="car-price-section">
          <div class="car-price">{{PRICE}}</div>
          <div class="moscow-price">{{MOSCOW_PRICE}}</div>
          <button
            class="buy-button"
            onclick="window.location.href='/feedback-mockup.html'"
          >
            Купить
          </button>
        </div>
      </div>

      <!-- Main Gallery -->
      <div class="main-gallery">
        <div class="main-image-container">
          <img
            class="main-image"
            src="{{MAIN_IMAGE}}"
            alt="{{FULL_TITLE}}"
            onclick="openFullscreen(0)"
          />
          <button class="gallery-nav prev" onclick="previousImage()">‹</button>
          <button class="gallery-nav next" onclick="nextImage()">›</button>
        </div>
      </div>

      <!-- Content Section -->
      <div class="content-section">
        <div class="description-section">
          <h2>Описание</h2>
          <div class="description-text">{{DESCRIPTION}}</div>

          <div class="additional-images">{{DESCRIPTION_IMAGES}}</div>
        </div>

        <div class="sidebar">
          <div class="sidebar-price">{{PRICE}}</div>
          <div class="sidebar-moscow-price">{{MOSCOW_PRICE}}</div>

          <div class="info-section">
            <h3>Инфо</h3>
            <div class="info-item">
              <div class="info-icon">🚗</div>
              <span>{{CAR_TYPE}}</span>
            </div>
            <div class="info-item">
              <div class="info-icon">⚡</div>
              <span>{{POWER}}</span>
            </div>
            <div class="info-item">
              <div class="info-icon">📊</div>
              <span>{{MILEAGE}}</span>
            </div>
          </div>

          <div class="priority-section">
            <h3>Приоритет</h3>
            <p class="priority-text">
              Вы станете первым владельцем автомобиля, так как его пробег
              обусловлен исключительно автомобильной эксплуатацией (логистика, в
              транспортировке).
            </p>
            <button
              class="buy-button"
              style="width: 100%; margin-top: 15px"
              onclick="window.location.href='/feedback-mockup.html'"
            >
              Купить
            </button>
          </div>
        </div>
      </div>

      <!-- Specifications -->
      <div class="specs-section">
        <h2>Характеристики</h2>
        <div class="specs-table">{{SPECIFICATIONS}}</div>

        <!-- Specifications Images -->
        <div class="additional-images" style="margin-top: 30px">
          {{SPECS_IMAGES}}
        </div>
      </div>

      <!-- Best Features -->
      <div class="best-features-section">
        <h2>Лучшее в авто</h2>
        <div class="features-grid">
          <div class="features-column">
            <h3>Основные опции</h3>
            <ul class="features-list">
              {{MAIN_FEATURES}}
            </ul>
          </div>
          <div class="features-column">
            <h3>Дополнительные</h3>
            <ul class="features-list">
              {{ADDITIONAL_FEATURES}}
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Fullscreen Modal -->
    <div
      class="fullscreen-modal"
      id="fullscreenModal"
      onclick="closeFullscreen()"
    >
      <img class="fullscreen-image" id="fullscreenImage" src="" alt="" />
      <button class="close-fullscreen" onclick="closeFullscreen()">×</button>
      <button
        class="gallery-nav prev"
        onclick="previousImage()"
        style="left: 50px"
      >
        ‹
      </button>
      <button
        class="gallery-nav next"
        onclick="nextImage()"
        style="right: 50px"
      >
        ›
      </button>
    </div>

    <script>
      // Gallery functionality
      let currentImageIndex = 0;
      const images = [{{IMAGE_ARRAY}}];

      function nextImage() {
          currentImageIndex = (currentImageIndex + 1) % images.length;
          updateMainImage();
      }

      function previousImage() {
          currentImageIndex = (currentImageIndex - 1 + images.length) % images.length;
          updateMainImage();
      }

      function updateMainImage() {
          const mainImage = document.querySelector('.main-image');
          const fullscreenImage = document.getElementById('fullscreenImage');
          mainImage.src = images[currentImageIndex];
          fullscreenImage.src = images[currentImageIndex];
      }

      function openFullscreen(index) {
          currentImageIndex = index;
          updateMainImage();
          document.getElementById('fullscreenModal').style.display = 'flex';
          document.querySelector('.close-fullscreen').style.display = 'block';
      }

      function closeFullscreen() {
          document.getElementById('fullscreenModal').style.display = 'none';
      }

      // Additional image clicks
      document.querySelectorAll('.additional-image').forEach((img, index) => {
          img.addEventListener('click', () => openFullscreen(index + 1));
      });

      // Keyboard navigation
      document.addEventListener('keydown', (e) => {
          if (document.getElementById('fullscreenModal').style.display === 'flex') {
              if (e.key === 'ArrowLeft') previousImage();
              if (e.key === 'ArrowRight') nextImage();
              if (e.key === 'Escape') closeFullscreen();
          }
      });
    </script>

    <!-- Footer -->
    <footer class="footer">
      <div class="footer__info">
        <div class="footer__left">
          <div class="footer__logo">
            <img
              src="/assets/img/icons/logo-shms.png"
              alt="SHMS"
              class="footer__logo-img"
            />
          </div>
          <div class="footer__address">
            Наш адрес:<br />
            Москва,<br />
            Пресненская набережная 12<br />
            Башня "Федерация"<br />
            12 этаж, офис К2
          </div>
        </div>
        <div class="footer__right">
          <nav class="footer__nav">
            <a href="/order.html">Под заказ</a>
            <a href="/contacts.html">Контакты</a>
            <a href="/stock.html">Авто в наличии</a>
          </nav>
          <div class="footer__requisites">
            <div class="footer__requisites-title">Реквизиты организации</div>
            <div class="footer__requisites-info">
              ИП Шамаев Мансур Махмудович<br />
              ИНН 201578554480, ОГРН 324200000020490<br />
            </div>
          </div>
        </div>
      </div>
      <div class="footer__copyright">© 2025 Все права защищены</div>
    </footer>

    <!-- Mobile Menu JavaScript -->
    <script>
      // Mobile menu functionality
      document.addEventListener("DOMContentLoaded", function () {
        const burgerBtn = document.getElementById("burgerBtn");
        const dropdownMenu = document.getElementById("dropdownMenu");

        if (burgerBtn && dropdownMenu) {
          burgerBtn.addEventListener("click", function () {
            burgerBtn.classList.toggle("open");
            dropdownMenu.classList.toggle("active");
          });

          // Close menu when clicking outside
          document.addEventListener("click", function (e) {
            if (
              !burgerBtn.contains(e.target) &&
              !dropdownMenu.contains(e.target)
            ) {
              burgerBtn.classList.remove("open");
              dropdownMenu.classList.remove("active");
            }
          });
        }
      });
    </script>
  </body>
</html>
