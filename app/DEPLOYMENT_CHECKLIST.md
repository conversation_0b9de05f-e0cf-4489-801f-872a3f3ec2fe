# ✅ Чек-лист развертывания SHMS Auto на Рег.ру

## 📋 Предварительная подготовка

### Заказ и настройка VPS
- [ ] Заказан VPS на Рег.ру (Ubuntu 20.04+, 2GB RAM, 20GB SSD)
- [ ] Получены данные доступа (IP, логин root, пароль)
- [ ] Выполнено первое подключение по SSH
- [ ] Домен зарегистрирован и привязан к аккаунту

### Подготовка файлов проекта
- [ ] Все файлы проекта готовы к загрузке
- [ ] Проверена работоспособность на локальной машине
- [ ] Подготовлен .env файл для продакшена
- [ ] Настроены email адреса для уведомлений

## 🔧 Настройка сервера

### Обновление системы
```bash
- [ ] apt update && apt upgrade -y
- [ ] apt install -y curl wget git nano htop unzip nginx certbot python3-certbot-nginx
```

### Установка Node.js и PM2
```bash
- [ ] curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
- [ ] apt-get install -y nodejs
- [ ] npm install -g pm2
- [ ] node --version (проверка)
- [ ] pm2 --version (проверка)
```

### Создание пользователя
```bash
- [ ] adduser shmsauto --disabled-password --gecos ""
- [ ] usermod -aG sudo shmsauto
- [ ] su - shmsauto (переключение)
```

## 📁 Загрузка проекта

### Создание структуры
```bash
- [ ] mkdir /home/<USER>/shms-auto
- [ ] cd /home/<USER>/shms-auto
```

### Загрузка файлов (выберите один способ)
#### Вариант A: Git
```bash
- [ ] git clone [ваш-репозиторий] .
```

#### Вариант B: SFTP
- [ ] Подключение через FileZilla/WinSCP
- [ ] Загрузка всех файлов в /home/<USER>/shms-auto/
- [ ] Проверка целостности файлов

#### Вариант C: Архив
```bash
- [ ] wget [ссылка-на-архив]
- [ ] unzip [архив]
```

## ⚙️ Настройка приложения

### Установка зависимостей
```bash
- [ ] cd /home/<USER>/shms-auto
- [ ] npm install --production
- [ ] Проверка отсутствия ошибок
```

### Настройка .env файла
```bash
- [ ] cp .env.example .env
- [ ] nano .env
```

#### Обязательные изменения в .env:
- [ ] `DOMAIN=ваш-домен.ru`
- [ ] `SITE_URL=https://ваш-домен.ru`
- [ ] `CONTACT_EMAIL=ваша-почта@gmail.com`
- [ ] `MANAGER_EMAIL=ваша-почта@gmail.com`
- [ ] `SESSION_SECRET=[уникальный-ключ]`

### Создание директорий
```bash
- [ ] mkdir -p logs data/uploads data/cache
- [ ] chmod 755 data/uploads logs
```

### Тестирование
```bash
- [ ] node server.js (тест запуска)
- [ ] Ctrl+C (остановка)
- [ ] Проверка отсутствия ошибок
```

## 🚀 Запуск через PM2

### Запуск приложения
```bash
- [ ] pm2 start ecosystem.config.js --env production
- [ ] pm2 list (проверка статуса)
- [ ] pm2 logs shms-auto (проверка логов)
```

### Настройка автозапуска
```bash
- [ ] pm2 startup
- [ ] Выполнение команды, выданной PM2
- [ ] pm2 save
```

### Проверка работы
- [ ] `curl http://localhost:3001/test` (должен вернуть JSON)

## 🌐 Настройка Nginx

### Создание конфигурации
```bash
- [ ] sudo nano /etc/nginx/sites-available/shms-auto
```

### Содержимое конфигурации
- [ ] Скопирован шаблон из `server/utils/nginx-config.conf`
- [ ] Заменен `ваш-домен.ru` на реальный домен
- [ ] Проверены пути к файлам

### Активация
```bash
- [ ] sudo ln -s /etc/nginx/sites-available/shms-auto /etc/nginx/sites-enabled/
- [ ] sudo rm -f /etc/nginx/sites-enabled/default
- [ ] sudo nginx -t (проверка конфигурации)
- [ ] sudo systemctl restart nginx
- [ ] sudo systemctl enable nginx
```

## 🌍 Настройка DNS

### В панели Рег.ру
- [ ] Переход в "Домены" → "Управление DNS"
- [ ] Добавление A-записи: @ → IP-сервера
- [ ] Добавление A-записи: www → IP-сервера
- [ ] Сохранение изменений
- [ ] Ожидание распространения DNS (до 24 часов)

### Проверка DNS
```bash
- [ ] nslookup ваш-домен.ru
- [ ] ping ваш-домен.ru
```

## 🔒 Настройка SSL

### Установка сертификата
```bash
- [ ] sudo certbot --nginx -d ваш-домен.ru -d www.ваш-домен.ru
- [ ] Ввод email для уведомлений
- [ ] Согласие с условиями (Y)
- [ ] Выбор редиректа на HTTPS (2)
```

### Настройка автообновления
```bash
- [ ] sudo crontab -e
- [ ] Добавление: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Проверка SSL
- [ ] Открытие https://ваш-домен.ru в браузере
- [ ] Проверка зеленого замка

## ✅ Финальная проверка

### Проверка сервисов
```bash
- [ ] pm2 list (статус приложения)
- [ ] sudo systemctl status nginx (статус Nginx)
- [ ] sudo netstat -tulpn | grep -E ':(80|443|3001)' (открытые порты)
```

### Проверка в браузере
- [ ] `http://IP-сервера:3001/test` - прямой доступ к Node.js
- [ ] `https://ваш-домен.ru` - через Nginx с SSL
- [ ] `https://ваш-домен.ru/admin` - админ-панель
- [ ] `https://ваш-домен.ru/stock` - страница stock

### Проверка функций
- [ ] Главная страница загружается полностью
- [ ] Админ-панель доступна
- [ ] Загрузка файлов работает
- [ ] Отправка форм работает
- [ ] API отвечает корректно

### Проверка здоровья
```bash
- [ ] npm run health-check
- [ ] npm run test-mail
```

## 🔧 Настройка мониторинга

### Логирование
```bash
- [ ] Настройка ротации логов Nginx
- [ ] Настройка ротации логов приложения
```

### Резервное копирование
```bash
- [ ] chmod +x scripts/backup.sh
- [ ] ./scripts/backup.sh --full (тестовый бэкап)
- [ ] crontab -e (настройка автоматического бэкапа)
- [ ] Добавление: 0 2 * * * /home/<USER>/shms-auto/scripts/backup.sh
```

### Мониторинг
```bash
- [ ] pm2 install pm2-server-monit (опционально)
- [ ] Настройка уведомлений о сбоях
```

## 📞 Документация и поддержка

### Создание документации
- [ ] Сохранение всех паролей и ключей в безопасном месте
- [ ] Документирование настроек сервера
- [ ] Создание инструкций для администрирования

### Контакты
- [ ] Email для технической поддержки настроен
- [ ] Доступы переданы ответственным лицам

## 🎉 Завершение

### Финальные проверки
- [ ] Все функции сайта работают
- [ ] SSL сертификат установлен и работает
- [ ] Автозапуск настроен
- [ ] Резервное копирование настроено
- [ ] Мониторинг работает

### Уведомления
- [ ] Команда уведомлена о завершении развертывания
- [ ] Клиент уведомлен о готовности сайта
- [ ] Документация передана администраторам

---

## 🚨 В случае проблем

### Логи для диагностики
```bash
# Логи приложения
pm2 logs shms-auto

# Логи Nginx
sudo tail -f /var/log/nginx/error.log

# Системные логи
sudo journalctl -u nginx -f
```

### Перезапуск сервисов
```bash
# Перезапуск приложения
pm2 restart shms-auto

# Перезапуск Nginx
sudo systemctl restart nginx

# Полный перезапуск
sudo reboot
```

### Контакты поддержки
- Email: <EMAIL>
- Документация: см. PRODUCTION_DEPLOY_GUIDE.md

---

**✅ Развертывание завершено успешно!**

Дата завершения: _______________
Ответственный: _______________
Подпись: _______________
