# Обновление размера логотипа и анимации на странице stock

## Выполненные изменения

### 1. Восстановлен изначальный размер логотипа
- **Было**: 40px высота на всех страницах
- **Стало**: 30px высота на всех страницах (как было изначально)

### 2. Добавлена специальная анимация для страницы stock
- Логотип сдвинут влево на 35px для корректной работы анимации
- При hover логотип поднимается на 2px вверх
- Анимация работает плавно с transition 0.3s ease

### 3. <PERSON><PERSON><PERSON><PERSON><PERSON>, которые были изменены:

#### CSS файлы:
- `public/css/header-common.css`:
  - Изменены переменные: `--logo-height-desktop: 30px` и `--logo-height-mobile: 30px`
  - Добавлены специальные стили для `body.stock-page`

- `public/css/stock.css`:
  - Обновлены размеры логотипа с 56px/40px на 30px
  - Исправлены медиа-запросы (32px → 26px для мобильных)

#### HTML файлы:
- `public/stock.html`:
  - Добавлен класс `stock-page` к элементу `<body>`

### 4. Технические детали анимации:

```css
/* Специальные стили только для страницы stock */
body.stock-page .header__logo-link {
  margin-left: -35px; /* Сдвигаем контейнер влево */
}

body.stock-page .header__logo-img {
  transform: translateX(35px); /* Компенсируем сдвиг логотипа */
  margin-top: 15px;
  transition: transform 0.3s ease;
}

body.stock-page .header__logo-link:hover .header__logo-img {
  transform: translateX(35px) translateY(-2px); /* Анимация при hover */
}
```

### 5. Результат:

✅ **Логотип теперь имеет правильный размер (30px) на всех страницах**
✅ **На странице stock логотип сдвинут влево для анимации**
✅ **Hover-анимация работает корректно**
✅ **Единообразие сохранено на всех остальных страницах**

### 6. Проверка:

Откройте следующие страницы и убедитесь, что:

1. **http://localhost:3000/stock.html** - логотип сдвинут влево, анимация работает
2. **http://localhost:3000/contacts.html** - логотип обычного размера, по центру
3. **http://localhost:3000/order.html** - логотип обычного размера, по центру
4. **http://localhost:3000/feedback-mockup.html** - логотип обычного размера, по центру
5. **http://localhost:3000/** - логотип обычного размера

### 7. Мобильная версия:

На мобильных устройствах (ширина экрана < 768px):
- Используется мобильная шапка с логотипом 30px
- Специальные стили для stock не применяются
- Анимация работает только на десктопе

### 8. Совместимость:

- ✅ Все браузеры поддерживают CSS transform и transition
- ✅ Мобильные устройства отображают корректно
- ✅ Не влияет на производительность
- ✅ Обратная совместимость сохранена
