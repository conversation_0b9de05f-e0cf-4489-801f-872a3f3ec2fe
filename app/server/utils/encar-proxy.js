const express = require("express");
const fetch = require("node-fetch");
const app = express();
const PORT = 3002; // Изменен порт на 3002

// Внешний API-эндпоинт (замените на актуальный, если потребуется)
const BASE_URL = "https://autobase-wade.auto-parser.ru/encar";

// Добавляем CORS middleware
app.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  res.header(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept, Authorization"
  );
  if (req.method === "OPTIONS") {
    res.sendStatus(200);
  } else {
    next();
  }
});

app.get("/api/encar", async (req, res) => {
  const {
    fileType = "active_offer",
    date,
    brand = "",
    model = "",
    limit = 100,
    offset = 0,
    direct = 1,
  } = req.query;

  if (!date) {
    return res.status(400).json({ error: "date required" });
  }

  console.log("Received parameters:", {
    fileType,
    date,
    brand,
    model,
    limit,
    offset,
  });

  // Собираем URL для запроса к внешнему API с параметрами поиска
  const searchParams = new URLSearchParams();
  if (brand) searchParams.append("brand", brand);
  if (model) searchParams.append("model", model);
  searchParams.append("limit", limit);
  if (offset) searchParams.append("offset", offset);

  const url = `${BASE_URL}/${date}/search?${searchParams.toString()}`;
  console.log("Proxying request to:", url);

  try {
    const response = await fetch(url, { timeout: 30000 });
    if (!response.ok) {
      console.error("API error:", response.status, response.statusText);
      return res
        .status(502)
        .json({ error: "API error", status: response.status });
    }
    const data = await response.json();
    console.log(
      "API response received, data length:",
      Array.isArray(data) ? data.length : "not array"
    );
    res.json(data);
  } catch (err) {
    console.error("Proxy error:", err.message);
    res.status(500).json({ error: "Proxy error", details: err.message });
  }
});

app.listen(PORT, () => {
  console.log(`Encar proxy running on http://localhost:${PORT}/api/encar`);
});
