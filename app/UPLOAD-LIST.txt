ФАЙЛЫ ДЛЯ ЗАГРУЗКИ НА СЕРВЕР ЧЕРЕЗ FILEZILLA:

ОБЯЗАТЕЛЬНЫЕ ФАЙЛЫ (загружать):
✅ .env
✅ package.json  
✅ server.js
✅ ecosystem.config.js
✅ deploy-regru.sh

ПАПКИ (загружать полностью):
✅ admin/ (вся папка)
✅ public/ (вся папка) 
✅ server/ (вся папка)
✅ config/ (вся папка)
✅ scripts/ (вся папка)
✅ data/ (включая stock.db, но uploads можно не трогать если работает)

НЕ ЗАГРУЖАТЬ:
❌ node_modules/ (будет установлено командой npm install)
❌ logs/ (создастся автоматически)
❌ .git/ (если есть)

ИТОГО: загружаете ВСЕ файлы проекта КРОМЕ node_modules
