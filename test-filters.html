<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест фильтров SHMS Auto</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .filter-test {
            display: flex;
            gap: 20px;
            margin: 10px 0;
        }
        .filter-test input, .filter-test select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🧪 Тест фильтров SHMS Auto</h1>
    
    <div class="test-section">
        <h2>📋 Информация о тестировании</h2>
        <div class="info test-result">
            <strong>Цель:</strong> Проверить автоматическое применение фильтров без нажатия кнопки "Применить"
        </div>
        <div class="info test-result">
            <strong>Ожидаемое поведение:</strong>
            <ul>
                <li>✅ Dropdown фильтры (марка, модель, кузов, КПП) применяются мгновенно</li>
                <li>✅ Слайдеры (год, цена, пробег) применяются с задержкой 500мс</li>
                <li>✅ Кнопка "Применить" все еще работает</li>
                <li>✅ Кнопка "Сбросить" корректно сбрасывает все фильтры</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🔗 Быстрые ссылки для тестирования</h2>
        <button onclick="window.open('http://localhost:3000/order', '_blank')">
            🚗 Открыть страницу "Под заказ"
        </button>
        <button onclick="window.open('http://localhost:3000/stock', '_blank')">
            🏪 Открыть страницу "В наличии"
        </button>
    </div>

    <div class="test-section">
        <h2>📝 Инструкции по тестированию</h2>
        <ol>
            <li><strong>Откройте страницу "Под заказ"</strong> по ссылке выше</li>
            <li><strong>Откройте консоль браузера</strong> (F12 → Console)</li>
            <li><strong>Протестируйте dropdown фильтры:</strong>
                <ul>
                    <li>Выберите марку → должны загрузиться автомобили этой марки</li>
                    <li>Выберите модель → результаты должны обновиться</li>
                    <li>Измените тип кузова → результаты должны обновиться</li>
                    <li>Измените КПП → результаты должны обновиться</li>
                </ul>
            </li>
            <li><strong>Протестируйте слайдеры:</strong>
                <ul>
                    <li>Измените диапазон года → через 500мс результаты должны обновиться</li>
                    <li>Измените диапазон цены → через 500мс результаты должны обновиться</li>
                </ul>
            </li>
            <li><strong>Проверьте консоль:</strong>
                <ul>
                    <li>Должны появляться сообщения "Updated activeFilters:"</li>
                    <li>Должны появляться сообщения "=== ОТЛАДКА buildApiUrl ==="</li>
                    <li>URL запросов должны содержать параметры фильтров</li>
                </ul>
            </li>
            <li><strong>Протестируйте кнопки:</strong>
                <ul>
                    <li>Кнопка "Применить" должна работать</li>
                    <li>Кнопка "Сбросить" должна очистить все фильтры</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🐛 Возможные проблемы и решения</h2>
        <div class="error test-result">
            <strong>Проблема:</strong> Фильтры не применяются автоматически<br>
            <strong>Решение:</strong> Проверьте консоль на ошибки JavaScript
        </div>
        <div class="error test-result">
            <strong>Проблема:</strong> Слишком много запросов к серверу<br>
            <strong>Решение:</strong> Увеличьте задержку debounce в коде
        </div>
        <div class="error test-result">
            <strong>Проблема:</strong> Фильтры сбрасываются при изменении<br>
            <strong>Решение:</strong> Проверьте функцию updateActiveFilters()
        </div>
    </div>

    <div class="test-section">
        <h2>📊 Результаты тестирования</h2>
        <div id="test-results">
            <div class="info test-result">
                Откройте страницу "Под заказ" и протестируйте фильтры согласно инструкциям выше.
                Результаты тестирования записывайте здесь.
            </div>
        </div>
    </div>

    <script>
        // Простая функция для записи результатов тестирования
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
        }

        // Добавляем кнопки для быстрого добавления результатов
        const testSection = document.querySelector('#test-results').parentElement;
        const buttonContainer = document.createElement('div');
        buttonContainer.innerHTML = `
            <button onclick="addTestResult('✅ Dropdown фильтры работают корректно', 'success')">✅ Dropdown OK</button>
            <button onclick="addTestResult('✅ Слайдеры работают корректно', 'success')">✅ Слайдеры OK</button>
            <button onclick="addTestResult('✅ Кнопки Применить/Сбросить работают', 'success')">✅ Кнопки OK</button>
            <button onclick="addTestResult('❌ Обнаружена проблема с фильтрами', 'error')">❌ Проблема</button>
            <button onclick="document.getElementById('test-results').innerHTML = '<div class=\\'info test-result\\'>Результаты очищены</div>'">🗑️ Очистить</button>
        `;
        testSection.appendChild(buttonContainer);

        console.log('🧪 Тестовая страница фильтров загружена');
        console.log('📋 Откройте http://localhost:3000/order для тестирования');
    </script>
</body>
</html>
