# Enable PHP processing
<IfModule mod_php7.c>
    php_flag display_errors on
    php_flag log_errors on
    php_value error_reporting E_ALL
    php_value max_execution_time 300
    php_value memory_limit 256M
</IfModule>

# Handle PHP files
<FilesMatch "\.php$">
    SetHandler application/x-httpd-php
    ForceType application/x-httpd-php
</FilesMatch>

# Allow API access
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type, Authorization"
</IfModule>

# Disable directory browsing
Options -Indexes

# Handle errors
ErrorDocument 500 '{"error": "Internal Server Error", "status": 500}'
ErrorDocument 404 '{"error": "Not Found", "status": 404}'
ErrorDocument 403 '{"error": "Forbidden", "status": 403}'

# Process OPTIONS requests
RewriteEngine On
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# Ensure PHP files are processed
AddType application/x-httpd-php .php
AddHandler application/x-httpd-php .php 