const http = require('http');

const data = JSON.stringify({
  outputPath: 'cars'
});

const options = {
  hostname: 'localhost',
  port: 3001,
  path: '/api/car-details/1/generate',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': data.length
  }
};

console.log('Отправляем запрос на генерацию HTML...');

const req = http.request(options, (res) => {
  console.log(`Статус: ${res.statusCode}`);
  console.log(`Заголовки: ${JSON.stringify(res.headers)}`);
  
  let responseData = '';
  res.on('data', (chunk) => {
    responseData += chunk;
  });
  
  res.on('end', () => {
    console.log('Ответ сервера:');
    console.log(responseData);
  });
});

req.on('error', (e) => {
  console.error(`Ошибка запроса: ${e.message}`);
});

req.write(data);
req.end();
