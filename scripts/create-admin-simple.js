const Admin = require('../server/models/Admin');

async function createAdmin() {
    console.log('🔧 Создание администратора SHMS Auto');
    console.log('====================================\n');

    try {
        // Данные администратора (замените на свои)
        const adminData = {
            username: 'Admin',
            email: '<EMAIL>',
            password: 'Admin123!@#',  // Замените на безопасный пароль
            fullName: 'Главный администратор'
        };

        console.log('Создание администратора с данными:');
        console.log(`Логин: ${adminData.username}`);
        console.log(`Email: ${adminData.email}`);
        console.log(`Полное имя: ${adminData.fullName}`);
        console.log(`Пароль: ${'*'.repeat(adminData.password.length)}`);

        const adminModel = new Admin();
        
        // Проверяем, существует ли уже администратор
        const existingAdmin = await adminModel.findByUsername(adminData.username);
        if (existingAdmin) {
            console.log('❌ Администратор с таким логином уже существует');
            return;
        }

        const existingEmail = await adminModel.findByEmail(adminData.email);
        if (existingEmail) {
            console.log('❌ Администратор с таким email уже существует');
            return;
        }

        // Создаем администратора
        const newAdmin = await adminModel.create(adminData);

        console.log('\n✅ Администратор успешно создан!');
        console.log(`ID: ${newAdmin.id}`);
        console.log(`Логин: ${newAdmin.username}`);
        console.log(`Email: ${newAdmin.email}`);
        console.log(`Создан: ${newAdmin.createdAt}`);

        console.log('\n🎉 Теперь вы можете войти в админку:');
        console.log('http://localhost:3000/admin/login.html');

    } catch (error) {
        console.error('❌ Ошибка при создании администратора:', error.message);
        process.exit(1);
    }
}

// Запуск
createAdmin().then(() => {
    console.log('\n✅ Готово!');
    process.exit(0);
}).catch(error => {
    console.error('Критическая ошибка:', error);
    process.exit(1);
});
