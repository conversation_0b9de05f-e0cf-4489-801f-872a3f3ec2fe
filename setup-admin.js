const sqlite3 = require("sqlite3").verbose();
const bcrypt = require("bcrypt");
const path = require("path");
const fs = require("fs");

console.log("🔧 Настройка системы авторизации SHMS Auto");
console.log("==========================================\n");

// Путь к базе данных
const dbPath = path.join(__dirname, "data/stock.db");

// Проверяем существование директории data
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
  console.log("📁 Создание директории data...");
  fs.mkdirSync(dataDir, { recursive: true });
}

// Подключаемся к базе данных
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error("❌ Ошибка подключения к базе данных:", err.message);
    process.exit(1);
  }
  console.log("✅ Подключение к базе данных установлено");
});

// SQL запросы для создания таблиц
const createAdminsTable = `
CREATE TABLE IF NOT EXISTS admins (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME,
    reset_token VARCHAR(255),
    reset_token_expires DATETIME
);
`;

const createSecurityLogsTable = `
CREATE TABLE IF NOT EXISTS security_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    admin_id INTEGER,
    action VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    details TEXT,
    success BOOLEAN,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(id)
);
`;

const createLoginAttemptsTable = `
CREATE TABLE IF NOT EXISTS login_attempts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ip_address VARCHAR(45) NOT NULL,
    username VARCHAR(50),
    success BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
`;

// Функция для выполнения SQL запроса
function runQuery(sql, description) {
  return new Promise((resolve, reject) => {
    db.run(sql, (err) => {
      if (err) {
        console.error(`❌ Ошибка при ${description}:`, err.message);
        reject(err);
      } else {
        console.log(`✅ ${description} выполнено успешно`);
        resolve();
      }
    });
  });
}

// Функция создания администратора
function createAdmin(adminData) {
  return new Promise((resolve, reject) => {
    // Хешируем пароль
    bcrypt.hash(adminData.password, 12, (err, hash) => {
      if (err) {
        return reject(new Error("Ошибка хеширования пароля"));
      }

      const sql = `
                INSERT INTO admins (username, email, password_hash, full_name)
                VALUES (?, ?, ?, ?)
            `;

      db.run(
        sql,
        [adminData.username, adminData.email, hash, adminData.fullName],
        function (err) {
          if (err) {
            if (err.code === "SQLITE_CONSTRAINT_UNIQUE") {
              return reject(
                new Error(
                  "Пользователь с таким логином или email уже существует"
                )
              );
            }
            return reject(
              new Error("Ошибка создания администратора: " + err.message)
            );
          }

          resolve({
            id: this.lastID,
            username: adminData.username,
            email: adminData.email,
            fullName: adminData.fullName,
            isActive: true,
            createdAt: new Date().toISOString(),
          });
        }
      );
    });
  });
}

// Основная функция настройки
async function setupAuth() {
  try {
    console.log("\n📋 Создание таблиц...");

    // Создаем таблицы
    await runQuery(createAdminsTable, "Создание таблицы admins");
    await runQuery(createSecurityLogsTable, "Создание таблицы security_logs");
    await runQuery(createLoginAttemptsTable, "Создание таблицы login_attempts");

    console.log("\n👤 Создание администратора...");

    // Данные администратора
    const adminData = {
      username: "admin",
      email: "<EMAIL>",
      password: "SHMSAdmin2024!",
      fullName: "Администратор SHMS Auto",
    };

    console.log(`Логин: ${adminData.username}`);
    console.log(`Email: ${adminData.email}`);
    console.log(`Полное имя: ${adminData.fullName}`);
    console.log(`Пароль: ${"*".repeat(adminData.password.length)}`);

    // Создаем администратора
    const newAdmin = await createAdmin(adminData);

    console.log("\n✅ Администратор успешно создан!");
    console.log(`ID: ${newAdmin.id}`);
    console.log(`Логин: ${newAdmin.username}`);
    console.log(`Email: ${newAdmin.email}`);
    console.log(`Создан: ${newAdmin.createdAt}`);

    console.log("\n🎉 Настройка завершена!");
    console.log("📋 Что дальше:");
    console.log("1. Запустите сервер: npm run local");
    console.log("2. Откройте админку: http://localhost:3000/admin");
    console.log("3. Войдите с созданными учетными данными");
    console.log("\n⚠️  ВАЖНО: Обязательно смените пароль после первого входа!");
  } catch (error) {
    console.error("❌ Ошибка при настройке:", error.message);
    process.exit(1);
  } finally {
    // Закрываем соединение с базой данных
    db.close((err) => {
      if (err) {
        console.error("❌ Ошибка при закрытии базы данных:", err.message);
      } else {
        console.log("🔒 Соединение с базой данных закрыто");
      }
      process.exit(0);
    });
  }
}

// Запускаем настройку
setupAuth();
