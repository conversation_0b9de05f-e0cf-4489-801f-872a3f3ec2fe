const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Путь к базе данных
const dbPath = path.join(__dirname, 'admin/js/stock.db');
const db = new sqlite3.Database(dbPath);

console.log('Исправляем ограничение NOT NULL для best_features...');

// SQLite не поддерживает ALTER COLUMN, поэтому нужно пересоздать таблицу
db.serialize(() => {
  // Создаем временную таблицу с правильной схемой
  db.run(`
    CREATE TABLE car_details_temp (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      car_id INTEGER NOT NULL,
      full_title TEXT NOT NULL,
      price TEXT NOT NULL,
      moscow_price TEXT NOT NULL,
      description TEXT NOT NULL,
      specs TEXT NOT NULL,
      best_features TEXT,
      similar_cars TEXT,
      images TEXT NOT NULL,
      html_page TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      status TEXT DEFAULT 'draft',
      main_features TEXT,
      additional_features TEXT,
      mileage TEXT,
      body_type TEXT,
      FOREIGN KEY (car_id) REFERENCES stock_cars (id)
    )
  `, (err) => {
    if (err) {
      console.error('Ошибка при создании временной таблицы:', err);
      return;
    }
    console.log('Временная таблица создана');

    // Копируем данные из старой таблицы в новую
    db.run(`
      INSERT INTO car_details_temp 
      SELECT id, car_id, full_title, price, moscow_price, description, specs, 
             best_features, similar_cars, images, html_page, created_at, status,
             main_features, additional_features, mileage, body_type
      FROM car_details
    `, (err) => {
      if (err) {
        console.error('Ошибка при копировании данных:', err);
        return;
      }
      console.log('Данные скопированы');

      // Удаляем старую таблицу
      db.run('DROP TABLE car_details', (err) => {
        if (err) {
          console.error('Ошибка при удалении старой таблицы:', err);
          return;
        }
        console.log('Старая таблица удалена');

        // Переименовываем временную таблицу
        db.run('ALTER TABLE car_details_temp RENAME TO car_details', (err) => {
          if (err) {
            console.error('Ошибка при переименовании таблицы:', err);
            return;
          }
          console.log('Таблица переименована');
          console.log('✅ Ограничение NOT NULL для best_features успешно убрано!');
          
          db.close();
        });
      });
    });
  });
});
