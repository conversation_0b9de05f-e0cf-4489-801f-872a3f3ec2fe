# 🚀 Развертывание SHMS Auto на продакшн сервере Рег.ру

## 📚 Документация по развертыванию

Этот репозиторий содержит полную документацию и инструменты для развертывания приложения SHMS Auto на продакшн сервере в облачной консоли Рег.ру.

## 📋 Доступные руководства

### 🎯 Быстрое развертывание (30 минут)
**Файл:** `QUICK-DEPLOY.md`
- Экспресс-инструкция для быстрого развертывания
- Пошаговые команды для копирования
- Минимальная настройка для запуска

### 📖 Полное руководство по развертыванию
**Файл:** `PRODUCTION_DEPLOY_GUIDE.md`
- Детальное руководство с объяснениями
- Все этапы развертывания
- Настройка безопасности и мониторинга
- Устранение неполадок

### ✅ Чек-лист развертывания
**Файл:** `DEPLOYMENT_CHECKLIST.md`
- Пошаговый чек-лист для контроля
- Проверка каждого этапа
- Финальная проверка работоспособности

## 🛠️ Инструменты и скрипты

### Скрипты автоматизации
- `deploy-regru.sh` - Автоматический скрипт развертывания
- `scripts/backup.sh` - Резервное копирование
- `scripts/health-check.js` - Проверка здоровья системы
- `restart-services-windows.ps1` - Перезапуск сервисов (Windows)
- `server/utils/restart-services.sh` - Перезапуск сервисов (Linux)

### Конфигурационные файлы
- `.env.example` - Шаблон конфигурации для продакшена
- `server/utils/nginx-config.conf` - Конфигурация Nginx
- `ecosystem.config.js` - Конфигурация PM2

## 🎯 Выберите подходящий вариант

### Для новичков или быстрого запуска
👉 **Используйте `QUICK-DEPLOY.md`**
- Простые команды для копирования
- Минимум настроек
- Быстрый результат

### Для продакшн развертывания
👉 **Используйте `PRODUCTION_DEPLOY_GUIDE.md`**
- Полная настройка безопасности
- Мониторинг и резервное копирование
- Подробные объяснения

### Для контроля процесса
👉 **Используйте `DEPLOYMENT_CHECKLIST.md`**
- Пошаговая проверка
- Ничего не забудете
- Документирование процесса

## 📋 Требования к серверу

### Минимальные требования
- **ОС:** Ubuntu 20.04 LTS или новее
- **RAM:** 2GB (рекомендуется 4GB)
- **Диск:** 20GB SSD
- **CPU:** 1-2 ядра

### Рекомендуемые требования
- **ОС:** Ubuntu 22.04 LTS
- **RAM:** 4GB
- **Диск:** 40GB SSD
- **CPU:** 2 ядра

## 🔧 Что будет установлено

### Системные компоненты
- Node.js 18.x
- PM2 (менеджер процессов)
- Nginx (веб-сервер и reverse proxy)
- Certbot (SSL сертификаты)

### Приложение SHMS Auto
- Веб-сервер на Node.js
- База данных SQLite
- Система загрузки файлов
- Почтовые уведомления
- Админ-панель

## 🌐 Настройка домена

### В панели Рег.ру
1. Перейдите в "Домены" → "Управление DNS"
2. Добавьте A-записи:
   - `@` → IP-адрес сервера
   - `www` → IP-адрес сервера
3. Сохраните изменения

### Время распространения
DNS изменения могут занять от 15 минут до 24 часов.

## 🔒 Безопасность

### Автоматически настраивается
- SSL сертификат от Let's Encrypt
- HTTPS редирект
- Защита от скрытых файлов
- Ограничение размера загружаемых файлов

### Рекомендуется дополнительно
- Настройка файрвола (ufw)
- Регулярные обновления системы
- Мониторинг логов

## 📊 Мониторинг и обслуживание

### Автоматически настраивается
- PM2 для управления процессами
- Логирование приложения
- Автозапуск при перезагрузке сервера

### Полезные команды
```bash
# Статус приложения
pm2 list

# Логи приложения
pm2 logs shms-auto

# Перезапуск приложения
pm2 restart shms-auto

# Проверка здоровья
node scripts/health-check.js

# Резервное копирование
./scripts/backup.sh
```

## 🆘 Поддержка

### Если что-то пошло не так
1. Проверьте логи: `pm2 logs shms-auto`
2. Проверьте статус: `pm2 list`
3. Проверьте Nginx: `sudo systemctl status nginx`
4. Запустите проверку здоровья: `node scripts/health-check.js`

### Контакты
- **Email:** <EMAIL>
- **Документация:** см. файлы в этом репозитории

## 📁 Структура файлов после развертывания

```
/home/<USER>/shms-auto/
├── server.js                 # Основной файл сервера
├── package.json              # Зависимости
├── ecosystem.config.js       # Конфигурация PM2
├── .env                      # Конфигурация продакшена
├── public/                   # Статические файлы
├── admin/                    # Админ-панель
├── data/
│   ├── stock.db             # База данных
│   └── uploads/             # Загруженные файлы
├── logs/                    # Логи приложения
└── scripts/                 # Служебные скрипты
```

## 🎉 После успешного развертывания

Ваш сайт будет доступен по адресу:
- **Главная страница:** https://ваш-домен.ru
- **Админ-панель:** https://ваш-домен.ru/admin
- **Каталог автомобилей:** https://ваш-домен.ru/stock

### Функции, которые будут работать
- ✅ Просмотр каталога автомобилей
- ✅ Отправка заявок через формы
- ✅ Получение email уведомлений
- ✅ Загрузка изображений в админ-панели
- ✅ Управление контентом
- ✅ Автоматическое резервное копирование
- ✅ SSL шифрование

---

## 🚀 Начать развертывание

1. **Быстрый старт:** откройте `QUICK-DEPLOY.md`
2. **Подробное руководство:** откройте `PRODUCTION_DEPLOY_GUIDE.md`
3. **С контролем:** используйте `DEPLOYMENT_CHECKLIST.md`

**Удачного развертывания! 🎯**
