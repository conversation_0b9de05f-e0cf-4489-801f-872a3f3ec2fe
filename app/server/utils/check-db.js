const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'admin-stock', 'stock.db');
console.log('Checking database at:', dbPath);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Error opening database:', err);
    process.exit(1);
  }
  console.log('Connected to database');
});

// Check tables
db.all("SELECT name FROM sqlite_master WHERE type='table'", [], (err, tables) => {
  if (err) {
    console.error('Error listing tables:', err);
    process.exit(1);
  }
  
  console.log('Tables in database:');
  tables.forEach(table => {
    console.log(`- ${table.name}`);
  });
  
  // Specifically check for car_details and page_html tables
  const hasTables = {
    car_details: tables.some(t => t.name === 'car_details'),
    page_html: tables.some(t => t.name === 'page_html')
  };
  
  console.log('\nRequired tables:');
  console.log('- car_details:', hasTables.car_details ? 'FOUND' : 'MISSING');
  console.log('- page_html:', hasTables.page_html ? 'FOUND' : 'MISSING');
  
  // If car_details exists, check some records
  if (hasTables.car_details) {
    db.all("SELECT id, status, html_page FROM car_details LIMIT 5", [], (err, rows) => {
      if (err) {
        console.error('Error checking car_details table:', err);
      } else {
        console.log('\nSample car_details records:');
        console.log(rows);
        
        // Close the database connection
        db.close();
      }
    });
  } else {
    db.close();
  }
}); 