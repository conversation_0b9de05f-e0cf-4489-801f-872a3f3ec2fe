const db = require('./database.js');

// Получить все автомобили
db.all('SELECT * FROM stock_cars', [], (err, cars) => {
    if (err) {
        console.error('Ошибка при получении автомобилей:', err);
    } else {
        console.log('=== Автомобили в базе данных ===');
        console.log(cars);
    }
});

// Получить избранные автомобили
db.all(`
    SELECT f.id as featured_id, f.display_order, c.* 
    FROM featured_cars f 
    JOIN stock_cars c ON f.car_id = c.id 
    ORDER BY f.display_order ASC
`, [], (err, featured) => {
    if (err) {
        console.error('Ошибка при получении избранных автомобилей:', err);
    } else {
        console.log('\n=== Избранные автомобили ===');
        console.log(featured);
    }
}); 