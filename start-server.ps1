# Простой HTTP сервер на PowerShell
$port = 8000
$publicPath = Join-Path $PSScriptRoot "public"

Write-Host "Запуск локального сервера на порту $port"
Write-Host "Папка: $publicPath"
Write-Host "URL: http://localhost:$port"

# Проверяем существование папки public
if (-not (Test-Path $publicPath)) {
    Write-Host "Ошибка: папка public не найдена!" -ForegroundColor Red
    exit 1
}

# Создаем HTTP listener
$listener = New-Object System.Net.HttpListener
$listener.Prefixes.Add("http://localhost:$port/")

try {
    $listener.Start()
    Write-Host "Сервер запущен! Нажмите Ctrl+C для остановки" -ForegroundColor Green
    
    while ($listener.IsListening) {
        $context = $listener.GetContext()
        $request = $context.Request
        $response = $context.Response
        
        # Получаем путь к файлу
        $urlPath = $request.Url.AbsolutePath
        if ($urlPath -eq "/") {
            $urlPath = "/index.html"
        }
        
        $filePath = Join-Path $publicPath $urlPath.TrimStart('/')
        
        Write-Host "Запрос: $($request.HttpMethod) $urlPath"
        
        if (Test-Path $filePath -PathType Leaf) {
            # Определяем MIME тип
            $extension = [System.IO.Path]::GetExtension($filePath).ToLower()
            $contentType = switch ($extension) {
                ".html" { "text/html; charset=utf-8" }
                ".css" { "text/css" }
                ".js" { "application/javascript" }
                ".png" { "image/png" }
                ".jpg" { "image/jpeg" }
                ".jpeg" { "image/jpeg" }
                ".gif" { "image/gif" }
                ".ico" { "image/x-icon" }
                default { "application/octet-stream" }
            }
            
            $response.ContentType = $contentType
            $response.StatusCode = 200
            
            # Читаем и отправляем файл
            $fileBytes = [System.IO.File]::ReadAllBytes($filePath)
            $response.ContentLength64 = $fileBytes.Length
            $response.OutputStream.Write($fileBytes, 0, $fileBytes.Length)
        } else {
            # 404 - файл не найден
            $response.StatusCode = 404
            $response.ContentType = "text/html; charset=utf-8"
            $errorHtml = @"
<!DOCTYPE html>
<html>
<head><title>404 - Файл не найден</title></head>
<body>
<h1>404 - Файл не найден</h1>
<p>Файл $urlPath не найден</p>
</body>
</html>
"@
            $errorBytes = [System.Text.Encoding]::UTF8.GetBytes($errorHtml)
            $response.ContentLength64 = $errorBytes.Length
            $response.OutputStream.Write($errorBytes, 0, $errorBytes.Length)
        }
        
        $response.OutputStream.Close()
    }
} catch {
    Write-Host "Ошибка: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    if ($listener.IsListening) {
        $listener.Stop()
    }
    Write-Host "Сервер остановлен"
}
