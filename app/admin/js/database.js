const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Создаем подключение к базе данных
const db = new sqlite3.Database(path.join(__dirname, 'stock.db'), (err) => {
    if (err) {
        console.error('Ошибка при подключении к базе данных:', err);
    } else {
        console.log('Подключение к базе данных stock установлено');
        initDatabase();
    }
});

// Инициализация базы данных
function initDatabase() {
    db.serialize(() => {
        // Создаем таблицу
        db.run(`CREATE TABLE IF NOT EXISTS stock_cars (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            subtitle TEXT NOT NULL,
            image_path TEXT NOT NULL,
            power TEXT NOT NULL,
            mileage TEXT NOT NULL,
            body_type TEXT NOT NULL,
            engine TEXT NOT NULL,
            transmission TEXT NOT NULL,
            interior_colors TEXT NOT NULL,
            consumption TEXT NOT NULL,
            capacity TEXT NOT NULL,
            price TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`, (err) => {
            if (err) {
                console.error('Ошибка при создании таблицы stock_cars:', err);
            } else {
                console.log('Таблица stock_cars готова');
                
                // Проверяем, есть ли уже записи
                db.get('SELECT COUNT(*) as count FROM stock_cars', [], (err, row) => {
                    if (err) {
                        console.error('Ошибка при проверке количества записей:', err);
                    } else if (row.count === 0) {
                        // Добавляем тестовую карточку
                        const testCar = {
                            title: '2024 Audi RS6 Avante perfomance',
                            subtitle: 'Audi • Германия • Sebring Black Crystal Effect',
                            image_path: '/uploads/Audi_1.png',
                            power: '585 л.с.',
                            mileage: '78 км',
                            body_type: 'Avant',
                            engine: '4л. / V8 twin-turbo',
                            transmission: '8-ступенчатый автомат',
                            interior_colors: 'black,orange',
                            consumption: '100км 12л',
                            capacity: '5 мест',
                            price: '311328'
                        };

                        db.run(
                            `INSERT INTO stock_cars (title, subtitle, image_path, power, mileage, body_type, engine, transmission, interior_colors, consumption, capacity, price)
                             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                            [
                                testCar.title, testCar.subtitle, testCar.image_path, testCar.power,
                                testCar.mileage, testCar.body_type, testCar.engine, testCar.transmission,
                                testCar.interior_colors, testCar.consumption, testCar.capacity, testCar.price
                            ],
                            function(err) {
                                if (err) {
                                    console.error('Ошибка при добавлении тестовой карточки:', err);
                                } else {
                                    console.log('Тестовая карточка успешно добавлена');
                                }
                            }
                        );
                    } else {
                        console.log('В базе данных уже есть записи');
                    }
                });
            }
        });

        // Создаем таблицу для отображения автомобилей на главной странице
        db.run(`CREATE TABLE IF NOT EXISTS featured_cars (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            car_id INTEGER NOT NULL,
            display_order INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (car_id) REFERENCES stock_cars(id) ON DELETE CASCADE
        )`, (err) => {
            if (err) {
                console.error('Ошибка при создании таблицы featured_cars:', err);
            } else {
                console.log('Таблица featured_cars готова');
            }
        });

        // Добавляем индекс для быстрого поиска
        db.run(`CREATE INDEX IF NOT EXISTS idx_featured_car_id ON featured_cars(car_id)`, (err) => {
            if (err) {
                console.error('Ошибка при создании индекса:', err);
            }
        });

        // Создаем таблицу для детальной информации об автомобилях
        db.run(`CREATE TABLE IF NOT EXISTS car_details (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            car_id INTEGER NOT NULL,
            full_title TEXT NOT NULL,
            price TEXT NOT NULL,
            moscow_price TEXT NOT NULL,
            description TEXT NOT NULL,
            specs TEXT NOT NULL,
            best_features TEXT NOT NULL,
            similar_cars TEXT,
            images TEXT NOT NULL,
            html_page TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (car_id) REFERENCES stock_cars(id) ON DELETE CASCADE
        )`, (err) => {
            if (err) {
                console.error('Ошибка при создании таблицы car_details:', err);
            } else {
                console.log('Таблица car_details готова');
                
                // Проверяем, существует ли столбец html_page
                db.all("PRAGMA table_info(car_details)", [], (err, rows) => {
                    if (err) {
                        console.error('Ошибка при проверке столбцов таблицы car_details:', err);
                        return;
                    }
                    
                    // Если столбца нет, добавляем его
                    const hasHtmlPageColumn = rows.some(row => row.name === 'html_page');
                    if (!hasHtmlPageColumn) {
                        db.run(`ALTER TABLE car_details ADD COLUMN html_page TEXT`, (err) => {
                            if (err) {
                                console.error('Ошибка при добавлении столбца html_page:', err);
                            } else {
                                console.log('Добавлен столбец html_page в таблицу car_details');
                            }
                        });
                    }
                    
                    // Проверяем наличие столбца status
                    const hasStatusColumn = rows.some(row => row.name === 'status');
                    if (!hasStatusColumn) {
                        db.run(`ALTER TABLE car_details ADD COLUMN status TEXT DEFAULT 'draft'`, (err) => {
                            if (err) {
                                console.error('Ошибка при добавлении столбца status:', err);
                            } else {
                                console.log('Добавлен столбец status в таблицу car_details');
                            }
                        });
                    }
                });
            }
        });

        // Добавляем индекс для быстрого поиска
        db.run(`CREATE INDEX IF NOT EXISTS idx_car_details_car_id ON car_details(car_id)`, (err) => {
            if (err) {
                console.error('Ошибка при создании индекса car_details:', err);
            }
        });
        
        // Создаем таблицу для хранения информации о сгенерированных HTML-страницах
        db.run(`CREATE TABLE IF NOT EXISTS page_html (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            detail_id INTEGER NOT NULL,
            file_path TEXT NOT NULL,
            generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            status TEXT DEFAULT 'active',
            FOREIGN KEY (detail_id) REFERENCES car_details(id) ON DELETE CASCADE
        )`, (err) => {
            if (err) {
                console.error('Ошибка при создании таблицы page_html:', err);
            } else {
                console.log('Таблица page_html готова');
            }
        });
        
        // Добавляем индекс для быстрого поиска
        db.run(`CREATE INDEX IF NOT EXISTS idx_page_html_detail_id ON page_html(detail_id)`, (err) => {
            if (err) {
                console.error('Ошибка при создании индекса page_html:', err);
            }
        });
    });
}

module.exports = db; 