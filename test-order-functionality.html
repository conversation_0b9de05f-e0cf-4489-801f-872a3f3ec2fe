<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест функциональности страницы заказа</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Тест функциональности страницы "Авто под заказ"</h1>
    
    <div class="test-section">
        <h2>Тест 1: Первоначальная загрузка (должна показывать авто от $15,000)</h2>
        <button onclick="testInitialLoad()">Тестировать первоначальную загрузку</button>
        <div id="test1-result"></div>
    </div>

    <div class="test-section">
        <h2>Тест 2: Категория "Бизнес-класс" (должна показывать авто от $50,000)</h2>
        <button onclick="testBusinessCategory()">Тестировать бизнес-класс</button>
        <div id="test2-result"></div>
    </div>

    <div class="test-section">
        <h2>Тест 3: Категория "Спортивные" (должна показывать авто от $30,000)</h2>
        <button onclick="testSportCategory()">Тестировать спортивные</button>
        <div id="test3-result"></div>
    </div>

    <div class="test-section">
        <h2>Тест 4: Категория "SUV" (должна показывать авто от $25,000)</h2>
        <button onclick="testSUVCategory()">Тестировать SUV</button>
        <div id="test4-result"></div>
    </div>

    <div class="test-section">
        <h2>Тест 5: Поиск (должен показывать все авто без ограничений)</h2>
        <button onclick="testSearch()">Тестировать поиск</button>
        <div id="test5-result"></div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:3000';

        async function testAPICall(params, expectedMinPrice, testName) {
            try {
                const url = new URL(`${BASE_URL}/api/encar`);
                Object.keys(params).forEach(key => {
                    if (params[key] !== null && params[key] !== undefined) {
                        url.searchParams.append(key, params[key]);
                    }
                });

                console.log(`${testName} - URL:`, url.toString());
                
                const response = await fetch(url);
                const data = await response.json();
                
                console.log(`${testName} - Response:`, data);
                
                if (data && data.length > 0) {
                    const hasCorrectPricing = data.every(car => {
                        const price = parseFloat(car.price) || 0;
                        return price >= expectedMinPrice;
                    });
                    
                    return {
                        success: true,
                        message: `Получено ${data.length} автомобилей. Минимальная цена соблюдена: ${hasCorrectPricing ? 'Да' : 'Нет'}`,
                        data: data.slice(0, 3) // Показываем первые 3 для примера
                    };
                } else {
                    return {
                        success: false,
                        message: 'Нет данных в ответе',
                        data: null
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    message: `Ошибка: ${error.message}`,
                    data: null
                };
            }
        }

        async function testInitialLoad() {
            const result = await testAPICall({
                fileType: 'active_offer',
                date: new Date().toISOString().split('T')[0],
                limit: 12,
                offset: 0,
                price_from: 15000
            }, 15000, 'Первоначальная загрузка');
            
            displayResult('test1-result', result);
        }

        async function testBusinessCategory() {
            const result = await testAPICall({
                fileType: 'active_offer',
                date: new Date().toISOString().split('T')[0],
                limit: 12,
                offset: 0,
                category: 'business',
                price_from: 50000
            }, 50000, 'Бизнес-класс');
            
            displayResult('test2-result', result);
        }

        async function testSportCategory() {
            const result = await testAPICall({
                fileType: 'active_offer',
                date: new Date().toISOString().split('T')[0],
                limit: 12,
                offset: 0,
                category: 'sport',
                price_from: 30000
            }, 30000, 'Спортивные');
            
            displayResult('test3-result', result);
        }

        async function testSUVCategory() {
            const result = await testAPICall({
                fileType: 'active_offer',
                date: new Date().toISOString().split('T')[0],
                limit: 12,
                offset: 0,
                category: 'suv',
                price_from: 25000
            }, 25000, 'SUV');
            
            displayResult('test4-result', result);
        }

        async function testSearch() {
            const result = await testAPICall({
                fileType: 'active_offer',
                date: new Date().toISOString().split('T')[0],
                limit: 12,
                offset: 0,
                brand: 'BMW'
            }, 0, 'Поиск BMW');
            
            displayResult('test5-result', result);
        }

        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            const className = result.success ? 'success' : 'error';
            
            let html = `<div class="test-result ${className}">
                <strong>Результат:</strong> ${result.message}
            </div>`;
            
            if (result.data && result.data.length > 0) {
                html += '<div class="test-result info"><strong>Примеры автомобилей:</strong><ul>';
                result.data.forEach(car => {
                    html += `<li>${car.brand || 'N/A'} ${car.model || 'N/A'} - $${car.price || 'N/A'}</li>`;
                });
                html += '</ul></div>';
            }
            
            element.innerHTML = html;
        }
    </script>
</body>
</html>
