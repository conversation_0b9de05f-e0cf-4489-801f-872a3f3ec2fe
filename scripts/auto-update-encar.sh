#!/bin/bash
# Автоматическое обновление данных Encar и перезапуск сайта SHMS Auto
# Версия: 2.0 для продакшена
# Дата: 2025-01-16

# ===============================================================================
# КОНФИГУРАЦИЯ
# ===============================================================================

# Основные пути
SITE_ROOT="/var/www/shms-auto"
LOG_DIR="$SITE_ROOT/logs"
LOG_FILE="$LOG_DIR/encar-update.log"
ERROR_LOG="$LOG_DIR/encar-error.log"
BACKUP_DIR="$SITE_ROOT/backups/csv"
DATA_DIR="$SITE_ROOT/public/api/encar_data"

# Настройки PM2
PM2_APP_NAME="shms-auto"

# Настройки уведомлений
ADMIN_EMAIL="<EMAIL>"
WEBHOOK_URL=""  # Опционально: URL для webhook уведомлений

# Настройки резервного копирования
KEEP_BACKUPS=7  # Количество дней для хранения бэкапов

# ===============================================================================
# ФУНКЦИИ УТИЛИТЫ
# ===============================================================================

# Функция логирования
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

# Функция для отправки уведомлений
send_notification() {
    local subject="$1"
    local message="$2"
    local status="$3"  # success, warning, error
    
    # Логируем уведомление
    log "NOTIFY" "$subject: $message"
    
    # Отправляем email (если настроен)
    if [ ! -z "$ADMIN_EMAIL" ] && command -v mail >/dev/null 2>&1; then
        echo "$message" | mail -s "SHMS Auto: $subject" "$ADMIN_EMAIL"
    fi
    
    # Отправляем webhook (если настроен)
    if [ ! -z "$WEBHOOK_URL" ] && command -v curl >/dev/null 2>&1; then
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"text\":\"SHMS Auto: $subject\n$message\",\"status\":\"$status\"}" \
            >/dev/null 2>&1
    fi
}

# Функция создания резервной копии
create_backup() {
    log "INFO" "Создание резервной копии CSV файлов..."
    
    # Создаем директорию для бэкапов
    mkdir -p "$BACKUP_DIR"
    
    # Создаем архив с текущими данными
    local backup_name="encar_backup_$(date '+%Y%m%d_%H%M%S').tar.gz"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    if [ -d "$DATA_DIR" ] && [ "$(ls -A $DATA_DIR 2>/dev/null)" ]; then
        tar -czf "$backup_path" -C "$DATA_DIR" . 2>/dev/null
        if [ $? -eq 0 ]; then
            log "INFO" "Резервная копия создана: $backup_name"
            
            # Удаляем старые бэкапы
            find "$BACKUP_DIR" -name "encar_backup_*.tar.gz" -mtime +$KEEP_BACKUPS -delete 2>/dev/null
            log "INFO" "Старые бэкапы очищены (старше $KEEP_BACKUPS дней)"
        else
            log "ERROR" "Ошибка создания резервной копии"
            return 1
        fi
    else
        log "WARNING" "Нет данных для резервного копирования"
    fi
    
    return 0
}

# Функция проверки работоспособности сайта
check_site_health() {
    local max_attempts=5
    local attempt=1
    
    log "INFO" "Проверка работоспособности сайта..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s --max-time 10 "http://localhost:3000/test" >/dev/null 2>&1; then
            log "INFO" "Сайт отвечает корректно (попытка $attempt)"
            return 0
        fi
        
        log "WARNING" "Сайт не отвечает (попытка $attempt из $max_attempts)"
        sleep 5
        ((attempt++))
    done
    
    log "ERROR" "Сайт не отвечает после $max_attempts попыток"
    return 1
}

# Функция проверки API
check_api_health() {
    log "INFO" "Проверка API endpoints..."
    
    # Проверяем основной API
    if curl -f -s --max-time 15 "http://localhost:3000/api/encar?date=$(date '+%Y-%m-%d')&limit=1" >/dev/null 2>&1; then
        log "INFO" "API encar работает корректно"
    else
        log "WARNING" "API encar не отвечает или возвращает ошибку"
    fi
    
    # Проверяем API курсов валют
    if curl -f -s --max-time 10 "http://localhost:3000/api/exchange-rates.php" >/dev/null 2>&1; then
        log "INFO" "API курсов валют работает корректно"
    else
        log "WARNING" "API курсов валют не отвечает"
    fi
}

# ===============================================================================
# ОСНОВНАЯ ЛОГИКА
# ===============================================================================

main() {
    # Заголовок
    echo "🚀 АВТОМАТИЧЕСКОЕ ОБНОВЛЕНИЕ ENCAR API"
    echo "======================================"
    log "INFO" "Начало процесса автоматического обновления"
    log "INFO" "Версия скрипта: 2.0"
    log "INFO" "Сайт: $SITE_ROOT"
    
    # Проверяем, что мы в правильной директории
    if [ ! -d "$SITE_ROOT" ]; then
        log "ERROR" "Директория сайта не найдена: $SITE_ROOT"
        send_notification "Ошибка обновления" "Директория сайта не найдена" "error"
        exit 1
    fi
    
    # Переходим в директорию сайта
    cd "$SITE_ROOT" || {
        log "ERROR" "Не удалось перейти в директорию: $SITE_ROOT"
        exit 1
    }
    
    # Создаем необходимые директории
    mkdir -p "$LOG_DIR" "$BACKUP_DIR"
    
    # Создаем резервную копию
    if ! create_backup; then
        log "WARNING" "Не удалось создать резервную копию, но продолжаем..."
    fi
    
    # Обновление данных Encar
    log "INFO" "Запуск обновления данных Encar..."
    echo "📡 Запуск обновления данных Encar..."
    
    # Запускаем PHP скрипт обновления
    php public/api/auto-setup.php update >> "$LOG_FILE" 2>> "$ERROR_LOG"
    local update_status=$?
    
    if [ $update_status -eq 0 ]; then
        log "INFO" "Обновление данных Encar завершено успешно"
        echo "✅ Обновление данных Encar завершено успешно"
        
        # Перезапуск сайта
        log "INFO" "Перезапуск сайта..."
        echo "🔄 Перезапуск сайта..."
        
        # Проверяем, запущен ли PM2
        if command -v pm2 >/dev/null 2>&1; then
            # Мягкий перезапуск PM2
            pm2 reload "$PM2_APP_NAME" >> "$LOG_FILE" 2>> "$ERROR_LOG"
            local pm2_status=$?
            
            if [ $pm2_status -eq 0 ]; then
                log "INFO" "Сайт успешно перезапущен через PM2"
                echo "✅ Сайт успешно перезапущен"
                
                # Ждем немного для стабилизации
                sleep 10
                
                # Проверяем работоспособность
                if check_site_health; then
                    log "INFO" "Проверка работоспособности: OK"
                    echo "✅ Сайт работает корректно"
                    
                    # Проверяем API
                    check_api_health
                    
                    # Отправляем уведомление об успехе
                    local stats=$(tail -10 "$LOG_FILE" | grep -E "(Размер файла|Обработано строк|Время выполнения)" | tr '\n' '; ')
                    send_notification "Обновление успешно" "Данные Encar обновлены и сайт перезапущен. $stats" "success"
                    
                    log "INFO" "Процесс завершен успешно"
                    echo "🎉 Процесс завершен успешно"
                    
                else
                    log "ERROR" "Сайт не отвечает после перезапуска"
                    echo "❌ Ошибка: сайт не отвечает после перезапуска"
                    send_notification "Ошибка после перезапуска" "Сайт не отвечает после обновления данных" "error"
                    exit 1
                fi
            else
                log "ERROR" "Ошибка перезапуска PM2"
                echo "❌ Ошибка перезапуска PM2"
                send_notification "Ошибка перезапуска" "Не удалось перезапустить сайт через PM2" "error"
                exit 1
            fi
        else
            log "WARNING" "PM2 не найден, пропускаем перезапуск"
            echo "⚠️ PM2 не найден, пропускаем перезапуск"
        fi
        
    else
        log "ERROR" "Ошибка обновления данных Encar (код: $update_status)"
        echo "❌ Ошибка обновления данных Encar"
        
        # Читаем последние ошибки
        local error_details=""
        if [ -f "$ERROR_LOG" ]; then
            error_details=$(tail -5 "$ERROR_LOG" | tr '\n' '; ')
        fi
        
        send_notification "Ошибка обновления данных" "Не удалось обновить данные Encar. $error_details" "error"
        exit 1
    fi
    
    log "INFO" "Автоматическое обновление завершено"
    echo "🎉 Автоматическое обновление завершено"
}

# ===============================================================================
# ОБРАБОТКА ОШИБОК
# ===============================================================================

# Обработчик ошибок
error_handler() {
    local exit_code=$?
    log "ERROR" "Скрипт завершился с ошибкой (код: $exit_code)"
    send_notification "Критическая ошибка" "Скрипт обновления завершился аварийно" "error"
    exit $exit_code
}

# Обработчик сигналов
signal_handler() {
    log "WARNING" "Получен сигнал прерывания, завершаем работу..."
    send_notification "Прерывание обновления" "Процесс обновления был прерван" "warning"
    exit 130
}

# Устанавливаем обработчики
trap error_handler ERR
trap signal_handler INT TERM

# ===============================================================================
# ЗАПУСК
# ===============================================================================

# Проверяем права доступа
if [ ! -w "$SITE_ROOT" ]; then
    echo "❌ Ошибка: нет прав записи в директорию $SITE_ROOT"
    exit 1
fi

# Запускаем основную функцию
main "$@"
