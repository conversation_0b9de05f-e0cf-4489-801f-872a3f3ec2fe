import requests
import sys

def download_csv_chunk(url, chunk_size=50000):
    try:
        headers = {'Range': f'bytes=0-{chunk_size}'}
        response = requests.get(url, headers=headers, verify=False)
        
        if response.status_code in [200, 206]:
            with open('first50k.csv', 'wb') as f:
                f.write(response.content)
            print(f"Successfully downloaded first {len(response.content)} bytes to first50k.csv")
            return True
        else:
            print(f"Error: Server returned status code {response.status_code}")
            return False
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    url = "https://autobase-wade.auto-parser.ru/encar/2024-06-06/active_offer.csv"
    download_csv_chunk(url) 