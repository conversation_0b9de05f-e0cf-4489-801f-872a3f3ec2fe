# Настройка системы авторизации для админки SHMS Auto

## Обзор

Система авторизации обеспечивает защищенный доступ к административной панели SHMS Auto. Включает в себя:

- ✅ Сессионная авторизация с express-session
- ✅ Хеширование паролей с bcrypt
- ✅ Защита от брутфорса (rate limiting)
- ✅ Восстановление паролей через email
- ✅ Логирование всех действий безопасности
- ✅ CSRF защита
- ✅ Валидация паролей по требованиям безопасности
- ✅ Управление несколькими администраторами

## Быстрая настройка

### 1. Установка зависимостей

```bash
npm install
```

### 2. Автоматическая настройка

```bash
npm run setup-auth
```

Этот скрипт выполнит:
- Создание таблиц в базе данных
- Интерактивное создание первого администратора

### 3. Запуск сервера

```bash
npm start
```

### 4. Вход в админку

Откройте: `http://localhost:3000/admin`

Система автоматически перенаправит на страницу входа: `http://localhost:3000/admin/login.html`

## Ручная настройка

### 1. Настройка базы данных

```bash
npm run setup-auth-db
```

Создает таблицы:
- `admins` - администраторы
- `security_logs` - логи безопасности  
- `login_attempts` - попытки входа

### 2. Создание администратора

```bash
npm run create-admin
```

Интерактивно создает нового администратора с проверкой:
- Уникальности логина и email
- Требований безопасности пароля
- Валидации всех полей

## Структура системы

### Файлы авторизации

```
server/
├── controllers/
│   └── authController.js      # Контроллер авторизации
├── middleware/
│   └── auth.js               # Middleware безопасности
├── models/
│   └── Admin.js              # Модель администратора
└── utils/
    ├── security.js           # Утилиты безопасности
    └── logger.js             # Система логирования

admin/
├── login.html                # Страница входа
├── forgot-password.html      # Восстановление пароля
├── reset-password.html       # Сброс пароля
├── css/
│   └── login.css            # Стили авторизации
└── js/
    └── login.js             # JavaScript авторизации

scripts/
├── setup-auth.js            # Полная настройка
├── setup-auth-db.js         # Настройка БД
└── init-admin.js            # Создание админа
```

### API маршруты

```
POST /api/auth/login           # Вход в систему
POST /api/auth/logout          # Выход из системы
GET  /api/auth/check           # Проверка авторизации
POST /api/auth/forgot-password # Запрос восстановления
POST /api/auth/reset-password  # Сброс пароля
POST /api/auth/change-password # Смена пароля
```

### Защищенные маршруты

Все маршруты админки защищены авторизацией:
- `/admin/*` (кроме страниц авторизации)
- `/api/*` (административные API)

## Требования безопасности

### Пароли

- Минимум 8 символов
- Заглавные и строчные буквы
- Цифры
- Специальные символы

### Защита от атак

- **Rate Limiting**: 5 попыток входа за 15 минут
- **IP блокировка**: автоматическая при превышении лимита
- **CSRF защита**: токены для всех форм
- **Безопасные заголовки**: XSS, clickjacking защита

### Логирование

Все действия логируются:
- Попытки входа (успешные/неудачные)
- Выходы из системы
- Смена паролей
- Восстановление паролей
- Блокировки IP
- Подозрительная активность

## Управление администраторами

### Создание нового администратора

```bash
npm run create-admin
```

### Просмотр логов безопасности

Логи сохраняются в:
- База данных: таблица `security_logs`
- Файл: `logs/security.log`

### Деактивация администратора

Через базу данных:
```sql
UPDATE admins SET is_active = 0 WHERE username = 'username';
```

## Восстановление пароля

1. Пользователь заходит на `/admin/forgot-password.html`
2. Вводит email
3. Система отправляет ссылку на email
4. Пользователь переходит по ссылке
5. Устанавливает новый пароль

## Настройка email

Для восстановления паролей используется существующая система отправки email из `config/mail-config.js`.

Убедитесь, что настроены переменные окружения:
```env
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USER=your-email
MAIL_PASS=your-password
```

## Безопасность в продакшене

### Обязательные настройки

1. **HTTPS**: включите `SECURE_COOKIES=true`
2. **Сильный секрет сессии**: `SESSION_SECRET=long-random-string`
3. **Безопасные заголовки**: автоматически включены
4. **Регулярные бэкапы**: базы данных и логов

### Мониторинг

Следите за:
- Количеством неудачных попыток входа
- Подозрительной активностью в логах
- Блокировками IP адресов

## Устранение неполадок

### Проблема: "Недействительная сессия"

Решение:
1. Очистите cookies браузера
2. Перезапустите сервер
3. Проверьте настройки сессий

### Проблема: "IP заблокирован"

Решение:
1. Подождите 15 минут
2. Или очистите таблицу `login_attempts`

### Проблема: "Ошибка базы данных"

Решение:
1. Запустите `npm run setup-auth-db`
2. Проверьте права доступа к файлу БД
3. Убедитесь, что директория `data` существует

## Дополнительная безопасность

### Двухфакторная аутентификация

Для добавления 2FA в будущем:
1. Добавьте поле `two_factor_secret` в таблицу `admins`
2. Интегрируйте библиотеку `speakeasy`
3. Добавьте QR-код генерацию

### Аудит безопасности

Регулярно проверяйте:
- Логи безопасности
- Активных администраторов
- Настройки паролей
- Обновления зависимостей

## Поддержка

При возникновении проблем:
1. Проверьте логи в `logs/security.log`
2. Просмотрите таблицу `security_logs`
3. Убедитесь в правильности настроек окружения
