# 🚀 Быстрая настройка авторизации SHMS Auto

## ⚡ Быстрая настройка (3 шага)

### 1. Создание администратора

Запустите в корневой папке проекта:

```bash
node create-admin.js
```

Скрипт попросит ввести:

- ✅ Логин (минимум 3 символа)
- ✅ Полное имя (необязательно)
- ✅ Пароль (минимум 6 символов, скрытый ввод)
- ✅ Подтверждение пароля

Автоматически:

- ✅ Создаст таблицы в базе данных
- ✅ Проверит уникальность логина
- ✅ Зашифрует пароль

### 2. Запуск сервера

```bash
npm run local
```

### 3. Вход в админку

1. Откройте: http://localhost:3000/admin
2. Система перенаправит на: http://localhost:3000/admin/login.html
3. Введите учетные данные:
   - **Логин:** `admin`
   - **Пароль:** `SHMSAdmin2024!`

## 🔒 Что включено в систему безопасности

- ✅ **Хеширование паролей** с bcrypt
- ✅ **Rate limiting** - 5 попыток за 15 минут
- ✅ **IP блокировка** при превышении лимита
- ✅ **CSRF защита** для всех форм
- ✅ **Логирование** всех действий безопасности
- ✅ **Восстановление паролей** через email
- ✅ **Защищенные сессии**
- ✅ **Безопасные заголовки**

## 🛡️ Защищенные маршруты

После настройки будут защищены:

- `/admin/*` - все страницы админки
- `/admin/api/*` - все API админки
- Загрузка файлов
- Управление контентом

## 🔧 Дополнительные возможности

### Смена пароля

После входа в админку вы можете сменить пароль в настройках профиля.

### Восстановление пароля

Если забыли пароль:

1. Перейдите на http://localhost:3000/admin/forgot-password.html
2. Введите email: `<EMAIL>`
3. Проверьте почту и следуйте инструкциям

### Управление администраторами

**Создание нового администратора:**

```bash
node create-admin.js
# или
npm run create-admin
```

**Просмотр списка администраторов:**

```bash
node list-admins.js
# или
npm run list-admins
```

**Удаление/деактивация администратора:**

```bash
node delete-admin.js
# или
npm run delete-admin
```

Скрипт удаления позволяет:

- 🗑️ Полностью удалить администратора
- 🔴 Деактивировать (отключить доступ)
- 🟢 Активировать (включить доступ)

## ❗ Важные замечания

1. **Смените пароль** после первого входа
2. **Настройте email** в `.env` файле для восстановления паролей
3. **Включите HTTPS** в продакшене
4. **Регулярно проверяйте логи** безопасности

## 🐛 Устранение неполадок

### Проблема: "Ошибка базы данных"

```bash
# Удалите старую БД и создайте заново
rm data/stock.db
node setup-admin.js
```

### Проблема: "IP заблокирован"

Подождите 15 минут или очистите таблицу `login_attempts` в БД.

### Проблема: "Недействительная сессия"

Очистите cookies браузера и войдите заново.

## 📞 Поддержка

При возникновении проблем проверьте:

1. Логи в консоли браузера
2. Логи сервера
3. Файл `logs/security.log` (если создался)
4. Таблицу `security_logs` в базе данных
