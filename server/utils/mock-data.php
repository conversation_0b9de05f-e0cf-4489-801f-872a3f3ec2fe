<?php
// Скрипт с тестовыми данными для автомобилей
// Используется когда API недоступен или для отладки

/**
 * Возвращает тестовые данные автомобилей с реальными URL изображений
 * @return array Массив данных автомобилей
 */
function getMockCars() {
    return [
        [
            'brand' => 'BMW',
            'model' => 'M5 Competition',
            'year' => '2023',
            'power' => '625 л.с.',
            'engine' => '4.4л V8 twin-turbo',
            'mileage' => '1500',
            'price' => '$149,900',
            'image_url' => 'https://cdn.motor1.com/images/mgl/RqqPyW/s1/2023-bmw-m5-edition-m-50-jahre-bmw-m.jpg'
        ],
        [
            'brand' => 'Audi',
            'model' => 'RS7 Sportback',
            'year' => '2023',
            'power' => '600 л.с.',
            'engine' => '4.0л V8 twin-turbo',
            'mileage' => '800',
            'price' => '$125,000',
            'image_url' => 'https://cdn.motor1.com/images/mgl/13Bvoo/s1/2024-audi-rs-7-sportback-performance.jpg'
        ],
        [
            'brand' => 'Ferrari',
            'model' => 'Roma',
            'year' => '2023',
            'power' => '612 л.с.',
            'engine' => '3.9л V8 twin-turbocharged',
            'mileage' => '113',
            'price' => '$436,195',
            'image_url' => 'https://cdn.motor1.com/images/mgl/ZnnljA/s1/ferrari-roma-by-novitec.jpg'
        ],
        [
            'brand' => 'Lamborghini',
            'model' => 'Huracan',
            'year' => '2023',
            'power' => '631 л.с.',
            'engine' => '5.2л V10 атмосферный',
            'mileage' => '860',
            'price' => '$420,325',
            'image_url' => 'https://cdn.motor1.com/images/mgl/qkPgb/s1/lamborghini-huracan-sto.jpg'
        ],
        [
            'brand' => 'Bentley',
            'model' => 'Flying Spur',
            'year' => '2023',
            'power' => '542 л.с.',
            'engine' => '4.0л V8 twin-turbo',
            'mileage' => '2200',
            'price' => '$400,000',
            'image_url' => 'https://cdn.motor1.com/images/mgl/lPk8O/s1/bentley-flying-spur-mulliner-blackline-specification.jpg'
        ],
        [
            'brand' => 'Porsche',
            'model' => '911 Turbo S',
            'year' => '2023',
            'power' => '650 л.с.',
            'engine' => '3.8л Flat-6 twin-turbo',
            'mileage' => '320',
            'price' => '$229,900',
            'image_url' => 'https://cdn.motor1.com/images/mgl/P3GwKA/s1/porsche-911-turbo-s-2023.jpg'
        ],
        [
            'brand' => 'Mercedes-Benz',
            'model' => 'AMG GT 63 S',
            'year' => '2023',
            'power' => '639 л.с.',
            'engine' => '4.0л V8 twin-turbo',
            'mileage' => '750',
            'price' => '$189,520',
            'image_url' => 'https://cdn.motor1.com/images/mgl/Zpz7nP/s1/mercedes-amg-gt-63-s-e-performance-f1-edition.jpg'
        ],
        [
            'brand' => 'Rolls-Royce',
            'model' => 'Ghost',
            'year' => '2023',
            'power' => '563 л.с.',
            'engine' => '6.75л V12 twin-turbo',
            'mileage' => '120',
            'price' => '$475,000',
            'image_url' => 'https://cdn.motor1.com/images/mgl/mGMYr/s1/novitec-rolls-royce-ghost.jpg'
        ],
        [
            'brand' => 'Aston Martin',
            'model' => 'DB11',
            'year' => '2023',
            'power' => '503 л.с.',
            'engine' => '4.0л V8 twin-turbo',
            'mileage' => '900',
            'price' => '$284,800',
            'image_url' => 'https://cdn.motor1.com/images/mgl/MKKNAP/s1/q-by-aston-martin-vantage-roadster-a3.jpg'
        ]
    ];
}

// Если скрипт вызван напрямую, возвращаем JSON данные
if (basename($_SERVER['SCRIPT_NAME']) == basename(__FILE__)) {
    header('Content-Type: application/json');
    
    // Если запрошено определенное количество записей
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : null;
    $data = getMockCars();
    
    if ($limit && $limit > 0 && $limit < count($data)) {
        $data = array_slice($data, 0, $limit);
    }
    
    echo json_encode([
        'status' => 'success',
        'count' => count($data),
        'data' => $data,
        'mock' => true
    ]);
}
?> 