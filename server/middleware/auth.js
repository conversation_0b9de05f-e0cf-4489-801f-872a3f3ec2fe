const rateLimit = require('express-rate-limit');
const Admin = require('../models/Admin');
const securityUtils = require('../utils/security');
const logger = require('../utils/logger');

// Rate limiting для попыток входа
const loginLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 минут
    max: 5, // максимум 5 попыток за окно
    message: {
        error: 'Слишком много попыток входа. Попробуйте снова через 15 минут.',
        retryAfter: 15 * 60
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
        return securityUtils.getClientIp(req);
    },
    handler: async (req, res) => {
        const ipAddress = securityUtils.getClientIp(req);
        const userAgent = req.get('User-Agent') || '';
        
        await logger.logIpBlocked(ipAddress, userAgent, 5);
        
        res.status(429).json({
            error: 'Слишком много попыток входа. Попробуйте снова через 15 минут.',
            retryAfter: 15 * 60
        });
    }
});

// Rate limiting для восстановления пароля
const passwordResetLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 час
    max: 3, // максимум 3 попытки за час
    message: {
        error: 'Слишком много запросов на восстановление пароля. Попробуйте снова через час.',
        retryAfter: 60 * 60
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
        return securityUtils.getClientIp(req);
    }
});

// Middleware для проверки авторизации
const requireAuth = async (req, res, next) => {
    try {
        // Проверяем наличие сессии
        if (!req.session || !req.session.adminId) {
            return res.status(401).json({
                error: 'Требуется авторизация',
                redirectTo: '/admin/login.html'
            });
        }

        // Проверяем существование администратора в базе данных
        const adminModel = new Admin();
        const admin = await adminModel.findById(req.session.adminId);

        if (!admin) {
            // Очищаем недействительную сессию
            req.session.destroy();
            return res.status(401).json({
                error: 'Недействительная сессия',
                redirectTo: '/admin/login.html'
            });
        }

        // Добавляем информацию об администраторе в запрос
        req.admin = admin;
        next();
    } catch (error) {
        console.error('Ошибка проверки авторизации:', error.message);
        res.status(500).json({
            error: 'Ошибка сервера при проверке авторизации'
        });
    }
};

// Middleware для проверки авторизации для HTML страниц
const requireAuthHTML = async (req, res, next) => {
    try {
        // Проверяем наличие сессии
        if (!req.session || !req.session.adminId) {
            return res.redirect('/admin/login.html');
        }

        // Проверяем существование администратора в базе данных
        const adminModel = new Admin();
        const admin = await adminModel.findById(req.session.adminId);

        if (!admin) {
            // Очищаем недействительную сессию
            req.session.destroy();
            return res.redirect('/admin/login.html');
        }

        // Добавляем информацию об администраторе в запрос
        req.admin = admin;
        next();
    } catch (error) {
        console.error('Ошибка проверки авторизации:', error.message);
        res.redirect('/admin/login.html');
    }
};

// Middleware для проверки блокировки IP
const checkIpBlocked = async (req, res, next) => {
    try {
        const ipAddress = securityUtils.getClientIp(req);
        const isBlocked = await securityUtils.isIpBlocked(ipAddress);

        if (isBlocked) {
            const attempts = await securityUtils.getFailedAttempts(ipAddress);
            const userAgent = req.get('User-Agent') || '';
            
            await logger.logIpBlocked(ipAddress, userAgent, attempts);
            
            return res.status(429).json({
                error: 'IP адрес временно заблокирован из-за множественных неудачных попыток входа',
                retryAfter: 15 * 60
            });
        }

        next();
    } catch (error) {
        console.error('Ошибка проверки блокировки IP:', error.message);
        next(); // Продолжаем выполнение в случае ошибки
    }
};

// Middleware для логирования запросов к админке
const logAdminAccess = async (req, res, next) => {
    try {
        const ipAddress = securityUtils.getClientIp(req);
        const userAgent = req.get('User-Agent') || '';
        const method = req.method;
        const url = req.originalUrl;

        // Логируем только важные действия
        if (method !== 'GET' || url.includes('/api/')) {
            const adminId = req.session?.adminId || null;
            const username = req.admin?.username || 'anonymous';
            
            await logger.logAdminAction(
                adminId,
                username,
                ipAddress,
                userAgent,
                `${method} ${url}`,
                JSON.stringify({
                    body: req.body,
                    query: req.query
                })
            );
        }

        next();
    } catch (error) {
        console.error('Ошибка логирования доступа к админке:', error.message);
        next(); // Продолжаем выполнение в случае ошибки
    }
};

// Middleware для проверки CSRF токена
const checkCSRF = (req, res, next) => {
    // Пропускаем GET запросы
    if (req.method === 'GET') {
        return next();
    }

    const sessionToken = req.session.csrfToken;
    const requestToken = req.body.csrfToken || req.headers['x-csrf-token'];

    if (!securityUtils.verifyCSRFToken(sessionToken, requestToken)) {
        return res.status(403).json({
            error: 'Недействительный CSRF токен'
        });
    }

    next();
};

// Middleware для генерации CSRF токена
const generateCSRF = (req, res, next) => {
    if (!req.session.csrfToken) {
        req.session.csrfToken = securityUtils.generateCSRFToken();
    }
    next();
};

// Middleware для санитизации входных данных
const sanitizeInput = (req, res, next) => {
    // Санитизируем body
    if (req.body && typeof req.body === 'object') {
        for (const key in req.body) {
            if (typeof req.body[key] === 'string') {
                req.body[key] = securityUtils.sanitizeInput(req.body[key]);
            }
        }
    }

    // Санитизируем query параметры
    if (req.query && typeof req.query === 'object') {
        for (const key in req.query) {
            if (typeof req.query[key] === 'string') {
                req.query[key] = securityUtils.sanitizeInput(req.query[key]);
            }
        }
    }

    next();
};

// Middleware для установки безопасных заголовков
const setSecurityHeaders = (req, res, next) => {
    // Предотвращение clickjacking
    res.setHeader('X-Frame-Options', 'DENY');
    
    // Предотвращение MIME type sniffing
    res.setHeader('X-Content-Type-Options', 'nosniff');
    
    // XSS защита
    res.setHeader('X-XSS-Protection', '1; mode=block');
    
    // Referrer policy
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // Content Security Policy для админки
    if (req.originalUrl.startsWith('/admin')) {
        res.setHeader('Content-Security-Policy', 
            "default-src 'self'; " +
            "script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; " +
            "style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; " +
            "font-src 'self' https://cdnjs.cloudflare.com; " +
            "img-src 'self' data: https:; " +
            "connect-src 'self';"
        );
    }

    next();
};

module.exports = {
    loginLimiter,
    passwordResetLimiter,
    requireAuth,
    requireAuthHTML,
    checkIpBlocked,
    logAdminAccess,
    checkCSRF,
    generateCSRF,
    sanitizeInput,
    setSecurityHeaders
};
