# Интеграция API Encar для SHMS Автосалон

Этот проект содержит компоненты для интеграции данных Encar API с сайтом автосалона SHMS. Решение оптимизировано для работы с большими CSV-файлами (3-3,5 ГБ) без перегрузки памяти.

## Компоненты интеграции

1. **PHP-прокси** (`public/api/encar-proxy.php`) - обрабатывает запросы к API Encar, выполняет потоковую обработку CSV и фильтрацию.
2. **Тестовый API** (`public/api/mock-data.php`) - предоставляет тестовые данные для отладки без доступа к Encar.
3. **JavaScript** (`public/order-encar.js`) - отображает автомобили на странице сайта с поддержкой поиска и пагинации.
4. **Тестер API** (`public/api-tester.html`) - инструмент для отладки и тестирования API.

## Особенности решения

- Потоковая обработка больших CSV файлов без загрузки всего файла в память
- Фильтрация по марке и модели, поддержка пагинации и смещения (offset)
- Возможность отображения только "главных карточек" (featured)
- Автоматическое определение среды разработки и продакшена
- Русификация данных (цвета, типы кузова, двигателя и т.д.)
- Встроенные запасные решения при сбоях API

## Запуск

### Локальная разработка

Для локальной разработки запустите PHP-сервер на порту 8000:

```bash
cd /путь/к/проекту
php -S localhost:8000 -t public
```

Затем откройте тестовую страницу:
http://localhost:8000/api-tester.html

### На хостинге Рег.ру

Файлы должны быть размещены в соответствующих директориях. PHP-прокси и настройки .htaccess уже сконфигурированы для работы на хостинге.

## Параметры API

API поддерживает следующие параметры:

- `fileType` - тип файла (active_offer или removed_offer)
- `date` - дата в формате YYYY-MM-DD
- `brand` - марка автомобиля для фильтрации
- `model` - модель автомобиля для фильтрации
- `limit` - количество результатов (по умолчанию 20)
- `offset` - смещение для пагинации
- `featured` - если 1, показывать только главные карточки
- `debug` - если 1, включить расширенное логирование

## Управление главными карточками

Главные карточки (featured) настраиваются через массив `$featuredIds` в файле `encar-proxy.php`. Для добавления главной карточки добавьте ID автомобиля в этот массив.

## Решение проблем

- **Ошибка Out of memory**: Уменьшите значение параметра `limit` или используйте пагинацию.
- **PHP-файлы отдаются как текст**: Проверьте настройки .htaccess и убедитесь, что PHP установлен и включен.
- **Пустой ответ от API**: Проверьте доступность CSV файла и аутентификацию.

## Доработка

Для дальнейшего развития можно:
1. Добавить кеширование результатов в SQLite или Redis
2. Создать администраторский интерфейс для управления главными карточками
3. Расширить фильтрацию по дополнительным полям (цвет, тип кузова и т.д.) 