server {
    server_name shms-auto.ru www.shms-auto.ru;

    # Логи
    access_log /var/log/nginx/shms-auto.access.log;
    error_log /var/log/nginx/shms-auto.error.log;

#    location ^~ /api/(encar-proxy\.php) {
#        alias /home/<USER>/shms-auto/public;
#        index index.php index.html;
#
#        try_files $uri $uri/ /api/index.php?$args;
#
#        location ~ \.php$ {
#            include fastcgi_params;
#            fastcgi_pass unix:/run/php/php8.3-fpm.sock;
#            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
#            fastcgi_split_path_info ^(.+\.php)(/.+)$;
#            fastcgi_param PATH_INFO $fastcgi_path_info;
#        }
#    }
location ~ ^/api/.*\.php$ {
    root /home/<USER>/shms-auto/public;
    include fastcgi_params;
    fastcgi_pass unix:/run/php/php8.3-fpm.sock;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    fastcgi_split_path_info ^(.+\.php)(/.*)$;
    fastcgi_param PATH_INFO $fastcgi_path_info;
    fastcgi_index index.php;
}
    # Основное проксирование на Node.js
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300;
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
    }

    # Статические файлы загрузок
    location /uploads/ {
        alias /home/<USER>/shms-auto/data/uploads/;
        expires 30d;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Статические файлы сайта
    location /css/ {
        alias /home/<USER>/shms-auto/public/css/;
        expires 7d;
        add_header Cache-Control "public";
    }

    location /js/ {
        alias /home/<USER>/shms-auto/public/js/;
        expires 7d;
        add_header Cache-Control "public";
    }

    location /assets/ {
        alias /home/<USER>/shms-auto/public/assets/;
        expires 30d;
        add_header Cache-Control "public";
    }

    # Специальные файлы
    location = /favicon.ico {
        alias /home/<USER>/shms-auto/public/favicon.ico;
        expires 30d;
        access_log off;
    }

    location = /robots.txt {
        alias /home/<USER>/shms-auto/public/robots.txt;
        expires 7d;
        access_log off;
    }

    location = /sitemap.xml {
        alias /home/<USER>/shms-auto/public/sitemap.xml;
        expires 1d;
        access_log off;
    }

    # Безопасность
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Ограничение размера загружаемых файлов
    client_max_body_size 50M;

    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/shms-auto.ru/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/shms-auto.ru/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot


}
server {
    if ($host = www.shms-auto.ru) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


    if ($host = shms-auto.ru) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


    listen 80;
    server_name shms-auto.ru www.shms-auto.ru;
    return 404; # managed by Certbot




}
