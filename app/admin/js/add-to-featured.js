const db = require('./database.js');

// Получить все автомобили, которые еще не в избранном
db.all(`
    SELECT sc.id 
    FROM stock_cars sc 
    LEFT JOIN featured_cars fc ON sc.id = fc.car_id 
    WHERE fc.id IS NULL
`, [], (err, cars) => {
    if (err) {
        console.error('Ошибка при получении автомобилей:', err);
        process.exit(1);
    }
    
    console.log(`Найдено ${cars.length} автомобилей для добавления в избранное`);
    
    // Если нет автомобилей для добавления, завершаем работу
    if (cars.length === 0) {
        console.log('Все автомобили уже добавлены в избранное');
        process.exit(0);
    }
    
    // Добавляем каждый автомобиль в избранное
    let processed = 0;
    
    cars.forEach((car, index) => {
        db.run('INSERT INTO featured_cars (car_id, display_order) VALUES (?, ?)', 
            [car.id, index + 1], function(err) {
            if (err) {
                console.error(`Ошибка при добавлении автомобиля ID=${car.id} в избранное:`, err);
            } else {
                console.log(`Автомобиль ID=${car.id} добавлен в избранное с порядком ${index + 1}`);
            }
            
            processed++;
            if (processed === cars.length) {
                console.log('Все автомобили успешно добавлены в избранное');
                process.exit(0);
            }
        });
    });
}); 