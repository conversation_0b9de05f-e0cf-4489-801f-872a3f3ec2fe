document.addEventListener("DOMContentLoaded", () => {
  // Элементы страницы
  const featuredCarsContainer = document.getElementById("featured-container");
  const availableCarsContainer = document.getElementById("available-container");
  const searchAvailable = document.getElementById("search-available");
  const searchAvailableBtn = document.getElementById("search-available-btn");
  const resetAvailableSearchBtn = document.getElementById(
    "reset-available-search-btn"
  );
  const featuredStatus = document.getElementById("featured-status");
  const availableStatus = document.getElementById("available-status");
  const notification = document.getElementById("notification");
  const orderDialog = document.getElementById("order-dialog");
  const displayOrderInput = document.getElementById("display-order");
  const featuredIdInput = document.getElementById("featured-id");
  const dialogCloseBtn = document.getElementById("dialog-close");
  const dialogSaveBtn = document.getElementById("dialog-save");
  const notificationTitle = document.querySelector(".notification-title");
  const notificationText = document.querySelector(".notification-text");
  const notificationClose = document.querySelector(".notification-close");

  // Глобальные переменные
  let allCars = [];
  let featuredCars = [];

  // Инициализация
  init();

  // Обработчики событий
  searchAvailable.addEventListener("input", filterAvailableCars);
  searchAvailableBtn.addEventListener("click", filterAvailableCars);
  resetAvailableSearchBtn.addEventListener("click", () => {
    searchAvailable.value = "";
    filterAvailableCars();
  });
  dialogCloseBtn.addEventListener("click", closeOrderDialog);
  dialogSaveBtn.addEventListener("click", saveDisplayOrder);
  notificationClose.addEventListener("click", () => {
    notification.classList.remove(
      "show",
      "notification-success",
      "notification-error",
      "notification-info"
    );
  });

  // Основная инициализация
  function init() {
    loadData();
  }

  // Обновление данных
  function refresh() {
    loadData();
    showNotification("Успешно", "Данные обновлены", "success");
  }

  // Загрузка всех необходимых данных
  async function loadData() {
    try {
      featuredStatus.textContent = "Загрузка данных...";
      availableStatus.textContent = "Загрузка данных...";

      // Загружаем избранные автомобили
      const featuredResponse = await fetch("/api/featured");
      if (!featuredResponse.ok)
        throw new Error("Ошибка загрузки избранных автомобилей");
      featuredCars = await featuredResponse.json();

      // Загружаем все доступные автомобили
      const carsResponse = await fetch("/api/cars");
      if (!carsResponse.ok)
        throw new Error("Ошибка загрузки доступных автомобилей");
      allCars = await carsResponse.json();

      // Обновляем интерфейс
      displayFeaturedCars();
      displayAvailableCars();

      featuredStatus.textContent = `Всего автомобилей на главной: ${featuredCars.length}`;
      availableStatus.textContent = "";
    } catch (error) {
      console.error("Ошибка при загрузке данных:", error);
      showNotification(
        "Ошибка",
        "Ошибка при загрузке данных: " + error.message,
        "error"
      );
      featuredStatus.textContent = "Ошибка загрузки данных";
      availableStatus.textContent = "Ошибка загрузки данных";
    }
  }

  // Отображение избранных автомобилей
  function displayFeaturedCars() {
    if (!featuredCarsContainer) return;

    if (featuredCars.length === 0) {
      featuredCarsContainer.innerHTML = `
                <div class="empty-state">
                    <h3>Нет автомобилей на главной странице</h3>
                    <p>Добавьте автомобили из списка доступных автомобилей ниже</p>
                </div>
            `;
      return;
    }

    featuredCarsContainer.innerHTML = "";

    // Сортируем по порядку отображения
    featuredCars.sort((a, b) => a.display_order - b.display_order);

    featuredCars.forEach((car) => {
      const card = document.createElement("div");
      card.className = "featured-car";

      const imagePath = car.image_path.startsWith("/")
        ? car.image_path
        : `/${car.image_path}`;

      card.innerHTML = `
                <img src="${imagePath}" alt="${car.title}" onerror="this.src='/uploads/default-car.jpg'">
                <div class="featured-car-content">
                    <div class="featured-car-title">${car.title}</div>
                    <div class="featured-car-price">${car.price}</div>
                    <div class="featured-car-order">
                        <span class="badge">${car.display_order}</span>
                        <span>Порядок отображения</span>
                    </div>
                    <div class="featured-car-actions">
                        <button class="order-btn" data-id="${car.featured_id}">Изменить порядок</button>
                        <button class="remove-btn" data-id="${car.featured_id}">Удалить</button>
                    </div>
                </div>
            `;

      featuredCarsContainer.appendChild(card);

      // Добавляем обработчики событий
      card
        .querySelector(".remove-btn")
        .addEventListener("click", () => removeFeatured(car.featured_id));
      card
        .querySelector(".order-btn")
        .addEventListener("click", () =>
          openOrderDialog(car.featured_id, car.display_order)
        );
    });
  }

  // Отображение доступных автомобилей
  function displayAvailableCars(searchText = "") {
    if (!availableCarsContainer) return;

    const availableCars = getAvailableCars();

    if (availableCars.length === 0) {
      availableCarsContainer.innerHTML = `
                <div class="empty-state">
                    <h3>Все автомобили уже добавлены на главную страницу</h3>
                </div>
            `;
      return;
    }

    availableCarsContainer.innerHTML = "";

    // Фильтруем по поисковому запросу
    let filteredCars = availableCars;
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filteredCars = availableCars.filter(
        (car) =>
          car.title.toLowerCase().includes(searchLower) ||
          (car.subtitle && car.subtitle.toLowerCase().includes(searchLower)) ||
          (car.price && car.price.toLowerCase().includes(searchLower))
      );

      availableStatus.textContent = `Найдено автомобилей: ${filteredCars.length}`;
    } else {
      availableStatus.textContent = `Доступно автомобилей: ${availableCars.length}`;
    }

    if (filteredCars.length === 0) {
      availableCarsContainer.innerHTML = `
                <div class="empty-state">
                    <h3>Нет автомобилей по вашему запросу</h3>
                    <p>Попробуйте изменить условия поиска</p>
                </div>
            `;
      return;
    }

    filteredCars.forEach((car) => {
      const card = document.createElement("div");
      card.className = "car-card";

      const imagePath =
        car.image_path && car.image_path.startsWith("/")
          ? car.image_path
          : `/${car.image_path || "uploads/default-car.jpg"}`;

      card.innerHTML = `
                <div class="car-card-image">
                    <img src="${imagePath}" alt="${
        car.title
      }" onerror="this.src='/uploads/default-car.jpg'">
                </div>
                <div class="car-card-content">
                    <div class="car-card-title">${car.title}</div>
                    <div class="car-card-subtitle">${car.subtitle || ""}</div>
                    <div class="car-card-price">${car.price || ""}</div>
                    <div class="car-card-actions">
                        <button class="add-to-featured-btn" data-id="${
                          car.id
                        }">Добавить на главную</button>
                    </div>
                </div>
            `;

      availableCarsContainer.appendChild(card);

      // Добавляем обработчик события
      card
        .querySelector(".add-to-featured-btn")
        .addEventListener("click", () => addToFeatured(car.id));
    });
  }

  // Фильтрация автомобилей по поиску
  function filterAvailableCars() {
    const searchText = searchAvailable.value.trim();
    displayAvailableCars(searchText);
  }

  // Получаем список автомобилей, которых еще нет в избранном
  function getAvailableCars() {
    // Получаем id всех избранных автомобилей
    const featuredIds = featuredCars.map((car) => car.id);

    // Отфильтровываем доступные автомобили, исключая те, что уже в избранном
    return allCars.filter((car) => !featuredIds.includes(car.id));
  }

  // Добавление автомобиля на главную страницу
  async function addToFeatured(carId) {
    try {
      const button = availableCarsContainer.querySelector(
        `.add-to-featured-btn[data-id="${carId}"]`
      );
      if (button) {
        button.classList.add("btn-loading");
        button.disabled = true;
      }

      const response = await fetch(`/api/featured/${carId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ displayOrder: featuredCars.length }), // Устанавливаем порядок в конец списка
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(
          result.error || "Ошибка при добавлении автомобиля на главную"
        );
      }

      // Обновляем данные и интерфейс
      loadData();
      showNotification(
        "Успешно",
        "Автомобиль добавлен на главную страницу",
        "success"
      );
    } catch (error) {
      console.error("Ошибка при добавлении автомобиля:", error);
      showNotification(
        "Ошибка",
        "Ошибка при добавлении автомобиля: " + error.message,
        "error"
      );
    } finally {
      const button = availableCarsContainer.querySelector(
        `.add-to-featured-btn[data-id="${carId}"]`
      );
      if (button) {
        button.classList.remove("btn-loading");
        button.disabled = false;
      }
    }
  }

  // Удаление автомобиля с главной страницы
  async function removeFeatured(featuredId) {
    if (
      !confirm("Вы уверены, что хотите удалить автомобиль с главной страницы?")
    ) {
      return;
    }

    try {
      const button = featuredCarsContainer.querySelector(
        `.remove-btn[data-id="${featuredId}"]`
      );
      if (button) {
        button.classList.add("btn-loading");
        button.disabled = true;
      }

      const response = await fetch(`/api/featured/${featuredId}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(
          result.error || "Ошибка при удалении автомобиля с главной"
        );
      }

      // Обновляем данные и интерфейс
      loadData();
      showNotification(
        "Успешно",
        "Автомобиль удален с главной страницы",
        "success"
      );
    } catch (error) {
      console.error("Ошибка при удалении автомобиля:", error);
      showNotification(
        "Ошибка",
        "Ошибка при удалении автомобиля: " + error.message,
        "error"
      );
    }
  }

  // Открыть диалог изменения порядка
  function openOrderDialog(featuredId, currentOrder) {
    featuredIdInput.value = featuredId;
    displayOrderInput.value = currentOrder;
    orderDialog.classList.add("active");
  }

  // Закрыть диалог изменения порядка
  function closeOrderDialog() {
    orderDialog.classList.remove("active");
  }

  // Сохранить новый порядок отображения
  async function saveDisplayOrder() {
    const featuredId = featuredIdInput.value;
    const newOrder = parseInt(displayOrderInput.value);

    if (isNaN(newOrder) || newOrder < 0) {
      alert(
        "Пожалуйста, введите корректное значение порядка (число больше или равное 0)"
      );
      return;
    }

    try {
      dialogSaveBtn.classList.add("btn-loading");
      dialogSaveBtn.disabled = true;

      const response = await fetch(`/api/featured/${featuredId}/order`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ displayOrder: newOrder }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(
          result.error || "Ошибка при изменении порядка отображения"
        );
      }

      // Закрываем диалог
      closeOrderDialog();

      // Обновляем данные и интерфейс
      loadData();
      showNotification("Успешно", "Порядок отображения изменен", "success");
    } catch (error) {
      console.error("Ошибка при изменении порядка:", error);
      showNotification(
        "Ошибка",
        "Ошибка при изменении порядка: " + error.message,
        "error"
      );
    } finally {
      dialogSaveBtn.classList.remove("btn-loading");
      dialogSaveBtn.disabled = false;
    }
  }

  // Показать уведомление
  function showNotification(title, message, type = "success") {
    notification.classList.remove(
      "notification-success",
      "notification-error",
      "notification-info"
    );
    notification.classList.add(`notification-${type}`);

    notificationTitle.textContent = title;
    notificationText.textContent = message;

    notification.classList.add("show");

    // Автоматически скрываем уведомление через 5 секунд
    setTimeout(() => {
      notification.classList.remove("show");
    }, 5000);
  }
});
