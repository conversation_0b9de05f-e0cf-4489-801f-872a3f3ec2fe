<!DOCTYPE html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Управление отзывами - SHMS Auto</title>
    <link rel="stylesheet" href="css/styles.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"
    />
    <style>
      .reviews-container {
        display: grid;
        grid-template-columns: 1fr;
        gap: 30px;
        margin-top: 20px;
      }

      @media (min-width: 1200px) {
        .reviews-container {
          grid-template-columns: 400px 1fr;
        }
      }

      .review-form {
        background-color: #fff;
        border-radius: var(--radius);
        padding: 20px;
        box-shadow: 0 2px 10px var(--shadow);
      }

      .review-form h3 {
        margin-top: 0;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--border);
      }

      .review-list {
        background-color: #fff;
        border-radius: var(--radius);
        padding: 20px;
        box-shadow: 0 2px 10px var(--shadow);
      }

      .review-list h3 {
        margin-top: 0;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--border);
      }

      .review-items {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
      }

      .review-item {
        border: 1px solid var(--border);
        border-radius: var(--radius-sm);
        overflow: hidden;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
      }

      .review-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px var(--shadow-lg);
      }

      .review-item-image {
        width: 100%;
        height: 160px;
        object-fit: cover;
      }

      .review-item-content {
        padding: 15px;
      }

      .review-item-title {
        font-weight: 600;
        margin-bottom: 5px;
        font-size: 1rem;
      }

      .review-item-date {
        color: var(--text-light);
        font-size: 0.85rem;
        margin-bottom: 10px;
      }

      .review-item-car {
        font-size: 0.9rem;
        margin-bottom: 5px;
      }

      .review-item-author {
        font-size: 0.9rem;
        margin-bottom: 10px;
      }

      .review-item-actions {
        display: flex;
        gap: 10px;
        margin-top: 10px;
      }

      .review-item-actions button {
        padding: 5px 10px;
        font-size: 0.85rem;
      }

      .image-preview {
        margin-top: 10px;
        margin-bottom: 15px;
        max-width: 100%;
        border-radius: var(--radius-sm);
        overflow: hidden;
      }

      .image-preview img {
        max-width: 100%;
        max-height: 200px;
        display: block;
      }

      .preview-placeholder {
        background-color: #f0f0f0;
        color: #888;
        text-align: center;
        padding: 30px;
        border-radius: var(--radius-sm);
      }

      .content-preview {
        margin-top: 10px;
        padding: 10px;
        background-color: #f9f9f9;
        border-radius: var(--radius-sm);
        max-height: 200px;
        overflow-y: auto;
        margin-bottom: 15px;
      }

      .empty-state {
        text-align: center;
        padding: 30px;
        color: var(--text-light);
      }

      .editor-container {
        margin-bottom: 15px;
      }

      .editor-toolbar {
        display: flex;
        gap: 5px;
        margin-bottom: 5px;
      }

      .editor-toolbar button {
        padding: 5px 10px;
        background-color: #f0f0f0;
        border: 1px solid #ddd;
        border-radius: 3px;
        cursor: pointer;
        font-size: 0.85rem;
      }

      .editor-toolbar button:hover {
        background-color: #e0e0e0;
      }

      #content-editor {
        min-height: 200px;
        border: 1px solid #ddd;
        border-radius: 3px;
        padding: 10px;
        font-family: inherit;
        font-size: 0.95rem;
        line-height: 1.5;
        width: 100%;
        box-sizing: border-box;
        resize: vertical;
      }

      .confirmation-dialog {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
      }

      .confirmation-dialog.active {
        opacity: 1;
        visibility: visible;
      }

      .confirmation-dialog-content {
        background-color: #fff;
        border-radius: var(--radius);
        padding: 20px;
        width: 90%;
        max-width: 400px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
      }

      .confirmation-dialog-title {
        font-weight: 600;
        margin-bottom: 15px;
      }

      .confirmation-dialog-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
      }
    </style>
  </head>

  <body>
    <!-- Mobile Header -->
    <header class="admin-mobile-header">
      <button
        class="admin-burger"
        id="adminBurgerBtn"
        aria-label="Открыть меню"
      >
        <span></span>
        <span></span>
        <span></span>
      </button>
      <a href="index.html" class="admin-mobile-header__logo"> SHMS Admin </a>
    </header>

    <!-- Пустой раздел для отступа -->
    <div class="admin-mobile-header-spacer"></div>

    <!-- Mobile Menu -->
    <nav class="admin-dropdown-menu" id="adminDropdownMenu">
      <a href="index.html">Автомобили в наличии</a>
      <a href="featured.html">Автомобили на главной</a>
      <a href="car-detail-simple.html">Детальные страницы</a>
      <a href="reviews.html" class="active">Отзывы</a>
    </nav>

    <!-- Hero / Title Section -->
    <section class="admin-hero-stock">
      <div class="admin-hero-header-group">
        <div class="admin-header__left">
          <a
            href="#"
            class="admin-header__back-link"
            onclick="window.history.back(); return false;"
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19 12H5M5 12L12 19M5 12L12 5"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            Назад
          </a>
        </div>
        <div class="admin-header__center-group">
          <nav class="admin-header__nav">
            <a href="index.html" class="admin-header__logo-link">SHMS Admin</a>
            <div class="admin-header__nav-links">
              <a href="index.html" class="admin-header__nav-link"
                >Автомобили в наличии</a
              >
              <a href="featured.html" class="admin-header__nav-link"
                >Автомобили на главной</a
              >
              <a href="car-detail-simple.html" class="admin-header__nav-link"
                >Детальные страницы</a
              >
              <a href="reviews.html" class="admin-header__nav-link active"
                >Отзывы</a
              >
            </div>
          </nav>
        </div>
      </div>
    </section>

    <div class="admin-container">
      <div class="reviews-container">
        <div class="review-form">
          <h3 id="form-title">Добавить новый отзыв</h3>
          <form id="reviewForm">
            <input type="hidden" id="reviewId" value="" />

            <div class="form-group">
              <label for="title">Заголовок отзыва*</label>
              <input type="text" id="title" name="title" required />
            </div>

            <div class="form-group">
              <label for="date">Дата</label>
              <input type="date" id="date" name="date" />
            </div>

            <div class="form-group">
              <label for="carModel">Модель автомобиля</label>
              <input type="text" id="carModel" name="carModel" />
            </div>

            <div class="form-group">
              <label for="author">Автор отзыва</label>
              <input type="text" id="author" name="author" />
            </div>

            <div class="form-group">
              <label for="image">Изображение*</label>
              <input type="file" id="image" name="image" accept="image/*" />
              <input type="hidden" id="imagePath" name="imagePath" />

              <div class="image-preview" id="imagePreview">
                <div class="preview-placeholder">Предпросмотр изображения</div>
              </div>
            </div>

            <div class="form-group">
              <label for="content-editor">Содержание отзыва*</label>
              <div class="editor-container">
                <div class="editor-toolbar">
                  <button type="button" data-action="bold">Жирный</button>
                  <button type="button" data-action="italic">Курсив</button>
                  <button type="button" data-action="paragraph">Абзац</button>
                </div>
                <textarea
                  id="content-editor"
                  placeholder="Введите текст отзыва..."
                ></textarea>
              </div>
              <input type="hidden" id="content" name="content" />
            </div>

            <div class="form-group">
              <label>Предпросмотр содержания</label>
              <div class="content-preview" id="contentPreview"></div>
            </div>

            <div class="form-actions">
              <button type="submit" id="submitBtn">Добавить отзыв</button>
              <button type="button" id="resetBtn">Очистить форму</button>
            </div>
          </form>
        </div>

        <div class="review-list">
          <h3>Существующие отзывы</h3>
          <div class="review-items" id="reviewItems">
            <div class="empty-state">Загрузка отзывов...</div>
          </div>
        </div>
      </div>
    </div>

    <div class="confirmation-dialog" id="deleteConfirmation">
      <div class="confirmation-dialog-content">
        <div class="confirmation-dialog-title">Подтверждение удаления</div>
        <p>
          Вы уверены, что хотите удалить этот отзыв? Это действие нельзя
          отменить.
        </p>
        <div class="confirmation-dialog-actions">
          <button type="button" id="cancelDelete">Отмена</button>
          <button type="button" id="confirmDelete" class="danger">
            Удалить
          </button>
        </div>
      </div>
    </div>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Элементы формы
        const reviewForm = document.getElementById("reviewForm");
        const reviewIdInput = document.getElementById("reviewId");
        const titleInput = document.getElementById("title");
        const dateInput = document.getElementById("date");
        const carModelInput = document.getElementById("carModel");
        const authorInput = document.getElementById("author");
        const imageInput = document.getElementById("image");
        const imagePathInput = document.getElementById("imagePath");
        const imagePreview = document.getElementById("imagePreview");
        const contentEditor = document.getElementById("content-editor");
        const contentInput = document.getElementById("content");
        const contentPreview = document.getElementById("contentPreview");
        const submitBtn = document.getElementById("submitBtn");
        const resetBtn = document.getElementById("resetBtn");
        const formTitle = document.getElementById("form-title");
        const reviewItems = document.getElementById("reviewItems");

        // Диалог подтверждения удаления
        const deleteConfirmation =
          document.getElementById("deleteConfirmation");
        const cancelDelete = document.getElementById("cancelDelete");
        const confirmDelete = document.getElementById("confirmDelete");
        let reviewToDelete = null;

        // Установка текущей даты по умолчанию
        const today = new Date().toISOString().split("T")[0];
        dateInput.value = today;

        // Загрузка списка отзывов
        loadReviews();

        // Обработчик отправки формы
        reviewForm.addEventListener("submit", function (e) {
          e.preventDefault();

          // Проверка обязательных полей
          if (!titleInput.value.trim()) {
            alert("Пожалуйста, введите заголовок отзыва");
            titleInput.focus();
            return;
          }

          if (!contentEditor.value.trim()) {
            alert("Пожалуйста, введите содержание отзыва");
            contentEditor.focus();
            return;
          }

          // Перенос содержания из редактора в скрытое поле
          contentInput.value = contentEditor.value;

          // Если это новый отзыв, проверяем наличие изображения
          if (
            !reviewIdInput.value &&
            !imageInput.files[0] &&
            !imagePathInput.value
          ) {
            alert("Пожалуйста, выберите изображение для отзыва");
            return;
          }

          // Если выбрано новое изображение, загружаем его
          if (imageInput.files[0]) {
            uploadImage(imageInput.files[0])
              .then((filePath) => {
                imagePathInput.value = filePath;
                saveReview();
              })
              .catch((error) => {
                alert("Ошибка при загрузке изображения: " + error.message);
              });
          } else {
            // Если изображение не выбрано или уже загружено
            saveReview();
          }
        });

        // Функция сохранения отзыва
        function saveReview() {
          const reviewData = {
            title: titleInput.value.trim(),
            date: dateInput.value,
            carModel: carModelInput.value.trim(),
            author: authorInput.value.trim(),
            image: imagePathInput.value,
            content: contentInput.value,
          };

          const isEditing = !!reviewIdInput.value;
          const url = isEditing
            ? `/api/reviews/${reviewIdInput.value}`
            : "/api/reviews";
          const method = isEditing ? "PUT" : "POST";

          fetch(url, {
            method: method,
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(reviewData),
          })
            .then((response) => {
              if (!response.ok) {
                throw new Error("Ошибка при сохранении отзыва");
              }
              return response.json();
            })
            .then((data) => {
              alert(
                isEditing ? "Отзыв успешно обновлен" : "Отзыв успешно добавлен"
              );
              resetForm();
              loadReviews();
            })
            .catch((error) => {
              alert("Ошибка: " + error.message);
            });
        }

        // Функция загрузки изображения
        function uploadImage(file) {
          const formData = new FormData();
          formData.append("image", file);

          return fetch("/api/upload-review-image", {
            method: "POST",
            body: formData,
          })
            .then((response) => {
              if (!response.ok) {
                throw new Error("Ошибка при загрузке изображения");
              }
              return response.json();
            })
            .then((data) => {
              if (data.success) {
                return data.filePath;
              } else {
                throw new Error(data.error || "Неизвестная ошибка");
              }
            });
        }

        // Функция загрузки списка отзывов
        function loadReviews() {
          fetch("/api/reviews")
            .then((response) => response.json())
            .then((reviews) => {
              if (reviews.length === 0) {
                reviewItems.innerHTML =
                  '<div class="empty-state">Нет доступных отзывов</div>';
                return;
              }

              reviewItems.innerHTML = "";
              reviews.forEach((review) => {
                const reviewElement = createReviewElement(review);
                reviewItems.appendChild(reviewElement);
              });
            })
            .catch((error) => {
              reviewItems.innerHTML = `<div class="empty-state">Ошибка загрузки отзывов: ${error.message}</div>`;
            });
        }

        // Функция создания элемента отзыва
        function createReviewElement(review) {
          const reviewElement = document.createElement("div");
          reviewElement.className = "review-item";
          reviewElement.innerHTML = `
                    <img src="${review.image}" alt="${
            review.title
          }" class="review-item-image">
                    <div class="review-item-content">
                        <div class="review-item-title">${review.title}</div>
                        <div class="review-item-date">${review.date}</div>
                        ${
                          review.carModel
                            ? `<div class="review-item-car">Автомобиль: ${review.carModel}</div>`
                            : ""
                        }
                        ${
                          review.author
                            ? `<div class="review-item-author">Автор: ${review.author}</div>`
                            : ""
                        }
                        <div class="review-item-actions">
                            <button type="button" class="edit-btn" data-id="${
                              review.id
                            }">Редактировать</button>
                            <button type="button" class="delete-btn" data-id="${
                              review.id
                            }">Удалить</button>
                        </div>
                    </div>
                `;

          // Обработчик кнопки редактирования
          const editBtn = reviewElement.querySelector(".edit-btn");
          editBtn.addEventListener("click", function () {
            editReview(review.id);
          });

          // Обработчик кнопки удаления
          const deleteBtn = reviewElement.querySelector(".delete-btn");
          deleteBtn.addEventListener("click", function () {
            showDeleteConfirmation(review.id);
          });

          return reviewElement;
        }

        // Функция редактирования отзыва
        function editReview(reviewId) {
          fetch(`/api/reviews/${reviewId}`)
            .then((response) => response.json())
            .then((review) => {
              reviewIdInput.value = review.id;
              titleInput.value = review.title;
              dateInput.value = review.date;
              carModelInput.value = review.carModel || "";
              authorInput.value = review.author || "";
              imagePathInput.value = review.image;
              contentEditor.value = review.content;
              contentPreview.innerHTML = review.content;

              // Обновляем предпросмотр изображения
              imagePreview.innerHTML = `<img src="${review.image}" alt="${review.title}">`;

              // Обновляем заголовок формы и текст кнопки
              formTitle.textContent = "Редактировать отзыв";
              submitBtn.textContent = "Сохранить изменения";

              // Прокручиваем к форме
              reviewForm.scrollIntoView({ behavior: "smooth" });
            })
            .catch((error) => {
              alert("Ошибка при загрузке отзыва: " + error.message);
            });
        }

        // Функция сброса формы
        function resetForm() {
          reviewForm.reset();
          reviewIdInput.value = "";
          imagePathInput.value = "";
          contentInput.value = "";
          contentEditor.value = "";
          contentPreview.innerHTML = "";
          imagePreview.innerHTML =
            '<div class="preview-placeholder">Предпросмотр изображения</div>';

          // Возвращаем исходные заголовок и текст кнопки
          formTitle.textContent = "Добавить новый отзыв";
          submitBtn.textContent = "Добавить отзыв";

          // Устанавливаем текущую дату
          dateInput.value = today;
        }

        // Обработчик кнопки сброса формы
        resetBtn.addEventListener("click", resetForm);

        // Обработчик изменения изображения
        imageInput.addEventListener("change", function (e) {
          if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function (e) {
              imagePreview.innerHTML = `<img src="${e.target.result}" alt="Предпросмотр">`;
            };
            reader.readAsDataURL(this.files[0]);
          }
        });

        // Обработчик изменения содержания
        contentEditor.addEventListener("input", function () {
          contentPreview.innerHTML = this.value;
        });

        // Обработчики кнопок форматирования текста
        document
          .querySelectorAll(".editor-toolbar button")
          .forEach((button) => {
            button.addEventListener("click", function () {
              const action = this.getAttribute("data-action");
              const editor = contentEditor;

              const start = editor.selectionStart;
              const end = editor.selectionEnd;
              const selectedText = editor.value.substring(start, end);

              let replacement = "";
              switch (action) {
                case "bold":
                  replacement = `<strong>${selectedText}</strong>`;
                  break;
                case "italic":
                  replacement = `<em>${selectedText}</em>`;
                  break;
                case "paragraph":
                  replacement = `<p>${selectedText}</p>`;
                  break;
              }

              editor.value =
                editor.value.substring(0, start) +
                replacement +
                editor.value.substring(end);
              contentPreview.innerHTML = editor.value;

              // Восстанавливаем фокус и позицию курсора
              editor.focus();
              editor.selectionStart = start + replacement.length;
              editor.selectionEnd = start + replacement.length;
            });
          });

        // Функция отображения диалога подтверждения удаления
        function showDeleteConfirmation(reviewId) {
          reviewToDelete = reviewId;
          deleteConfirmation.classList.add("active");
        }

        // Обработчик отмены удаления
        cancelDelete.addEventListener("click", function () {
          deleteConfirmation.classList.remove("active");
          reviewToDelete = null;
        });

        // Обработчик подтверждения удаления
        confirmDelete.addEventListener("click", function () {
          if (reviewToDelete) {
            deleteReview(reviewToDelete);
            deleteConfirmation.classList.remove("active");
            reviewToDelete = null;
          }
        });

        // Функция удаления отзыва
        function deleteReview(reviewId) {
          fetch(`/api/reviews/${reviewId}`, {
            method: "DELETE",
          })
            .then((response) => response.json())
            .then((data) => {
              if (data.success) {
                alert("Отзыв успешно удален");
                loadReviews();
              } else {
                throw new Error(data.error || "Ошибка при удалении отзыва");
              }
            })
            .catch((error) => {
              alert("Ошибка: " + error.message);
            });
        }
      });
    </script>

    <!-- Mobile Menu JavaScript -->
    <script>
      // Mobile menu functionality
      document.addEventListener("DOMContentLoaded", function () {
        const burgerBtn = document.getElementById("adminBurgerBtn");
        const dropdownMenu = document.getElementById("adminDropdownMenu");

        if (burgerBtn && dropdownMenu) {
          burgerBtn.addEventListener("click", function () {
            burgerBtn.classList.toggle("open");
            dropdownMenu.classList.toggle("active");
          });

          // Close menu when clicking outside
          document.addEventListener("click", function (e) {
            if (
              !burgerBtn.contains(e.target) &&
              !dropdownMenu.contains(e.target)
            ) {
              burgerBtn.classList.remove("open");
              dropdownMenu.classList.remove("active");
            }
          });
        }
      });
    </script>
  </body>
</html>
