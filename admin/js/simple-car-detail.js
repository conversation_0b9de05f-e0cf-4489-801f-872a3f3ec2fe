// Simple Car Detail Editor
document.addEventListener("DOMContentLoaded", function () {
  // State
  let selectedCar = null;
  let uploadedImages = [];

  // Elements
  const carSelect = document.getElementById("car-select");
  const loadCarBtn = document.getElementById("load-car-btn");
  const selectedCarPreview = document.getElementById("selected-car-preview");
  const detailFormSection = document.getElementById("detail-form-section");
  const gallerySection = document.getElementById("gallery-section");
  const uploadZone = document.getElementById("upload-zone");
  const imageInput = document.getElementById("image-input");
  const uploadedImagesContainer = document.getElementById("uploaded-images");
  const previewBtn = document.getElementById("preview-btn");
  const saveDraftBtn = document.getElementById("save-draft-btn");
  const publishBtn = document.getElementById("publish-btn");
  const generateHtmlBtn = document.getElementById("generate-html-btn");

  // Initialize
  loadCars();
  loadExistingPages();
  setupEventListeners();

  // Check for car ID in URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const carId = urlParams.get("car");
  if (carId) {
    // Auto-select car after cars are loaded
    setTimeout(() => {
      carSelect.value = carId;
      loadSelectedCar();
    }, 500);
  }

  // Load cars from API
  async function loadCars() {
    try {
      const response = await fetch("/api/cars");
      const cars = await response.json();

      carSelect.innerHTML =
        '<option value="">-- Выберите автомобиль --</option>';

      cars.forEach((car) => {
        const option = document.createElement("option");
        option.value = car.id;
        option.textContent = `${car.title} - ${car.price}`;
        carSelect.appendChild(option);
      });
    } catch (error) {
      console.error("Ошибка загрузки автомобилей:", error);
      showNotification(
        "Ошибка",
        "Не удалось загрузить список автомобилей",
        "error"
      );
    }
  }

  // Setup event listeners
  function setupEventListeners() {
    // Car selection
    loadCarBtn.addEventListener("click", loadSelectedCar);
    carSelect.addEventListener("change", function () {
      if (this.value) {
        loadCarBtn.disabled = false;
      } else {
        loadCarBtn.disabled = true;
        hideAllSections();
      }
    });

    // Image upload
    uploadZone.addEventListener("click", () => imageInput.click());
    uploadZone.addEventListener("dragover", handleDragOver);
    uploadZone.addEventListener("drop", handleDrop);
    imageInput.addEventListener("change", handleFileSelect);

    // Action buttons
    previewBtn.addEventListener("click", showPreview);
    saveDraftBtn.addEventListener("click", saveDraft);
    publishBtn.addEventListener("click", publishPage);
    generateHtmlBtn.addEventListener("click", generateHtmlForExisting);

    // Form validation
    const form = document.getElementById("simple-detail-form");
    const inputs = form.querySelectorAll("input, textarea");
    inputs.forEach((input) => {
      input.addEventListener("input", validateForm);
    });

    // Modal close
    document
      .getElementById("close-preview")
      .addEventListener("click", closePreview);
    document
      .getElementById("preview-modal")
      .addEventListener("click", function (e) {
        if (e.target === this) closePreview();
      });
  }

  // Load selected car data
  async function loadSelectedCar() {
    const carId = carSelect.value;
    if (!carId) return;

    try {
      loadCarBtn.innerHTML =
        '<i class="fas fa-spinner fa-spin"></i> Загрузка...';
      loadCarBtn.disabled = true;

      const response = await fetch(`/api/cars/${carId}`);
      const car = await response.json();

      selectedCar = car;
      displayCarPreview(car);
      prefillForm(car);
      showFormSections();

      // Check if detail page already exists
      await checkExistingDetailPage(carId);

      showNotification("Успех", "Данные автомобиля загружены", "success");
    } catch (error) {
      console.error("Ошибка загрузки автомобиля:", error);
      showNotification(
        "Ошибка",
        "Не удалось загрузить данные автомобиля",
        "error"
      );
    } finally {
      loadCarBtn.innerHTML = '<i class="fas fa-download"></i> Загрузить данные';
      loadCarBtn.disabled = false;
    }
  }

  // Display car preview
  function displayCarPreview(car) {
    document.getElementById("preview-image").src =
      car.image_path || "/assets/img/cars/default-car.jpg";
    document.getElementById("preview-title").textContent = car.title;
    document.getElementById("preview-subtitle").textContent = car.subtitle;
    document.getElementById("preview-power").textContent = car.power;
    document.getElementById("preview-mileage").textContent = car.mileage;
    document.getElementById("preview-price").textContent = car.price;

    selectedCarPreview.style.display = "block";
  }

  // Prefill form with car data
  function prefillForm(car) {
    document.getElementById("car-id").value = car.id;
    document.getElementById("full-title").value = car.title;
    document.getElementById("price").value = car.price
      .replace("$", "")
      .replace(",", "");

    // Auto-generate description template
    const description = `Представляем ${car.title} - воплощение совершенства в автомобильной индустрии.

Этот автомобиль сочетает в себе передовые технологии, роскошь и непревзойденную производительность.

Основные характеристики:
• Мощность: ${car.power}
• Пробег: ${car.mileage}
• Тип кузова: ${car.body_type}
• Двигатель: ${car.engine}
• Трансмиссия: ${car.transmission}

Идеальный выбор для тех, кто ценит качество, комфорт и стиль.`;

    document.getElementById("description").value = description;

    // Auto-generate specs
    const specs = `Двигатель: ${car.engine}
Мощность: ${car.power}
Трансмиссия: ${car.transmission}
Тип кузова: ${car.body_type}
Пробег: ${car.mileage}
Расход топлива: ${car.consumption}
Вместимость: ${car.capacity}`;

    document.getElementById("specs").value = specs;

    // Auto-generate main features
    const mainFeatures = `**${car.engine}**, ${car.power}
Полный привод quattro с активными дифференциалами
Разгон 0-100 км/ч - 3.4 секунды
Керамические тормоза (опция)`;

    document.getElementById("main-features").value = mainFeatures;

    // Auto-generate additional features
    const additionalFeatures = `Динамическая подвеска RS
Матрица LED-фары с лазерной технологией
Спортивная выхлопная система RS
Аудиосистема Bang & Olufsen 3D Premium`;

    document.getElementById("additional-features").value = additionalFeatures;

    validateForm();
  }

  // Show form sections
  function showFormSections() {
    detailFormSection.style.display = "block";
    gallerySection.style.display = "block";
  }

  // Hide all sections
  function hideAllSections() {
    selectedCarPreview.style.display = "none";
    detailFormSection.style.display = "none";
    gallerySection.style.display = "none";
    disableActionButtons();
  }

  // Image upload handlers
  function handleDragOver(e) {
    e.preventDefault();
    uploadZone.classList.add("dragover");
  }

  function handleDrop(e) {
    e.preventDefault();
    uploadZone.classList.remove("dragover");
    const files = Array.from(e.dataTransfer.files);
    processFiles(files);
  }

  function handleFileSelect(e) {
    const files = Array.from(e.target.files);
    processFiles(files);
  }

  // Process uploaded files
  function processFiles(files) {
    const imageFiles = files.filter((file) => file.type.startsWith("image/"));

    if (imageFiles.length === 0) {
      showNotification(
        "Предупреждение",
        "Выберите файлы изображений",
        "warning"
      );
      return;
    }

    imageFiles.forEach((file) => {
      if (file.size > 10 * 1024 * 1024) {
        // 10MB limit
        showNotification(
          "Ошибка",
          `Файл ${file.name} слишком большой (макс. 10MB)`,
          "error"
        );
        return;
      }

      const reader = new FileReader();
      reader.onload = function (e) {
        const imageData = {
          file: file,
          url: e.target.result,
          name: file.name,
        };

        uploadedImages.push(imageData);
        displayUploadedImage(imageData, uploadedImages.length - 1);
        validateForm();
      };
      reader.readAsDataURL(file);
    });
  }

  // Display uploaded image
  function displayUploadedImage(imageData, index) {
    // Проверяем валидность данных изображения
    if (
      !imageData ||
      !imageData.url ||
      typeof imageData.url !== "string" ||
      imageData.url.trim() === "" ||
      imageData.url.length > 500 // Дополнительная проверка на разумную длину URL
    ) {
      console.warn("Некорректные данные изображения:", imageData);
      return;
    }

    // Проверяем, что индекс валидный
    if (typeof index !== "number" || index < 0 || index > 50) {
      console.warn("Некорректный индекс изображения:", index);
      return;
    }

    // Проверяем, что контейнер существует
    if (!uploadedImagesContainer) {
      console.error("Контейнер для изображений не найден");
      return;
    }

    const imageDiv = document.createElement("div");
    imageDiv.className = `uploaded-image ${index === 0 ? "main-image" : ""}`;
    imageDiv.draggable = true;
    imageDiv.dataset.index = index;

    // Создаем img элемент с обработкой ошибок
    const img = document.createElement("img");
    img.src = imageData.url;
    img.alt = imageData.name || `Изображение ${index + 1}`;
    img.loading = "lazy"; // Ленивая загрузка для производительности
    img.onerror = function () {
      console.error("Ошибка загрузки изображения:", imageData.url);
      this.src = "/PNG/SHMS_Logo_Black.png"; // Заглушка
      this.style.opacity = "0.5";
    };

    imageDiv.innerHTML = `
            <div class="image-order-badge">${index + 1}</div>
            <div class="image-actions">
                <button type="button" class="image-action-btn" onclick="removeImage(${index})" title="Удалить">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;

    // Вставляем img в начало
    imageDiv.insertBefore(img, imageDiv.firstChild);

    // Add drag event listeners
    imageDiv.addEventListener("dragstart", handleDragStart);
    imageDiv.addEventListener("dragover", handleDragOver);
    imageDiv.addEventListener("drop", handleDrop);
    imageDiv.addEventListener("dragend", handleDragEnd);

    uploadedImagesContainer.appendChild(imageDiv);
  }

  // Remove image (global function)
  window.removeImage = function (index) {
    uploadedImages.splice(index, 1);
    refreshImageGallery();
    validateForm();
  };

  // Refresh image gallery
  function refreshImageGallery() {
    if (!uploadedImagesContainer) {
      console.error("Контейнер для изображений не найден");
      return;
    }

    uploadedImagesContainer.innerHTML = "";

    // Проверяем, что uploadedImages - это массив
    if (!Array.isArray(uploadedImages)) {
      console.error("uploadedImages не является массивом");
      uploadedImages = [];
      return;
    }

    // Ограничиваем количество изображений для безопасности
    const maxImages = Math.min(uploadedImages.length, 20);

    for (let i = 0; i < maxImages; i++) {
      const imageData = uploadedImages[i];
      if (imageData && typeof imageData === "object") {
        displayUploadedImage(imageData, i);
      }
    }
  }

  // Drag and drop functionality
  let draggedElement = null;

  function handleDragStart(e) {
    draggedElement = this;
    this.classList.add("dragging");
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("text/html", this.outerHTML);
  }

  function handleDragOver(e) {
    if (e.preventDefault) {
      e.preventDefault();
    }
    e.dataTransfer.dropEffect = "move";
    return false;
  }

  function handleDrop(e) {
    if (e.stopPropagation) {
      e.stopPropagation();
    }

    if (draggedElement !== this) {
      const draggedIndex = parseInt(draggedElement.dataset.index);
      const targetIndex = parseInt(this.dataset.index);

      // Swap images in array
      const draggedImage = uploadedImages[draggedIndex];
      uploadedImages.splice(draggedIndex, 1);
      uploadedImages.splice(targetIndex, 0, draggedImage);

      // Refresh gallery
      refreshImageGallery();
      validateForm();
    }

    return false;
  }

  function handleDragEnd(e) {
    this.classList.remove("dragging");
    draggedElement = null;
  }

  // Form validation
  function validateForm() {
    const fullTitle = document.getElementById("full-title").value.trim();
    const description = document.getElementById("description").value.trim();
    const specs = document.getElementById("specs").value.trim();
    const mainFeatures = document.getElementById("main-features").value.trim();

    const isValid =
      selectedCar && fullTitle && description && specs && mainFeatures;

    if (isValid) {
      enableActionButtons();
    } else {
      disableActionButtons();
    }
  }

  // Enable action buttons
  function enableActionButtons() {
    previewBtn.disabled = false;
    saveDraftBtn.disabled = false;
    publishBtn.disabled = false;
  }

  // Disable action buttons
  function disableActionButtons() {
    previewBtn.disabled = true;
    saveDraftBtn.disabled = true;
    publishBtn.disabled = true;
  }

  // Show preview
  async function showPreview() {
    try {
      const formData = getFormData();

      const response = await fetch("/api/car-details/preview", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.html) {
        const previewFrame = document.getElementById("preview-frame");
        const blob = new Blob([result.html], { type: "text/html" });
        const url = URL.createObjectURL(blob);
        previewFrame.src = url;

        document.getElementById("preview-modal").style.display = "flex";
      }
    } catch (error) {
      console.error("Ошибка предпросмотра:", error);
      showNotification("Ошибка", "Не удалось создать предпросмотр", "error");
    }
  }

  // Close preview
  function closePreview() {
    document.getElementById("preview-modal").style.display = "none";
    const previewFrame = document.getElementById("preview-frame");
    previewFrame.src = "";
  }

  // Save draft
  async function saveDraft() {
    const success = await saveCarDetail("draft");
    if (success) {
      // Generate HTML page for draft as well
      const detailId = document.getElementById("detail-id").value;
      if (detailId) {
        await generateHtmlPage(detailId);
      }
    }
  }

  // Publish page
  async function publishPage() {
    const success = await saveCarDetail("published");
    if (success) {
      // Generate HTML page
      const detailId = document.getElementById("detail-id").value;
      if (detailId) {
        await generateHtmlPage(detailId);
      }
    }
  }

  // Save car detail
  async function saveCarDetail(status) {
    try {
      const formData = new FormData();
      const data = getFormData();

      // Add form data
      Object.keys(data).forEach((key) => {
        if (key !== "images") {
          formData.append(key, data[key]);
        }
      });

      // Add status
      formData.append("status", status);

      // Add images
      uploadedImages.forEach((imageData, index) => {
        formData.append("detail_images", imageData.file);
      });

      const response = await fetch("/api/car-details", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (response.ok) {
        document.getElementById("detail-id").value = result.id;
        showNotification(
          "Успех",
          status === "draft"
            ? "Черновик сохранен"
            : "Детальная страница создана",
          "success"
        );
        return true;
      } else {
        throw new Error(result.error || "Ошибка сохранения");
      }
    } catch (error) {
      console.error("Ошибка сохранения:", error);
      showNotification("Ошибка", error.message, "error");
      return false;
    }
  }

  // Check if detail page already exists for this car
  async function checkExistingDetailPage(carId) {
    try {
      const response = await fetch(`/api/car-details/car/${carId}`);
      if (response.ok) {
        const detail = await response.json();
        if (detail) {
          // Show generate HTML button
          generateHtmlBtn.style.display = "inline-block";
          generateHtmlBtn.disabled = false;
          generateHtmlBtn.dataset.detailId = detail.id;

          // Hide create page button and show warning
          publishBtn.style.display = "none";
          saveDraftBtn.style.display = "none";

          showNotification(
            "Информация",
            `Детальная страница уже существует (ID: ${detail.id}). Используйте кнопку "Создать HTML" для генерации файла.`,
            "info"
          );
        } else {
          // No existing detail page, show normal buttons
          generateHtmlBtn.style.display = "none";
          publishBtn.style.display = "inline-block";
          saveDraftBtn.style.display = "inline-block";
        }
      }
    } catch (error) {
      console.error("Ошибка проверки существующей страницы:", error);
      // Show normal buttons on error
      generateHtmlBtn.style.display = "none";
      publishBtn.style.display = "inline-block";
      saveDraftBtn.style.display = "inline-block";
    }
  }

  // Generate HTML for existing detail page
  async function generateHtmlForExisting() {
    const detailId = generateHtmlBtn.dataset.detailId;
    if (!detailId) {
      showNotification("Ошибка", "ID детальной страницы не найден", "error");
      return;
    }

    await generateHtmlPage(detailId);
  }

  // Generate HTML page
  async function generateHtmlPage(detailId) {
    try {
      const response = await fetch(`/api/car-details/${detailId}/generate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ outputPath: "cars" }),
      });

      const result = await response.json();

      if (response.ok) {
        showNotification(
          "Успех",
          `HTML страница создана: ${result.filePath}`,
          "success"
        );

        // Ask if user wants to open the page
        if (confirm("Открыть созданную страницу?")) {
          window.open(result.filePath, "_blank");
        }
      } else {
        throw new Error(result.error || "Ошибка генерации HTML");
      }
    } catch (error) {
      console.error("Ошибка генерации HTML:", error);
      showNotification("Ошибка", error.message, "error");
    }
  }

  // Get form data
  function getFormData() {
    return {
      car_id: document.getElementById("car-id").value,
      full_title: document.getElementById("full-title").value,
      price: document.getElementById("price").value,
      moscow_price: document.getElementById("moscow-price").value,
      description: document.getElementById("description").value,
      specs: document.getElementById("specs").value,
      main_features: document.getElementById("main-features").value,
      additional_features: document.getElementById("additional-features").value,
      car_interior_colors: selectedCar ? selectedCar.interior_colors : "",
      images: uploadedImages.map((img) => ({ path: img.url, name: img.name })),
    };
  }

  // Generate HTML page using the exact template
  async function generateCarDetailPage(carData, detailData, images) {
    try {
      // Load the template
      const response = await fetch("/admin/templates/car-detail-template.html");
      let template = await response.text();

      // Prepare image data
      const mainImage = images.length > 0 ? images[0].url : carData.image;
      const additionalImages = images.slice(1, 5); // Take up to 4 additional images (excluding main)
      const allImages = images.slice(0, 5); // All images for carousel (including main)
      const imageArray = allImages.map((img) => img.url);

      // Generate additional images HTML
      const additionalImagesHtml = additionalImages
        .map(
          (img, index) =>
            `<div class="additional-image" onclick="openFullscreen(${
              index + 1
            })">
           <img src="${img.url}" alt="${detailData.full_title}">
         </div>`
        )
        .join("");

      // Generate specifications HTML
      const specificationsHtml = detailData.specs
        ? detailData.specs
            .split("\n")
            .filter((spec) => spec.trim())
            .map((spec) => {
              const [label, value] = spec.split(":").map((s) => s.trim());
              if (label && value) {
                // Handle color values specially
                if (
                  label.toLowerCase().includes("цвет") &&
                  value.includes("#")
                ) {
                  const colors = value.split(",").map((color) => color.trim());
                  const colorDots = colors
                    .map((color) => {
                      const colorCode = color.match(/#[0-9A-Fa-f]{6}/);
                      return colorCode
                        ? `<span class="color-dot" style="background-color: ${colorCode[0]}"></span>`
                        : "";
                    })
                    .join("");
                  return `<div class="spec-row">
                        <span class="spec-label">${label}</span>
                        <span class="spec-value">
                          <div class="color-dots">${colorDots}</div>
                        </span>
                      </div>`;
                }
                return `<div class="spec-row">
                      <span class="spec-label">${label}</span>
                      <span class="spec-value">${value}</span>
                    </div>`;
              }
              return "";
            })
            .join("")
        : "";

      // Generate features HTML
      const mainFeaturesArray = detailData.main_features
        ? detailData.main_features
            .split("\n")
            .filter((feature) => feature.trim())
        : [];

      const additionalFeaturesArray = detailData.additional_features
        ? detailData.additional_features
            .split("\n")
            .filter((feature) => feature.trim())
        : [];

      const mainFeatures = mainFeaturesArray
        .map((feature) => `<li>${formatDescription(feature)}</li>`)
        .join("");

      const additionalFeatures = additionalFeaturesArray
        .map((feature) => `<li>${formatDescription(feature)}</li>`)
        .join("");

      // Replace template placeholders
      template = template
        .replace(/\{\{FULL_TITLE\}\}/g, detailData.full_title || carData.title)
        .replace(
          /\{\{CAR_SUBTITLE\}\}/g,
          `Цена в Москве: ${detailData.moscow_price || "По запросу"}`
        )
        .replace(/\{\{PRICE\}\}/g, detailData.price || carData.price)
        .replace(
          /\{\{MOSCOW_PRICE\}\}/g,
          `Цена в Москве по всем документам: ${
            detailData.moscow_price || "По запросу"
          }`
        )
        .replace(/\{\{MAIN_IMAGE\}\}/g, mainImage)
        .replace(
          /\{\{DESCRIPTION\}\}/g,
          formatDescription(detailData.description || "")
        )
        .replace(/\{\{ADDITIONAL_IMAGES\}\}/g, additionalImagesHtml)
        .replace(/\{\{CAR_TYPE\}\}/g, carData.subtitle || "Универсал")
        .replace(/\{\{POWER\}\}/g, carData.power || "Мощность")
        .replace(/\{\{MILEAGE\}\}/g, carData.mileage || "Пробег")
        .replace(/\{\{SPECIFICATIONS\}\}/g, specificationsHtml)
        .replace(/\{\{MAIN_FEATURES\}\}/g, mainFeatures)
        .replace(/\{\{ADDITIONAL_FEATURES\}\}/g, additionalFeatures)
        .replace(
          /\{\{IMAGE_ARRAY\}\}/g,
          imageArray.map((url) => `"${url}"`).join(", ")
        );

      return template;
    } catch (error) {
      console.error("Error loading template:", error);
      return generateFallbackTemplate(carData, detailData, images);
    }
  }

  // Format description with bold text support
  function formatDescription(description) {
    if (!description) return "";

    return description
      .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>") // **bold** -> <strong>bold</strong>
      .replace(/\n/g, "<br>") // Line breaks
      .replace(/(\d+[\.,]\d+[\s]*л[\s]*с\.)/gi, "<strong>$1</strong>") // Auto-bold power values
      .replace(/(Quattro|AWD|4WD)/gi, "<strong>$1</strong>") // Auto-bold drive types
      .replace(/(\d+[\s]*км\/ч)/gi, "<strong>$1</strong>"); // Auto-bold speed values
  }

  // Fallback template if main template fails to load
  function generateFallbackTemplate(carData, detailData, images) {
    const mainImage = images.length > 0 ? images[0].url : carData.image;

    return `<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${detailData.full_title} | SHMS Auto</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .title { font-size: 2.5rem; font-weight: 700; margin-bottom: 10px; }
        .price { font-size: 1.5rem; color: #666; }
        .main-image { width: 100%; max-height: 500px; object-fit: cover; border-radius: 12px; margin-bottom: 30px; }
        .content { line-height: 1.6; }
        .section { margin-bottom: 30px; }
        .section h2 { font-size: 1.8rem; margin-bottom: 15px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">${detailData.full_title}</h1>
            <div class="price">${detailData.price}</div>
        </div>
        <img class="main-image" src="${mainImage}" alt="${detailData.full_title}">
        <div class="content">
            <div class="section">
                <h2>Описание</h2>
                <p>${formatDescription(detailData.description || "")}</p>
            </div>
        </div>
    </div>
</body>
</html>`;
  }

  // Show notification
  function showNotification(title, message, type = "success") {
    const notification = document.getElementById("notification");
    const notificationContent = notification.querySelector(
      ".notification-content"
    );
    const notificationIcon = notification.querySelector(".notification-icon i");
    const notificationTitle = notification.querySelector(".notification-title");
    const notificationText = notification.querySelector(".notification-text");

    // Set content
    notificationTitle.textContent = title;
    notificationText.textContent = message;

    // Set type
    notificationContent.className = `notification-content ${type}`;

    // Set icon
    const icons = {
      success: "fas fa-check-circle",
      error: "fas fa-exclamation-circle",
      warning: "fas fa-exclamation-triangle",
      info: "fas fa-info-circle",
    };
    notificationIcon.className = icons[type] || icons.success;

    // Show notification
    notification.classList.add("show");

    // Auto hide after 5 seconds
    setTimeout(() => {
      notification.classList.remove("show");
    }, 5000);

    // Close button
    notification.querySelector(".notification-close").onclick = () => {
      notification.classList.remove("show");
    };
  }

  // Load existing pages
  async function loadExistingPages() {
    const loadingState = document.getElementById("pages-loading");
    const emptyState = document.getElementById("pages-empty");
    const pagesList = document.getElementById("existing-pages-list");

    try {
      loadingState.style.display = "flex";
      emptyState.style.display = "none";

      const response = await fetch("/api/car-details");
      const pages = await response.json();

      loadingState.style.display = "none";

      if (pages.length === 0) {
        emptyState.style.display = "flex";
        return;
      }

      displayExistingPages(pages);
    } catch (error) {
      console.error("Ошибка загрузки страниц:", error);
      loadingState.style.display = "none";
      emptyState.style.display = "flex";
      showNotification(
        "Ошибка",
        "Не удалось загрузить список страниц",
        "error"
      );
    }
  }

  // Display existing pages
  function displayExistingPages(pages) {
    const pagesList = document.getElementById("existing-pages-list");
    const loadingState = document.getElementById("pages-loading");
    const emptyState = document.getElementById("pages-empty");

    // Clear existing content
    const existingItems = pagesList.querySelectorAll(".page-item");
    existingItems.forEach((item) => item.remove());

    pages.forEach((page) => {
      const pageItem = createPageItem(page);
      pagesList.appendChild(pageItem);
    });
  }

  // Create page item element
  function createPageItem(page) {
    const pageItem = document.createElement("div");
    pageItem.className = "page-item";

    const createdDate = new Date(page.created_at).toLocaleDateString("ru-RU");
    const updatedDate = new Date(page.updated_at).toLocaleDateString("ru-RU");

    pageItem.innerHTML = `
      <div class="page-item-header">
        <div class="page-item-info">
          <h3>${page.full_title || "Без названия"}</h3>
          <p>${
            page.description
              ? page.description.substring(0, 100) + "..."
              : "Описание отсутствует"
          }</p>
        </div>
        <div class="page-status ${page.status}">${
      page.status === "draft" ? "Черновик" : "Опубликовано"
    }</div>
      </div>

      <div class="page-item-meta">
        <span><i class="fas fa-calendar-plus"></i> Создано: ${createdDate}</span>
        <span><i class="fas fa-calendar-edit"></i> Обновлено: ${updatedDate}</span>
        <span><i class="fas fa-car"></i> ID автомобиля: ${page.car_id}</span>
      </div>

      <div class="page-item-actions">
        <button class="page-action-btn edit" onclick="editPage(${page.id})">
          <i class="fas fa-edit"></i> Редактировать
        </button>
        ${
          page.status === "published"
            ? `
          <a href="/cars/${
            page.slug || page.id
          }.html" target="_blank" class="page-action-btn view">
            <i class="fas fa-eye"></i> Просмотр
          </a>
        `
            : ""
        }
        <button class="page-action-btn regenerate" onclick="regeneratePage(${
          page.id
        })">
          <i class="fas fa-sync-alt"></i> Обновить HTML
        </button>
        ${
          page.status === "draft"
            ? `
          <button class="page-action-btn publish" onclick="publishExistingPage(${page.id})">
            <i class="fas fa-globe"></i> Опубликовать
          </button>
        `
            : `
          <button class="page-action-btn unpublish" onclick="unpublishPage(${page.id})">
            <i class="fas fa-eye-slash"></i> Снять с публикации
          </button>
        `
        }
        <button class="page-action-btn delete" onclick="deletePage(${page.id})">
          <i class="fas fa-trash"></i> Удалить
        </button>
      </div>
    `;

    return pageItem;
  }

  // Setup existing pages event listeners
  function setupExistingPagesListeners() {
    const refreshBtn = document.getElementById("refresh-pages-btn");
    const statusFilter = document.getElementById("status-filter");

    if (refreshBtn) {
      refreshBtn.addEventListener("click", loadExistingPages);
    }

    if (statusFilter) {
      statusFilter.addEventListener("change", filterPages);
    }
  }

  // Filter pages by status
  function filterPages() {
    const statusFilter = document.getElementById("status-filter");
    const selectedStatus = statusFilter.value;
    const pageItems = document.querySelectorAll(".page-item");

    pageItems.forEach((item) => {
      const status = item
        .querySelector(".page-status")
        .classList.contains("draft")
        ? "draft"
        : "published";

      if (selectedStatus === "" || status === selectedStatus) {
        item.style.display = "block";
      } else {
        item.style.display = "none";
      }
    });
  }

  // Add existing pages listeners to setup
  const originalSetupEventListeners = setupEventListeners;
  setupEventListeners = function () {
    originalSetupEventListeners();
    setupExistingPagesListeners();
  };

  // Global functions for page management
  window.editPage = async function (pageId) {
    try {
      const response = await fetch(`/api/car-details/${pageId}`);
      const pageData = await response.json();

      // Проверяем и логируем информацию об изображениях
      if (pageData.images) {
        console.log("Данные изображений:", pageData.images);
        console.log("Тип данных изображений:", typeof pageData.images);
        console.log("Количество изображений:", pageData.images.length);

        if (pageData.images.length > 10) {
          console.warn(
            `Обнаружено ${pageData.images.length} изображений. Будет загружено только первые 10.`
          );
        }
      }

      // Load the car data first
      carSelect.value = pageData.car_id;
      await loadSelectedCar();

      // Fill form with existing data
      document.getElementById("detail-id").value = pageData.id;
      document.getElementById("full-title").value = pageData.full_title || "";
      document.getElementById("price").value = pageData.price || "";
      document.getElementById("moscow-price").value =
        pageData.moscow_price || "";
      document.getElementById("description").value = pageData.description || "";
      document.getElementById("specs").value = pageData.specifications || "";
      document.getElementById("main-features").value =
        pageData.main_features || "";
      document.getElementById("additional-features").value =
        pageData.additional_features || "";

      // Load existing images if any
      if (pageData.images) {
        uploadedImages = [];
        uploadedImagesContainer.innerHTML = "";

        // Обрабатываем изображения с дополнительной валидацией
        let imagesArray = [];

        try {
          // Если images - строка, пытаемся её распарсить
          if (typeof pageData.images === "string") {
            imagesArray = JSON.parse(pageData.images);
          } else if (Array.isArray(pageData.images)) {
            imagesArray = pageData.images;
          }
        } catch (e) {
          console.error("Ошибка парсинга изображений:", e);
          imagesArray = [];
        }

        // Проверяем, что у нас есть валидный массив
        if (Array.isArray(imagesArray) && imagesArray.length > 0) {
          // Ограничиваем количество изображений максимум 10 штук
          const maxImages = Math.min(imagesArray.length, 10);

          for (let i = 0; i < maxImages; i++) {
            const imageItem = imagesArray[i];
            let imagePath = "";

            // Обрабатываем разные форматы данных изображений
            if (typeof imageItem === "string") {
              imagePath = imageItem;
            } else if (imageItem && typeof imageItem === "object") {
              imagePath = imageItem.path || imageItem.url || "";
            }

            // Проверяем, что путь к изображению валидный
            if (
              imagePath &&
              typeof imagePath === "string" &&
              imagePath.trim() !== "" &&
              imagePath.length < 500 // Дополнительная проверка на разумную длину URL
            ) {
              try {
                const imageData = {
                  url: imagePath,
                  name: `image_${i + 1}.jpg`,
                  existing: true,
                };
                uploadedImages.push(imageData);
                displayUploadedImage(imageData, i);
              } catch (error) {
                console.error("Ошибка загрузки изображения:", error);
              }
            }
          }

          console.log(
            `Загружено ${uploadedImages.length} изображений из ${imagesArray.length}`
          );
        } else {
          console.log("Нет валидных изображений для загрузки");
        }
      }

      validateForm();
      showNotification(
        "Успех",
        "Данные страницы загружены для редактирования",
        "success"
      );

      // Scroll to form
      document
        .getElementById("detail-form-section")
        .scrollIntoView({ behavior: "smooth" });
    } catch (error) {
      console.error("Ошибка загрузки страницы:", error);
      showNotification(
        "Ошибка",
        "Не удалось загрузить данные страницы",
        "error"
      );
    }
  };

  window.regeneratePage = async function (pageId) {
    try {
      const response = await fetch(`/api/car-details/${pageId}/generate`, {
        method: "POST",
      });

      const result = await response.json();

      if (response.ok) {
        showNotification("Успех", "HTML страница обновлена", "success");
      } else {
        throw new Error(result.error || "Ошибка обновления");
      }
    } catch (error) {
      console.error("Ошибка обновления страницы:", error);
      showNotification("Ошибка", "Не удалось обновить HTML страницу", "error");
    }
  };

  window.publishExistingPage = async function (pageId) {
    try {
      const response = await fetch(`/api/car-details/${pageId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: "published" }),
      });

      const result = await response.json();

      if (response.ok) {
        // Generate HTML page
        await regeneratePage(pageId);
        loadExistingPages(); // Refresh list
        showNotification("Успех", "Страница опубликована", "success");
      } else {
        throw new Error(result.error || "Ошибка публикации");
      }
    } catch (error) {
      console.error("Ошибка публикации:", error);
      showNotification("Ошибка", "Не удалось опубликовать страницу", "error");
    }
  };

  window.unpublishPage = async function (pageId) {
    try {
      const response = await fetch(`/api/car-details/${pageId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: "draft" }),
      });

      const result = await response.json();

      if (response.ok) {
        loadExistingPages(); // Refresh list
        showNotification("Успех", "Страница снята с публикации", "success");
      } else {
        throw new Error(result.error || "Ошибка снятия с публикации");
      }
    } catch (error) {
      console.error("Ошибка снятия с публикации:", error);
      showNotification(
        "Ошибка",
        "Не удалось снять страницу с публикации",
        "error"
      );
    }
  };

  window.deletePage = async function (pageId) {
    if (
      !confirm(
        "Вы уверены, что хотите удалить эту страницу? Это действие нельзя отменить."
      )
    ) {
      return;
    }

    try {
      const response = await fetch(`/api/car-details/${pageId}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (response.ok) {
        loadExistingPages(); // Refresh list
        showNotification("Успех", "Страница удалена", "success");
      } else {
        throw new Error(result.error || "Ошибка удаления");
      }
    } catch (error) {
      console.error("Ошибка удаления:", error);
      showNotification("Ошибка", "Не удалось удалить страницу", "error");
    }
  };
});
