#!/usr/bin/env node

/**
 * Скрипт полной настройки системы авторизации для SHMS Auto
 * Выполняет:
 * 1. Настройку базы данных
 * 2. Создание первого администратора
 */

const path = require('path');
const fs = require('fs');

console.log('🚀 Настройка системы авторизации SHMS Auto');
console.log('============================================\n');

async function setupAuth() {
    try {
        // Шаг 1: Настройка базы данных
        console.log('📋 Шаг 1: Настройка базы данных...\n');
        
        const setupDbScript = path.join(__dirname, 'setup-auth-db.js');
        if (!fs.existsSync(setupDbScript)) {
            throw new Error('Файл setup-auth-db.js не найден');
        }
        
        // Запускаем настройку БД
        require('./setup-auth-db.js');
        
        // Ждем завершения настройки БД
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        console.log('\n📋 Шаг 2: Создание первого администратора...\n');
        
        // Шаг 2: Создание первого администратора
        const initAdminScript = path.join(__dirname, 'init-admin.js');
        if (!fs.existsSync(initAdminScript)) {
            throw new Error('Файл init-admin.js не найден');
        }
        
        const { createFirstAdmin } = require('./init-admin.js');
        await createFirstAdmin();
        
        console.log('\n✅ Настройка системы авторизации завершена!');
        console.log('\n📋 Что дальше:');
        console.log('1. Запустите сервер: npm start');
        console.log('2. Откройте админку: http://localhost:3000/admin');
        console.log('3. Войдите с созданными учетными данными');
        
    } catch (error) {
        console.error('❌ Ошибка при настройке системы авторизации:', error.message);
        process.exit(1);
    }
}

// Проверяем, запущен ли скрипт напрямую
if (require.main === module) {
    setupAuth().then(() => {
        process.exit(0);
    }).catch((error) => {
        console.error('Критическая ошибка:', error);
        process.exit(1);
    });
}

module.exports = { setupAuth };
