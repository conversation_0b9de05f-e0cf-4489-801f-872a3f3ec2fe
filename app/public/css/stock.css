/* === Общие стили и сброс === */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
body {
  font-family: "Inter", Arial, sans-serif;
  background: #fff;
  color: #111;
  min-height: 100vh;
  padding: 0;
  overflow-x: hidden;
}
a {
  color: inherit;
  text-decoration: none;
}
img {
  max-width: 100%;
  display: block;
}

/* === Header === */
.header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 110px;
  padding: 0 2vw;
  background: #fff;
  z-index: 100;
}
.header__logo-center {
  position: static;
  left: auto;
  top: auto;
  height: 100%;
  width: auto;
  transform: none;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  z-index: 3;
  pointer-events: auto;
  margin-right: 60px;
}
.header__logo-img {
  height: 56px;
  width: auto;
  display: block;
  background: #fff;
  vertical-align: middle;
  margin-top: 15px;
  transform: translateX(35px);
}
.header__logo-link {
  display: inline-flex;
  align-items: center;
  margin-right: 0;
}
.header__nav {
  display: flex;
  align-items: center;
  gap: 3.5rem;
}
.header__back-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #333;
  text-decoration: none;
  font-size: 1rem;
  transition: all 0.3s ease;
  padding: 0 12px;
  border-radius: 4px;
  height: 48px;
  line-height: 48px;
  position: static;
  background: none;
  z-index: 2;
}
.header__back-link:hover {
  color: #000;
  background: rgba(0, 0, 0, 0.05);
  transform: translateX(-2px);
}
.header__nav-link {
  font-weight: 700;
  font-size: 1.1rem;
  color: #111;
  transition: color 0.2s;
  padding: 0 0.5rem;
  display: flex;
  align-items: center;
}
.header__nav-link.active {
  color: #000;
  text-decoration: none;
  font-weight: 700;
}
.header__nav-link:hover {
  color: #6f9aab;
}
.header__center-group {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2.5rem;
}
.header__logo-link {
  display: flex;
  align-items: center;
  margin-right: 2.5rem;
}
.header__logo-img {
  height: 95px;
  width: auto;
  display: block;
  background: #fff;
  vertical-align: middle;
  align-self: center;
}
.header__nav {
  display: flex;
  align-items: center;
  gap: 3.5rem;
}
.header__nav-link {
  font-weight: 700;
  font-size: 1.1rem;
  color: #111;
  transition: color 0.2s;
  line-height: 95px;
  display: flex;
  align-items: center;
}

/* === Hero Section === */
.hero {
  background: #fff;
  position: relative;
  padding-bottom: 2.5rem;
}
.hero__content {
  text-align: right;
  padding: 2.5rem 2vw 0 2vw;
  position: relative;
  z-index: 2;
}
.hero__title {
  font-size: 3.2rem;
  font-weight: 800;
  margin-bottom: 1.2rem;
  text-align: left;
}
.hero__subtitle {
  font-size: 1.15rem;
  color: #444;
  margin-bottom: 1.5rem;
  text-align: left;
  max-width: 600px;
}
.hero__car-img {
  position: absolute;
  right: 2vw;
  top: 2.5rem;
  width: 40vw;
  min-width: 320px;
  max-width: 600px;
  height: auto;
  z-index: 1;
  pointer-events: none;
}
.hero__highlight {
  position: relative;
  margin-top: 10rem;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.12) 0%, #222 100%);
  min-height: 220px;
  display: flex;
  align-items: flex-end;
  padding: 2.5rem 0 2.5rem 2vw;
}
.highlight__info {
  color: #fff;
  max-width: 600px;
}
.highlight__title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.7rem;
}
.highlight__desc {
  font-size: 1.1rem;
  margin-bottom: 1.2rem;
}
.highlight__btn {
  display: inline-block;
  background: #111;
  color: #fff;
  font-weight: 700;
  border-radius: 8px;
  padding: 0.7em 2em;
  font-size: 1.1rem;
  border: none;
  cursor: pointer;
  transition: background 0.2s;
}
.highlight__btn:hover {
  background: #b2c9d6;
  color: #111;
}

/* === Фильтры === */
.filters {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  width: 100%;
  margin-top: 2rem;
  padding: 1rem 0;
  background: #fff;
  z-index: 10;
  position: relative;
  flex-direction: column;
}

.filter-btn {
  background: #fff;
  color: #111;
  border: 1.5px solid #e5e7eb;
  border-radius: 2em;
  padding: 1em 2.2em;
  font-size: 1.2rem;
  font-weight: 700;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}

/* Make the button smaller on desktop screens */
@media (min-width: 601px) {
  .filter-btn {
    padding: 0.7em 1.8em;
    font-size: 1rem;
  }
}

.filter-btn.active,
.filter-btn:hover {
  background: #222;
  color: #fff;
  border-color: #222;
}

.filters-row {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  max-width: 700px;
  justify-content: center;
  margin-bottom: 10px;
}

.search__form {
  width: 100%;
  max-width: 500px;
  border-radius: 12px;
  position: relative;
  margin: 0.7em 0;
  min-height: 48px;
}

.search__input {
  width: 100%;
  padding: 0.7em 3em 0.7em 1em;
  font-size: 1rem;
  border: 1.5px solid #e5e7eb;
  border-radius: 2em;
  outline: none;
  min-height: 48px;
  box-sizing: border-box;
}

.search__input:focus {
  border-color: #6f9aab;
  box-shadow: 0 0 0 2px rgba(111, 154, 171, 0.2);
}

.search__btn {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  border: none;
  background: #222;
  color: #fff;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.search__btn:hover {
  background: #444;
  transform: translateY(-50%) scale(1.05);
}

/* Улучшенная мобильная адаптация для формы поиска в stock.css */
@media (max-width: 768px) {
  .search__form {
    min-height: 56px;
  }
  .search__input {
    padding: 1em 3.5em 1em 1.2em;
    font-size: 1.1rem;
    min-height: 56px;
  }
  .search__btn {
    width: 42px;
    height: 42px;
    right: 8px;
  }
  .search__btn svg {
    width: 22px;
    height: 22px;
  }
}

@media (max-width: 600px) {
  .search__form {
    min-height: 58px;
  }
  .search__input {
    padding: 1.1em 3.8em 1.1em 1.3em;
    font-size: 1.1rem;
    min-height: 58px;
  }
  .search__btn {
    width: 44px;
    height: 44px;
    right: 7px;
  }
  .search__btn svg {
    width: 24px;
    height: 24px;
  }
}

@media (max-width: 480px) {
  .search__form {
    min-height: 60px;
  }
  .search__input {
    padding: 1.2em 4em 1.2em 1.4em;
    font-size: 1rem;
    min-height: 60px;
  }
  .search__btn {
    width: 46px;
    height: 46px;
    right: 6px;
  }
  .search__btn svg {
    width: 26px;
    height: 26px;
  }
}

@media (max-width: 360px) {
  .search__form {
    min-height: 62px;
  }
  .search__input {
    padding: 1.3em 4.2em 1.3em 1.5em;
    font-size: 0.95rem;
    min-height: 62px;
  }
  .search__btn {
    width: 48px;
    height: 48px;
    right: 5px;
  }
  .search__btn svg {
    width: 28px;
    height: 28px;
  }
}

/* === Mobile Logo === */
.mobile-logo {
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1999;
  display: none;
}

.mobile-logo__img {
  height: 80px;
  width: auto;
}

/* === Mobile Menu === */
.burger {
  position: fixed;
  top: 24px;
  left: 24px;
  width: 40px;
  height: 40px;
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: none;
  border: none;
  z-index: 2000;
  cursor: pointer;
  gap: 6px;
}

.burger span {
  display: block;
  width: 32px;
  height: 4px;
  background: #fff;
  border-radius: 2px;
  transition: transform 0.3s cubic-bezier(0.4, 1.3, 0.6, 1), opacity 0.25s;
}

/* Burger button animation */
.burger.open span:nth-child(1) {
  transform: translateY(10px) rotate(45deg);
}
.burger.open span:nth-child(2) {
  opacity: 0;
}
.burger.open span:nth-child(3) {
  transform: translateY(-10px) rotate(-45deg);
}

.burger.dark span {
  background: #111;
}

.dropdown-menu {
  position: fixed;
  top: 64px;
  left: 16px;
  min-width: 260px;
  width: auto;
  height: auto;
  max-height: 90vh;
  background: #111;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
  padding: 32px 36px 24px 36px;
  z-index: 1500;
  display: none;
  flex-direction: column;
  gap: 18px;
  opacity: 0;
  pointer-events: none;
  transform: translateY(-20px) scale(0.98);
  transition: opacity 0.28s cubic-bezier(0.4, 1.3, 0.6, 1),
    transform 0.28s cubic-bezier(0.4, 1.3, 0.6, 1);
  overflow-y: auto;
}

.dropdown-menu.active {
  display: flex;
  opacity: 1;
  pointer-events: auto;
  transform: translateY(0) scale(1);
}

.dropdown-menu a {
  color: #fff;
  font-size: 1.15rem;
  text-decoration: none;
  transition: color 0.18s;
  font-family: inherit;
  padding: 5px 0;
}

.dropdown-menu a:hover,
.dropdown-menu a.active {
  color: #b2c9d6;
}

#menuBack {
  font-weight: 700;
  margin-top: 15px;
}

/* Body scroll lock when menu is active */
body.menu-open {
  overflow: hidden;
}

@media (max-width: 768px) {
  .mobile-header {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .mobile-header__logo {
    position: static;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: auto;
    text-align: center;
  }

  .mobile-header-spacer {
    display: block;
    height: 80px;
  }

  .hero-header-group {
    display: none;
  }

  .dropdown-menu {
    display: flex;
  }

  .header__nav-link:not(.header__logo-link) {
    display: none;
  }

  .header__center-group {
    justify-content: center;
  }

  .filters-row {
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
    max-width: 95vw;
  }

  .filter-dropdown {
    margin-right: 0;
    margin-bottom: 10px;
    align-self: center;
  }

  .search__form {
    max-width: 95vw;
  }

  .filter-menu {
    left: 0;
    right: 0;
    width: calc(100vw - 30px);
    max-width: none;
  }

  .filters {
    padding: 15px 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .active-filters-container {
    padding: 0 10px;
  }

  .active-filters {
    justify-content: center;
    padding: 0 15px;
    max-width: 100%;
  }
}

/* На больших экранах скрываем меню */
@media (min-width: 769px) {
  .mobile-header,
  .mobile-header-spacer,
  .burger,
  .dropdown-menu {
    display: none !important;
  }
}

@media (max-width: 600px) {
  html,
  body {
    width: 100vw;
    max-width: 100vw;
    overflow-x: hidden !important;
  }
  .stock-list {
    padding: 0 0 2rem 0 !important;
    margin: 0 !important;
    width: 100vw !important;
    max-width: 100vw !important;
  }
  .stock-cards {
    width: 100vw !important;
    max-width: 100vw !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 0 auto !important;
    padding: 0 !important;
  }
  .car-card {
    min-width: unset !important;
    max-width: unset !important;
    width: 98vw !important;
    margin: 0 auto 1rem auto !important;
    padding: 10px 4px 10px 4px !important;
    border-radius: 10px !important;
    box-sizing: border-box !important;
  }
  .car-card__image img {
    width: 100% !important;
    height: 190px !important;
    min-height: unset !important;
    max-height: 210px !important;
    object-fit: fill !important;
    border-radius: 8px !important;
    margin-bottom: 10px !important;
  }
  .car-card__info-list {
    min-width: 0 !important;
    font-size: 0.95rem !important;
  }
  .car-card__price-block {
    min-width: 80px !important;
    margin-left: 4px !important;
  }
  .filters-row,
  .filters {
    max-width: 100vw !important;
    width: 100vw !important;
    margin: 0 !important;
    padding: 0.5rem 0.5rem !important;
    box-sizing: border-box !important;
  }
  .footer {
    width: 100vw !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
  }
  .car-card__title {
    font-size: 1.1rem !important;
    white-space: normal !important;
    word-break: break-word !important;
    line-height: 1.2 !important;
    margin-bottom: 2px !important;
    max-width: 100% !important;
    overflow: visible !important;
    display: block !important;
  }
}

@media (max-width: 400px) {
  .hero-stock__title-main {
    font-size: 1.8rem;
  }

  .car-card {
    padding: 1rem;
  }

  .car-card__title {
    font-size: 1.2rem;
  }

  .car-card__info-list {
    font-size: 0.85rem;
  }
}

@media (min-width: 601px) {
  .burger,
  .dropdown-menu {
    display: none !important;
  }

  body {
    padding-top: 0;
  }
}

/* Стиль для затемнения фона при открытом меню */
body.menu-open::after {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  pointer-events: none;
}

/* === Список авто === */
.stock-list {
  background: #fff;
  padding: 0 2vw 3rem 2vw;
  margin-top: 0;
  padding-top: 0 !important;
}
.stock__title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-top: 0 !important;
  padding-top: 0 !important;
  margin-bottom: 2.5rem;
}
.stock-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2.5rem;
  justify-content: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.car-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid #dbe0e6;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  background: #fff;
  padding: 24px 24px 20px 24px;
  width: 100%;
  min-height: 460px;
  font-family: "Segoe UI", Arial, sans-serif;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.car-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}
.car-card__header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}
.car-card__title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.car-card__subtitle {
  color: #6c757d;
  font-size: 1rem;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.car-card__effect {
  font-weight: 700;
  color: #6e828a;
}
.car-card__image img {
  height: 260px;
  min-height: 260px;
  max-height: 260px;
  object-fit: fill;
  border-radius: 10px;
  margin-bottom: 18px;
}
.car-card__specs-row {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 32px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 12px;
  margin-bottom: 16px;
}
.car-card__spec {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.car-card__spec-divider {
  width: 1px;
  height: 36px;
  background: #e5e7eb;
  margin: 0 16px;
  border-radius: 2px;
}
.car-card__spec-main {
  font-size: 1.25rem;
  font-weight: 700;
  display: block;
}
.car-card__spec-label {
  font-size: 0.98rem;
  color: #6c757d;
}
.car-card__content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.car-card__info {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 16px;
  margin-top: auto;
  padding-top: 16px;
}
.car-card__info-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 1rem;
  min-width: 260px;
  flex-grow: 1;
  overflow: hidden;
}
.car-card__info-list b {
  font-weight: 750;
  color: #222;
}
.dot {
  display: inline-block;
  width: 13px;
  height: 13px;
  border-radius: 50%;
  vertical-align: middle;
  border: 2px solid #fff;
  margin-left: -5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}
.car-card__info-list .dot:first-child {
  margin-left: 0;
}

/* Поддержка старых классов для совместимости */
.dot--orange {
  background: #eb5d3d;
}
.dot--red {
  background: #b92626;
}
.dot--black {
  background: #1e1f1e;
}
.dot--white {
  background: #ffffff;
  border: 1px solid #ddd;
}
.dot--blue {
  background: #0066cc;
}
.dot--brown {
  background: #8b4513;
}
.dot--beige {
  background: #f5f5dc;
}
.dot--gray {
  background: #808080;
}

/* Контейнер для цветных точек */
.color-dots-container {
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

/* Подписи под цветами */
.color-dots-names {
  display: block;
  font-size: 0.8rem;
  color: #666;
  margin-top: 2px;
}

.car-card__price-block {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 140px;
  margin-left: 16px;
}
.car-card__price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111;
  margin-bottom: 2px;
  white-space: nowrap;
}
.car-card__in-stock {
  font-size: 1rem;
  color: #6c757d;
  white-space: nowrap;
}

/* === FOOTER STYLES FROM styles.css === */
@media (min-width: 601px) {
  /* Футер стили перенесены в footer.css */
}

@media (max-width: 600px) {
  .filter-btn {
    font-size: 0.8rem;
    padding: 0.5em 1em;
    font-weight: 600;
    width: 100%;
    text-align: center;
  }

  /* Футер стили перенесены в footer.css */
}

.burger span {
  background: #111 !important;
}

/* === Адаптивность === */
@media (max-width: 1200px) {
  .stock-cards {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    padding: 0 15px;
  }
}

@media (max-width: 1100px) {
  .stock-cards {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
  }
  .footer__info {
    flex-direction: column;
    gap: 1.5rem;
  }
  .hero__car-img {
    position: static;
    width: 100%;
    min-width: 0;
    max-width: 100%;
  }
  .hero__content {
    text-align: left;
  }
}

@media (max-width: 768px) {
  .stock-cards {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0 10px;
  }

  .car-card {
    min-height: 450px;
  }
}
@media (max-width: 900px) {
  .header {
    height: 70px;
  }
  .header__logo-img {
    height: 48px;
    margin-right: 1.2rem;
  }
  .header__center-group {
    gap: 1.2rem;
  }
  .header__logo-center {
    margin-right: 20px;
  }
  .header__nav {
    gap: 1.2rem;
  }
  .hero__title {
    font-size: 2.2rem;
  }
  .hero__car-img {
    width: 60vw;
  }
}
@media (max-width: 700px) {
  .footer {
    padding: 1rem 1vw 0.5rem 1vw;
  }
  .footer__nav {
    gap: 1.2rem;
    font-size: 1rem;
  }
  .footer__copyright {
    font-size: 0.95rem;
    margin-top: 1.2rem;
  }
  .car-card {
    padding: 14px 6px 14px 6px;
    min-width: 0;
    max-width: 98vw;
  }
  .car-card__image img {
    height: 200px;
    object-fit: fill;
  }
  .car-card__price-block {
    min-width: 90px;
    margin-left: 6px;
  }
  .hero__car-img {
    width: 90vw;
  }
}
@media (max-width: 500px) {
  .hero__title {
    font-size: 1.3rem;
  }
  .hero__subtitle {
    font-size: 0.95rem;
  }
  .footer__logo span {
    font-size: 1.5rem;
  }
}

.hero-stock {
  margin: 0;
  padding: 0;
  background: #fff;
  position: relative;
  min-height: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
.hero-stock__container {
  display: flex;
  align-items: stretch;
  justify-content: center;
  padding: 0 0 0 0;
  min-height: 420px;
  position: relative;
  width: 100vw;
  max-width: 100vw;
}
.hero-stock__text {
  flex: 1 1 60%;
  min-width: 320px;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding-top: 60px;
  max-width: 600px;
  margin: 0 auto;
}
.hero-stock__title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1.2rem;
  text-align: center;
}
.hero-stock__subtitle {
  font-size: 1.1rem;
  color: #444;
  margin-bottom: 1.5rem;
  text-align: center;
  max-width: 600px;
}
.hero-stock__img-wrap {
  flex: 1 1 50%;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  min-width: 320px;
  position: relative;
  right: 0;
}
.hero-stock__img {
  max-width: 800px;
  width: 100%;
  height: auto;
  display: block;
  position: relative;
  right: -80px;
  top: 30px;
  z-index: 1;
}
.hero-stock__highlight {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100vw;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.18) 0%, #222 100%);
  min-height: 220px;
  display: flex;
  align-items: flex-end;
  padding: 2.5rem 0 2.5rem 4vw;
  z-index: 3;
}
.highlight__info {
  color: #fff;
  max-width: 700px;
  padding-left: 1vw;
}
.highlight__title {
  font-size: 2.3rem;
  font-weight: 700;
  margin-bottom: 0.7rem;
}
.highlight__desc {
  font-size: 1.2rem;
  margin-bottom: 1.2rem;
}
.highlight__btn {
  display: inline-block;
  background: #111;
  color: #fff;
  font-weight: 700;
  border-radius: 8px;
  padding: 0.9em 2.2em;
  font-size: 1.2rem;
  border: none;
  cursor: pointer;
  transition: background 0.2s;
  margin-top: 0.7rem;
}
.highlight__btn:hover {
  background: #b2c9d6;
  color: #111;
}
@media (max-width: 1200px) {
  .hero-stock__img {
    max-width: 600px;
    right: -30px;
  }
  .hero-stock__title {
    font-size: 3rem;
  }
}
@media (max-width: 900px) {
  .hero-stock__container {
    flex-direction: column;
    align-items: center;
    min-height: 300px;
  }
  .hero-stock__img-wrap {
    justify-content: center;
    margin-top: 2rem;
    right: 0;
  }
  .hero-stock__img {
    max-width: 90vw;
    right: 0;
    top: 0;
  }
  .hero-stock__title {
    font-size: 2rem;
  }
  .hero-stock__highlight {
    min-height: 160px;
    padding: 1.2rem 0 1.2rem 2vw;
  }
  .hero-stock__text {
    padding-top: 30px;
    max-width: 98vw;
  }
  .hero-stock__subtitle {
    font-size: 0.95rem;
  }
}
@media (max-width: 600px) {
  .hero-stock__visual {
    min-height: 200px;
  }

  .hero-stock__title-main {
    font-size: 1.8rem;
    margin-bottom: 0.3rem;
  }

  .hero-stock__subtitle-main {
    font-size: 0.9rem;
    max-width: 90%;
    margin: 0 auto;
  }

  .hero-stock__car-img {
    max-width: 100%;
    right: 0;
    bottom: 0;
  }

  .hero-stock__gradient {
    min-height: 180px;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0.1) 0%,
      rgba(0, 0, 0, 0.2) 10%,
      rgba(0, 0, 0, 0.3) 20%,
      rgba(0, 0, 0, 0.4) 30%,
      rgba(0, 0, 0, 0.5) 40%,
      rgba(0, 0, 0, 0.6) 50%,
      rgba(0, 0, 0, 0.7) 60%,
      rgba(0, 0, 0, 0.8) 70%,
      rgba(0, 0, 0, 0.9) 80%,
      #222 100%
    );
  }

  .hero-stock__gradient-content {
    padding: 1rem 1rem 1.5rem 1rem;
  }

  .hero-stock__gradient-title {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .hero-stock__gradient-desc {
    font-size: 0.9rem;
    margin-bottom: 0.8rem;
  }

  .hero-stock__gradient-btn {
    padding: 0.5em 1.5em;
    font-size: 0.9rem;
  }
}

@media (min-width: 601px) {
  /* Футер стили перенесены в footer.css */
}

@media (max-width: 600px) {
  /* Футер стили перенесены в footer.css */
}

/* === Mobile Header === */
.mobile-header {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: #fff;
  z-index: 1000;
  padding: 0 20px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
}

.mobile-header__logo {
  position: static;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: auto;
  text-align: center;
}

.mobile-header__logo-img {
  height: 80px;
  width: auto;
}

/* Пустой раздел для отступа */
.mobile-header-spacer {
  display: none;
  height: 80px;
  width: 100%;
}

@media (max-width: 768px) {
  .mobile-header {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .mobile-header__logo {
    position: static;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: auto;
    text-align: center;
  }

  .mobile-header-spacer {
    display: block;
    height: 80px;
  }

  .hero-header-group {
    display: none;
  }

  .dropdown-menu {
    display: flex;
  }

  .header__nav-link:not(.header__logo-link) {
    display: none;
  }

  .header__center-group {
    justify-content: center;
  }
}

/* === Дополнительные фильтры === */
.additional-filters {
  display: none !important;
}

.filter-btn {
  display: none !important;
}

/* Стили для выпадающего меню фильтров */
.filter-dropdown {
  position: relative;
  display: inline-block;
  margin-right: 15px;
  z-index: 100;
}

.filter-menu-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  background: #222;
  color: #fff;
  font-weight: 600;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  position: relative;
  min-width: 120px;
}

.filter-menu-btn:hover {
  background: #333;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Индикатор активных фильтров на кнопке */
.filter-menu-btn::after {
  content: attr(data-count);
  position: absolute;
  top: -5px;
  right: -5px;
  width: 18px;
  height: 18px;
  background: #f44336;
  color: white;
  font-size: 11px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.2s;
}

.filter-menu-btn.has-active-filters::after {
  opacity: 1;
  transform: scale(1);
}

/* Стили для счетчика результатов */
.results-counter {
  margin: 15px 0;
  text-align: center;
  font-size: 0.95rem;
  color: #555;
  font-weight: 500;
  width: 100%;
  padding: 10px;
  background: #f9f9f9;
  border-radius: 8px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
}

.filter-menu {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 8px;
  min-width: 320px;
  max-width: 90vw;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
  border: 1px solid #eaeaea;
  padding: 18px;
  display: none;
  flex-direction: column;
  gap: 15px;
  max-height: 85vh;
  overflow-y: auto;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-menu.show {
  display: flex;
}

.filter-menu::before {
  content: "";
  position: absolute;
  top: -6px;
  left: 20px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  border-left: 1px solid #eaeaea;
  border-top: 1px solid #eaeaea;
}

/* Контейнер для активных фильтров */
.active-filters-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

/* Стили для активных фильтров */
.active-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 15px;
  justify-content: center;
  align-items: center;
  max-width: 800px;
}

.active-filter-tag {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #f2f5f8;
  border: 1px solid #dbe0e6;
  border-radius: 20px;
  font-size: 0.85rem;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin: 0 4px 8px 4px;
  white-space: nowrap;
}

.active-filter-tag:hover {
  background: #e8eef3;
}

.active-filter-tag span {
  font-weight: 600;
  color: #444;
}

.filter-tag-remove {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  background: #ddd;
  color: #555;
  border-radius: 50%;
  font-weight: bold;
  transition: all 0.2s;
  margin-left: 4px;
}

.filter-tag-remove:hover {
  background: #c4c4c4;
  color: #333;
}

/* Убираем старые фильтры, которые могут появляться сбоку */
.additional-filters {
  display: none !important;
}

.filter-btn {
  display: none !important;
}

/* Группа фильтров внутри выпадающего меню */
.filter-group {
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
  margin-bottom: 15px;
  width: 100%;
}

.filter-group:last-of-type {
  border-bottom: none;
  margin-bottom: 5px;
}

.filter-label {
  font-weight: 600;
  font-size: 0.95rem;
  margin-bottom: 12px;
  color: #333;
}

/* Опции фильтров */
.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
}

.filter-option {
  padding: 6px 12px;
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-option:hover {
  background: #e8e8e8;
}

.filter-option.active {
  background: #222;
  color: white;
  border-color: #222;
}

.reset-all-filters-btn {
  display: block;
  width: 100%;
  margin-top: 20px;
  padding: 10px;
  background: #f44336;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s, transform 0.1s;
  box-shadow: 0 2px 5px rgba(244, 67, 54, 0.3);
}

.reset-all-filters-btn:hover {
  background: #d32f2f;
  transform: translateY(-1px);
}

.reset-all-filters-btn:active {
  transform: translateY(1px);
}

/* Адаптивные стили для фильтров */
@media (max-width: 768px) {
  .filter-menu {
    left: 0;
    right: 0;
    width: calc(100vw - 30px);
    max-width: none;
  }

  .filters {
    padding: 15px 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .filter-dropdown {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .active-filters-container {
    padding: 0 10px;
  }

  .active-filters {
    justify-content: center;
    padding: 0 15px;
    max-width: 100%;
  }
}

/* === Hero header group === */
.hero-header-group {
  position: relative;
  top: auto;
  left: auto;
  width: 100vw;
  height: 56px;
  z-index: 10;
  background: transparent;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header__logo-img {
  height: 40px;
  width: auto;
  display: block;
  background: #fff;
  vertical-align: middle;
  align-self: center;
  margin-top: 0;
}

.header__nav-link {
  font-weight: 700;
  font-size: 1.1rem;
  color: #111;
  transition: color 0.2s;
  line-height: 56px;
  display: flex;
  align-items: center;
  height: 56px;
  padding: 0 0.5rem;
}

.header__logo-link {
  display: flex;
  align-items: center;
  margin-right: 2.5rem;
  height: 56px;
}

.header__back-link {
  font-size: 1.1rem;
  height: 56px;
  display: flex;
  align-items: center;
  line-height: 56px;
}

@media (max-width: 900px) {
  .hero-header-group {
    height: 48px;
  }
  .header__logo-img {
    height: 32px;
  }
  .header__nav-link,
  .header__logo-link,
  .header__back-link {
    height: 48px;
    line-height: 48px;
  }
}

/* === Новый hero-блок для stock: точное соответствие макету === */
.hero-stock--custom {
  background-color: #f9f9f9;
  position: relative;
  padding: 3rem 0 4rem 0;
  min-height: 350px;
}

.hero-stock--custom::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(
    circle at right,
    rgba(224, 229, 232, 0.95) 0%,
    rgba(224, 229, 232, 0.9) 10%,
    rgba(224, 229, 232, 0.7) 30%,
    rgba(224, 229, 232, 0.4) 60%,
    rgba(224, 229, 232, 0) 80%
  );
  background-size: 100% 100%;
  background-position: 100% 0%;
  background-repeat: no-repeat;
  z-index: 1;
}

.hero-stock__visual {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 2vw;
  position: relative;
  z-index: 2;
}

.hero-stock__center {
  text-align: center;
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
  position: relative;
  z-index: 5;
}

.hero-stock__title-main {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 2rem;
  text-align: center;
  color: #111;
  position: relative;
  z-index: 5;
}

.hero-stock__subtitle-main {
  font-size: 1.3rem;
  color: #111;
  text-align: center;
  max-width: 700px;
  font-weight: 400;
  pointer-events: auto;
  margin: 0;
  padding: 0;
}

.hero-stock__car-img {
  position: absolute;
  right: 0vw;
  bottom: 0;
  max-width: 44vw;
  height: auto;
  z-index: 2;
  pointer-events: none;
  box-shadow: none;
  background: none;
}

.hero-stock__gradient {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100vw;
  min-height: 260px;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.2) 10%,
    rgba(0, 0, 0, 0.3) 20%,
    rgba(0, 0, 0, 0.4) 30%,
    rgba(0, 0, 0, 0.5) 40%,
    rgba(0, 0, 0, 0.6) 50%,
    rgba(0, 0, 0, 0.7) 60%,
    rgba(0, 0, 0, 0.8) 70%,
    rgba(0, 0, 0, 0.9) 80%,
    #222 100%
  );
  display: flex;
  align-items: flex-end;
  z-index: 3;
}

.hero-stock__gradient-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 2.5rem 0 2.5rem 4vw;
  max-width: 900px;
}

.hero-stock__gradient-title {
  font-size: 2.2rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 0.7rem;
  margin-top: 0;
}

.hero-stock__gradient-desc {
  font-size: 1.15rem;
  color: #fff;
  margin-bottom: 1.2rem;
  font-weight: 400;
  max-width: 900px;
}

.hero-stock__gradient-btn {
  display: inline-block;
  background: #111;
  color: #fff;
  font-weight: 700;
  border-radius: 12px;
  padding: 0.7em 2em;
  font-size: 1.1rem;
  border: none;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  margin-top: 0.7rem;
  box-shadow: none;
  text-decoration: none;
}

.hero-stock__gradient-btn:hover {
  background: #fff;
  color: #111;
}

@media (max-width: 1200px) {
  .hero-stock__car-img {
    max-width: 60vw;
  }

  .hero-stock__title-main {
    font-size: 3rem;
  }
}

@media (max-width: 900px) {
  .hero-stock__visual {
    min-height: 400px;
  }

  .hero-stock__car-img {
    max-width: 90vw;
    right: 0;
  }

  .hero-stock__title-main {
    font-size: 2rem;
  }

  .hero-stock__gradient {
    min-height: 140px;
    padding: 1.2rem 0 1.2rem 2vw;
  }

  .hero-stock__gradient-content {
    padding: 1.2rem 0 1.2rem 2vw;
    max-width: 98vw;
  }

  .hero-stock__center {
    top: 2%;
  }

  .hero-stock__subtitle-main {
    font-size: 0.95rem;
  }
}

/* Стиль для SVG иконки в кнопке фильтров */
.filter-menu-btn svg {
  width: 20px;
  height: 20px;
}

@media (max-width: 600px) {
  .active-filter-tag {
    font-size: 0.8rem;
    padding: 5px 10px;
    margin: 0 2px 6px 2px;
  }
}

@media (min-width: 769px) and (max-width: 900px) {
  .filters-row {
    max-width: 90vw;
  }

  .search__form {
    max-width: 70vw;
  }
}

/* Стили для автодополнения */
.autocomplete-container {
  position: relative;
  width: 100%;
}

.autocomplete-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  max-height: 300px;
  overflow-y: auto;
  background: white;
  border: 1px solid #ddd;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 150;
  display: none;
}

.autocomplete-dropdown.show {
  display: block;
}

.autocomplete-item {
  padding: 10px 15px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background 0.2s;
  position: relative;
}

.autocomplete-item:hover,
.autocomplete-item.active {
  background: #f5f5f5;
}

.autocomplete-item:last-child {
  border-bottom: none;
}

/* Стили для разделения брендов и моделей */
.autocomplete-brand {
  font-weight: 600;
}

.autocomplete-brand::before {
  content: "•";
  color: #222;
  margin-right: 6px;
  display: inline-block;
}

.autocomplete-model {
  padding-left: 25px;
  color: #555;
}

.autocomplete-model::before {
  content: "○";
  color: #666;
  margin-right: 6px;
  display: inline-block;
  font-size: 0.8em;
}



@media (max-width: 375px) {
  .car-card {
    min-height: 390px !important;
    max-height: 430px !important;
    padding: 8px 2px 10px 2px !important;
    border-radius: 8px !important;
  }
  .car-card__image img {
    height: 150px !important;
    max-height: 170px !important;
    object-fit: fill !important;
    border-radius: 6px !important;
    margin-bottom: 6px !important;
  }
  .car-card__title {
    font-size: 1.05rem !important;
    line-height: 1.2 !important;
    margin-bottom: 1px !important;
    white-space: normal !important;
    word-break: break-word !important;
  }
  .car-card__subtitle {
    font-size: 0.85rem !important;
    margin-bottom: 2px !important;
    white-space: normal !important;
    word-break: break-word !important;
  }
  .car-card__specs-row {
    gap: 12px !important;
    padding-bottom: 6px !important;
    margin-bottom: 8px !important;
  }
  .car-card__spec-main {
    font-size: 1rem !important;
  }
  .car-card__spec-label {
    font-size: 0.8rem !important;
  }
  .car-card__info-list {
    font-size: 0.8rem !important;
    min-width: 0 !important;
    gap: 2px !important;
  }
  .car-card__price-block {
    min-width: 60px !important;
    margin-left: 2px !important;
  }
  .car-card__price {
    font-size: 1.1rem !important;
    margin-bottom: 0 !important;
  }
}
@media (max-width: 340px) {
  .car-card {
    min-height: 350px !important;
    max-height: 390px !important;
    padding: 4px 1px 6px 1px !important;
  }
  .car-card__image img {
    height: 130px !important;
    max-height: 150px !important;
  }
  .car-card__title {
    font-size: 0.95rem !important;
  }
  .car-card__price {
    font-size: 1rem !important;
  }
}
