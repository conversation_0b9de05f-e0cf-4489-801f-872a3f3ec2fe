<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест исправлений API - SHMS Auto</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #555;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>🔧 Тест исправлений API фильтрации SHMS Auto</h1>
    
    <div class="test-section">
        <h2>1. Тест PHP API (encar-proxy.php)</h2>
        <p>Проверяем, что PHP API возвращает корректный JSON без HTML ошибок</p>
        <button class="test-button" onclick="testPhpApi()">Тест PHP API</button>
        <div id="php-api-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. Тест featured-cars.json</h2>
        <p>Проверяем, что файл главных карточек доступен и корректен</p>
        <button class="test-button" onclick="testFeaturedCars()">Тест Featured Cars</button>
        <div id="featured-cars-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. Тест фильтрации по цене</h2>
        <p>Проверяем фильтр цены $90,000-$10,000,000</p>
        <button class="test-button" onclick="testPriceFilter()">Тест фильтра цены</button>
        <div id="price-filter-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. Тест множественных запросов</h2>
        <p>Проверяем, что нет дублирующихся запросов и AbortError</p>
        <button class="test-button" onclick="testMultipleRequests()">Тест множественных запросов</button>
        <div id="multiple-requests-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>5. Тест курсов валют</h2>
        <p>Проверяем API курсов валют</p>
        <button class="test-button" onclick="testExchangeRates()">Тест курсов валют</button>
        <div id="exchange-rates-result" class="result"></div>
    </div>

    <script>
        // Функция для отображения результатов
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // Тест PHP API
        async function testPhpApi() {
            showResult('php-api-result', 'Тестирование PHP API...', 'info');
            
            try {
                const response = await fetch('/api/encar?limit=2&date=2025-06-17');
                const contentType = response.headers.get('content-type');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error(`Неверный Content-Type: ${contentType}`);
                }
                
                const data = await response.json();
                
                if (!Array.isArray(data)) {
                    throw new Error('Ответ не является массивом');
                }
                
                showResult('php-api-result', 
                    `✅ PHP API работает корректно!\n` +
                    `Content-Type: ${contentType}\n` +
                    `Получено записей: ${data.length}\n` +
                    `Первая запись: ${JSON.stringify(data[0], null, 2)}`, 
                    'success'
                );
                
            } catch (error) {
                showResult('php-api-result', 
                    `❌ Ошибка PHP API:\n${error.message}`, 
                    'error'
                );
            }
        }

        // Тест featured-cars.json
        async function testFeaturedCars() {
            showResult('featured-cars-result', 'Тестирование featured-cars.json...', 'info');
            
            try {
                const response = await fetch('/data/featured-cars.json');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (!Array.isArray(data)) {
                    throw new Error('Файл не содержит массив');
                }
                
                if (data.length === 0) {
                    throw new Error('Файл пустой');
                }
                
                showResult('featured-cars-result', 
                    `✅ Featured cars загружен корректно!\n` +
                    `Количество автомобилей: ${data.length}\n` +
                    `Первый автомобиль: ${data[0].brand} ${data[0].model} (${data[0].year})`, 
                    'success'
                );
                
            } catch (error) {
                showResult('featured-cars-result', 
                    `❌ Ошибка featured-cars.json:\n${error.message}`, 
                    'error'
                );
            }
        }

        // Тест фильтрации по цене
        async function testPriceFilter() {
            showResult('price-filter-result', 'Тестирование фильтра цены...', 'info');
            
            try {
                const response = await fetch('/api/encar?price_from=90000&price_to=10000000&limit=5&date=2025-06-17');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (!Array.isArray(data)) {
                    throw new Error('Ответ не является массивом');
                }
                
                // Проверяем, что все автомобили в диапазоне цен
                const outOfRange = data.filter(car => {
                    const price = parseInt(car.price_usd || car.price || 0);
                    return price < 90000 || price > 10000000;
                });
                
                if (outOfRange.length > 0) {
                    throw new Error(`Найдены автомобили вне диапазона цен: ${outOfRange.length}`);
                }
                
                showResult('price-filter-result', 
                    `✅ Фильтр цены работает корректно!\n` +
                    `Найдено автомобилей в диапазоне $90,000-$10,000,000: ${data.length}\n` +
                    `Примеры цен: ${data.slice(0, 3).map(car => `$${car.price_usd || car.price}`).join(', ')}`, 
                    'success'
                );
                
            } catch (error) {
                showResult('price-filter-result', 
                    `❌ Ошибка фильтра цены:\n${error.message}`, 
                    'error'
                );
            }
        }

        // Тест множественных запросов
        async function testMultipleRequests() {
            showResult('multiple-requests-result', 'Тестирование множественных запросов...', 'info');
            
            try {
                const startTime = Date.now();
                
                // Отправляем несколько запросов одновременно
                const promises = [
                    fetch('/api/encar?limit=2&date=2025-06-17&brand=BMW'),
                    fetch('/api/encar?limit=2&date=2025-06-17&brand=Mercedes-Benz'),
                    fetch('/api/encar?limit=2&date=2025-06-17&brand=Audi')
                ];
                
                const responses = await Promise.all(promises);
                const endTime = Date.now();
                
                // Проверяем все ответы
                for (let i = 0; i < responses.length; i++) {
                    if (!responses[i].ok) {
                        throw new Error(`Запрос ${i + 1} failed: ${responses[i].status}`);
                    }
                }
                
                const dataPromises = responses.map(r => r.json());
                const results = await Promise.all(dataPromises);
                
                showResult('multiple-requests-result', 
                    `✅ Множественные запросы работают корректно!\n` +
                    `Время выполнения: ${endTime - startTime}ms\n` +
                    `Результаты: BMW(${results[0].length}), Mercedes(${results[1].length}), Audi(${results[2].length})`, 
                    'success'
                );
                
            } catch (error) {
                showResult('multiple-requests-result', 
                    `❌ Ошибка множественных запросов:\n${error.message}`, 
                    'error'
                );
            }
        }

        // Тест курсов валют
        async function testExchangeRates() {
            showResult('exchange-rates-result', 'Тестирование курсов валют...', 'info');
            
            try {
                const response = await fetch('/api/exchange-rates.php');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error('API вернул success: false');
                }
                
                if (!data.KRW_to_RUB || !data.USD_rates) {
                    throw new Error('Отсутствуют необходимые курсы валют');
                }
                
                showResult('exchange-rates-result', 
                    `✅ Курсы валют загружены корректно!\n` +
                    `KRW to RUB: ${data.KRW_to_RUB}\n` +
                    `USD sell rate: ${data.USD_rates.usdtrub.sell}\n` +
                    `Источник: ${data.source}`, 
                    'success'
                );
                
            } catch (error) {
                showResult('exchange-rates-result', 
                    `❌ Ошибка курсов валют:\n${error.message}`, 
                    'error'
                );
            }
        }

        // Автоматический запуск всех тестов при загрузке страницы
        window.addEventListener('load', () => {
            console.log('🧪 Страница тестирования загружена');
        });
    </script>
</body>
</html>
