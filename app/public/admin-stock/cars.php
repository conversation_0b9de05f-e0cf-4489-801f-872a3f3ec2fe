<?php
// Устанавливаем CORS заголовки
header('Access-Control-Allow-Origin: http://localhost:3002');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json; charset=utf-8');

// Обрабатываем preflight запросы
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Читаем данные из JSON файла
$jsonFile = __DIR__ . '/cars.json';

if (file_exists($jsonFile)) {
    $jsonData = file_get_contents($jsonFile);
    echo $jsonData;
} else {
    // Если файла нет, возвращаем пустой массив
    echo '[]';
}
?>
