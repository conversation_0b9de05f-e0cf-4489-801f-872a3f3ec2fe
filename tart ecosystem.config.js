[0;1;32m●[0m nginx.service - A high performance web server and a reverse proxy server
     Loaded: loaded (]8;;file://cv4779063.novalocal/usr/lib/systemd/system/nginx.service/usr/lib/systemd/system/nginx.service]8;;; [0;1;32menabled[0m; preset: [0;1;32menabled[0m)
     Active: [0;1;32mactive (running)[0m since Sun 2025-06-01 17:46:10 MSK; 7h ago
       Docs: ]8;;man:nginx(8)man:nginx(8)]8;;
    Process: 5970 ExecReload=/usr/sbin/nginx -g daemon on; master_process on; -s reload (code=exited, status=0/SUCCESS)
   Main PID: 733 (nginx)
      Tasks: 2 (limit: 2332)
     Memory: 5.0M (peak: 44.4M)
        CPU: 1.096s
     CGroup: /system.slice/nginx.service
             ├─[0;38;5;245m 733 "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;"[0m
             └─[0;38;5;245m5972 "nginx: worker process"[0m

Jun 01 20:05:17 cv4779063.novalocal systemd[1]: Reloaded nginx.service - A high performance web server and a reverse proxy server.
Jun 01 20:10:18 cv4779063.novalocal systemd[1]: Reloading nginx.service - A high performance web server and a reverse proxy server...
Jun 01 20:10:18 cv4779063.novalocal nginx[2719]: 2025/06/01 20:10:18 [notice] 2719#2719: signal process started
Jun 01 20:10:18 cv4779063.novalocal systemd[1]: Reloaded nginx.service - A high performance web server and a reverse proxy server.
Jun 01 20:19:04 cv4779063.novalocal systemd[1]: Reloading nginx.service - A high performance web server and a reverse proxy server...
Jun 01 20:19:04 cv4779063.novalocal nginx[2849]: 2025/06/01 20:19:04 [notice] 2849#2849: signal process started
Jun 01 20:19:04 cv4779063.novalocal systemd[1]: Reloaded nginx.service - A high performance web server and a reverse proxy server.
Jun 02 01:01:14 cv4779063.novalocal systemd[1]: Reloading nginx.service - A high performance web server and a reverse proxy server...
Jun 02 01:01:14 cv4779063.novalocal nginx[5970]: 2025/06/02 01:01:14 [notice] 5970#5970: signal process started
Jun 02 01:01:14 cv4779063.novalocal systemd[1]: Reloaded nginx.service - A high performance web server and a reverse proxy server.
