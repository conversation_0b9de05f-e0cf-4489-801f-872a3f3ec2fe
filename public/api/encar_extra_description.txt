{
  "diagnosis": { // Секция диагностики: содержит результаты проверки состояния автомобиля сертифицированным специалистом Encar.
    "items": [ // Список элементов диагностики, каждый из которых описывает конкретную часть автомобиля.
      {
        "code": "006003", // Уникальный код элемента диагностики, используемый Encar для идентификации (в данном случае — левая передняя дверь).
        "name": "FRONT_DOOR_LEFT", // Название проверяемой части (левая передняя дверь).
        "result": "정상", // Результат проверки на корейском языке ("정상" = "нормально"), указывает состояние детали.
        "resultCode": "NORMAL" // Код результата на английском, подтверждающий, что деталь в порядке.
      },
      {
        "code": "006006", // Код для левой задней двери.
        "name": "BACK_DOOR_LEFT", // Название — левая задняя дверь.
        "result": "정상", // Состояние нормально.
        "resultCode": "NORMAL" // Код результата — нормально.
      },
      {
        "code": "006009", // Код для крышки багажника.
        "name": "TRUNK_LID", // Название — крышка багажника.
        "result": "정상", // Состояние нормально.
        "resultCode": "NORMAL" // Код результата — нормально.
      },
      {
        "code": "006018", // Код для правой задней двери.
        "name": "BACK_DOOR_RIGHT", // Название — правая задняя дверь.
        "result": "정상", // Состояние нормально.
        "resultCode": "NORMAL" // Код результата — нормально.
      },
      {
        "code": "006023", // Код для правой передней двери.
        "name": "FRONT_DOOR_RIGHT", // Название — правая передняя дверь.
        "result": "정상", // Состояние нормально.
        "resultCode": "NORMAL" // Код результата — нормально.
      },
      {
        "code": "006026", // Код для капота.
        "name": "HOOD", // Название — капот.
        "result": "정상", // Состояние нормально.
        "resultCode": "NORMAL" // Код результата — нормально.
      },
      {
        "code": "006027", // Код для правого переднего крыла.
        "name": "FRONT_FENDER_RIGHT", // Название — правое переднее крыло.
        "result": "정상", // Состояние нормально.
        "resultCode": "NORMAL" // Код результата — нормально.
      },
      {
        "code": "006033", // Код для левого переднего крыла.
        "name": "FRONT_FENDER_LEFT", // Название — левое переднее крыло.
        "result": "정상", // Состояние нормально.
        "resultCode": "NORMAL" // Код результата — нормально.
      },
      {
        "code": "006039", // Код для общего комментария проверяющего.
        "name": "CHECKER_COMMENT", // Название — комментарий проверяющего.
        "result": "본 차량은 엔카의 진단 결과 모든 항목이 정상으로 확인되며, '무사고' 차량으로 판정합니다.", // Результат — текстовый комментарий на корейском: "Этот автомобиль по результатам диагностики Encar признан нормальным во всех аспектах и классифицирован как 'безаварийный'".
        "resultCode": null // Код результата отсутствует, так как это текстовый комментарий.
      },
      {
        "code": "006040", // Код для комментария по внешним панелям.
        "name": "OUTER_PANEL_COMMENT", // Название — комментарий по внешним панелям.
        "result": "본 차량의 진단 결과 외부패널의 교환이 없는 차량입니다. n트렁크탈부착", // Результат на корейском: "По результатам диагностики у этого автомобиля не было замены внешних панелей. Багажник снимался/устанавливался".
        "resultCode": null // Код результата отсутствует, так как это текстовый комментарий.
      }
    ],
    "ordNo": 22624975, // Номер заказа диагностики, уникальный идентификатор в системе Encar.
    "checkerId": "autodome02", // Идентификатор проверяющего (логин или код сотрудника).
    "vehicleId": 37424098, // Уникальный идентификатор автомобиля в базе Encar, связывает диагностику с конкретным объявлением.
    "centerCode": "171", // Код диагностического центра, где проводилась проверка.
    "registerId": "10822", // Идентификатор сотрудника, зарегистрировавшего результаты проверки.
    "checkerName": "안산 제휴성능장C(워런티코리아)", // Название центра или имя проверяющего: "Ансан, партнерский центр C (Warranty Korea)".
    "diagnosisNo": 131, // Номер диагностического отчёта для данного автомобиля.
    "diagnosisDate": "2024-11-16T00:00:00", // Дата проведения диагностики (официальная).
    "realDiagnosisDate": "2024-11-16T00:00:00", // Реальная дата диагностики (может отличаться от официальной, если запись корректировалась).
    "reservationCenterName": "안산 오토돔" // Название центра бронирования: "Ансан Autodome", где была назначена проверка.
  },
  "inspection": { // Секция инспекции: детализирует техническое состояние автомобиля и необходимые работы.
    "etcs": [ // Список дополнительных проверок или элементов (например, ремонтные нужды и базовые компоненты).
      {
        "type": { // Тип категории инспекции.
          "code": "", // Код категории (пустой, так как это общий заголовок).
          "title": "수리필요" // Название категории на корейском: "Требуется ремонт".
        },
        "children": [ // Подкатегории, описывающие, что именно требует ремонта.
          {
            "type": { // Тип подкатегории.
              "code": "e001", // Код для внешнего состояния.
              "title": "외장" // Название: "Внешний вид".
            },
            "exists": null, // Признак наличия проблемы (null — информация отсутствует или не применима).
            "statusTypes": [], // Типы состояния (пустой список, так как данные не указаны).
            "statusItemTypes": [] // Подробные элементы состояния (пусто, нет деталей).
          },
          {
            "type": { // Тип подкатегории.
              "code": "e002", // Код для внутреннего состояния.
              "title": "내장" // Название: "Интерьер".
            },
            "exists": null, // Признак наличия проблемы (null — нет данных).
            "statusTypes": [], // Типы состояния (пусто).
            "statusItemTypes": [] // Подробные элементы (пусто).
          },
          {
            "type": { // Тип подкатегории.
              "code": "e003", // Код для полировки.
              "title": "광택" // Название: "Полировка".
            },
            "exists": null, // Признак необходимости полировки (null — данных нет).
            "statusTypes": [], // Типы состояния (пусто).
            "statusItemTypes": [] // Подробные элементы (пусто).
          },
          {
            "type": { // Тип подкатегории.
              "code": "e004", // Код для уборки салона.
              "title": "룸 클리링" // Название: "Уборка салона" (ошибка в написании, скорее всего "룸 클리닝" = "Room Cleaning").
            },
            "exists": null, // Признак необходимости уборки (null — данных нет).
            "statusTypes": [], // Типы состояния (пусто).
            "statusItemTypes": [] // Подробные элементы (пусто).
          },
          {
            "type": { // Тип подкатегории.
              "code": "e005", // Код для колёс.
              "title": "휠" // Название: "Колёса".
            },
            "exists": null, // Признак проблем с колёсами (null — данных нет).
            "statusTypes": [], // Типы состояния (пусто).
            "statusItemTypes": [ // Подробные элементы состояния колёс.
              { "code": "1", "title": "운전석 전" }, // "Переднее колесо со стороны водителя".
              { "code": "2", "title": "운전석 후" }, // "Заднее колесо со стороны водителя".
              { "code": "3", "title": "동반석 전" }, // "Переднее колесо со стороны пассажира".
              { "code": "4", "title": "동반석 후" }, // "Заднее колесо со стороны пассажира".
              { "code": "5", "title": "응급" } // "Запасное колесо".
            ]
          },
          {
            "type": { // Тип подкатегории.
              "code": "e006", // Код для шин.
              "title": "타이어" // Название: "Шины".
            },
            "exists": null, // Признак проблем с шинами (null — данных нет).
            "statusTypes": [], // Типы состояния (пусто).
            "statusItemTypes": [ // Подробные элементы состояния шин.
              { "code": "1", "title": "운전석 전" }, // "Передняя шина со стороны водителя".
              { "code": "2", "title": "운전석 후" }, // "Задняя шина со стороны водителя".
              { "code": "3", "title": "동반석 전" }, // "Передняя шина со стороны пассажира".
              { "code": "4", "title": "동반석 후" }, // "Задняя шина со стороны пассажира".
              { "code": "5", "title": "응급" } // "Запасная шина".
            ]
          },
          {
            "type": { // Тип подкатегории.
              "code": "e007", // Код для стёкол.
              "title": "유리" // Название: "Стёкла".
            },
            "exists": null, // Признак проблем со стёклами (null — данных нет).
            "statusTypes": [], // Типы состояния (пусто).
            "statusItemTypes": [] // Подробные элементы (пусто).
          }
        ]
      },
      {
        "type": { // Тип категории инспекции.
          "code": "", // Код категории (пустой, общий заголовок).
          "title": "기본품목" // Название на корейском: "Базовые компоненты".
        },
        "children": [ // Подкатегории базовых компонентов.
          {
            "type": { // Тип подкатегории.
              "code": "e008", // Код для состояния владения.
              "title": "보유상태" // Название: "Состояние владения" (наличие аксессуаров).
            },
            "exists": null, // Признак наличия компонентов (null — данных нет).
            "statusTypes": [], // Типы состояния (пусто).
            "statusItemTypes": [ // Подробные элементы состояния.
              { "code": "1", "title": "사용설명서" }, // "Руководство пользователя".
              { "code": "2", "title": "안전삼각대" }, // "Знак аварийной остановки".
              { "code": "3", "title": "잭" }, // "Домкрат".
              { "code": "4", "title": "스패너" } // "Гаечный ключ".
            ]
          }
        ]
      }
    ],
    "price": null, // Стоимость инспекции или ремонта (null — не указана).
    "images": [ // Список фотографий, сделанных во время инспекции.
      {
        "path": "/carsdata/cars/inspection/37424098_photoFront.jpg", // Путь к файлу фотографии передней части автомобиля.
        "type": "M1", // Код типа фотографии (M1 — передняя часть).
        "title": "앞면" // Название: "Передняя часть" на корейском.
      },
      {
        "path": "/carsdata/cars/inspection/37424098_photoBack.jpg", // Путь к файлу фотографии задней части автомобиля.
        "type": "M2", // Код типа фотографии (M2 — задняя часть).
        "title": "뒷면" // Название: "Задняя часть" на корейском.
      }
    ],
    "inners": [ // Секция внутренней инспекции (технические системы автомобиля).
      {
        "type": { // Тип категории.
          "code": "S00", // Код для самодиагностики.
          "title": "자기진단" // Название: "Самодиагностика".
        },
        "price": null, // Стоимость проверки (null — не указана).
        "children": [ // Подкатегории самодиагностики.
          {
            "type": { "code": "s001", "title": "원동기" }, // Название: "Двигатель".
            "children": [], // Подкатегории отсутствуют.
            "statusType": { "code": "1", "title": "양호" }, // Состояние: "Хорошее".
            "description": null, // Описание состояния (null — не указано).
            "statusItemTypes": [ // Возможные состояния.
              { "code": "1", "title": "양호" }, // "Хорошее".
              { "code": "10", "title": "불량" } // "Плохое".
            ]
          },
          {
            "type": { "code": "s002", "title": "변속기" }, // Название: "Трансмиссия".
            "children": [],
            "statusType": { "code": "1", "title": "양호" }, // Состояние: "Хорошее".
            "description": null,
            "statusItemTypes": [
              { "code": "1", "title": "양호" },
              { "code": "10", "title": "불량" }
            ]
          }
        ]
      },
      {
        "type": { "code": "S01", "title": "원동기" }, // Название: "Двигатель" (детальная проверка).
        "price": null,
        "children": [
          {
            "type": { "code": "s003", "title": "작동상태(공회전)" }, // Название: "Рабочая состояние (холостой ход)".
            "children": [],
            "statusType": { "code": "1", "title": "양호" }, // Состояние: "Хорошее".
            "description": null,
            "statusItemTypes": [
              { "code": "1", "title": "양호" },
              { "code": "10", "title": "불량" }
            ]
          },
          {
            "type": { "code": "s0102", "title": "오일누유" }, // Название: "Утечка масла".
            "children": [
              {
                "type": { "code": "s004", "title": "실린더 커버(로커암 커버)" }, // Название: "Крышка цилиндра (крышка коромысел)".
                "statusType": { "code": "6", "title": "미세누유" }, // Состояние: "Микроутечка".
                "description": null,
                "statusItemTypes": [
                  { "code": "3", "title": "없음" }, // "Отсутствует".
                  { "code": "6", "title": "미세누유" }, // "Микроутечка".
                  { "code": "7", "title": "누유" } // "Утечка".
                ]
              },
              {
                "type": { "code": "s005", "title": "실린더 헤드 / 개스킷" }, // Название: "Головка цилиндра / прокладка".
                "statusType": { "code": "3", "title": "없음" }, // Состояние: "Отсутствует".
                "description": null,
                "statusItemTypes": [
                  { "code": "3", "title": "없음" },
                  { "code": "6", "title": "미세누유" },
                  { "code": "7", "title": "누유" }
                ]
              },
              {
                "type": { "code": "s006", "title": "실린더 블록 / 오일팬" }, // Название: "Блок цилиндров / масляный поддон".
                "statusType": { "code": "3", "title": "없음" }, // Состояние: "Отсутствует".
                "description": null,
                "statusItemTypes": [
                  { "code": "3", "title": "없음" },
                  { "code": "6", "title": "미세누유" },
                  { "code": "7", "title": "누유" }
                ]
              }
            ],
            "statusType": null,
            "description": null,
            "statusItemTypes": []
          },
          {
            "type": { "code": "s007", "title": "오일 유량" }, // Название: "Уровень масла".
            "children": [],
            "statusType": { "code": "2", "title": "적정" }, // Состояние: "Нормальный".
            "description": null,
            "statusItemTypes": [
              { "code": "2", "title": "적정" }, // "Нормальный".
              { "code": "8", "title": "부족" } // "Недостаточный".
            ]
          },
          {
            "type": { "code": "s0104", "title": "냉각수누수" }, // Название: "Утечка охлаждающей жидкости".
            "children": [
              {
                "type": { "code": "s008", "title": "실린더 헤드 / 개스킷" }, // Название: "Головка цилиндра / прокладка".
                "statusType": { "code": "3", "title": "없음" }, // Состояние: "Отсутствует".
                "description": null,
                "statusItemTypes": [
                  { "code": "3", "title": "없음" },
                  { "code": "4", "title": "미세누수" }, // "Микроутечка".
                  { "code": "5", "title": "누수" } // "Утечка".
                ]
              },
              {
                "type": { "code": "s009", "title": "워터펌프" }, // Название: "Водяной насос".
                "statusType": { "code": "3", "title": "없음" }, // Состояние: "Отсутствует".
                "description": null,
                "statusItemTypes": [
                  { "code": "3", "title": "없음" },
                  { "code": "4", "title": "미세누수" },
                  { "code": "5", "title": "누수" }
                ]
              },
              {
                "type": { "code": "s010", "title": "라디에이터" }, // Название: "Радиатор".
                "statusType": { "code": "3", "title": "없음" }, // Состояние: "Отсутствует".
                "description": null,
                "statusItemTypes": [
                  { "code": "3", "title": "없음" },
                  { "code": "4", "title": "미세누수" },
                  { "code": "5", "title": "누수" }
                ]
              },
              {
                "type": { "code": "s011", "title": "냉각수 수량" }, // Название: "Уровень охлаждающей жидкости".
                "statusType": { "code": "2", "title": "적정" }, // Состояние: "Нормальный".
                "description": null,
                "statusItemTypes": [
                  { "code": "2", "title": "적정" },
                  { "code": "8", "title": "부족" }
                ]
              }
            ],
            "statusType": null,
            "description": null,
            "statusItemTypes": []
          },
          {
            "type": { "code": "s012", "title": "커먼레일" }, // Название: "Common Rail" (система впрыска топлива).
            "children": [],
            "statusType": { "code": "1", "title": "양호" }, // Состояние: "Хорошее".
            "description": null,
            "statusItemTypes": [
              { "code": "1", "title": "양호" },
              { "code": "10", "title": "불량" }
            ]
          }
        ]
      },
      {
        "type": { "code": "S02", "title": "변속기" }, // Название: "Трансмиссия" (детальная проверка).
        "price": null,
        "children": [
          {
            "type": { "code": "s0201", "title": "자동변속기(A/T)" }, // Название: "Автоматическая трансмиссия".
            "children": [
              {
                "type": { "code": "s013", "title": "오일누유" }, // Название: "Утечка масла".
                "statusType": { "code": "3", "title": "없음" }, // Состояние: "Отсутствует".
                "description": null,
                "statusItemTypes": [
                  { "code": "3", "title": "없음" },
                  { "code": "4", "title": "미세누수" },
                  { "code": "5", "title": "누수" }
                ]
              },
              {
                "type": { "code": "s014", "title": "오일유량 및 상태" }, // Название: "Уровень и состояние масла".
                "statusType": null, // Состояние не указано.
                "description": null,
                "statusItemTypes": [
                  { "code": "2", "title": "적정" }, // "Нормальный".
                  { "code": "8", "title": "부족" }, // "Недостаточный".
                  { "code": "9", "title": "과다" } // "Избыточный".
                ]
              },
              {
                "type": { "code": "s015", "title": "작동상태(공회전)" }, // Название: "Рабочая состояние (холостой ход)".
                "statusType": { "code": "1", "title": "양호" }, // Состояние: "Хорошее".
                "description": null,
                "statusItemTypes": [
                  { "code": "1", "title": "양호" },
                  { "code": "10", "title": "불량" }
                ]
              }
            ],
            "statusType": null,
            "description": null,
            "statusItemTypes": []
          },
          {
            "type": { "code": "s0202", "title": "수동변속기(M/T)" }, // Название: "Механическая трансмиссия".
            "children": [
              {
                "type": { "code": "s016", "title": "오일누유" }, // Название: "Утечка масла".
                "statusType": null, // Состояние не указано.
                "description": null,
                "statusItemTypes": [
                  { "code": "3", "title": "없음" },
                  { "code": "4", "title": "미세누수" },
                  { "code": "5", "title": "누수" }
                ]
              },
              {
                "type": { "code": "s017", "title": "기어변속장치" }, // Название: "Механизм переключения передач".
                "statusType": null,
                "description": null,
                "statusItemTypes": [
                  { "code": "1", "title": "양호" },
                  { "code": "10", "title": "불량" }
                ]
              },
              {
                "type": { "code": "s018", "title": "오일유량 및 상태" }, // Название: "Уровень и состояние масла".
                "statusType": null,
                "description": null,
                "statusItemTypes": [
                  { "code": "2", "title": "적정" },
                  { "code": "8", "title": "부족" },
                  { "code": "9", "title": "과다" }
                ]
              },
              {
                "type": { "code": "s019", "title": "작동상태(공회전)" }, // Название: "Рабочая состояние (холостой ход)".
                "statusType": null,
                "description": null,
                "statusItemTypes": [
                  { "code": "1", "title": "양호" },
                  { "code": "10", "title": "불량" }
                ]
              }
            ],
            "statusType": null,
            "description": null,
            "statusItemTypes": []
          }
        ]
      },
      {
        "type": { "code": "S03", "title": "동력전달" }, // Название: "Система передачи мощности".
        "price": null,
        "children": [
          {
            "type": { "code": "s020", "title": "클러치 어셈블리" }, // Название: "Сцепление".
            "children": [],
            "statusType": null,
            "description": null,
            "statusItemTypes": [
              { "code": "1", "title": "양호" },
              { "code": "10", "title": "불량" }
            ]
          },
          {
            "type": { "code": "s021", "title": "등속조인트" }, // Название: "ШРУС (шарнир равных угловых скоростей)".
            "children": [],
            "statusType": { "code": "1", "title": "양호" }, // Состояние: "Хорошее".
            "description": null,
            "statusItemTypes": [
              { "code": "1", "title": "양호" },
              { "code": "10", "title": "불량" }
            ]
          },
          {
            "type": { "code": "s022", "title": "추친축 및 베어링" }, // Название: "Карданный вал и подшипники".
            "children": [],
            "statusType": null,
            "description": null,
            "statusItemTypes": [
              { "code": "1", "title": "양호" },
              { "code": "10", "title": "불량" }
            ]
          },
          {
            "type": { "code": "s037", "title": "디피렌셜 기어" }, // Название: "Дифференциальная передача".
            "children": [],
            "statusType": null,
            "description": null,
            "statusItemTypes": [
              { "code": "1", "title": "양호" },
              { "code": "10", "title": "불량" }
            ]
          }
        ]
      },
      {
        "type": { "code": "S04", "title": "조향" }, // Название: "Рулевое управление".
        "price": null,
        "children": [
          {
            "type": { "code": "s023", "title": "동력조향 작동 오일 누유" }, // Название: "Утечка масла гидроусилителя руля".
            "children": [],
            "statusType": { "code": "3", "title": "없음" }, // Состояние: "Отсутствует".
            "description": null,
            "statusItemTypes": [
              { "code": "3", "title": "없음" },
              { "code": "4", "title": "미세누수" },
              { "code": "5", "title": "누수" }
            ]
          },
          {
            "type": { "code": "S0402", "title": "작동상태" }, // Название: "Рабочая состояние".
            "children": [
              {
                "type": { "code": "s025", "title": "스티어링 펌프" }, // Название: "Насос рулевого управления".
                "statusType": null,
                "description": null,
                "statusItemTypes": [
                  { "code": "1", "title": "양호" },
                  { "code": "10", "title": "불량" }
                ]
              },
              {
                "type": { "code": "s024", "title": "스티어링 기어(MDPS포함)" }, // Название: "Рулевая рейка (включая MDPS)".
                "statusType": { "code": "1", "title": "양호" }, // Состояние: "Хорошее".
                "description": null,
                "statusItemTypes": [
                  { "code": "1", "title": "양호" },
                  { "code": "10", "title": "불량" }
                ]
              },
              {
                "type": { "code": "s038", "title": "스티어링 조인트" }, // Название: "Рулевой шарнир".
                "statusType": { "code": "1", "title": "양호" }, // Состояние: "Хорошее".
                "description": null,
                "statusItemTypes": [
                  { "code": "1", "title": "양호" },
                  { "code": "10", "title": "불량" }
                ]
              },
              {
                "type": { "code": "s039", "title": "파워고압호스" }, // Название: "Шланг высокого давления".
                "statusType": null,
                "description": null,
                "statusItemTypes": [
                  { "code": "1", "title": "양호" },
                  { "code": "10", "title": "불량" }
                ]
              },
              {
                "type": { "code": "s026", "title": "타이로드엔드 및 볼 조인트" }, // Название: "Наконечник рулевой тяги и шаровой шарнир".
                "statusType": { "code": "1", "title": "양호" }, // Состояние: "Хорошее".
                "description": null,
                "statusItemTypes": [
                  { "code": "1", "title": "양호" },
                  { "code": "10", "title": "불량" }
                ]
              }
            ],
            "statusType": null,
            "description": null,
            "statusItemTypes": []
          }
        ]
      },
      {
        "type": { "code": "S05", "title": "제동" }, // Название: "Тормозная система".
        "price": null,
        "children": [
          {
            "type": { "code": "s027", "title": "브레이크 마스터 실린더오일 누유" }, // Название: "Утечка масла главного тормозного цилиндра".
            "children": [],
            "statusType": { "code": "3", "title": "없음" }, // Состояние: "Отсутствует".
            "description": null,
            "statusItemTypes": [
              { "code": "3", "title": "없음" },
              { "code": "4", "title": "미세누수" },
              { "code": "5", "title": "누수" }
            ]
          },
          {
            "type": { "code": "s028", "title": "브레이크 오일 누유" }, // Название: "Утечка тормозной жидкости".
            "children": [],
            "statusType": { "code": "3", "title": "없음" }, // Состояние: "Отсутствует".
            "description": null,
            "statusItemTypes": [
              { "code": "3", "title": "없음" },
              { "code": "4", "title": "미세누수" },
              { "code": "5", "title": "누수" }
            ]
          },
          {
            "type": { "code": "s029", "title": "배력장치 상태" }, // Название: "Состояние вакуумного усилителя тормозов".
            "children": [],
            "statusType": { "code": "1", "title": "양호" }, // Состояние: "Хорошее".
            "description": null,
            "statusItemTypes": [
              { "code": "1", "title": "양호" },
              { "code": "10", "title": "불량" }
            ]
          }
        ]
      },
      {
        "type": { "code": "S06", "title": "전기" }, // Название: "Электрическая система".
        "price": null,
        "children": [
          {
            "type": { "code": "s030", "title": "발전기 출력" }, // Название: "Выход генератора".
            "children": [],
            "statusType": { "code": "1", "title": "양호" }, // Состояние: "Хорошее".
            "description": null,
            "statusItemTypes": [
              { "code": "1", "title": "양호" },
              { "code": "10", "title": "불량" }
            ]
          },
          {
            "type": { "code": "s031", "title": "시동 모터" }, // Название: "Стартер".
            "children": [],
            "statusType": { "code": "1", "title": "양호" }, // Состояние: "Хорошее".
            "description": null,
            "statusItemTypes": [
              { "code": "1", "title": "양호" },
              { "code": "10", "title": "불량" }
            ]
          },
          {
            "type": { "code": "s032", "title": "와이퍼 모터 기능" }, // Название: "Функция мотора стеклоочистителя".
            "children": [],
            "statusType": { "code": "1", "title": "양호" }, // Состояние: "Хорошее".
            "description": null,
            "statusItemTypes": [
              { "code": "1", "title": "양호" },
              { "code": "10", "title": "불량" }
            ]
          },
          {
            "type": { "code": "s033", "title": "실내송풍 모터" }, // Название: "Мотор вентилятора салона".
            "children": [],
            "statusType": { "code": "1", "title": "양호" }, // Состояние: "Хорошее".
            "description": null,
            "statusItemTypes": [
              { "code": "1", "title": "양호" },
              { "code": "10", "title": "불량" }
            ]
          },
          {
            "type": { "code": "s034", "title": "라디에이터 팬 모터" }, // Название: "Мотор вентилятора радиатора".
            "children": [],
            "statusType": { "code": "1", "title": "양호" }, // Состояние: "Хорошее".
            "description": null,
            "statusItemTypes": [
              { "code": "1", "title": "양호" },
              { "code": "10", "title": "불량" }
            ]
          },
          {
            "type": { "code": "s035", "title": "윈도우 모터" }, // Название: "Мотор стеклоподъёмников".
            "children": [],
            "statusType": { "code": "1", "title": "양호" }, // Состояние: "Хорошее".
            "description": null,
            "statusItemTypes": [
              { "code": "1", "title": "양호" },
              { "code": "10", "title": "불량" }
            ]
          }
        ]
      },
      {
        "type": { "code": "S07", "title": "연료" }, // Название: "Топливная система".
        "price": null,
        "children": [
          {
            "type": { "code": "s036", "title": "연료누출(LP가스포함)" }, // Название: "Утечка топлива (включая LP-газ)".
            "children": [],
            "statusType": { "code": "3", "title": "없음" }, // Состояние: "Отсутствует".
            "description": null,
            "statusItemTypes": [
              { "code": "3", "title": "없음" },
              { "code": "11", "title": "있음" } // "Присутствует".
            ]
          }
        ]
      }
    ],
    "master": { // Основные данные об автомобиле и его состоянии.
      "detail": {
        "vin": "ZN661TUG5JX305842", // VIN-номер автомобиля, уникальный идентификатор.
        "tuning": false, // Наличие тюнинга (false — отсутствует).
        "mileage": 103048, // Пробег автомобиля в километрах.
        "version": "V200922", // Версия системы диагностики или отчёта.
        "comments": "트렁크탈부착", // Комментарий: "Багажник снимался/устанавливался".
        "inspName": "안산 워런티코리아 김형근", // Имя инспектора: "Ким Хён Гын, Ансан Warranty Korea".
        "recordNo": "5501116616", // Номер записи в системе Encar.
        "waterlog": false, // Было ли затопление (false — нет).
        "colorType": null, // Тип цвета (null — не указан).
        "issueDate": "20240404", // Дата выпуска диагностического отчёта (4 апреля 2024).
        "modelYear": "2018  ", // Год модели автомобиля.
        "motorType": "M16164D", // Тип двигателя (код модели).
        "trnsCheck": "Y", // Проверка трансмиссии (Y — да).
        "noticeName": "퍼스트다이렉트중고차", // Название компании-продавца: "First Direct Used Car".
        "engineCheck": "Y", // Проверка двигателя (Y — да).
        "carStateType": { "code": "1", "title": "양호" }, // Общее состояние автомобиля: "Хорошее".
        "guarantyType": { "code": "2", "title": "보험사보증" }, // Тип гарантии: "Гарантия страховой компании".
        "seriousTypes": [], // Серьёзные дефекты (пустой список — их нет).
        "performTester": null, // Имя тестера производительности (null — не указан).
        "boardStateType": { "code": "1", "title": "양호" }, // Состояние приборной панели: "Хорошее".
        "paintPartTypes": [], // Части, требующие покраски (пусто — не требуется).
        "mainOptionTypes": [], // Основные опции (пусто — не указано).
        "validityEndDate": "20240812", // Дата окончания гарантии (12 августа 2024).
        "mileageStateType": null, // Состояние пробега (null — не указано).
        "transmissionType": null, // Тип трансмиссии (null — не указано здесь).
        "tuningStateTypes": [], // Состояние тюнинга (пусто — нет данных).
        "usageChangeTypes": [], // Изменения в использовании (пусто — нет данных).
        "validityStartDate": "20220813", // Дата начала гарантии (13 августа 2022).
        "recallFullFillTypes": [], // Выполненные отзыва (пусто — нет данных).
        "firstRegistrationDate": "20180813" // Дата первой регистрации (13 августа 2018).
      },
      "accdient": false, // Ошибка в написании "accident" (false — аварий не было по данным Encar).
      "supplyNum": "20241142946 ", // Номер поставки или лота в системе.
      "simpleRepair": false, // Простой ремонт (false — не проводился).
      "registrationDate": "2024-05-16T12:51:00" // Дата регистрации объявления.
    },
    "outers": [], // Внешние дефекты (пусто — не обнаружено).
    "formats": [ "TABLE" ], // Формат отображения данных (таблица).
    "vehicleId": 37424098, // Идентификатор автомобиля (совпадает с diagnosis.vehicleId).
    "directManagement": null, // Прямое управление (null — не указано).
    "inspectionSource": { // Источник данных инспекции.
      "code": "PARTNERSHIP", // Код источника: "Партнёрство" (проверка через партнёра Encar).
      "updaterId": "unused78", // Идентификатор обновившего данные.
      "registrantId": "unused78", // Идентификатор зарегистрировавшего данные.
      "reservationId": 2056035, // Номер бронирования инспекции.
      "inspectionVersion": "V200623" // Версия системы инспекции.
    }
  },
  "record_open": { // Секция открытых данных: информация об истории автомобиля из общедоступных источников.
    "use": "2", // Код использования (2 — вероятно, "личное использование").
    "fuel": null, // Тип топлива (null — не указан здесь).
    "loan": 0, // Наличие кредита (0 — нет кредита).
    "year": "2018", // Год выпуска автомобиля.
    "carNo": "62수3827", // Номерной знак автомобиля.
    "maker": "마세라티", // Производитель: "Maserati".
    "model": "르반떼", // Модель: "Levante".
    "carKind": "1", // Код типа автомобиля (1 — вероятно, легковой).
    "carName": null, // Полное название автомобиля (null — не указано).
    "regDate": "2024-05-16T10:51:50", // Дата регистрации в системе (возможно, объявления).
    "business": 0, // Использование в бизнесе (0 — не использовался).
    "carShape": null, // Форма кузова (null — не указано).
    "openData": true, // Доступность открытых данных (true — данные открыты).
    "accidents": [ // Список аварий из истории.
      {
        "date": "2020-05-22", // Дата аварии.
        "type": "3", // Тип аварии (3 — возможно, незначительное столкновение).
        "partCost": 0, // Стоимость запчастей (0 — не потребовались).
        "laborCost": 576000, // Стоимость работы (в вонах).
        "paintingCost": 375710, // Стоимость покраски.
        "insuranceBenefit": 1163400 // Страховое возмещение.
      },
      {
        "date": "2019-09-28", // Дата аварии.
        "type": "2", // Тип аварии (2 — возможно, средняя тяжесть).
        "partCost": 1169600, // Стоимость запчастей.
        "laborCost": 325500, // Стоимость работы.
        "paintingCost": 0, // Стоимость покраски (0 — не проводилась).
        "insuranceBenefit": 2635900 // Страховое возмещение.
      }
    ],
    "firstDate": "2018-08-13", // Дата первой регистрации (совпадает с master.detail.firstRegistrationDate).
    "floodDate": null, // Дата затопления (null — не было).
    "robberCnt": 0, // Количество угонов (0 — не угонялся).
    "government": 0, // Использование государством (0 — не использовался).
    "robberDate": null, // Дата угона (null — не было).
    "accidentCnt": 1, // Общее количество аварий (1 — по данным Encar).
    "carNameCode": null, // Код полного названия автомобиля (null — не указано).
    "carInfoUse1s": [ "2" ], // Код основного использования (2 — личное).
    "carInfoUse2s": [ "1" ], // Код дополнительного использования (1 — возможно, другое значение).
    "displacement": "2987", // Объём двигателя в куб. см (2987 — 3.0 литра).
    "notJoinDate1": null, // Дата неучастия в программах (null — не указано).
    "notJoinDate2": null,
    "notJoinDate3": null,
    "notJoinDate4": null,
    "notJoinDate5": null,
    "ownerChanges": [ // Даты смены владельцев.
      "2024-04-02",
      "2024-03-13",
      "2023-11-30"
    ],
    "totalLossCnt": 0, // Количество случаев полной утраты (0 — не было).
    "transmission": null, // Тип трансмиссии (null — не указано здесь).
    "myAccidentCnt": 1, // Количество аварий владельца (1).
    "totalLossDate": null, // Дата полной утраты (null — не было).
    "carInfoChanges": [ // Изменения информации об автомобиле.
      {
        "date": "2018-08-13", // Дата изменения.
        "carNo": "62수XXXX" // Старый номер (замаскирован).
      }
    ],
    "carNoChangeCnt": 0, // Количество смен номеров (0 — не менялся).
    "myAccidentCost": 1495100, // Стоимость аварий владельца (в вонах).
    "ownerChangeCnt": 3, // Количество смен владельцев (3).
    "floodPartLossCnt": null, // Количество частичных утрат от затопления (null — не указано).
    "otherAccidentCnt": 1, // Количество аварий других сторон (1).
    "floodTotalLossCnt": 0, // Количество полных утрат от затопления (0).
    "otherAccidentCost": 951710 // Стоимость аварий других сторон (в вонах).
  }
}