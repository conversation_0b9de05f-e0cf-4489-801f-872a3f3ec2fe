<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Тест размера логотипа | SHMS</title>
  <link rel="stylesheet" href="css/header-common.css" />
  <style>
    body {
      font-family: "Inter", Arial, sans-serif;
      margin: 0;
      padding: 0;
      background: #f5f5f5;
    }
    
    .test-section {
      padding: 2rem;
      margin: 2rem auto;
      max-width: 1200px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .test-section h2 {
      color: #333;
      margin-bottom: 1rem;
      border-bottom: 2px solid #000;
      padding-bottom: 0.5rem;
    }
    
    .logo-test {
      display: flex;
      align-items: center;
      gap: 2rem;
      margin: 1rem 0;
      padding: 1rem;
      background: #f9f9f9;
      border-radius: 4px;
    }
    
    .logo-info {
      font-size: 0.9rem;
      color: #666;
    }
    
    .size-indicator {
      background: #e3f2fd;
      padding: 0.5rem;
      border-radius: 4px;
      font-weight: bold;
      color: #1976d2;
    }
    
    .stock-page-demo {
      background: #fff3e0;
      border: 2px dashed #ff9800;
    }
    
    .stock-page-demo .header__logo-img {
      transform: translateX(35px) !important;
      margin-top: 15px !important;
    }
  </style>
</head>

<body>
  <div class="test-section">
    <h2>🔍 Тест размеров логотипа SHMS</h2>
    
    <div class="logo-test">
      <img src="/Img/logo.png" alt="SHMS" class="header__logo-img">
      <div class="logo-info">
        <div class="size-indicator">Обычный логотип: 30px высота</div>
        <p>Этот логотип должен быть 30px в высоту на всех страницах</p>
      </div>
    </div>
    
    <div class="logo-test stock-page-demo">
      <img src="/Img/logo.png" alt="SHMS" class="header__logo-img">
      <div class="logo-info">
        <div class="size-indicator">Логотип на странице stock: 30px + сдвиг влево</div>
        <p>На странице stock.html логотип сдвинут на 35px влево для анимации</p>
      </div>
    </div>
    
    <div class="logo-test">
      <img src="/Img/logo.png" alt="SHMS" class="mobile-header__logo-img">
      <div class="logo-info">
        <div class="size-indicator">Мобильный логотип: 30px высота</div>
        <p>Мобильная версия логотипа также 30px в высоту</p>
      </div>
    </div>
  </div>
  
  <div class="test-section">
    <h2>📏 CSS переменные</h2>
    <div style="font-family: monospace; background: #f5f5f5; padding: 1rem; border-radius: 4px;">
      --logo-height-desktop: 30px;<br>
      --logo-height-mobile: 30px;<br>
      --header-height-desktop: 60px;<br>
      --header-height-mobile: 60px;
    </div>
  </div>
  
  <div class="test-section">
    <h2>🎯 Проверка на разных страницах</h2>
    <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
      <a href="/stock.html" style="background: #4caf50; color: white; padding: 0.8rem 1.5rem; text-decoration: none; border-radius: 4px;">
        Авто в наличии (с анимацией)
      </a>
      <a href="/contacts.html" style="background: #2196f3; color: white; padding: 0.8rem 1.5rem; text-decoration: none; border-radius: 4px;">
        Контакты
      </a>
      <a href="/order.html" style="background: #ff9800; color: white; padding: 0.8rem 1.5rem; text-decoration: none; border-radius: 4px;">
        Авто под заказ
      </a>
      <a href="/feedback-mockup.html" style="background: #9c27b0; color: white; padding: 0.8rem 1.5rem; text-decoration: none; border-radius: 4px;">
        Обратная связь
      </a>
      <a href="/" style="background: #607d8b; color: white; padding: 0.8rem 1.5rem; text-decoration: none; border-radius: 4px;">
        Главная
      </a>
    </div>
  </div>
  
  <div class="test-section">
    <h2>✅ Ожидаемый результат</h2>
    <ul>
      <li>✅ Логотип имеет высоту 30px на всех страницах</li>
      <li>✅ На странице stock.html логотип сдвинут влево на 35px</li>
      <li>✅ Hover-анимация работает корректно</li>
      <li>✅ Мобильная версия отображается правильно</li>
      <li>✅ Единообразие сохранено на всех страницах</li>
    </ul>
  </div>
</body>
</html>
