const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// Путь к базе данных
const dbPath = path.join(__dirname, '../data/stock.db');

console.log('🔧 Настройка базы данных для системы авторизации...');
console.log('📍 Путь к БД:', dbPath);

// Проверяем существование директории data
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
    console.log('📁 Создание директории data...');
    fs.mkdirSync(dataDir, { recursive: true });
}

// Подключаемся к базе данных
const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('❌ Ошибка подключения к базе данных:', err.message);
        process.exit(1);
    }
    console.log('✅ Подключение к базе данных установлено');
});

// SQL запросы для создания таблиц
const createAdminsTable = `
CREATE TABLE IF NOT EXISTS admins (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME,
    reset_token VARCHAR(255),
    reset_token_expires DATETIME
);
`;

const createSecurityLogsTable = `
CREATE TABLE IF NOT EXISTS security_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    admin_id INTEGER,
    action VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    details TEXT,
    success BOOLEAN,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(id)
);
`;

const createLoginAttemptsTable = `
CREATE TABLE IF NOT EXISTS login_attempts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ip_address VARCHAR(45) NOT NULL,
    username VARCHAR(50),
    success BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
`;

// Создание индексов для оптимизации
const createIndexes = [
    'CREATE INDEX IF NOT EXISTS idx_admins_username ON admins(username);',
    'CREATE INDEX IF NOT EXISTS idx_admins_email ON admins(email);',
    'CREATE INDEX IF NOT EXISTS idx_admins_reset_token ON admins(reset_token);',
    'CREATE INDEX IF NOT EXISTS idx_security_logs_admin_id ON security_logs(admin_id);',
    'CREATE INDEX IF NOT EXISTS idx_security_logs_action ON security_logs(action);',
    'CREATE INDEX IF NOT EXISTS idx_security_logs_created_at ON security_logs(created_at);',
    'CREATE INDEX IF NOT EXISTS idx_login_attempts_ip ON login_attempts(ip_address);',
    'CREATE INDEX IF NOT EXISTS idx_login_attempts_created_at ON login_attempts(created_at);'
];

// Функция для выполнения SQL запроса
function runQuery(sql, description) {
    return new Promise((resolve, reject) => {
        db.run(sql, (err) => {
            if (err) {
                console.error(`❌ Ошибка при ${description}:`, err.message);
                reject(err);
            } else {
                console.log(`✅ ${description} выполнено успешно`);
                resolve();
            }
        });
    });
}

// Основная функция настройки
async function setupDatabase() {
    try {
        console.log('\n📋 Создание таблиц...');
        
        // Создаем таблицы
        await runQuery(createAdminsTable, 'Создание таблицы admins');
        await runQuery(createSecurityLogsTable, 'Создание таблицы security_logs');
        await runQuery(createLoginAttemptsTable, 'Создание таблицы login_attempts');
        
        console.log('\n🔍 Создание индексов...');
        
        // Создаем индексы
        for (const indexSql of createIndexes) {
            await runQuery(indexSql, 'Создание индекса');
        }
        
        console.log('\n✅ База данных для системы авторизации настроена успешно!');
        console.log('📊 Созданные таблицы:');
        console.log('   - admins (администраторы)');
        console.log('   - security_logs (логи безопасности)');
        console.log('   - login_attempts (попытки входа)');
        
    } catch (error) {
        console.error('❌ Ошибка при настройке базы данных:', error);
        process.exit(1);
    } finally {
        // Закрываем соединение с базой данных
        db.close((err) => {
            if (err) {
                console.error('❌ Ошибка при закрытии базы данных:', err.message);
            } else {
                console.log('🔒 Соединение с базой данных закрыто');
            }
        });
    }
}

// Запускаем настройку
setupDatabase();
