<!DOCTYPE html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Авто под заказ | SHMS</title>
    <link rel="stylesheet" href="css/order.css" />
    <link rel="stylesheet" href="css/footer.css" />
    <link rel="stylesheet" href="css/mobile-header.css" />
    <link rel="stylesheet" href="css/hero.css?v=5" />
    <link rel="stylesheet" href="css/header-common.css?v=5" />
    <link rel="stylesheet" href="css/fiziev-modal.css" />
    <script src="js/menu.js"></script>
    <script src="js/currency-converter.js"></script>
    <script src="js/order-encar.js"></script>
    <style>
      .gradient-oval,
      .pulse {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
        width: 0 !important;
        height: 0 !important;
        position: absolute !important;
        pointer-events: none !important;
        z-index: -9999 !important;
      }

      #original-cards-template {
        display: none;
      }

      .loading {
        text-align: center;
        padding: 30px;
        font-size: 18px;
        color: #333;
      }

      .error {
        text-align: center;
        padding: 30px;
        font-size: 18px;
        color: #cc0000;
      }

      .no-cars {
        text-align: center;
        padding: 30px;
        font-size: 18px;
        color: #666;
      }

      .load-more-btn {
        display: block;
        margin: 30px auto;
        padding: 12px 30px;
        background-color: #000;
        color: #fff;
        border: none;
        border-radius: 4px;
        font-size: 16px;
        cursor: pointer;
        transition: background-color 0.3s;
      }

      .load-more-btn:hover {
        background-color: #333;
      }

      .retry-button {
        display: inline-block;
        margin-top: 10px;
        padding: 8px 20px;
        background-color: #000;
        color: #fff;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }

      .clear-search {
        display: inline-block;
        margin-top: 10px;
        padding: 8px 20px;
        background-color: #000;
        color: #fff;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }

      /* Стили для поисковой панели */
      .search-container {
        display: flex;
        max-width: 600px;
        margin: 0 auto;
        width: 100%;
      }

      .search-box {
        position: relative;
        flex-grow: 1;
      }

      .search-input {
        width: 100%;
        padding: 12px 15px;
        font-size: 16px;
        border: 1px solid #ddd;
        border-radius: 4px 0 0 4px;
        height: 100%;
        box-sizing: border-box;
      }

      .search-button {
        padding: 12px 25px;
        background: #000;
        color: white;
        border: none;
        border-radius: 0 4px 4px 0;
        cursor: pointer;
        font-size: 16px;
        transition: background-color 0.3s;
      }

      .search-button:hover {
        background: #333;
      }

      /* Стили для фильтров */
      .filter-container {
        max-width: 600px;
        margin: 15px auto 0;
        width: 100%;
      }

      .filter-dropdown {
        border: 1px solid #ddd;
        border-radius: 4px;
        background: #fff;
        width: 100%;
      }

      .filter-summary {
        padding: 10px 15px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 500;
        user-select: none;
      }

      .filter-arrow {
        font-size: 12px;
        transition: transform 0.3s ease;
      }

      details[open] .filter-arrow {
        transform: rotate(180deg);
      }

      .filter-options {
        padding: 15px;
        border-top: 1px solid #eee;
      }

      .filter-row {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
        flex-wrap: wrap;
      }

      .filter-group {
        flex: 1;
        min-width: 200px;
      }

      .filter-group label {
        display: block;
        margin-bottom: 5px;
        font-size: 14px;
        color: #555;
      }

      .filter-select {
        width: 100%;
        padding: 8px 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
      }

      .range-filter {
        display: flex;
        gap: 10px;
      }

      .range-filter .filter-select {
        flex: 1;
      }

      /* Range slider styles */
      .range-slider {
        width: 100%;
        margin: 20px 0;
      }

      .range-slider .slider-label {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        font-size: 14px;
        color: #555;
      }

      .slider-container {
        position: relative;
        width: 100%;
        height: 30px;
        margin: 15px 0;
      }

      .slider-track {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 100%;
        height: 2px;
        background: #ddd;
        border-radius: 1px;
      }

      .range-slider input[type="range"] {
        -webkit-appearance: none;
        position: absolute;
        width: 100%;
        height: 0;
        background: transparent;
        top: 50%;
        transform: translateY(-50%);
        margin: 0;
        z-index: 2;
        pointer-events: none;
      }

      .range-slider input[type="range"]::-webkit-slider-thumb {
        -webkit-appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #000;
        cursor: pointer;
        pointer-events: auto;
        border: 2px solid #fff;
        box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
      }

      .range-slider input[type="range"]::-moz-range-thumb {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #000;
        cursor: pointer;
        pointer-events: auto;
        border: 2px solid #fff;
        box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
      }

      .range-display {
        text-align: center;
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-top: 5px;
      }

      .filter-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
      }

      .filter-button {
        padding: 8px 16px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      #reset-filters {
        background: #f5f5f5;
        color: #555;
      }

      #reset-filters:hover {
        background: #eaeaea;
        color: #333;
      }

      #apply-filters {
        background: #000;
        color: #fff;
        border-color: #000;
      }

      #apply-filters:hover {
        background: #333;
      }

      /* Responsive styles */
      @media (max-width: 767px) {
        .filter-row {
          flex-direction: column;
          gap: 10px;
        }

        .filter-group {
          width: 100%;
        }
      }

      /* Стили для секции герой */
      .hero-section {
        background-color: #f5f5f5;
        padding: 40px 0;
        text-align: center;
        margin-bottom: 30px;
      }

      .hero-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 15px;
      }

      .hero-content h1 {
        font-size: 32px;
        margin: 0;
        color: #333;
      }

      /* Стили для секции с контентом */
      .order-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 15px;
      }

      .order-status {
        margin: 20px 0;
        font-size: 16px;
        color: #666;
      }

      /* Стили для кнопки "Загрузить еще" */
      .load-more-container {
        display: flex;
        justify-content: center;
        margin: 30px 0;
      }

      .load-more-btn {
        padding: 12px 30px;
        background-color: #000;
        color: #fff;
        border: none;
        border-radius: 4px;
        font-size: 16px;
        cursor: pointer;
        transition: background-color 0.3s;
      }

      .load-more-btn:hover {
        background-color: #333;
      }

      /* Стили для категорий авто */
      .category-tabs {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 10px;
        margin: 20px 0;
      }

      .category-tab {
        padding: 8px 16px;
        background: #f8f8f8;
        border: 1px solid #ddd;
        border-radius: 20px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .category-tab:hover {
        background: #eee;
      }

      .category-tab.active {
        background: #000;
        color: white;
        border-color: #000;
      }

      /* Стили для категорий в карточках */
      .car-category-badge {
        position: absolute;
        top: 10px;
        left: 10px;
        background: rgba(255, 255, 255, 0.8);
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        z-index: 2;
      }

      .car-category-business {
        border-left: 3px solid #394e6a;
      }

      .car-category-sport {
        border-left: 3px solid #b42b2b;
      }

      .car-category-suv {
        border-left: 3px solid #2b7b2b;
      }
    </style>
  </head>

  <body>
    <!-- Mobile Header -->
    <header class="mobile-header">
      <button class="burger" id="burgerBtn" aria-label="Открыть меню">
        <span></span>
        <span></span>
        <span></span>
      </button>
      <a href="index.html" class="mobile-header__logo">
        <img src="/Img/logo.png" alt="SHMS" class="mobile-header__logo-img" />
      </a>
    </header>

    <!-- Пустой раздел для отступа -->
    <div class="mobile-header-spacer"></div>

    <!-- Mobile Menu -->
    <nav class="dropdown-menu" id="dropdownMenu">
      <a href="stock.html">Авто в наличии</a>
      <a href="order.html">Авто под заказ</a>
      <a href="contacts.html">Наши контакты</a>
      <a href="feedback-mockup.html">Обратная связь</a>
    </nav>

    <!-- Hero / Title Section -->
    <section class="hero-stock">
      <div class="hero-header-group">
        <div class="header__left">
          <a
            href="#"
            class="header__back-link"
            onclick="window.history.back(); return false;"
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19 12H5M5 12L12 19M5 12L12 5"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            Назад
          </a>
        </div>
        <div class="header__center-group">
          <nav class="header__nav">
            <a href="index.html" class="header__logo-link"
              ><img src="/Img/logo.png" alt="SHMS" class="header__logo-img"
            /></a>
            <div class="header__nav-links">
              <a href="stock.html" class="header__nav-link">Авто в наличии</a>
              <a href="order.html" class="header__nav-link active"
                >Заказать авто</a
              >
              <a href="contacts.html" class="header__nav-link">Наши контакты</a>
              <a href="feedback-mockup.html" class="header__nav-link"
                >Обратная связь</a
              >
            </div>
          </nav>
        </div>
      </div>
    </section>
    <section class="hero-stock hero-stock--custom">
      <div class="hero-stock__visual">
        <div class="hero-stock__center">
          <h1 class="hero-stock__title-main">Авто под заказ</h1>

          <div class="search-container">
            <div class="search-box">
              <input
                type="text"
                class="search-input"
                placeholder="Напишите марку и модель автомобиля"
                id="search-input"
                autocomplete="off"
              />
            </div>
            <button class="search-button" id="search-btn">Найти</button>
          </div>

          <!-- Категории автомобилей -->
          <div class="category-tabs">
            <button class="category-tab active" data-category="all">
              Все автомобили
            </button>
            <button class="category-tab" data-category="featured" style="display: none;">
              Рекомендуемые
            </button>
            <button class="category-tab" data-category="business">
              Бизнес-класс
            </button>
            <button class="category-tab" data-category="sport">
              Спортивные
            </button>
            <button class="category-tab" data-category="suv">SUV</button>
          </div>

          <!-- Фильтры поиска -->
          <div class="filter-container">
            <details class="filter-dropdown">
              <summary class="filter-summary">
                Фильтры <span class="filter-arrow">▼</span>
              </summary>
              <div class="filter-options">
                <div class="filter-row">
                  <div class="filter-group filter-group--half">
                    <label for="brand-filter">Марка</label>
                    <select id="brand-filter" class="filter-select">
                      <option value="">Все марки</option>
                    </select>
                  </div>
                  <div class="filter-group filter-group--half">
                    <label for="model-filter">Модель</label>
                    <select id="model-filter" class="filter-select">
                      <option value="">Все модели</option>
                    </select>
                  </div>
                </div>
                <div class="filter-row">
                  <div class="filter-group">
                    <label for="body-filter">Тип кузова</label>
                    <select id="body-filter" class="filter-select">
                      <option value="">Любой</option>
                      <option value="Седан">Седан</option>
                      <option value="Хэтчбек">Хэтчбек</option>
                      <option value="Универсал">Универсал</option>
                      <option value="Внедорожник">Внедорожник</option>
                      <option value="Кроссовер">Кроссовер</option>
                      <option value="Купе">Купе</option>
                      <option value="Кабриолет">Кабриолет</option>
                      <option value="Пикап">Пикап</option>
                      <option value="Минивэн">Минивэн</option>
                      <option value="Фургон">Фургон</option>
                    </select>
                  </div>
                  <div class="filter-group">
                    <label for="category-filter">Категория</label>
                    <select id="category-filter" class="filter-select">
                      <option value="">Все категории</option>
                      <option value="business">Бизнес-класс</option>
                      <option value="sport">Спортивные</option>
                      <option value="suv">SUV</option>
                    </select>
                  </div>
                </div>
                <div class="filter-row">
                  <div class="filter-group">
                    <label for="year-slider"
                      >Год выпуска:
                      <span id="year-display">1990 - 2025</span></label
                    >
                    <div class="slider-container">
                      <input
                        type="range"
                        id="year-from-slider"
                        class="range-slider"
                        min="1990"
                        max="2025"
                        value="1990"
                      />
                      <input
                        type="range"
                        id="year-to-slider"
                        class="range-slider"
                        min="1990"
                        max="2025"
                        value="2025"
                      />
                      <div class="slider-track"></div>
                    </div>
                  </div>
                </div>
                <div class="filter-row">
                  <div class="filter-group">
                    <label for="price-slider"
                      >Цена:
                      <span id="price-display">$0 - $1,000,000</span></label
                    >
                    <div class="slider-container">
                      <input
                        type="range"
                        id="price-from-slider"
                        class="range-slider"
                        min="0"
                        max="1000000"
                        step="5000"
                        value="0"
                      />
                      <input
                        type="range"
                        id="price-to-slider"
                        class="range-slider"
                        min="0"
                        max="1000000"
                        step="5000"
                        value="1000000"
                      />
                      <div class="slider-track"></div>
                    </div>
                  </div>
                </div>
                <!-- Фильтр пробега скрыт по требованию -->
                <div class="filter-row" style="display: none">
                  <div class="filter-group">
                    <label for="mileage-slider"
                      >Пробег:
                      <span id="mileage-display">0 - 200,000 км</span></label
                    >
                    <div class="slider-container">
                      <input
                        type="range"
                        id="mileage-from-slider"
                        class="range-slider"
                        min="0"
                        max="200000"
                        step="1000"
                        value="0"
                      />
                      <input
                        type="range"
                        id="mileage-to-slider"
                        class="range-slider"
                        min="0"
                        max="200000"
                        step="1000"
                        value="200000"
                      />
                      <div class="slider-track"></div>
                    </div>
                  </div>
                </div>
                <div class="filter-row">
                  <div class="filter-group">
                    <label for="transmission-filter">Коробка передач</label>
                    <select id="transmission-filter" class="filter-select">
                      <option value="">Любая</option>
                      <option value="Автомат">Автомат</option>
                      <option value="Механика">Механика</option>
                      <option value="Робот">Робот (DCT)</option>
                      <option value="Вариатор">Вариатор (CVT)</option>
                      <option value="Полуавтомат">Полуавтомат</option>
                    </select>
                  </div>
                  <!-- Фильтр типа двигателя скрыт по требованию -->
                  <div class="filter-group" style="display: none">
                    <label for="fuel-filter">Тип двигателя</label>
                    <select id="fuel-filter" class="filter-select">
                      <option value="">Любой</option>
                      <option value="Бензин">Бензин</option>
                      <option value="Дизель">Дизель</option>
                      <option value="Гибрид">Гибрид</option>
                      <option value="Электро">Электромобиль</option>
                      <option value="Газ">Газ</option>
                    </select>
                  </div>
                </div>
                <!-- Фильтр привода скрыт по требованию -->
                <div class="filter-row" style="display: none">
                  <div class="filter-group">
                    <label for="drive-filter">Привод</label>
                    <select id="drive-filter" class="filter-select">
                      <option value="">Любой</option>
                      <option value="Передний">Передний</option>
                      <option value="Задний">Задний</option>
                      <option value="Полный">Полный</option>
                    </select>
                  </div>
                </div>
                <div class="filter-actions">
                  <button id="apply-filters" class="filter-button">
                    Применить
                  </button>
                  <button
                    id="reset-filters"
                    class="filter-button filter-button--reset"
                  >
                    Сбросить
                  </button>
                </div>
              </div>
            </details>
          </div>
        </div>
      </div>
    </section>

    <!-- Main Content -->
    <main class="order-content">
      <div class="order-status">
        <!-- Здесь будет информация о количестве найденных автомобилей -->
      </div>

      <div class="order-cards">
        <!-- Cards will be populated by JavaScript -->
      </div>

      <!-- Кнопка для загрузки дополнительных автомобилей -->
      <div class="load-more-container">
        <button id="load-more-btn" class="load-more-btn">Загрузить еще</button>
      </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
      <div class="footer__info">
        <div class="footer__left">
          <div class="footer__logo">
            <img
              src="/assets/img/icons/logo-shms.png"
              alt="SHMS"
              class="footer__logo-img"
            />
          </div>
          <div class="footer__address">
            Наш адрес:<br />
            Москва,<br />
            Пресненская набережная 12<br />
            Башня "Федерация"<br />
            12 этаж, офис К2
          </div>
        </div>
        <div class="footer__right">
          <nav class="footer__nav">
            <a href="order.html">Под заказ</a>
            <a href="contacts.html">Контакты</a>
            <a href="stock.html">Авто в наличии</a>
          </nav>
          <div class="footer__requisites">
            <div class="footer__requisites-title">Реквизиты организации</div>
            <div class="footer__requisites-info">
              ИП Шамаев Мансур Махмудович<br />
              ИНН 201578554480, ОГРН 324200000020490<br />
            </div>
          </div>
        </div>
      </div>
      <div class="footer__copyright">© 2025 Все права защищены</div>
    </footer>

    <!-- Подключение модального окна Физиева -->
    <script src="js/fiziev-modal.js"></script>
  </body>
</html>
