#!/bin/bash



echo "🚀 Обновление сайта SHMS Auto"

echo "================================"



# Создание бэкапа

echo "💾 Создание бэкапа..."

./backup-site.sh



# Сохранение изменений в Git

echo "📝 Сохранение текущих изменений..."

git add .

git commit -m "Обновление конфигурации сервера $(date '+%Y-%m-%d %H:%M:%S')"



# Остановка приложения

echo "⏹️  Остановка приложения..."

pm2 stop shms-auto



# Обновление кода (если есть удаленный репозиторий)

echo "📥 Получение обновлений..."

git pull origin main || echo "⚠️  Нет удаленного репозитория"



# Установка зависимостей

echo "📦 Обновление зависимостей..."

npm install



# Перезапуск

echo "🔄 Перезапуск приложения..."

pm2 start shms-auto



# Проверка

echo "🔍 Проверка статуса..."

pm2 status

curl -I https://shms-auto.ru



echo "✅ Обновление завершено!"
