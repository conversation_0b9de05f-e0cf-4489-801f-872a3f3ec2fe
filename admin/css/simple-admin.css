/* Simple Admin Styles */

/* Layout */
.simple-editor {
  max-width: 1000px;
  margin: 0 auto;
}

.section-description {
  color: var(--text-light);
  font-size: 0.9rem;
  margin: 0;
  margin-top: 0.5rem;
}

/* Car Selector */
.car-selector {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-bottom: 2rem;
}

.car-select-dropdown {
  flex: 1;
  max-width: 400px;
  padding: 0.875rem 1rem;
  border: 2px solid var(--border);
  border-radius: var(--radius);
  font-size: 1rem;
  background: white;
  transition: var(--transition);
}

.car-select-dropdown:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

/* Selected Car Preview */
.selected-car-preview {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: var(--radius-lg);
  border: 2px solid var(--primary);
}

.car-preview-card {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.car-preview-image {
  width: 120px;
  height: 80px;
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.car-preview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.car-preview-info h3 {
  margin: 0 0 0.5rem 0;
  color: var(--primary);
  font-size: 1.25rem;
}

.car-preview-info p {
  margin: 0 0 1rem 0;
  color: var(--text-light);
}

.car-preview-specs {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.car-preview-specs span {
  background: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  color: var(--text);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Form Sections */
.simple-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--primary);
}

.form-section h3 {
  margin: 0 0 1.5rem 0;
  color: var(--primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text);
  font-size: 0.9rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 1rem;
  transition: var(--transition);
  background: white;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

.form-group textarea {
  resize: vertical;
  font-family: inherit;
  line-height: 1.5;
}

/* Image Upload */
.image-upload-area {
  margin-top: 1rem;
}

.upload-zone {
  border: 2px dashed var(--border);
  border-radius: var(--radius-lg);
  padding: 3rem 2rem;
  text-align: center;
  background: #f8fafc;
  transition: var(--transition);
  cursor: pointer;
}

.upload-zone:hover,
.upload-zone.dragover {
  border-color: var(--primary);
  background: rgba(67, 97, 238, 0.05);
}

.upload-icon {
  font-size: 3rem;
  color: var(--primary);
  margin-bottom: 1rem;
}

.upload-text h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text);
}

.upload-text p {
  margin: 0 0 0.5rem 0;
  color: var(--text-light);
}

.upload-btn {
  background: none;
  border: none;
  color: var(--primary);
  text-decoration: underline;
  cursor: pointer;
  font-size: inherit;
  padding: 0;
  box-shadow: none;
}

.upload-btn:hover {
  color: var(--primary-hover);
  transform: none;
  box-shadow: none;
}

.uploaded-images {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.uploaded-image {
  position: relative;
  height: 120px;
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: white;
  cursor: move;
  transition: all 0.3s ease;
}

.uploaded-image:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.uploaded-image.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.uploaded-image.main-image {
  border: 3px solid #007bff;
  position: relative;
}

.uploaded-image.main-image::before {
  content: "Главное фото";
  position: absolute;
  top: 5px;
  left: 5px;
  background: #007bff;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 600;
  z-index: 2;
}

.image-order-badge {
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
  z-index: 2;
}

.uploaded-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.uploaded-image .image-actions {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  gap: 0.25rem;
}

.image-action-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: none;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.75rem;
  transition: var(--transition);
  box-shadow: none;
  padding: 0;
}

.image-action-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
  box-shadow: none;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  padding: 2rem 0;
}

.action-buttons .btn {
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  min-width: 160px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.btn-secondary {
  background: var(--secondary);
  box-shadow: 0 4px 12px rgba(110, 119, 135, 0.25);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--secondary-hover);
  box-shadow: 0 6px 15px rgba(110, 119, 135, 0.3);
}

.btn-info {
  background: var(--info-gradient);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.25);
}

.btn-info:hover:not(:disabled) {
  box-shadow: 0 6px 15px rgba(52, 152, 219, 0.3);
}

.btn-success {
  background: var(--success-gradient);
  box-shadow: 0 4px 12px rgba(46, 204, 113, 0.25);
}

.btn-success:hover:not(:disabled) {
  box-shadow: 0 6px 15px rgba(46, 204, 113, 0.3);
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border);
  background: var(--light);
}

.modal-header h3 {
  margin: 0;
  color: var(--text);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-light);
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--transition);
  box-shadow: none;
}

.modal-close:hover {
  background: var(--border);
  color: var(--text);
  transform: none;
  box-shadow: none;
}

.modal-body {
  padding: 0;
  height: 70vh;
}

.modal-body iframe {
  width: 100%;
  height: 100%;
  border: none;
}

/* Improved Notifications */
.notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 1100;
  transform: translateX(400px);
  transition: transform 0.3s ease;
}

.notification.show {
  transform: translateX(0);
}

.notification-content {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  min-width: 350px;
  border-left: 4px solid var(--success);
}

.notification-content.error {
  border-left-color: var(--danger);
}

.notification-content.warning {
  border-left-color: #f39c12;
}

.notification-content.info {
  border-left-color: var(--info);
}

.notification-icon {
  font-size: 1.5rem;
  color: var(--success);
}

.notification-content.error .notification-icon {
  color: var(--danger);
}

.notification-content.warning .notification-icon {
  color: #f39c12;
}

.notification-content.info .notification-icon {
  color: var(--info);
}

.notification-message {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--text);
}

.notification-text {
  color: var(--text-light);
  font-size: 0.9rem;
}

.notification-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: var(--text-light);
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--transition);
  box-shadow: none;
}

.notification-close:hover {
  background: var(--border);
  color: var(--text);
  transform: none;
  box-shadow: none;
}

/* Responsive */
@media (max-width: 768px) {
  .car-selector {
    flex-direction: column;
    align-items: stretch;
  }

  .car-preview-card {
    flex-direction: column;
    text-align: center;
  }

  .form-row {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .btn {
    min-width: auto;
  }

  .modal-content {
    width: 95%;
    margin: 1rem;
  }

  .notification {
    right: 1rem;
    left: 1rem;
  }

  .notification-content {
    min-width: auto;
  }
}

/* Existing Pages Management Styles */
.existing-pages-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.pages-filter {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.filter-select {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  background: white;
  font-size: 0.9rem;
  min-width: 150px;
}

.existing-pages-list {
  min-height: 200px;
  position: relative;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  color: var(--text-light);
  text-align: center;
}

.loading-state i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--primary);
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--text-light);
}

.page-item {
  background: white;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: var(--transition);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.page-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.page-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.page-item-info h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text);
  font-size: 1.1rem;
}

.page-item-info p {
  margin: 0;
  color: var(--text-light);
  font-size: 0.9rem;
}

.page-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.page-status.draft {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.page-status.published {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.page-item-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 0.85rem;
  color: var(--text-light);
}

.page-item-meta span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.page-item-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.page-action-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: var(--radius-sm);
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.25rem;
  text-decoration: none;
}

.page-action-btn.edit {
  background: var(--primary);
  color: white;
}

.page-action-btn.edit:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
}

.page-action-btn.view {
  background: var(--info);
  color: white;
}

.page-action-btn.view:hover {
  background: var(--info-hover);
  transform: translateY(-1px);
}

.page-action-btn.delete {
  background: var(--danger);
  color: white;
}

.page-action-btn.delete:hover {
  background: var(--danger-hover);
  transform: translateY(-1px);
}

.page-action-btn.publish {
  background: var(--success);
  color: white;
}

.page-action-btn.publish:hover {
  background: var(--success-hover);
  transform: translateY(-1px);
}

.page-action-btn.unpublish {
  background: var(--warning);
  color: white;
}

.page-action-btn.unpublish:hover {
  background: var(--warning-hover);
  transform: translateY(-1px);
}

.page-action-btn.regenerate {
  background: var(--secondary);
  color: white;
}

.page-action-btn.regenerate:hover {
  background: var(--secondary-hover);
  transform: translateY(-1px);
}

/* Mobile responsiveness for existing pages */
@media (max-width: 768px) {
  .existing-pages-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .page-item-header {
    flex-direction: column;
    gap: 1rem;
  }

  .page-item-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .page-item-actions {
    justify-content: center;
  }

  .page-action-btn {
    flex: 1;
    justify-content: center;
    min-width: 0;
  }
}
