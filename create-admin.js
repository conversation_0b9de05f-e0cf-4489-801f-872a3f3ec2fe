const sqlite3 = require("sqlite3").verbose();
const bcrypt = require("bcrypt");
const path = require("path");
const fs = require("fs");
const readline = require("readline");

// Создаем интерфейс для ввода данных
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

console.log("👤 Создание администратора SHMS Auto");
console.log("===================================\n");

// Путь к базе данных
const dbPath = path.join(__dirname, "data/stock.db");

// Проверяем существование директории data
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
  console.log("📁 Создание директории data...");
  fs.mkdirSync(dataDir, { recursive: true });
}

// Подключаемся к базе данных
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error("❌ Ошибка подключения к базе данных:", err.message);
    process.exit(1);
  }
  console.log("✅ Подключение к базе данных установлено\n");
});

// SQL запросы для создания таблиц
const createAdminsTable = `
CREATE TABLE IF NOT EXISTS admins (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100),
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME,
    reset_token VARCHAR(255),
    reset_token_expires DATETIME
);
`;

const createSecurityLogsTable = `
CREATE TABLE IF NOT EXISTS security_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    admin_id INTEGER,
    action VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    details TEXT,
    success BOOLEAN,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(id)
);
`;

const createLoginAttemptsTable = `
CREATE TABLE IF NOT EXISTS login_attempts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ip_address VARCHAR(45) NOT NULL,
    username VARCHAR(50),
    success BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
`;

// Функция для выполнения SQL запроса
function runQuery(sql, description) {
  return new Promise((resolve, reject) => {
    db.run(sql, (err) => {
      if (err) {
        console.error(`❌ Ошибка при ${description}:`, err.message);
        reject(err);
      } else {
        resolve();
      }
    });
  });
}

// Функция для ввода данных
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

// Функция для ввода пароля (обычный ввод)
function askPassword(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

// Функция проверки существования пользователя
function checkUserExists(username) {
  return new Promise((resolve, reject) => {
    const sql = "SELECT username FROM admins WHERE username = ?";
    db.get(sql, [username], (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

// Функция создания администратора
function createAdmin(adminData) {
  return new Promise((resolve, reject) => {
    // Хешируем пароль
    bcrypt.hash(adminData.password, 12, (err, hash) => {
      if (err) {
        return reject(new Error("Ошибка хеширования пароля"));
      }

      const sql = `
                INSERT INTO admins (username, password_hash, full_name)
                VALUES (?, ?, ?)
            `;

      db.run(
        sql,
        [adminData.username, hash, adminData.fullName],
        function (err) {
          if (err) {
            return reject(
              new Error("Ошибка создания администратора: " + err.message)
            );
          }

          resolve({
            id: this.lastID,
            username: adminData.username,
            fullName: adminData.fullName,
          });
        }
      );
    });
  });
}

// Основная функция
async function main() {
  try {
    // Создаем таблицы если не существуют
    await runQuery(createAdminsTable, "создание таблицы admins");
    await runQuery(createSecurityLogsTable, "создание таблицы security_logs");
    await runQuery(createLoginAttemptsTable, "создание таблицы login_attempts");

    console.log("📋 Введите данные нового администратора:\n");

    // Ввод логина
    let username;
    while (true) {
      username = await askQuestion("Логин: ");
      if (!username) {
        console.log("❌ Логин не может быть пустым");
        continue;
      }
      if (username.length < 3) {
        console.log("❌ Логин должен содержать минимум 3 символа");
        continue;
      }
      break;
    }

    // Проверяем существование пользователя
    const existingUser = await checkUserExists(username);
    if (existingUser) {
      console.log("❌ Пользователь с таким логином уже существует");
      rl.close();
      db.close();
      return;
    }

    // Ввод полного имени
    const fullName = await askQuestion("Полное имя (необязательно): ");

    // Ввод пароля
    let password;
    while (true) {
      password = await askPassword("Пароль: ");
      if (!password) {
        console.log("❌ Пароль не может быть пустым");
        continue;
      }
      if (password.length < 6) {
        console.log("❌ Пароль должен содержать минимум 6 символов");
        continue;
      }

      const confirmPassword = await askPassword("Подтвердите пароль: ");
      if (password !== confirmPassword) {
        console.log("❌ Пароли не совпадают");
        continue;
      }
      break;
    }

    console.log("\n🔄 Создание администратора...");

    // Создаем администратора
    const newAdmin = await createAdmin({
      username,
      password,
      fullName: fullName || null,
    });

    console.log("\n✅ Администратор успешно создан!");
    console.log(`   ID: ${newAdmin.id}`);
    console.log(`   Логин: ${newAdmin.username}`);
    if (newAdmin.fullName) {
      console.log(`   Полное имя: ${newAdmin.fullName}`);
    }

    console.log("\n🎉 Теперь вы можете войти в админку:");
    console.log("   http://localhost:3000/admin");
  } catch (error) {
    console.error("\n❌ Ошибка:", error.message);
  } finally {
    rl.close();
    db.close();
  }
}

// Обработка сигналов завершения
process.on("SIGINT", () => {
  console.log("\n\nОтменено пользователем.");
  rl.close();
  db.close();
  process.exit(0);
});

// Запуск
main();
