* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary: #4361ee;
  --primary-hover: #3a56e4;
  --primary-light: rgba(67, 97, 238, 0.1);
  --primary-gradient: linear-gradient(135deg, #4361ee, #3a56e4);
  --secondary: #6e7787;
  --secondary-hover: #5a6377;
  --success: #2ecc71;
  --success-hover: #27ae60;
  --success-gradient: linear-gradient(135deg, #2ecc71, #27ae60);
  --danger: #e53e3e;
  --danger-hover: #c53030;
  --danger-gradient: linear-gradient(135deg, #e53e3e, #c53030);
  --info: #3498db;
  --info-hover: #2980b9;
  --info-gradient: linear-gradient(135deg, #3498db, #2980b9);
  --warning: #f39c12;
  --warning-hover: #e67e22;
  --warning-gradient: linear-gradient(135deg, #f39c12, #e67e22);
  --dark: #1e293b;
  --light: #f8fafc;
  --border: #e2e8f0;
  --text: #1e293b;
  --text-light: #64748b;
  --white: #ffffff;
  --shadow: rgba(0, 0, 0, 0.1);
  --shadow-lg: rgba(0, 0, 0, 0.08);
  --radius: 10px;
  --radius-lg: 16px;
  --radius-sm: 6px;
  --transition: all 0.3s ease;
  --header-bg: linear-gradient(135deg, #4361ee, #805ad5);
}

body {
  font-family: "Segoe UI", Roboto, -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
  background-color: #f1f5f9;
  color: var(--text);
  font-size: 16px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 1rem;
  color: var(--dark);
}

h1 {
  font-size: 1.875rem;
  background: var(--header-bg);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-bottom: 0.75rem;
}

h2 {
  font-size: 1.5rem;
  position: relative;
  padding-bottom: 0.5rem;
}

h2:after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  height: 3px;
  width: 40px;
  background: var(--primary-gradient);
  border-radius: 3px;
}

h3 {
  font-size: 1.25rem;
}

.admin-container {
  max-width: 1280px;
  margin: 2rem auto;
  padding: 0 1.5rem;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--header-bg);
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 25px -5px rgba(67, 97, 238, 0.15);
  position: relative;
  overflow: hidden;
}

.admin-header:before {
  content: "";
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  pointer-events: none;
}

.admin-header h1 {
  color: white;
  background: none;
  margin-bottom: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.admin-nav {
  display: flex;
  gap: 0.7rem;
}

.admin-nav a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  padding: 0.5rem 1.25rem;
  border-radius: var(--radius);
  transition: var(--transition);
  font-weight: 500;
  backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.15);
}

.admin-nav a:hover {
  background-color: rgba(255, 255, 255, 0.25);
  color: white;
  transform: translateY(-2px);
}

.admin-nav a.active {
  font-weight: 600;
  color: white;
  background-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.admin-section {
  background: var(--white);
  padding: 1.75rem;
  border-radius: var(--radius-lg);
  box-shadow: 0 5px 15px var(--shadow-lg);
  margin-bottom: 2rem;
  transition: var(--transition);
  border: 1px solid rgba(226, 232, 240, 0.6);
  position: relative;
  overflow: hidden;
}

.admin-section:hover {
  box-shadow: 0 10px 25px var(--shadow-lg);
  transform: translateY(-3px);
}

.admin-section:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.form-group {
  margin-bottom: 1.5rem;
  position: relative;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text);
  font-size: 0.9rem;
  transition: var(--transition);
}

input[type="text"],
input[type="password"],
input[type="number"],
textarea,
select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 1rem;
  background-color: var(--white);
  color: var(--text);
  transition: var(--transition);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

input[type="file"] {
  padding: 0.6rem;
  border: 1px dashed var(--border);
  border-radius: var(--radius);
  background-color: var(--light);
  cursor: pointer;
  transition: var(--transition);
}

input[type="file"]:hover {
  border-color: var(--primary);
  background-color: rgba(67, 97, 238, 0.05);
}

button {
  background: var(--primary-gradient);
  color: var(--white);
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--radius);
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(67, 97, 238, 0.25);
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(67, 97, 238, 0.3);
}

button:active {
  transform: translateY(0);
}

button:before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
}

button:hover:before {
  left: 100%;
}

.form-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

#cancel-btn {
  background-color: var(--secondary);
}

#cancel-btn:hover {
  background-color: var(--secondary-hover);
}

.cars-list {
  margin-top: 1.5rem;
}

.car-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  margin-bottom: 0.75rem;
  background-color: var(--white);
  transition: var(--transition);
}

.car-item:hover {
  box-shadow: 0 2px 6px var(--shadow);
  border-color: var(--primary);
}

.car-info {
  flex-grow: 1;
}

.car-actions {
  display: flex;
  gap: 0.5rem;
}

.edit-btn {
  background: var(--success-gradient);
  box-shadow: 0 4px 12px rgba(46, 204, 113, 0.25);
}

.edit-btn:hover {
  box-shadow: 0 6px 15px rgba(46, 204, 113, 0.3);
}

.delete-btn {
  background: var(--danger-gradient);
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.25);
}

.delete-btn:hover {
  box-shadow: 0 6px 15px rgba(229, 62, 62, 0.3);
}

.copy-btn {
  background: var(--info-gradient);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.25);
}

.copy-btn:hover {
  box-shadow: 0 6px 15px rgba(52, 152, 219, 0.3);
}

.detail-btn {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  box-shadow: 0 4px 12px rgba(155, 89, 182, 0.25);
  color: white;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
}

.detail-btn:hover {
  box-shadow: 0 6px 15px rgba(155, 89, 182, 0.3);
  transform: translateY(-2px);
}

.current-file {
  margin-top: 0.75rem;
  padding: 1rem;
  background-color: var(--light);
  border: 1px solid var(--border);
  border-radius: var(--radius);
}

.current-file img {
  max-width: 200px;
  max-height: 150px;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 1px 3px var(--shadow);
}

.controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
  background: var(--light);
  padding: 1.25rem;
  border-radius: var(--radius-lg);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.search-box {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex: 1;
  position: relative;
}

.search-box input {
  flex: 1;
  min-width: 200px;
  padding: 0.875rem 1.25rem;
  padding-left: 3rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.search-box input:focus {
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
  border-color: var(--primary);
}

.search-box:before {
  content: "🔍";
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
  font-size: 1.25rem;
  pointer-events: none;
}

.search-box button {
  padding: 0.875rem 1.25rem;
}

.sort-controls {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.sort-controls label {
  margin-bottom: 0;
  white-space: nowrap;
}

.sort-controls select {
  padding: 0.875rem 1.25rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  min-width: 220px;
  background-color: var(--white);
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236e7787' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  padding-right: 2.5rem;
  appearance: none;
}

.sort-controls select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

.view-controls {
  display: flex;
  align-items: center;
}

.view-controls button {
  padding: 0.875rem 1.25rem;
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  color: var(--text);
  box-shadow: none;
}

.view-controls button:hover {
  background: var(--light);
  border-color: var(--primary);
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.view-controls button.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.search-status {
  margin-bottom: 1rem;
  font-size: 0.875rem;
  color: var(--text-light);
  padding: 0.5rem 0;
  min-height: 2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.search-status:not(:empty):before {
  content: "📋";
  display: inline-block;
  margin-right: 0.25rem;
}

/* Пустые состояния */
.no-results {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--text-light);
  font-weight: 500;
  background-color: var(--light);
  border-radius: var(--radius-lg);
  border: 1px dashed var(--border);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  margin: 1.5rem 0;
  overflow: hidden;
}

.no-results:before {
  content: "🤔";
  font-size: 3rem;
  display: block;
  margin-bottom: 1rem;
  animation: float 3s ease-in-out infinite;
}

.no-results h3 {
  margin-bottom: 0.5rem;
  color: var(--dark);
}

.no-results p {
  max-width: 500px;
  margin: 0 auto;
  opacity: 0.8;
}

.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  background: var(--light);
  border-radius: var(--radius-lg);
  color: var(--text-light);
  border: 1px dashed var(--border);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.empty-state:before {
  content: "📦";
  font-size: 3rem;
  display: block;
  margin-bottom: 1rem;
  animation: float 3s ease-in-out infinite;
}

.empty-state h3 {
  margin-bottom: 0.5rem;
  color: var(--dark);
}

.empty-state p {
  max-width: 500px;
  margin: 0 auto;
  opacity: 0.8;
}

/* Уведомления */
.notification {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  padding: 0;
  border-radius: var(--radius-lg);
  background-color: var(--white);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  transform: translateY(100px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 1000;
  max-width: 350px;
  overflow: hidden;
}

.notification.show {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.notification-success {
  border-left: 4px solid var(--success);
}

.notification-error {
  border-left: 4px solid var(--danger);
}

.notification-info {
  border-left: 4px solid var(--primary);
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1.25rem;
}

.notification-message {
  flex-grow: 1;
  font-size: 0.875rem;
}

.notification-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
  font-size: 1rem;
}

.notification-text {
  color: var(--text-light);
  line-height: 1.5;
}

.notification-close {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  color: var(--text-light);
  font-size: 1.25rem;
  line-height: 1;
  box-shadow: none;
}

.notification-close:hover {
  color: var(--text);
  box-shadow: none;
  transform: none;
}

/* Диалоговые окна */
.order-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.order-dialog.active {
  opacity: 1;
  visibility: visible;
}

.order-dialog-content {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--radius-lg);
  width: 90%;
  max-width: 500px;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
  transform: translateY(-20px) scale(0.95);
  transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.order-dialog.active .order-dialog-content {
  transform: translateY(0) scale(1);
}

.order-dialog-content:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.order-dialog-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.25rem;
  color: var(--dark);
}

.order-dialog-form {
  margin-bottom: 1.5rem;
}

.order-dialog-form label {
  margin-bottom: 0.5rem;
  display: block;
}

.order-dialog-form input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
}

.order-dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.dialog-close-btn {
  background: linear-gradient(135deg, #6e7787, #5a6377);
  color: white;
  border: none;
  padding: 0.75rem 1.25rem;
  border-radius: var(--radius);
  cursor: pointer;
  transition: var(--transition);
}

.dialog-close-btn:hover {
  background: linear-gradient(135deg, #5a6377, #4a5366);
}

.dialog-save-btn {
  background: var(--primary-gradient);
  color: white;
  border: none;
  padding: 0.75rem 1.25rem;
  border-radius: var(--radius);
  cursor: pointer;
  transition: var(--transition);
}

.dialog-save-btn:hover {
  background: linear-gradient(135deg, #3a56e4, #2a46d4);
}

/* Анимации */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes button-loading-spinner {
  from {
    transform: rotate(0turn);
  }
  to {
    transform: rotate(1turn);
  }
}

/* Стили для палитры цветов */
.color-picker-container {
  margin-top: 0.75rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1.25rem;
  background-color: var(--light);
}

.selected-colors {
  margin-bottom: 1.25rem;
}

.selected-colors p {
  font-weight: 500;
  margin-bottom: 0.75rem;
  color: var(--text);
}

.color-preview-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  min-height: 40px;
}

.color-preview {
  position: relative;
  width: 80px;
  height: 40px;
  border-radius: 6px;
  box-shadow: 0 2px 4px var(--shadow);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: var(--transition);
}

.color-preview:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px var(--shadow);
}

.color-preview-swatch {
  flex: 1;
}

.color-preview-name {
  background: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 0.75rem;
  padding: 2px 4px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.color-preview-remove {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--danger);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: 600;
  border: 2px solid white;
  box-shadow: 0 1px 3px var(--shadow);
  opacity: 0;
  transition: var(--transition);
}

.color-preview:hover .color-preview-remove {
  opacity: 1;
  top: 3px;
  right: 3px;
}

.color-preview-remove:hover {
  background: var(--danger-hover);
  transform: scale(1.1);
}

.add-color-section {
  margin-top: 1rem;
}

.color-input-group {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: wrap;
}

.color-picker {
  width: 50px;
  height: 40px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  padding: 0;
  background-color: transparent;
}

.color-name-input {
  flex: 1;
  min-width: 150px;
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
}

.add-color-button {
  background-color: var(--success);
  color: white;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: var(--radius);
  cursor: pointer;
  font-size: 0.875rem;
  white-space: nowrap;
}

.add-color-button:hover {
  background-color: var(--success-hover);
}

/* Компактная и удобная визуализация списка автомобилей */
#cars-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* Альтернативный табличный вид для админки */
.cars-table-view #cars-container {
  display: block;
}

.cars-table-view .car-card {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0.75rem;
  height: auto;
  gap: 1rem;
}

.cars-table-view .car-card-image {
  width: 80px;
  height: 60px;
  flex-shrink: 0;
}

.cars-table-view .car-card-content {
  flex: 1;
  padding: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1rem;
}

.cars-table-view .car-card-info {
  flex: 1;
  min-width: 0;
}

.cars-table-view .car-card-title {
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
  -webkit-line-clamp: 1;
}

.cars-table-view .car-card-subtitle {
  font-size: 0.75rem;
  margin-bottom: 0;
}

.cars-table-view .car-card-price {
  font-size: 0.9rem;
  margin: 0;
  min-width: 100px;
  text-align: right;
}

.cars-table-view .car-card-actions {
  padding-top: 0;
  margin-top: 0;
  flex-shrink: 0;
  min-width: 200px;
}

.cars-table-view .car-card-specs {
  display: none;
}

.featured-container,
.car-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.car-card,
.featured-car {
  background: var(--white);
  border-radius: var(--radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.2s ease;
  border: 1px solid var(--border);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.car-card:hover,
.featured-car:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  border-color: var(--primary);
}

.car-card-image,
.featured-car img {
  height: 180px;
  overflow: hidden;
  position: relative;
  background-color: var(--light);
}

.car-card-image img,
.featured-car img {
  width: 100%;
  height: 100%;
  object-fit: fill;
  transition: none;
}

.car-card-content,
.featured-car-content {
  padding: 1rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.car-card-title,
.featured-car-title {
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  color: var(--dark);
}

.car-card-subtitle,
.featured-car-subtitle {
  color: var(--text-light);
  margin-bottom: 0.75rem;
  font-size: 0.8rem;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.car-card-price,
.featured-car-price {
  font-weight: 600;
  color: var(--primary);
  font-size: 1.1rem;
  margin: 0.5rem 0;
}

.car-card-specs {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
  margin: 0.75rem 0;
  font-size: 0.8rem;
  background-color: var(--light);
  padding: 0.75rem;
  border-radius: var(--radius);
}

.spec-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.spec-label {
  color: var(--text-light);
  font-size: 0.75rem;
  white-space: nowrap;
}

.spec-value {
  font-weight: 600;
  color: var(--dark);
}

.car-card-actions,
.featured-car-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: auto;
  padding-top: 0.75rem;
}

.car-card-actions button,
.featured-car-actions button {
  flex: 1;
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
}

/* Стили для импорта */
.import-form {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
}

.import-form .form-group {
  flex: 1;
  margin-bottom: 0;
}

/* Дополнительные утилиты */
.badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 2rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-primary {
  background-color: rgba(74, 108, 247, 0.1);
  color: var(--primary);
}

.badge-success {
  background-color: rgba(33, 150, 83, 0.1);
  color: var(--success);
}

.badge-danger {
  background-color: rgba(229, 62, 62, 0.1);
  color: var(--danger);
}

.badge-info {
  background-color: rgba(3, 155, 229, 0.1);
  color: var(--info);
}

/* Адаптивность */
@media (max-width: 768px) {
  .controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    width: 100%;
  }

  .sort-controls {
    width: 100%;
  }

  .admin-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .import-form {
    flex-direction: column;
  }

  #cars-container {
    grid-template-columns: 1fr;
  }
}

/* Стили для страницы featured.html */
.featured-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.25rem;
}

.featured-car {
  background: var(--white);
  border-radius: var(--radius);
  box-shadow: 0 1px 3px var(--shadow);
  overflow: hidden;
  position: relative;
  transition: var(--transition);
  border: 1px solid var(--border);
}

.featured-car:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 6px var(--shadow);
  border-color: var(--primary);
}

.featured-car img {
  width: 100%;
  height: 150px;
  object-fit: fill;
  transition: none;
}

.featured-car-content {
  padding: 1.25rem;
}

.featured-car-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
  height: 48px;
  overflow: hidden;
  font-size: 1.125rem;
  line-height: 1.3;
}

.featured-car-price {
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 0.75rem;
  font-size: 1.25rem;
}

.featured-car-order {
  font-size: 0.875rem;
  color: var(--text-light);
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.featured-car-order .badge {
  background-color: rgba(74, 108, 247, 0.1);
  color: var(--primary);
}

.featured-car-actions {
  display: flex;
  gap: 0.5rem;
}

.remove-btn {
  background-color: var(--danger);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  cursor: pointer;
  font-size: 0.875rem;
  flex: 1;
  transition: var(--transition);
}

.remove-btn:hover {
  background-color: var(--danger-hover);
}

.order-btn {
  background-color: var(--secondary);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  cursor: pointer;
  font-size: 0.875rem;
  flex: 1;
  transition: var(--transition);
}

.order-btn:hover {
  background-color: var(--secondary-hover);
}

.car-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.25rem;
}

.add-to-featured-btn {
  background-color: var(--success);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: var(--radius);
  cursor: pointer;
  font-size: 0.875rem;
  width: 100%;
  transition: var(--transition);
}

.add-to-featured-btn:hover {
  background-color: var(--success-hover);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
}

.alert {
  padding: 0.75rem 1rem;
  border-radius: var(--radius);
  margin-bottom: 1rem;
  font-size: 0.875rem;
  display: inline-block;
}

.alert-success {
  background-color: rgba(33, 150, 83, 0.1);
  color: var(--success);
  border: 1px solid rgba(33, 150, 83, 0.2);
}

.alert-danger {
  background-color: rgba(229, 62, 62, 0.1);
  color: var(--danger);
  border: 1px solid rgba(229, 62, 62, 0.2);
}

.alert-info {
  background-color: rgba(3, 155, 229, 0.1);
  color: var(--info);
  border: 1px solid rgba(3, 155, 229, 0.2);
}

.loading {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--text-light);
}

/* Состояния загрузки */
.btn-loading {
  position: relative;
  color: transparent !important;
}

.btn-loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-top-color: white;
  border-radius: 50%;
  animation: button-loading-spinner 0.8s linear infinite;
}

@keyframes button-loading-spinner {
  from {
    transform: rotate(0turn);
  }
  to {
    transform: rotate(1turn);
  }
}

/* Дополнительные утилиты */
.text-success {
  color: var(--success);
}

.text-danger {
  color: var(--danger);
}

.text-primary {
  color: var(--primary);
}

.text-info {
  color: var(--info);
}

.mt-0 {
  margin-top: 0;
}
.mb-0 {
  margin-bottom: 0;
}
.ml-0 {
  margin-left: 0;
}
.mr-0 {
  margin-right: 0;
}

.mt-1 {
  margin-top: 0.25rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.ml-1 {
  margin-left: 0.25rem;
}
.mr-1 {
  margin-right: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.mr-2 {
  margin-right: 0.5rem;
}

.mt-3 {
  margin-top: 1rem;
}
.mb-3 {
  margin-bottom: 1rem;
}
.ml-3 {
  margin-left: 1rem;
}
.mr-3 {
  margin-right: 1rem;
}

.mt-4 {
  margin-top: 1.5rem;
}
.mb-4 {
  margin-bottom: 1.5rem;
}
.ml-4 {
  margin-left: 1.5rem;
}
.mr-4 {
  margin-right: 1.5rem;
}

.mt-5 {
  margin-top: 2rem;
}
.mb-5 {
  margin-bottom: 2rem;
}
.ml-5 {
  margin-left: 2rem;
}
.mr-5 {
  margin-right: 2rem;
}

.p-0 {
  padding: 0;
}
.p-1 {
  padding: 0.25rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 1rem;
}
.p-4 {
  padding: 1.5rem;
}
.p-5 {
  padding: 2rem;
}

.w-100 {
  width: 100%;
}
.h-100 {
  height: 100%;
}

.d-flex {
  display: flex;
}
.flex-column {
  flex-direction: column;
}
.justify-content-between {
  justify-content: space-between;
}
.justify-content-center {
  justify-content: center;
}
.align-items-center {
  align-items: center;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-grow-1 {
  flex-grow: 1;
}

.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-left {
  text-align: left;
}

.font-weight-bold {
  font-weight: 600;
}
.font-weight-normal {
  font-weight: normal;
}

.text-small {
  font-size: 0.875rem;
}
.text-large {
  font-size: 1.125rem;
}

.rounded {
  border-radius: var(--radius);
}
.shadow {
  box-shadow: 0 1px 3px var(--shadow);
}
.shadow-lg {
  box-shadow: 0 4px 6px var(--shadow);
}

@media (max-width: 576px) {
  .featured-container,
  .car-grid,
  #cars-container {
    grid-template-columns: 1fr;
  }

  .admin-section {
    padding: 1.25rem;
  }

  .section-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .section-title h2 {
    margin-bottom: 0;
  }

  .controls {
    width: 100%;
  }

  .search-box input {
    min-width: auto;
  }

  .notification {
    width: 90%;
    left: 5%;
    right: 5%;
  }
}

/* Дополнительные эффекты и улучшения */
.car-card-image .badge,
.featured-car .badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 2;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 0.35rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 2rem;
  backdrop-filter: blur(4px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.featured-car-order {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.featured-car-order .badge {
  position: static;
  background: var(--primary-light);
  color: var(--primary);
  font-size: 0.875rem;
  padding: 0.25rem 0.75rem;
  box-shadow: none;
}

/* Эффект пульсации для кнопки добавления */
.add-to-featured-btn {
  animation: pulse 2s infinite;
}

/* Эффект наведения на карточку */
.car-card::before,
.featured-car::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: var(--radius-lg);
  background: linear-gradient(45deg, var(--primary), transparent);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.car-card:hover::before,
.featured-car:hover::before {
  opacity: 0.05;
}

/* Стильный скроллбар */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: var(--light);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--primary), var(--primary-hover));
  border-radius: 10px;
  border: 3px solid var(--light);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--primary-hover), var(--primary));
}

/* Добавим фокус для кнопок и цветные акценты */
button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.3);
}

#reset-search-btn,
#reset-available-search-btn {
  background: linear-gradient(135deg, #6e7787, #5a6377);
}

#reset-search-btn:hover,
#reset-available-search-btn:hover {
  background: linear-gradient(135deg, #5a6377, #4a5366);
}

/* Адаптивность и медиа-запросы */
@media (max-width: 768px) {
  .controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    width: 100%;
  }

  .sort-controls {
    width: 100%;
  }

  .admin-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .import-form {
    flex-direction: column;
  }

  #cars-container {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  .featured-container,
  .car-grid,
  #cars-container {
    grid-template-columns: 1fr;
  }

  .admin-section {
    padding: 1.25rem;
  }

  .section-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .section-title h2 {
    margin-bottom: 0;
  }

  .controls {
    width: 100%;
  }

  .search-box input {
    min-width: auto;
  }

  .notification {
    width: 90%;
    left: 5%;
    right: 5%;
  }
}

.featured-section {
  margin-bottom: 2rem;
}

.available-section {
  margin-top: 2rem;
  border-top: 1px solid var(--border);
  padding-top: 1.5rem;
}

/* Заголовки секций с счетчиками */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  position: relative;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
}

.section-counter {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: var(--primary-gradient);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-weight: 600;
  font-size: 0.875rem;
  box-shadow: 0 4px 10px rgba(67, 97, 238, 0.25);
  position: relative;
  overflow: hidden;
}

.section-counter:before {
  content: "";
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  pointer-events: none;
}

.section-counter i {
  font-size: 1rem;
}

.counter-value {
  font-weight: 700;
  min-width: 1.5rem;
  text-align: center;
}

#featured-counter {
  background: linear-gradient(135deg, #f6d365, #fda085);
}

#cars-counter {
  background: linear-gradient(135deg, #4361ee, #3a56e4);
}

#results-counter {
  background: linear-gradient(135deg, #0ba360, #3cba92);
}

#available-counter {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

/* Кнопка фикса цветов */
.fix-all-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin: 1.5rem 0;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #805ad5, #6b46c1);
  color: white;
  font-weight: 500;
  border-radius: var(--radius);
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(107, 70, 193, 0.25);
  transition: var(--transition);
}

.fix-all-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(107, 70, 193, 0.35);
}

/* === Car Detail Admin Styles === */
.select-car-group {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 25px;
}

.select-car-group select {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
}

.action-btn {
  background-color: #4a6bff;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 15px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn:hover {
  background-color: #3a59e0;
}

/* Табы для формы */
.form-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 10px;
}

.form-tab {
  padding: 10px 15px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  transition: all 0.2s ease;
}

.form-tab:hover {
  background-color: #e8e8e8;
}

.form-tab.active {
  background-color: #4a6bff;
  color: white;
  border-color: #4a6bff;
}

.form-tab-content {
  display: none;
  padding: 15px;
  border: 1px solid #e5e5e5;
  border-radius: 5px;
  margin-bottom: 20px;
}

.form-tab-content.active {
  display: block;
}

/* Стили для загрузки изображений */
.images-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 15px;
}

.image-upload-container {
  position: relative;
  width: 150px;
  height: 150px;
  border: 2px dashed #ccc;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  overflow: hidden;
}

.image-upload-container:hover {
  border-color: #4a6bff;
}

.image-upload {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  cursor: pointer;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #777;
}

.upload-placeholder i {
  font-size: 24px;
}

.uploaded-images {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.uploaded-image-container {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 5px;
  overflow: hidden;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.uploaded-image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.6);
}

.image-action-btn {
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: color 0.2s ease;
}

.image-action-btn:hover {
  color: #ff4b4b;
}

/* Модальное окно для предпросмотра */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  overflow-y: auto;
}

.modal-content {
  position: relative;
  margin: 30px auto;
  width: 90%;
  max-width: 1200px;
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

.modal-close {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 24px;
  color: #666;
  cursor: pointer;
}

.modal-close:hover {
  color: #333;
}

.preview-container {
  margin-top: 20px;
  background: #f9f9f9;
  padding: 20px;
  border-radius: 5px;
  max-height: 80vh;
  overflow-y: auto;
}

/* Карточки детальных страниц */
.detail-card {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-bottom: 15px;
  border: 1px solid #e5e5e5;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.detail-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.detail-card-image {
  width: 100px;
  height: 70px;
  object-fit: cover;
  border-radius: 5px;
  margin-right: 20px;
}

.detail-card-info {
  flex: 1;
}

.detail-card-title {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 5px;
}

.detail-card-subtitle {
  color: #666;
  font-size: 14px;
}

.detail-card-price {
  font-weight: 600;
  color: #333;
  margin-right: 20px;
}

.detail-card-actions {
  display: flex;
  gap: 10px;
}

.detail-action-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.detail-edit-btn {
  background-color: #4a6bff;
  color: white;
}

.detail-edit-btn:hover {
  background-color: #3a59e0;
}

.detail-delete-btn {
  background-color: #ff4b4b;
  color: white;
}

.detail-delete-btn:hover {
  background-color: #e03a3a;
}

.detail-preview-btn {
  background-color: #4caf50;
  color: white;
}

.detail-preview-btn:hover {
  background-color: var(--info-hover);
}

.detail-generate-btn {
  background-color: #673ab7;
  color: white;
}

.detail-generate-btn:hover {
  background-color: #5e35b1;
}

/* Адаптивность для детальных страниц */
@media (max-width: 768px) {
  .form-tabs {
    overflow-x: auto;
    padding-bottom: 15px;
  }

  .select-car-group {
    flex-direction: column;
    align-items: flex-start;
  }

  .select-car-group select {
    width: 100%;
  }

  .detail-card {
    flex-direction: column;
    align-items: flex-start;
  }

  .detail-card-image {
    margin-right: 0;
    margin-bottom: 15px;
    width: 100%;
    height: 120px;
  }

  .detail-card-info,
  .detail-card-actions {
    width: 100%;
  }

  .detail-card-actions {
    margin-top: 15px;
  }
}

/* Admin Overrides - Fix styles affected by car-detail-styles.css */
.admin-container .car-detail__gallery,
.admin-container .car-detail__specs-section,
.admin-container .car-detail__best-section,
.admin-container .car-detail__desc-row {
  width: auto !important;
  max-width: none !important;
  margin: 15px 0 !important;
  padding: 15px !important;
  position: static !important;
}

.admin-container .car-detail__info-block {
  background-color: var(--light);
  padding: 12px !important;
  border-radius: var(--radius);
  margin-bottom: 10px;
  box-shadow: 0 1px 3px var(--shadow);
  width: 100%;
  box-sizing: border-box;
  display: flex !important;
  align-items: flex-start !important;
}

.admin-container .car-detail__info-block-icon {
  background-color: var(--white) !important;
  width: 36px !important;
  height: 36px !important;
  margin-right: 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 50% !important;
  padding: 8px !important;
}

.admin-container .car-detail__info-block-text {
  display: flex !important;
  flex-direction: column !important;
}

.admin-container .car-detail__specs-content,
.admin-container .car-detail__specs-table,
.admin-container .specs-table-container {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  overflow: visible !important;
}

.admin-container .slider {
  height: auto !important;
  min-height: 200px;
  margin-bottom: 20px;
  position: relative !important;
}

.admin-container .car-detail__main-img {
  position: static !important;
  height: auto !important;
  max-height: 200px;
  display: block !important;
}

.admin-container .slider-img {
  display: block !important;
  position: static !important;
  opacity: 1 !important;
}

.admin-container .slider-img:not(.active) {
  display: none !important;
}

.admin-container .car-detail__tabs {
  margin: 0;
  padding: 0;
  border-bottom: 1px solid var(--border);
  gap: 20px;
}

.admin-container .car-detail__best-columns {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: 20px !important;
}

.admin-container .car-detail__best-column:first-child:after {
  display: none !important;
}

/* Fix for the modal appearing in the background */
.img-modal {
  z-index: 1500 !important;
}

/* Additional admin fixes */
.admin-container .car-detail__desc-col,
.admin-container .car-detail__info-col {
  max-width: none !important;
  width: auto !important;
  flex: 1 !important;
  position: static !important;
}

.admin-container .car-detail__desc-gallery,
.admin-container .car-detail__specs-gallery {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 10px !important;
}

.admin-container .car-detail__desc-img,
.admin-container .car-detail__specs-img {
  width: 120px !important;
  height: 90px !important;
  object-fit: fill !important;
}

/* Admin Hero Section Styles */
.admin-mobile-header {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 64px;
  background: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  padding: 0;
  z-index: 100;
}

.admin-mobile-header__logo {
  position: static;
  margin: 0 auto;
  width: auto;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-decoration: none;
  font-weight: 600;
  color: var(--primary);
}

.admin-mobile-header-spacer {
  display: none;
  height: 64px;
  width: 100%;
}

/* Admin Burger Menu */
.admin-burger {
  position: absolute;
  top: 50%;
  left: 20px;
  width: 32px;
  height: 32px;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  background: none;
  border: none;
  z-index: 2000;
  cursor: pointer;
  gap: 4px;
}

.admin-burger span {
  display: block;
  width: 22px;
  height: 2px;
  background: var(--primary);
  border-radius: 0;
  transition: transform 0.3s cubic-bezier(0.4, 1.3, 0.6, 1), opacity 0.25s;
}

.admin-burger.open span:nth-child(1) {
  transform: translateY(6px) rotate(45deg);
}
.admin-burger.open span:nth-child(2) {
  opacity: 0;
}
.admin-burger.open span:nth-child(3) {
  transform: translateY(-6px) rotate(-45deg);
}

/* Admin Dropdown Menu */
.admin-dropdown-menu {
  position: fixed;
  top: 60px;
  left: 0;
  width: 100%;
  background: #fff;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  padding: 20px;
  z-index: 1000;
  display: none;
  flex-direction: column;
  gap: 15px;
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 0.2s, transform 0.2s;
}

.admin-dropdown-menu.active {
  display: flex;
  opacity: 1;
  transform: translateY(0);
}

.admin-dropdown-menu a {
  color: #111;
  font-size: 1.1rem;
  text-decoration: none;
  padding: 10px 15px;
  border-radius: 5px;
  transition: background 0.2s;
}

.admin-dropdown-menu a:hover {
  background: #f0f0f0;
}

.admin-dropdown-menu a.active {
  background: var(--primary-light);
  color: var(--primary);
}

/* Admin Hero Section */
.admin-hero-stock {
  position: relative;
  width: 100%;
  background: #fff;
}

.admin-hero-header-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100;
  height: 48px;
  min-height: 48px;
}

.admin-header__left {
  display: flex;
  align-items: center;
}

.admin-header__back-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #333;
  text-decoration: none;
  font-size: 1rem;
  transition: all 0.3s ease;
  padding: 0;
  border-radius: 4px;
  height: 48px;
  line-height: 48px;
}

.admin-header__back-link:hover {
  color: var(--primary);
  background: rgba(67, 97, 238, 0.05);
  transform: translateX(-2px);
}

.admin-header__back-link svg {
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;
}

.admin-header__back-link:hover svg {
  transform: translateX(-2px);
}

.admin-header__center-group {
  flex: 1;
  display: flex;
  justify-content: center;
}

.admin-header__nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.admin-header__logo-link {
  font-family: "Inter", Arial, sans-serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary);
  text-decoration: none;
  margin-right: 2rem;
  transition: all 0.3s ease;
  position: relative;
  line-height: 48px;
  padding: 0;
}

.admin-header__nav-links {
  display: flex;
  gap: 1.5rem;
}

.admin-header__nav-link {
  color: #333;
  text-decoration: none;
  font-size: 1rem;
  transition: all 0.3s ease;
  position: relative;
  padding: 0 0.5rem;
  font-weight: 400;
  line-height: 48px;
  height: 48px;
  display: flex;
  align-items: center;
}

.admin-header__nav-link:hover {
  color: var(--primary);
  transform: translateY(-2px);
}

.admin-header__nav-link.active {
  color: var(--primary);
  font-weight: 500;
}

/* Responsive styles for admin hero */
@media (max-width: 768px) {
  .admin-burger {
    display: flex;
  }

  .admin-mobile-header {
    display: flex;
  }

  .admin-mobile-header-spacer {
    display: block;
  }

  .admin-hero-header-group {
    display: none;
  }

  .admin-container {
    margin-top: 1rem;
  }
}

@media (min-width: 769px) {
  .admin-mobile-header,
  .admin-mobile-header-spacer,
  .admin-burger,
  .admin-dropdown-menu {
    display: none !important;
  }

  .admin-hero-header-group {
    display: flex !important;
  }
}
