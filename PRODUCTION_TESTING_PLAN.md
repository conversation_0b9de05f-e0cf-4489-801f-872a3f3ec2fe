# План тестирования для продакшн сервера SHMS Auto

## Подготовка к продакшену - ВЫПОЛНЕНО ✅

### 1. Анализ локального сервера ✅
- ✅ Изучен файл `server-local.js`
- ✅ Определены все настройки, пути и конфигурации
- ✅ Выявлены различия с продакшн версией

### 2. Обновление продакшн сервера ✅
- ✅ Обновлен файл `server.js` с новой функциональностью
- ✅ Добавлен API курсов валют `/api/exchange-rates.php`
- ✅ Обновлены маршруты для модального окна Физиева
- ✅ Исправлены пути к PHP API
- ✅ Сохранены все middleware и настройки безопасности

### 3. Обновление .env файла ✅
- ✅ Обновлен SESSION_SECRET для продакшена
- ✅ Проверены все переменные окружения
- ✅ Настроены правильные домены и URL

### 4. Проверка файлов модального окна ✅
- ✅ `public/js/fiziev-modal.js` - JavaScript логика
- ✅ `public/css/fiziev-modal.css` - Стили модального окна
- ✅ `public/Fiziev.html` - Основной файл перенаправления
- ✅ `public/Fiziev/index.html` - Папка для URL без расширения
- ✅ `public/fiziev` - Файл с маленькой буквы
- ✅ Все файлы подключены к основным страницам сайта

## План тестирования после деплоя

### 1. Базовая функциональность сервера
- [ ] **Запуск сервера**: `node server.js`
- [ ] **Проверка тестового endpoint**: `GET /test`
- [ ] **Проверка переменных окружения**: Убедиться что NODE_ENV=production
- [ ] **Проверка логов**: Сервер должен запуститься без ошибок

### 2. Основные страницы сайта
- [ ] **Главная страница**: `GET /` → `index.html`
- [ ] **Авто в наличии**: `GET /stock` → `stock.html`
- [ ] **Авто под заказ**: `GET /order` → `order.html`
- [ ] **Контакты**: `GET /contacts` → `contacts.html`

### 3. Модальное окно Физиева (КРИТИЧНО!)
- [ ] **Основной URL**: `GET /Fiziev` → должен открыть модальное окно
- [ ] **Альтернативный URL**: `GET /fiziev` → redirect на `/Fiziev`
- [ ] **URL с папкой**: `GET /Fiziev/` → должен работать
- [ ] **Старые URL**: `GET /Fiziyev` и `/fiziyev` → redirect на `/Fiziev`
- [ ] **Модальное окно**: Проверить что открывается поверх сайта
- [ ] **Функциональность**: Кнопка копирования промокода ATAMAN
- [ ] **Закрытие**: По X, Escape, клику вне окна
- [ ] **Мобильная версия**: Адаптивность на мобильных устройствах

### 4. API функциональность
- [ ] **API курсов валют**: `GET /api/exchange-rates.php`
- [ ] **API поиска авто**: `GET /api/encar?date=YYYY-MM-DD`
- [ ] **API отправки заявок**: `POST /api/send-review`
- [ ] **Обработка промокодов**: Проверить валидацию промокода ATAMAN

### 5. Админ-панель
- [ ] **Доступ к админке**: `GET /admin` → redirect на login
- [ ] **Страница логина**: `GET /admin/login.html`
- [ ] **Авторизация**: `POST /api/auth/login`
- [ ] **Защищенные маршруты**: Проверить что требуют авторизации

### 6. Статические файлы
- [ ] **CSS файлы**: Проверить загрузку всех стилей
- [ ] **JS файлы**: Проверить загрузку всех скриптов
- [ ] **Изображения**: Проверить загрузку логотипов и изображений
- [ ] **Загруженные файлы**: `GET /uploads/*`

### 7. Безопасность (только для продакшена)
- [ ] **HTTPS редирект**: HTTP → HTTPS
- [ ] **Безопасные заголовки**: X-Frame-Options, X-XSS-Protection и др.
- [ ] **Secure cookies**: Проверить флаг secure для cookies
- [ ] **Файловое хранилище сессий**: Проверить создание файлов сессий

### 8. Производительность
- [ ] **Кеширование**: Статические файлы должны кешироваться (maxAge: 1d)
- [ ] **Сжатие**: Проверить gzip сжатие (если настроено на сервере)
- [ ] **Время ответа**: Основные страницы должны загружаться быстро

## Команды для тестирования

### Запуск продакшн сервера
```bash
NODE_ENV=production node server.js
```

### Тестирование API endpoints
```bash
# Тест основной функциональности
curl http://your-domain.com/test

# Тест курсов валют
curl http://your-domain.com/api/exchange-rates.php

# Тест модального окна Физиева
curl -I http://your-domain.com/Fiziev
curl -I http://your-domain.com/fiziev
```

### Проверка модального окна в браузере
1. Открыть `http://your-domain.com/Fiziev`
2. Убедиться что происходит перенаправление на главную страницу
3. Проверить что модальное окно открывается автоматически
4. Протестировать все функции модального окна

## Критические проверки

### ❗ ОБЯЗАТЕЛЬНО ПРОВЕРИТЬ:
1. **Модальное окно Физиева работает** - это основное требование
2. **Все URL варианты работают**: /Fiziev, /fiziev, /Fiziev/, etc.
3. **API encar-proxy.php доступен** - основная функциональность сайта
4. **Отправка заявок работает** - критично для бизнеса
5. **Админ-панель защищена** - безопасность

### 🔧 Возможные проблемы:
- Неправильные пути к PHP файлам
- Проблемы с CORS для API
- Ошибки в путях к статическим файлам
- Проблемы с сессиями в продакшене
- Неработающие перенаправления для модального окна

## После успешного тестирования
- [ ] Обновить документацию
- [ ] Создать backup текущей версии
- [ ] Настроить мониторинг
- [ ] Проверить логи на наличие ошибок
