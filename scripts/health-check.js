#!/usr/bin/env node

/**
 * Health Check скрипт для SHMS Авто
 * Проверяет работоспособность сервера и основных компонентов
 */

const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const config = {
  host: process.env.HOST || 'localhost',
  port: process.env.PORT || 3001,
  protocol: process.env.NODE_ENV === 'production' ? 'https' : 'http',
  timeout: 5000
};

// Цвета для консоли
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Проверка HTTP сервера
function checkServer() {
  return new Promise((resolve, reject) => {
    const client = config.protocol === 'https' ? https : http;
    const url = `${config.protocol}://${config.host}:${config.port}/test`;
    
    const req = client.get(url, { timeout: config.timeout }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          resolve({ status: 'ok', data: JSON.parse(data) });
        } else {
          reject(new Error(`HTTP ${res.statusCode}`));
        }
      });
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Timeout'));
    });
    
    req.on('error', reject);
  });
}

// Проверка базы данных
function checkDatabase() {
  return new Promise((resolve, reject) => {
    const dbPath = process.env.DB_PATH || './data/stock.db';
    
    if (!fs.existsSync(dbPath)) {
      reject(new Error('Database file not found'));
      return;
    }
    
    const sqlite3 = require('sqlite3').verbose();
    const db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        reject(err);
        return;
      }
      
      db.get("SELECT COUNT(*) as count FROM stock_cars", (err, row) => {
        db.close();
        if (err) {
          reject(err);
        } else {
          resolve({ status: 'ok', cars_count: row.count });
        }
      });
    });
  });
}

// Проверка директорий
function checkDirectories() {
  const dirs = [
    process.env.UPLOAD_DIR || './data/uploads',
    './logs',
    './data/cache'
  ];
  
  const results = {};
  
  dirs.forEach(dir => {
    try {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        results[dir] = 'created';
      } else {
        const stats = fs.statSync(dir);
        results[dir] = stats.isDirectory() ? 'ok' : 'not_directory';
      }
    } catch (error) {
      results[dir] = `error: ${error.message}`;
    }
  });
  
  return results;
}

// Проверка почты
function checkMail() {
  return new Promise((resolve, reject) => {
    try {
      const { createTransporter } = require('../config/mail-config');
      const transporter = createTransporter();
      
      if (!transporter) {
        reject(new Error('Failed to create mail transporter'));
        return;
      }
      
      transporter.verify((error, success) => {
        if (error) {
          reject(error);
        } else {
          resolve({ status: 'ok', ready: success });
        }
      });
    } catch (error) {
      reject(error);
    }
  });
}

// Основная функция проверки
async function runHealthCheck() {
  log('🏥 SHMS Авто - Health Check', 'blue');
  log('================================', 'blue');
  
  const results = {
    timestamp: new Date().toISOString(),
    status: 'ok',
    checks: {}
  };
  
  // Проверка сервера
  try {
    log('🌐 Проверка HTTP сервера...', 'yellow');
    const serverResult = await checkServer();
    results.checks.server = serverResult;
    log('✅ Сервер работает', 'green');
  } catch (error) {
    results.checks.server = { status: 'error', error: error.message };
    results.status = 'error';
    log(`❌ Сервер недоступен: ${error.message}`, 'red');
  }
  
  // Проверка базы данных
  try {
    log('🗄️  Проверка базы данных...', 'yellow');
    const dbResult = await checkDatabase();
    results.checks.database = dbResult;
    log(`✅ База данных работает (${dbResult.cars_count} автомобилей)`, 'green');
  } catch (error) {
    results.checks.database = { status: 'error', error: error.message };
    results.status = 'error';
    log(`❌ Проблема с базой данных: ${error.message}`, 'red');
  }
  
  // Проверка директорий
  log('📁 Проверка директорий...', 'yellow');
  const dirResults = checkDirectories();
  results.checks.directories = dirResults;
  
  Object.entries(dirResults).forEach(([dir, status]) => {
    if (status === 'ok' || status === 'created') {
      log(`✅ ${dir}: ${status}`, 'green');
    } else {
      log(`❌ ${dir}: ${status}`, 'red');
      results.status = 'error';
    }
  });
  
  // Проверка почты
  try {
    log('📧 Проверка почтового сервера...', 'yellow');
    const mailResult = await checkMail();
    results.checks.mail = mailResult;
    log('✅ Почтовый сервер готов', 'green');
  } catch (error) {
    results.checks.mail = { status: 'error', error: error.message };
    log(`⚠️  Проблема с почтой: ${error.message}`, 'yellow');
  }
  
  // Итоговый результат
  log('================================', 'blue');
  if (results.status === 'ok') {
    log('🎉 Все системы работают нормально!', 'green');
  } else {
    log('⚠️  Обнаружены проблемы!', 'red');
  }
  
  // Сохранение результатов
  const logDir = './logs';
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
  
  fs.writeFileSync(
    path.join(logDir, 'health-check.json'),
    JSON.stringify(results, null, 2)
  );
  
  return results;
}

// Запуск если файл вызван напрямую
if (require.main === module) {
  runHealthCheck()
    .then(results => {
      process.exit(results.status === 'ok' ? 0 : 1);
    })
    .catch(error => {
      log(`💥 Критическая ошибка: ${error.message}`, 'red');
      process.exit(1);
    });
}

module.exports = { runHealthCheck };
