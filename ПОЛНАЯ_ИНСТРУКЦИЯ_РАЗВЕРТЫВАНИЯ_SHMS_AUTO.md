# 🚀 ПОЛНАЯ ИНСТРУКЦИЯ ПО РАЗВЕРТЫВАНИЮ SHMS AUTO С АВТООБНОВЛЕНИЕМ

## 📋 ИНФОРМАЦИЯ О СЕРВЕРЕ

- **IP адрес**: *************
- **Пользователь**: root
- **Пароль**: kxTH3qxEO3F24d8t
- **Путь на сервере**: /var/www/shms-auto
- **Домен**: shms-auto.ru
- **Порт Node.js**: 3000
- **Управление**: PM2

---

## 🔧 ПОДГОТОВКА К РАЗВЕРТЫВАНИЮ

### ✅ ПРОВЕРКА ГОТОВНОСТИ ФАЙЛОВ

Убедитесь что у вас есть следующие файлы и директории:

**ОСНОВНЫЕ ФАЙЛЫ:**
- `server.js` (основной файл сервера)
- `.env` (продакшн настройки)
- `package.json` и `package-lock.json`
- `ecosystem.config.js` (конфигурация PM2)

**ДИРЕКТОРИИ:**
- `public/` (статические файлы)
- `admin/` (админ-панель)
- `config/` (конфигурация)
- `data/` (база данных и загрузки)
- `scripts/` (скрипты автообновления)
- `server/` (серверные модули)

---

## 🚀 ПОШАГОВОЕ РАЗВЕРТЫВАНИЕ

### ШАГ 1: ПОДКЛЮЧЕНИЕ К СЕРВЕРУ
```bash
ssh root@*************
# Введите пароль: kxTH3qxEO3F24d8t
```

### ШАГ 2: ОСТАНОВКА ТЕКУЩЕГО САЙТА
```bash
cd /var/www/shms-auto
pm2 stop shms-auto
pm2 delete shms-auto
```

### ШАГ 3: СОЗДАНИЕ БЭКАПА
```bash
cd /var/www
mv shms-auto shms-auto-backup-$(date +%Y%m%d-%H%M%S)
mkdir -p shms-auto
cd shms-auto
```

### ШАГ 4: ЗАГРУЗКА ФАЙЛОВ С КОМПЬЮТЕРА

Откройте **НОВЫЙ PowerShell** (не закрывая SSH сессию):
```powershell
cd "C:\Users\<USER>\OneDrive\Рабочий стол\shms-auto123"

# ЗАГРУЗКА ОСНОВНЫХ ФАЙЛОВ:
scp server.js root@*************:/var/www/shms-auto/
scp .env root@*************:/var/www/shms-auto/
scp package.json root@*************:/var/www/shms-auto/
scp package-lock.json root@*************:/var/www/shms-auto/
scp ecosystem.config.js root@*************:/var/www/shms-auto/

# ЗАГРУЗКА ДИРЕКТОРИЙ:
scp -r public root@*************:/var/www/shms-auto/
scp -r admin root@*************:/var/www/shms-auto/
scp -r config root@*************:/var/www/shms-auto/
scp -r data root@*************:/var/www/shms-auto/
scp -r scripts root@*************:/var/www/shms-auto/
scp -r server root@*************:/var/www/shms-auto/
```

### ШАГ 5: УСТАНОВКА ЗАВИСИМОСТЕЙ

Вернитесь в SSH сессию:
```bash
cd /var/www/shms-auto
npm install
npm install session-file-store
```

### ШАГ 6: НАСТРОЙКА СИСТЕМЫ АВТОРИЗАЦИИ

#### 6.1 Создание таблиц базы данных:
```bash
node scripts/setup-auth-db.js
```

#### 6.2 Создание администратора:
```bash
node scripts/create-admin-simple.js
```

Это создаст администратора с данными:
- **Логин**: Admin
- **Email**: <EMAIL>
- **Пароль**: Admin123!@#

### ШАГ 7: ИСПРАВЛЕНИЕ ПРОБЛЕМ С СЕССИЯМИ

#### 7.1 Создание скрипта исправления сессий:
```bash
nano fix-sessions-final.js
```

Вставьте следующий код:
```javascript
const fs = require('fs');
const path = require('path');

console.log('🔧 Финальное исправление сессий...');

const serverPath = '/var/www/shms-auto/server.js';
let content = fs.readFileSync(serverPath, 'utf8');

// Исправляем настройки сессий для продакшена
const oldSessionPattern = /\/\/ Настройка сессий[\s\S]*?app\.use\(session\(sessionConfig\)\);/;

const newSessionCode = \`// Настройка сессий
  console.log("🔧 Настройка сессий для продакшена...");
  
  const sessionConfig = {
    secret: process.env.SESSION_SECRET || "your-secret-key",
    resave: false,
    saveUninitialized: false,
    name: 'shms-admin-session',
    cookie: {
      secure: false, // Отключено для совместимости с прокси
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000, // 24 часа
      sameSite: 'lax'
    }
  };

  // Используем файловое хранилище сессий
  try {
    const FileStore = require('session-file-store')(session);
    sessionConfig.store = new FileStore({
      path: path.join(__dirname, 'data', 'sessions'),
      ttl: 24 * 60 * 60,
      retries: 3,
      logFn: function(message) {
        console.log('📁 Session store:', message);
      }
    });
    console.log("✅ Файловое хранилище сессий активировано");
  } catch (error) {
    console.log("⚠️ Файловое хранилище недоступно, используем память:", error.message);
  }

  app.use(session(sessionConfig));

  // Middleware для диагностики сессий
  app.use((req, res, next) => {
    if (req.url.includes('/admin') || req.url.includes('/api/auth')) {
      console.log('🔍 Session Debug:', {
        url: req.url,
        method: req.method,
        sessionID: req.sessionID,
        hasSession: !!req.session,
        adminId: req.session?.adminId,
        username: req.session?.username
      });
    }
    next();
  });
\`;

if (oldSessionPattern.test(content)) {
  content = content.replace(oldSessionPattern, newSessionCode);
  fs.writeFileSync(serverPath, content);
  console.log('✅ Настройки сессий обновлены');
} else {
  console.log('❌ Не удалось найти секцию сессий для замены');
}

console.log('🎉 Исправление сессий завершено');
```

Сохраните: `Ctrl + X`, `Y`, `Enter`

#### 7.2 Запуск исправления:
```bash
node fix-sessions-final.js
node -c server.js  # Проверка синтаксиса
```

### ШАГ 8: НАСТРОЙКА АВТОМАТИЧЕСКОГО ОБНОВЛЕНИЯ ENCAR

#### 8.1 Установка прав доступа для скриптов:
```bash
chmod +x scripts/auto-update-encar.sh
chmod +x scripts/setup-cron.sh
```

#### 8.2 Настройка автоматического обновления:
```bash
./scripts/setup-cron.sh install
```

#### 8.3 Тестирование системы автообновления:
```bash
./scripts/setup-cron.sh test
```

#### 8.4 Проверка статуса автообновления:
```bash
./scripts/setup-cron.sh status
```

### ШАГ 9: НАСТРОЙКА ПРАВ ДОСТУПА

```bash
# Создание необходимых директорий
mkdir -p logs
mkdir -p backups/csv
mkdir -p data/sessions
mkdir -p public/api/encar_data

# Настройка прав доступа
chown -R www-data:www-data /var/www/shms-auto
chmod -R 755 /var/www/shms-auto
chmod -R 777 /var/www/shms-auto/data/uploads
chmod -R 777 /var/www/shms-auto/data/sessions
chmod -R 777 /var/www/shms-auto/logs
chmod -R 777 /var/www/shms-auto/backups
chmod -R 777 /var/www/shms-auto/public/api/encar_data
```

### ШАГ 10: ЗАПУСК САЙТА

```bash
pm2 start ecosystem.config.js --env production
```

### ШАГ 11: ПРОВЕРКА РАБОТЫ

```bash
# Проверка статуса PM2
pm2 status

# Проверка логов
pm2 logs shms-auto --lines 20

# Проверка локального порта
curl -I http://localhost:3000

# Проверка публичного домена
curl -I https://shms-auto.ru
```

---

## 🔐 ТЕСТИРОВАНИЕ СИСТЕМЫ

### Тестирование основного сайта:
- Откройте: https://shms-auto.ru/
- Проверьте все страницы: главная, stock, order, contacts

### Тестирование админки:
- Откройте: https://shms-auto.ru/admin
- Введите данные:
  - **Логин**: Admin
  - **Пароль**: Admin123!@#

### Тестирование модального окна Физиева:
- Откройте: https://shms-auto.ru/Fiziev
- Проверьте что модальное окно открывается

### Тестирование автообновления:
```bash
# Проверка cron задач
crontab -l

# Просмотр логов автообновления
tail -f logs/encar-update.log

# Ручной запуск обновления
./scripts/auto-update-encar.sh
```

---

## 🔧 ДИАГНОСТИКА ПРОБЛЕМ

### Если админка не работает:
```bash
# Проверьте логи
pm2 logs shms-auto --lines 50

# Убедитесь, что созданы таблицы
node scripts/test-auth.js

# Проверьте сессии
ls -la data/sessions/
```

### Если сессии не сохраняются:
```bash
# Проверьте права
chmod 777 data/sessions/

# Перезапустите сервер
pm2 restart shms-auto
```

### Если автообновление не работает:
```bash
# Проверьте cron
systemctl status cron

# Проверьте логи автообновления
tail -20 logs/encar-update.log
tail -20 logs/encar-error.log

# Тестируйте API напрямую
curl -u "admin:n2Q8ewyLft9qgPmim5ng" \
  "https://autobase-wade.auto-parser.ru/encar/$(date '+%Y-%m-%d')/active_offer.csv" \
  | head -5
```

### Если ошибки синтаксиса:
```bash
# Проверьте файл
node -c server.js

# Перезагрузите исправленные файлы с компьютера
```

---

## 🔄 СИСТЕМА АВТОМАТИЧЕСКОГО ОБНОВЛЕНИЯ

### Что делает система:
- **Каждый день в 10:00** автоматически:
  1. Создает резервную копию текущих CSV файлов
  2. Загружает новые данные с API сервера
  3. Сохраняет как `encar_active_YYYY-MM-DD.csv`
  4. Перезапускает сайт через PM2
  5. Проверяет работоспособность
  6. Отправляет уведомления на email

### Управление автообновлением:
```bash
# Проверить статус
./scripts/setup-cron.sh status

# Запустить обновление вручную
./scripts/auto-update-encar.sh

# Посмотреть логи
tail -f logs/encar-update.log

# Удалить автообновление
./scripts/setup-cron.sh remove
```

### Настройки автообновления:
- **Время**: 10:00 каждый день
- **Email уведомления**: <EMAIL>
- **Хранение бэкапов**: 7 дней
- **API сервер**: https://autobase-wade.auto-parser.ru

---

## 🔧 КЛЮЧЕВЫЕ ИСПРАВЛЕНИЯ В ЭТОЙ ВЕРСИИ

### 1. Исправлена проблема с сессиями:
- ✅ Отключены secure cookies для совместимости с прокси
- ✅ Добавлено файловое хранилище сессий
- ✅ Исправлена логика перенаправлений в админке

### 2. Исправлена система авторизации:
- ✅ Добавлено подробное логирование
- ✅ Исправлены URL перенаправлений
- ✅ Улучшена обработка ошибок

### 3. Добавлена система автообновления:
- ✅ Автоматическая загрузка CSV файлов
- ✅ Резервное копирование данных
- ✅ Умный перезапуск сайта
- ✅ Email уведомления
- ✅ Подробное логирование

### 4. Добавлены зависимости:
- ✅ session-file-store для хранения сессий
- ✅ Все необходимые пакеты для авторизации

---

## ✅ ФИНАЛЬНАЯ ПРОВЕРКА

После выполнения всех шагов должно работать:

- ✅ **Главная страница**: https://shms-auto.ru/
- ✅ **Админ-панель**: https://shms-auto.ru/admin
- ✅ **API авторизации**: https://shms-auto.ru/api/auth/check
- ✅ **Модальное окно Физиева**: https://shms-auto.ru/Fiziev
- ✅ **Сохранение сессий** между запросами
- ✅ **Корректное перенаправление** после входа
- ✅ **Автоматическое обновление** CSV файлов
- ✅ **Система логирования** и мониторинга

---

## 📝 ВАЖНЫЕ ЗАМЕТКИ

- **Пароли**: После первого входа смените пароль администратора
- **Безопасность**: В будущем включите secure cookies для HTTPS
- **Мониторинг**: Регулярно проверяйте логи: `pm2 logs shms-auto`
- **Бэкапы**: Делайте бэкапы перед обновлениями
- **Автообновление**: Проверяйте логи автообновления: `tail -f logs/encar-update.log`
- **Email**: Настройте SMTP для получения уведомлений об обновлениях

---

## 🎉 РАЗВЕРТЫВАНИЕ ЗАВЕРШЕНО! САЙТ ГОТОВ К РАБОТЕ!

**Ваш сайт теперь имеет:**
- ✅ Полнофункциональную админ-панель
- ✅ Автоматическое обновление данных автомобилей
- ✅ Модальное окно промо-страницы Физиева
- ✅ Систему мониторинга и логирования
- ✅ Резервное копирование данных
