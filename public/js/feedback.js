console.log("feedback.js подключен");

// Глобальные переменные для хранения данных автомобилей
let stockCars = [];
let orderCars = [];

// Функция для загрузки автомобилей в наличии
async function loadStockCars() {
  try {
    // Пытаемся загрузить данные из API
    const response = await fetch("/admin-stock/cars");

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const cars = await response.json();

    if (!Array.isArray(cars) || cars.length === 0) {
      throw new Error("No cars found or data is not an array");
    }

    return cars;
  } catch (error) {
    console.error("Error loading stock cars:", error);

    // Используем резервные данные в случае ошибки
    return [
      {
        id: 1,
        title: "2024 Mercedes-Benz AMG GT 63 S 4-Door",
        subtitle: "Mercedes-Benz • Германия • Nardo Grey",
        power: "659 л.с.",
        price: "$243000",
      },
      {
        id: 2,
        title: "Porsche Cayenne Coupe Turbo GT",
        subtitle: "Porsche • Германия • Brooklyn Grey Metallic",
        power: "640 л.с.",
        price: "$258000",
      },
      {
        id: 3,
        title: "BMW X5 M Competition",
        subtitle: "BMW • Германия • Alpine White",
        power: "625 л.с.",
        price: "$175000",
      },
      {
        id: 4,
        title: "Audi RS6 Avant",
        subtitle: "Audi • Германия • Daytona Grey",
        power: "600 л.с.",
        price: "$180000",
      },
    ];
  }
}

// Функция для загрузки автомобилей под заказ
async function loadOrderCars() {
  try {
    const response = await fetch("/data/cars.json");
    if (!response.ok) throw new Error("Не удалось загрузить cars.json");
    const cars = await response.json();
    return cars;
  } catch (error) {
    console.warn("Ошибка при загрузке cars.json:", error);
    // Возвращаем пустой массив, чтобы не мешать работе формы
    return [];
  }
}

// Функция для извлечения названия модели из данных автомобиля
function getCarModelName(car, type) {
  if (type === "stock") {
    // Для авто в наличии
    if (car.title) {
      // Убираем год из начала строки, если он есть
      return car.title.replace(/^\d+\s+/, "");
    }
    return "Неизвестная модель";
  } else {
    // Для авто под заказ
    return `${car.mark} ${car.model} ${car.year}`;
  }
}

// Функция для обновления списка моделей в выпадающем списке
function updateModelsList(type) {
  const modelSelect = document.getElementById("model");

  // Очищаем текущий список
  modelSelect.innerHTML = '<option value="">Выберите модель</option>';

  let cars = [];

  // Выбираем нужный массив автомобилей в зависимости от типа
  if (type === "stock") {
    cars = stockCars;
  } else {
    cars = orderCars;
  }

  // Добавляем модели в выпадающий список
  cars.forEach((car) => {
    const modelName = getCarModelName(car, type);
    const option = document.createElement("option");
    option.value = type === "stock" ? car.id : `${car.mark} ${car.model}`;
    option.textContent = modelName;
    modelSelect.appendChild(option);
  });
}

// Функция для форматирования телефонного номера
function formatPhoneNumber(input) {
  // Получаем только цифры из ввода
  const phoneNumber = input.value.replace(/\D/g, "");

  // Форматируем номер в зависимости от длины
  if (phoneNumber.length <= 3) {
    input.value = `+${phoneNumber}`;
  } else if (phoneNumber.length <= 6) {
    input.value = `+${phoneNumber.slice(0, 1)} (${phoneNumber.slice(1)}`;
  } else if (phoneNumber.length <= 10) {
    input.value = `+${phoneNumber.slice(0, 1)} (${phoneNumber.slice(
      1,
      4
    )}) ${phoneNumber.slice(4)}`;
  } else {
    input.value = `+${phoneNumber.slice(0, 1)} (${phoneNumber.slice(
      1,
      4
    )}) ${phoneNumber.slice(4, 7)}-${phoneNumber.slice(7, 11)}`;
  }
}

// Функция для проверки промокода
function validatePromoCode(promoCode) {
  const validPromoCodes = ["ATAMAN"];
  return validPromoCodes.includes(promoCode.toUpperCase());
}

// Функция для отображения статуса промокода
function showPromoStatus(isValid, promoInput) {
  // Удаляем предыдущие индикаторы
  const existingIndicator =
    promoInput.parentNode.querySelector(".promo-indicator");
  if (existingIndicator) {
    existingIndicator.remove();
  }

  // Создаем новый индикатор
  const indicator = document.createElement("div");
  indicator.className = "promo-indicator";
  indicator.style.cssText = `
        margin-top: 5px;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
    `;

  if (isValid) {
    indicator.textContent = "✓ Промокод ATAMAN активирован!";
    indicator.style.background = "#d4edda";
    indicator.style.color = "#155724";
    indicator.style.border = "1px solid #c3e6cb";
    promoInput.style.borderColor = "#28a745";
  } else {
    indicator.textContent = "✗ Неверный промокод";
    indicator.style.background = "#f8d7da";
    indicator.style.color = "#721c24";
    indicator.style.border = "1px solid #f5c6cb";
    promoInput.style.borderColor = "#dc3545";
  }

  promoInput.parentNode.appendChild(indicator);
}

// Функция для улучшения UX при работе с формой
function enhanceFormExperience() {
  console.log("enhanceFormExperience вызвана");
  const form = document.querySelector(".feedback__form");
  const inputs = form.querySelectorAll("input, select, textarea");
  const phoneInput = document.getElementById("phone");
  const promoInput = document.getElementById("promo");

  // Добавляем обработчик для форматирования телефона
  if (phoneInput) {
    phoneInput.addEventListener("input", function () {
      formatPhoneNumber(this);
    });
  }

  // Добавляем обработчик для промокода
  if (promoInput) {
    promoInput.addEventListener("input", function () {
      const promoCode = this.value.trim();
      if (promoCode.length > 0) {
        const isValid = validatePromoCode(promoCode);
        showPromoStatus(isValid, this);
      } else {
        // Удаляем индикатор если поле пустое
        const existingIndicator =
          this.parentNode.querySelector(".promo-indicator");
        if (existingIndicator) {
          existingIndicator.remove();
        }
        this.style.borderColor = "";
      }
    });
  }

  // Визуальная обратная связь при фокусе
  inputs.forEach((input) => {
    if (input.type !== "checkbox") {
      input.addEventListener("focus", function () {
        this.style.borderColor = "#6F9AAB";
        const group = this.closest(".feedback__form-group");
        if (group) group.classList.add("active");
      });
      input.addEventListener("blur", function () {
        this.style.borderColor = "";
        const group = this.closest(".feedback__form-group");
        if (group) group.classList.remove("active");
      });
    }
  });

  // --- Универсальный обработчик отправки формы через fetch ---
  form.addEventListener("submit", async function (e) {
    e.preventDefault();
    console.log("submit обработан через fetch");
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    if (!data.formType) data.formType = "feedback";

    // Проверяем промокод и добавляем информацию о нем
    if (data.promo) {
      const isValidPromo = validatePromoCode(data.promo);
      data.promoValid = isValidPromo;
      if (isValidPromo) {
        data.promoCode = data.promo.toUpperCase();
        console.log("Валидный промокод:", data.promoCode);
      }
    }

    try {
      const response = await fetch("/api/send-review", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      const result = await response.json();

      let successMessage = "Сообщение успешно отправлено";
      if (data.promoValid) {
        successMessage +=
          '<br><span style="color:#28a745;">🎉 Промокод ATAMAN применен!</span>';
      }

      form.innerHTML = `<div style="text-align:center; padding:20px;"><h3 style="color:#38a169; font-size:20px; margin-bottom:10px;">${
        response.ok ? successMessage : "Ошибка отправки"
      }</h3><p style="margin-bottom:15px;">${
        result.message || "Заявка обработана"
      }</p><button onclick="window.location.reload()" style="background:#000; color:#fff; padding:10px 20px; border:none; border-radius:8px; font-size:16px; cursor:pointer;">Отправить еще одно сообщение</button></div>`;
    } catch (err) {
      form.innerHTML = `<div style="text-align:center; padding:20px;"><h3 style="color:#e53e3e; font-size:20px; margin-bottom:10px;">Ошибка отправки</h3><p style="margin-bottom:15px;">${err.message}</p><button onclick="window.location.reload()" style="background:#000; color:#fff; padding:10px 20px; border:none; border-radius:8px; font-size:16px; cursor:pointer;">Попробовать снова</button></div>`;
    }
  });
}

// Инициализация при загрузке страницы
document.addEventListener("DOMContentLoaded", async () => {
  // Загружаем данные об автомобилях
  stockCars = await loadStockCars();
  orderCars = await loadOrderCars();

  // Получаем элемент выбора типа автомобиля
  const carTypeSelect = document.getElementById("carType");

  // Инициализируем список моделей для выбранного типа
  updateModelsList(carTypeSelect.value);

  // Добавляем обработчик события изменения типа автомобиля
  carTypeSelect.addEventListener("change", (e) => {
    updateModelsList(e.target.value);
  });

  // Получаем и обрабатываем параметры из URL
  fillFormFromUrlParams();

  // Улучшаем UX при работе с формой
  enhanceFormExperience();
});

// Функция для обработки параметров URL и заполнения формы
function fillFormFromUrlParams() {
  // Получаем параметры из URL
  const urlParams = new URLSearchParams(window.location.search);

  // Проверяем наличие параметров
  if (
    urlParams.has("type") ||
    urlParams.has("brand") ||
    urlParams.has("model")
  ) {
    console.log("Параметры из URL обнаружены:", Object.fromEntries(urlParams));

    // Получаем элементы формы
    const carTypeSelect = document.getElementById("carType");
    const modelSelect = document.getElementById("model");

    // Если есть параметр типа авто, выбираем соответствующий вариант
    if (urlParams.has("type")) {
      const type = urlParams.get("type");
      if (type === "stock" || type === "order") {
        carTypeSelect.value = type;
        // Обновляем список моделей для выбранного типа
        updateModelsList(type);
      }
    }

    // Если есть параметр марки и модели, создаем строку для поиска в списке моделей
    if (urlParams.has("brand") && urlParams.has("model")) {
      const brand = urlParams.get("brand");
      const model = urlParams.get("model");
      const year = urlParams.has("year") ? urlParams.get("year") : "";

      // Создаем полное название для сравнения
      const fullModelName = `${brand} ${model}`;

      // Добавляем небольшую задержку, чтобы список моделей успел обновиться
      setTimeout(() => {
        // Ищем соответствующую опцию в выпадающем списке
        Array.from(modelSelect.options).forEach((option) => {
          if (
            option.textContent
              .toLowerCase()
              .includes(fullModelName.toLowerCase())
          ) {
            option.selected = true;
          }
        });

        // Если соответствие не найдено, добавляем новую опцию
        if (modelSelect.selectedIndex === 0) {
          const option = document.createElement("option");
          option.value = fullModelName;
          option.textContent = year
            ? `${brand} ${model} ${year}`
            : fullModelName;
          option.selected = true;
          modelSelect.appendChild(option);
        }

        // Создаем и вставляем WOW-эффект с анимацией выбора авто
        showSelectionEffect(modelSelect);
      }, 300);
    }

    // Обрабатываем параметр source (URL автомобиля)
    if (urlParams.has("source")) {
      const sourceUrl = urlParams.get("source");
      const carUrlInput = document.getElementById("carUrl");

      if (carUrlInput && sourceUrl) {
        // Заполняем поле ссылкой на источник (поле остается скрытым)
        carUrlInput.value = sourceUrl;

        console.log(
          "URL автомобиля установлен (скрыто от пользователя):",
          sourceUrl
        );
      }
    }
  }
}

// Функция для создания эффекта WOW при выборе авто
function showSelectionEffect(element) {
  if (!element) return;

  // Создаем контейнер для эффекта
  const effectContainer = document.createElement("div");
  effectContainer.className = "selection-effect";
  effectContainer.style.cssText = `
        position: absolute;
        left: ${element.offsetLeft}px;
        top: ${element.offsetTop - 10}px;
        width: ${element.offsetWidth}px;
        height: ${element.offsetHeight + 20}px;
        background: rgba(105, 179, 231, 0.1);
        border-radius: 8px;
        pointer-events: none;
        z-index: 100;
        box-shadow: 0 0 15px rgba(105, 179, 231, 0.5);
        animation: pulse 1.5s ease-out;
    `;

  // Создаем стиль для анимации
  const style = document.createElement("style");
  style.textContent = `
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.8; }
            100% { transform: scale(1); opacity: 0; }
        }
    `;
  document.head.appendChild(style);

  // Добавляем эффект на страницу
  document.body.appendChild(effectContainer);

  // Показываем сообщение об автозаполнении
  const message = document.createElement("div");
  message.className = "auto-fill-message";
  message.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid #e0e0e0;
        border-left: 4px solid #69b3e7;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        padding: 15px 20px;
        border-radius: 8px;
        font-size: 16px;
        color: #333;
        z-index: 1000;
        animation: slideIn 0.3s ease-out, fadeOut 0.3s ease-in 3s forwards;
    `;
  message.textContent = "Информация об автомобиле заполнена автоматически";

  // Добавляем стиль анимации сообщения
  const messageStyle = document.createElement("style");
  messageStyle.textContent = `
        @keyframes slideIn {
            0% { transform: translate(-50%, -100%); opacity: 0; }
            100% { transform: translate(-50%, 0); opacity: 1; }
        }
        @keyframes fadeOut {
            0% { opacity: 1; }
            100% { opacity: 0; visibility: hidden; }
        }
    `;
  document.head.appendChild(messageStyle);

  // Добавляем сообщение на страницу
  document.body.appendChild(message);

  // Удаляем эффект через 1.5 секунды
  setTimeout(() => {
    effectContainer.remove();
  }, 1500);

  // Удаляем сообщение через 3.3 секунды
  setTimeout(() => {
    message.remove();
  }, 3300);
}

// Demo data for car classes
const cars = [
  {
    class: "business",
    name: "Mercedes-Benz S-Class",
    img: "https://cdn.bmw.ru/mercedes-s-class.jpg",
    link: "/stock/mercedes-s-class",
  },
  {
    class: "business",
    name: "BMW 7 Series",
    img: "https://cdn.bmw.ru/bmw-7-series.jpg",
    link: "/order/bmw-7-series",
  },
  {
    class: "sport",
    name: "Porsche 911",
    img: "https://cdn.bmw.ru/porsche-911.jpg",
    link: "/stock/porsche-911",
  },
  {
    class: "sport",
    name: "Lamborghini Huracan",
    img: "https://cdn.bmw.ru/huracan.jpg",
    link: "/order/lamborghini-huracan",
  },
  {
    class: "suv",
    name: "BMW X5",
    img: "https://cdn.bmw.ru/bmw-x5.jpg",
    link: "/stock/bmw-x5",
  },
  {
    class: "suv",
    name: "Mercedes-Benz GLE",
    img: "https://cdn.bmw.ru/gle.jpg",
    link: "/order/mercedes-gle",
  },
];

const classTitles = {
  business: "Бизнес-класс",
  sport: "Спортивные Авто",
  suv: "SUV",
};

document.addEventListener("DOMContentLoaded", function () {
  const modal = document.getElementById("carClassModal");
  const modalTitle = document.getElementById("carClassModalTitle");
  const modalList = document.getElementById("carClassModalList");
  const closeBtn = document.getElementById("carClassModalClose");
  const cardBtns = document.querySelectorAll(".car-class-card");

  cardBtns.forEach((btn) => {
    btn.addEventListener("click", function () {
      const carClass = btn.getAttribute("data-class");
      const filtered = cars.filter((car) => car.class === carClass);
      modalTitle.textContent = classTitles[carClass] || "";
      modalList.innerHTML = filtered
        .map(
          (car) => `
        <div class="car-class-modal__item">
          <img class="car-class-modal__item-img" src="${car.img}" alt="${car.name}">
          <a class="car-class-modal__item-link" href="${car.link}">${car.name}</a>
        </div>
      `
        )
        .join("");
      modal.style.display = "flex";
      setTimeout(() => {
        modal.classList.add("active");
      }, 10);
    });
  });

  closeBtn.addEventListener("click", function () {
    modal.style.display = "none";
    modal.classList.remove("active");
  });

  modal.addEventListener("click", function (e) {
    if (e.target === modal) {
      modal.style.display = "none";
      modal.classList.remove("active");
    }
  });
});
