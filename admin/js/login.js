// Система авторизации для админки SHMS Auto
class LoginSystem {
    constructor() {
        this.form = document.getElementById('loginForm');
        this.usernameInput = document.getElementById('username');
        this.passwordInput = document.getElementById('password');
        this.passwordToggle = document.getElementById('passwordToggle');
        this.loginButton = document.getElementById('loginButton');
        this.formMessage = document.getElementById('formMessage');
        this.notification = document.getElementById('notification');
        
        this.init();
    }

    init() {
        // Проверяем, не авторизован ли уже пользователь
        this.checkExistingAuth();
        
        // Привязываем обработчики событий
        this.bindEvents();
        
        // Фокус на первое поле
        this.usernameInput.focus();
    }

    bindEvents() {
        // Обработка формы
        this.form.addEventListener('submit', (e) => this.handleLogin(e));
        
        // Показать/скрыть пароль
        this.passwordToggle.addEventListener('click', () => this.togglePassword());
        
        // Валидация в реальном времени
        this.usernameInput.addEventListener('input', () => this.validateField('username'));
        this.passwordInput.addEventListener('input', () => this.validateField('password'));
        
        // Закрытие уведомлений
        const notificationClose = this.notification.querySelector('.notification-close');
        if (notificationClose) {
            notificationClose.addEventListener('click', () => this.hideNotification());
        }
        
        // Автоматическое скрытие уведомлений
        setTimeout(() => this.hideNotification(), 5000);
        
        // Обработка Enter в полях
        this.usernameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.passwordInput.focus();
            }
        });
    }

    async checkExistingAuth() {
        try {
            const response = await fetch('/api/auth/check', {
                method: 'GET',
                credentials: 'include'
            });
            
            const data = await response.json();
            
            if (data.authenticated) {
                // Пользователь уже авторизован, перенаправляем
                window.location.href = '/admin';
            }
        } catch (error) {
            console.log('Проверка авторизации не удалась:', error.message);
        }
    }

    async handleLogin(e) {
        e.preventDefault();
        
        // Очищаем предыдущие ошибки
        this.clearErrors();
        
        // Валидация
        if (!this.validateForm()) {
            return;
        }
        
        // Показываем загрузку
        this.setLoading(true);
        
        try {
            const formData = new FormData(this.form);
            const loginData = {
                username: formData.get('username').trim(),
                password: formData.get('password')
            };
            
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify(loginData)
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                // Успешный вход
                this.showMessage('Успешный вход! Перенаправление...', 'success');
                
                // Сохраняем CSRF токен если есть
                if (data.csrfToken) {
                    sessionStorage.setItem('csrfToken', data.csrfToken);
                }
                
                // Перенаправляем через небольшую задержку
                setTimeout(() => {
                    window.location.href = '/admin';
                }, 1000);
                
            } else {
                // Ошибка входа
                this.handleLoginError(data, response.status);
            }
            
        } catch (error) {
            console.error('Ошибка при входе:', error);
            this.showMessage('Ошибка соединения с сервером. Попробуйте позже.', 'error');
        } finally {
            this.setLoading(false);
        }
    }

    handleLoginError(data, status) {
        let message = data.error || 'Произошла ошибка при входе';
        
        if (status === 429) {
            // Слишком много попыток
            message = data.error || 'Слишком много попыток входа. Попробуйте позже.';
            this.showNotification('Доступ ограничен', message, 'warning');
        } else if (status === 401) {
            // Неверные учетные данные
            this.showFieldError('password', 'Неверный логин или пароль');
        } else {
            // Другие ошибки
            this.showMessage(message, 'error');
        }
    }

    validateForm() {
        let isValid = true;
        
        // Проверка логина
        const username = this.usernameInput.value.trim();
        if (!username) {
            this.showFieldError('username', 'Логин обязателен для заполнения');
            isValid = false;
        } else if (username.length < 3) {
            this.showFieldError('username', 'Логин должен содержать минимум 3 символа');
            isValid = false;
        }
        
        // Проверка пароля
        const password = this.passwordInput.value;
        if (!password) {
            this.showFieldError('password', 'Пароль обязателен для заполнения');
            isValid = false;
        } else if (password.length < 6) {
            this.showFieldError('password', 'Пароль должен содержать минимум 6 символов');
            isValid = false;
        }
        
        return isValid;
    }

    validateField(fieldName) {
        const field = document.getElementById(fieldName);
        const value = field.value.trim();
        
        // Очищаем предыдущую ошибку
        this.clearFieldError(fieldName);
        
        if (fieldName === 'username' && value && value.length < 3) {
            this.showFieldError(fieldName, 'Логин должен содержать минимум 3 символа');
            return false;
        }
        
        if (fieldName === 'password' && value && value.length < 6) {
            this.showFieldError(fieldName, 'Пароль должен содержать минимум 6 символов');
            return false;
        }
        
        return true;
    }

    showFieldError(fieldName, message) {
        const errorElement = document.getElementById(`${fieldName}-error`);
        const fieldElement = document.getElementById(fieldName);
        
        if (errorElement) {
            errorElement.textContent = message;
        }
        
        if (fieldElement) {
            fieldElement.style.borderColor = 'var(--danger)';
        }
    }

    clearFieldError(fieldName) {
        const errorElement = document.getElementById(`${fieldName}-error`);
        const fieldElement = document.getElementById(fieldName);
        
        if (errorElement) {
            errorElement.textContent = '';
        }
        
        if (fieldElement) {
            fieldElement.style.borderColor = '';
        }
    }

    clearErrors() {
        this.clearFieldError('username');
        this.clearFieldError('password');
        this.hideMessage();
    }

    showMessage(message, type = 'error') {
        this.formMessage.textContent = message;
        this.formMessage.className = `form-message ${type}`;
        this.formMessage.style.display = 'block';
    }

    hideMessage() {
        this.formMessage.style.display = 'none';
    }

    showNotification(title, message, type = 'error') {
        const titleElement = this.notification.querySelector('.notification-title');
        const textElement = this.notification.querySelector('.notification-text');
        const iconElement = this.notification.querySelector('.notification-icon i');
        
        if (titleElement) titleElement.textContent = title;
        if (textElement) textElement.textContent = message;
        
        // Устанавливаем иконку в зависимости от типа
        if (iconElement) {
            iconElement.className = type === 'success' ? 'fas fa-check-circle' :
                                   type === 'warning' ? 'fas fa-exclamation-triangle' :
                                   'fas fa-exclamation-circle';
        }
        
        // Устанавливаем класс уведомления
        this.notification.className = `notification ${type} show`;
        
        // Автоматически скрываем через 5 секунд
        setTimeout(() => this.hideNotification(), 5000);
    }

    hideNotification() {
        this.notification.classList.remove('show');
    }

    togglePassword() {
        const type = this.passwordInput.type === 'password' ? 'text' : 'password';
        this.passwordInput.type = type;
        
        const icon = this.passwordToggle.querySelector('i');
        icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
    }

    setLoading(loading) {
        const buttonText = this.loginButton.querySelector('.button-text');
        const buttonLoader = this.loginButton.querySelector('.button-loader');
        
        if (loading) {
            buttonText.style.display = 'none';
            buttonLoader.style.display = 'inline-block';
            this.loginButton.disabled = true;
        } else {
            buttonText.style.display = 'inline-block';
            buttonLoader.style.display = 'none';
            this.loginButton.disabled = false;
        }
    }
}

// Инициализация при загрузке страницы
document.addEventListener('DOMContentLoaded', () => {
    new LoginSystem();
});

// Обработка ошибок JavaScript
window.addEventListener('error', (e) => {
    console.error('JavaScript ошибка:', e.error);
});

// Предотвращение отправки формы при нажатии Enter в неподходящих местах
document.addEventListener('keydown', (e) => {
    if (e.key === 'Enter' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'BUTTON') {
        e.preventDefault();
    }
});
