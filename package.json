{"name": "shms-car-dealership", "version": "1.0.0", "description": "Car dealership website with admin panel", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "production": "NODE_ENV=production node server.js", "local": "node server-local.js", "dev:local": "nodemon server-local.js", "start:local": "NODE_ENV=development node server-local.js", "pm2": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop shms-auto", "pm2:restart": "pm2 restart shms-auto", "pm2:reload": "pm2 reload shms-auto", "pm2:logs": "pm2 logs shms-auto", "pm2:monit": "pm2 monit", "health-check": "node scripts/health-check.js", "backup-db": "node scripts/backup-db.js", "test-mail": "node scripts/test-mail.js", "test": "node scripts/test-app.js", "deploy:ci": "chmod +x scripts/deploy-ci.sh && ./scripts/deploy-ci.sh", "rollback": "chmod +x scripts/rollback.sh && ./scripts/rollback.sh", "backup": "chmod +x scripts/backup-before-deploy.sh && ./scripts/backup-before-deploy.sh", "lint": "echo 'Linting...' && node -c server.js && node -c ecosystem.config.js && node -c server-local.js", "pretest": "npm run lint", "switch:local": "node switch-mode.js local", "switch:production": "node switch-mode.js production", "switch:help": "node switch-mode.js", "setup-auth": "node scripts/setup-auth.js", "setup-auth-db": "node scripts/setup-auth-db.js", "create-admin": "node create-admin.js", "delete-admin": "node delete-admin.js", "list-admins": "node list-admins.js", "fix-database": "node fix-database.js", "reset-database": "node reset-database.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "cheerio": "^1.0.0-rc.12", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "fs": "0.0.1-security", "multer": "^1.4.5-lts.1", "node-fetch": "^2.6.7", "nodemailer": "^7.0.3", "path": "^0.12.7", "session-file-store": "^1.5.0", "sqlite3": "^5.1.6"}, "devDependencies": {"nodemon": "^3.0.1", "pm2": "^5.3.0"}}