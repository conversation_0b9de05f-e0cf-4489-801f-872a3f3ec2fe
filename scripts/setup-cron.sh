#!/bin/bash
# Скрипт для настройки автоматического обновления Encar через cron
# Версия: 1.0 для продакшена

# ===============================================================================
# КОНФИГУРАЦИЯ
# ===============================================================================

SITE_ROOT="/var/www/shms-auto"
SCRIPT_PATH="$SITE_ROOT/scripts/auto-update-encar.sh"
CRON_TIME="0 10 * * *"  # Каждый день в 10:00
CRON_USER="www-data"    # Пользователь для запуска (может быть другой)

# ===============================================================================
# ФУНКЦИИ
# ===============================================================================

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*"
}

check_requirements() {
    log "Проверка требований..."
    
    # Проверяем наличие основного скрипта
    if [ ! -f "$SCRIPT_PATH" ]; then
        log "❌ Ошибка: скрипт не найден: $SCRIPT_PATH"
        return 1
    fi
    
    # Проверяем права на выполнение
    if [ ! -x "$SCRIPT_PATH" ]; then
        log "⚠️ Устанавливаем права на выполнение для скрипта"
        chmod +x "$SCRIPT_PATH"
    fi
    
    # Проверяем наличие cron
    if ! command -v crontab >/dev/null 2>&1; then
        log "❌ Ошибка: cron не установлен"
        return 1
    fi
    
    # Проверяем наличие PHP
    if ! command -v php >/dev/null 2>&1; then
        log "❌ Ошибка: PHP не найден"
        return 1
    fi
    
    # Проверяем наличие PM2
    if ! command -v pm2 >/dev/null 2>&1; then
        log "⚠️ Предупреждение: PM2 не найден, перезапуск сайта будет пропущен"
    fi
    
    log "✅ Все требования выполнены"
    return 0
}

setup_directories() {
    log "Создание необходимых директорий..."
    
    # Создаем директории
    mkdir -p "$SITE_ROOT/logs"
    mkdir -p "$SITE_ROOT/backups/csv"
    mkdir -p "$SITE_ROOT/scripts"
    
    # Устанавливаем права
    chmod 755 "$SITE_ROOT/logs"
    chmod 755 "$SITE_ROOT/backups"
    chmod 755 "$SITE_ROOT/scripts"
    
    log "✅ Директории созданы"
}

install_cron_job() {
    log "Установка cron задачи..."
    
    # Создаем временный файл с новой cron задачей
    local temp_cron=$(mktemp)
    local cron_comment="# SHMS Auto - автоматическое обновление Encar данных"
    local cron_job="$CRON_TIME $SCRIPT_PATH >/dev/null 2>&1"
    
    # Получаем текущие cron задачи
    crontab -l 2>/dev/null > "$temp_cron" || true
    
    # Проверяем, есть ли уже наша задача
    if grep -q "auto-update-encar.sh" "$temp_cron"; then
        log "⚠️ Cron задача уже существует, обновляем..."
        # Удаляем старые записи
        grep -v "auto-update-encar.sh" "$temp_cron" > "${temp_cron}.new"
        mv "${temp_cron}.new" "$temp_cron"
    fi
    
    # Добавляем новую задачу
    echo "" >> "$temp_cron"
    echo "$cron_comment" >> "$temp_cron"
    echo "$cron_job" >> "$temp_cron"
    
    # Устанавливаем новый crontab
    if crontab "$temp_cron"; then
        log "✅ Cron задача установлена успешно"
        log "📅 Расписание: каждый день в 10:00"
        log "🔧 Команда: $cron_job"
    else
        log "❌ Ошибка установки cron задачи"
        rm -f "$temp_cron"
        return 1
    fi
    
    # Удаляем временный файл
    rm -f "$temp_cron"
    return 0
}

test_script() {
    log "Тестирование скрипта обновления..."
    
    # Запускаем скрипт в тестовом режиме
    if [ -x "$SCRIPT_PATH" ]; then
        log "🧪 Запуск тестового обновления..."
        
        # Создаем тестовый лог
        local test_log="$SITE_ROOT/logs/test-update.log"
        
        # Запускаем скрипт
        if "$SCRIPT_PATH" > "$test_log" 2>&1; then
            log "✅ Тестовое обновление прошло успешно"
            log "📄 Лог сохранен в: $test_log"
        else
            log "❌ Ошибка при тестовом обновлении"
            log "📄 Проверьте лог: $test_log"
            return 1
        fi
    else
        log "❌ Скрипт не найден или не исполняемый: $SCRIPT_PATH"
        return 1
    fi
    
    return 0
}

show_status() {
    log "Текущий статус автоматического обновления:"
    echo ""
    
    # Показываем cron задачи
    echo "📅 Cron задачи:"
    crontab -l 2>/dev/null | grep -A1 -B1 "auto-update-encar" || echo "   Задачи не найдены"
    echo ""
    
    # Показываем последние логи
    local log_file="$SITE_ROOT/logs/encar-update.log"
    if [ -f "$log_file" ]; then
        echo "📄 Последние записи лога:"
        tail -5 "$log_file" | sed 's/^/   /'
    else
        echo "📄 Лог файл не найден: $log_file"
    fi
    echo ""
    
    # Показываем статус PM2
    if command -v pm2 >/dev/null 2>&1; then
        echo "🔄 Статус PM2:"
        pm2 status 2>/dev/null | grep -E "(shms-auto|App name)" || echo "   PM2 приложения не найдены"
    else
        echo "🔄 PM2 не установлен"
    fi
}

remove_cron_job() {
    log "Удаление cron задачи..."
    
    # Создаем временный файл
    local temp_cron=$(mktemp)
    
    # Получаем текущие cron задачи и удаляем наши
    crontab -l 2>/dev/null | grep -v "auto-update-encar" > "$temp_cron" || true
    
    # Устанавливаем обновленный crontab
    if crontab "$temp_cron"; then
        log "✅ Cron задача удалена"
    else
        log "❌ Ошибка удаления cron задачи"
        rm -f "$temp_cron"
        return 1
    fi
    
    rm -f "$temp_cron"
    return 0
}

# ===============================================================================
# ОСНОВНАЯ ЛОГИКА
# ===============================================================================

show_help() {
    echo "Настройка автоматического обновления Encar для SHMS Auto"
    echo ""
    echo "Использование: $0 [команда]"
    echo ""
    echo "Команды:"
    echo "  install    - Установить автоматическое обновление"
    echo "  remove     - Удалить автоматическое обновление"
    echo "  status     - Показать текущий статус"
    echo "  test       - Протестировать скрипт обновления"
    echo "  help       - Показать эту справку"
    echo ""
    echo "Примеры:"
    echo "  $0 install    # Установить автообновление"
    echo "  $0 status     # Проверить статус"
    echo "  $0 test       # Протестировать"
}

main() {
    local command="${1:-help}"
    
    echo "🔧 НАСТРОЙКА АВТОМАТИЧЕСКОГО ОБНОВЛЕНИЯ ENCAR"
    echo "=============================================="
    echo ""
    
    case "$command" in
        "install")
            log "Начало установки автоматического обновления..."
            
            if check_requirements && setup_directories && install_cron_job; then
                log "🎉 Автоматическое обновление успешно настроено!"
                echo ""
                log "📋 Что было сделано:"
                log "   • Проверены все требования"
                log "   • Созданы необходимые директории"
                log "   • Установлена cron задача (каждый день в 10:00)"
                echo ""
                log "🔍 Для проверки статуса используйте: $0 status"
                log "🧪 Для тестирования используйте: $0 test"
            else
                log "❌ Ошибка при установке автоматического обновления"
                exit 1
            fi
            ;;
            
        "remove")
            log "Удаление автоматического обновления..."
            if remove_cron_job; then
                log "✅ Автоматическое обновление удалено"
            else
                log "❌ Ошибка при удалении"
                exit 1
            fi
            ;;
            
        "status")
            show_status
            ;;
            
        "test")
            if check_requirements; then
                test_script
            else
                log "❌ Не выполнены требования для тестирования"
                exit 1
            fi
            ;;
            
        "help"|*)
            show_help
            ;;
    esac
}

# Запуск
main "$@"
