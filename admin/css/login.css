/* Сброс стилей */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* CSS переменные в стиле SHMS Auto */
:root {
  --primary: #4361ee;
  --primary-hover: #3a56e4;
  --primary-light: rgba(67, 97, 238, 0.1);
  --primary-gradient: linear-gradient(135deg, #4361ee, #3a56e4);
  --secondary: #6e7787;
  --success: #2ecc71;
  --danger: #e53e3e;
  --warning: #f39c12;
  --dark: #1e293b;
  --light: #f8fafc;
  --border: #e2e8f0;
  --text: #1e293b;
  --text-light: #64748b;
  --white: #ffffff;
  --shadow: rgba(0, 0, 0, 0.1);
  --shadow-lg: rgba(0, 0, 0, 0.15);
  --radius: 10px;
  --radius-lg: 16px;
  --transition: all 0.3s ease;
  --header-bg: linear-gradient(135deg, #4361ee, #805ad5);
}

/* Основные стили */
body {
  font-family: "Segoe UI", <PERSON><PERSON>, -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
  color: var(--text);
  background: var(--light);
  overflow-x: hidden;
}

/* Контейнер входа */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 2rem;
}

/* Фон */
.login-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--header-bg);
  z-index: -2;
}

.login-background::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.login-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at 30% 70%,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%
  );
  z-index: -1;
}

/* Контент входа */
.login-content {
  width: 100%;
  max-width: 450px;
  z-index: 1;
}

/* Карточка входа */
.login-card {
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: 0 20px 40px var(--shadow-lg);
  padding: 2.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.login-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

/* Заголовок */
.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.login-logo i {
  font-size: 2rem;
  color: var(--primary);
}

.login-logo h1 {
  font-size: 1.75rem;
  font-weight: 700;
  background: var(--header-bg);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin: 0;
}

.login-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--dark);
  margin-bottom: 0.5rem;
}

.login-header p {
  color: var(--text-light);
  font-size: 0.9rem;
}

/* Форма */
.login-form {
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text);
  font-size: 0.9rem;
}

.form-group label i {
  color: var(--primary);
  width: 16px;
}

/* Поля ввода */
input[type="text"],
input[type="password"] {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid var(--border);
  border-radius: var(--radius);
  font-size: 1rem;
  background-color: var(--white);
  color: var(--text);
  transition: var(--transition);
  outline: none;
}

input[type="text"]:focus,
input[type="password"]:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-light);
}

input[type="text"]:invalid,
input[type="password"]:invalid {
  border-color: var(--danger);
}

/* Поле пароля с кнопкой показать/скрыть */
.password-input {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: var(--transition);
}

.password-toggle:hover {
  color: var(--primary);
  background: var(--primary-light);
}

/* Ошибки полей */
.field-error {
  color: var(--danger);
  font-size: 0.8rem;
  margin-top: 0.25rem;
  min-height: 1rem;
}

/* Опции формы */
.form-options {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

/* Чекбокс "Запомнить меня" */
.remember-me {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--text);
}

.remember-me input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--border);
  border-radius: 4px;
  position: relative;
  transition: var(--transition);
}

.remember-me input[type="checkbox"]:checked + .checkmark {
  background: var(--primary);
  border-color: var(--primary);
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* Кнопка входа */
.login-button {
  width: 100%;
  padding: 1rem;
  background: var(--primary-gradient);
  color: var(--white);
  border: none;
  border-radius: var(--radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4);
}

.login-button:active {
  transform: translateY(0);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.login-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
}

.login-button:hover::before {
  left: 100%;
}

/* Сообщения формы */
.form-message {
  margin-top: 1rem;
  padding: 0.75rem;
  border-radius: var(--radius);
  font-size: 0.9rem;
  text-align: center;
  display: none;
}

.form-message.success {
  background: rgba(46, 204, 113, 0.1);
  color: var(--success);
  border: 1px solid rgba(46, 204, 113, 0.3);
}

.form-message.error {
  background: rgba(229, 62, 62, 0.1);
  color: var(--danger);
  border: 1px solid rgba(229, 62, 62, 0.3);
}

/* Подвал */
.login-footer {
  text-align: center;
  color: var(--text-light);
  font-size: 0.85rem;
}

.login-footer p {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.login-footer i {
  color: var(--success);
}

.security-info {
  opacity: 0.8;
}

.security-info i {
  color: var(--primary);
}

/* Уведомления */
.notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: var(--white);
  border-radius: var(--radius);
  box-shadow: 0 10px 25px var(--shadow-lg);
  padding: 1rem;
  max-width: 400px;
  z-index: 1000;
  transform: translateX(100%);
  transition: var(--transition);
  border-left: 4px solid var(--danger);
}

.notification.show {
  transform: translateX(0);
}

.notification.success {
  border-left-color: var(--success);
}

.notification.warning {
  border-left-color: var(--warning);
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.notification-icon {
  color: var(--danger);
  font-size: 1.25rem;
  margin-top: 0.125rem;
}

.notification.success .notification-icon {
  color: var(--success);
}

.notification.warning .notification-icon {
  color: var(--warning);
}

.notification-message {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--dark);
}

.notification-text {
  color: var(--text-light);
  font-size: 0.9rem;
}

.notification-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--text-light);
  cursor: pointer;
  padding: 0;
  line-height: 1;
  transition: var(--transition);
}

.notification-close:hover {
  color: var(--text);
}

/* Адаптивность */
@media (max-width: 768px) {
  .login-container {
    padding: 1rem;
  }

  .login-card {
    padding: 2rem;
  }

  .form-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .notification {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .login-card {
    padding: 1.5rem;
  }

  .login-logo h1 {
    font-size: 1.5rem;
  }

  .login-header h2 {
    font-size: 1.25rem;
  }
}
