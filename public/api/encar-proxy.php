<?php
// Увеличиваем лимиты для обработки больших файлов
ini_set('max_execution_time', 300); // 5 минут
ini_set('memory_limit', '4G');      // Увеличиваем до 4GB для больших файлов

// КРИТИЧНО: Отключаем отображение ошибок чтобы не ломать JSON
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// КРИТИЧНО: Устанавливаем JSON заголовки в самом начале
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Cache-Control: max-age=300'); // Кэшировать результаты на стороне браузера на 5 минут

// Обработка OPTIONS запроса
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Включаем отладку
$debug = isset($_GET['debug']) && $_GET['debug'] == 1;
// КРИТИЧНО: Никогда не включаем display_errors в продакшене для JSON API
// if ($debug) {
//     ini_set('display_errors', 1);
// }

// Логирование запросов
function logDebug($message) {
    global $debug;
    if ($debug) {
        $logFile = __DIR__ . '/logs/debug.log';
        $timestamp = date('Y-m-d H:i:s');
        file_put_contents($logFile, "[$timestamp] [API Debug] $message\n", FILE_APPEND | LOCK_EX);
        // КРИТИЧНО: Никогда не выводим в браузер, чтобы не ломать JSON
    }
}

// Функция для отправки JSON ответа
function sendJsonResponse($data, $statusCode = 200) {
    // КРИТИЧНО: Заголовки уже установлены в начале файла
    if (!headers_sent()) {
        http_response_code($statusCode);
    }

    // КРИТИЧНО: Очищаем буфер вывода перед отправкой JSON
    if (ob_get_level()) {
        ob_clean();
    }

    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

// Функция для обработки ошибок
function handleError($message, $statusCode = 500, $debug = false) {
    $response = ['error' => $message];
    if ($debug) {
        $response['debug'] = [
            'file' => __FILE__,
            'line' => __LINE__,
            'time' => date('Y-m-d H:i:s')
        ];
    }
    sendJsonResponse($response, $statusCode);
}

// Обработка ошибок PHP
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    // КРИТИЧНО: Логируем ошибку, но не выводим HTML
    error_log("PHP Error: $errstr in $errfile on line $errline");

    // Только для критических ошибок отправляем JSON ответ
    if ($errno === E_ERROR || $errno === E_PARSE || $errno === E_CORE_ERROR || $errno === E_COMPILE_ERROR) {
        handleError("Internal server error", 500, false);
    }

    return true; // Предотвращаем стандартный вывод ошибки PHP
});

// Обработка исключений
set_exception_handler(function($e) {
    // КРИТИЧНО: Логируем исключение, но не выводим HTML
    error_log("PHP Exception: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    handleError("Internal server error", 500, false);
});

logDebug("API request started");

// ===== ФУНКЦИИ УЛУЧШЕННОГО ПОИСКА =====

// Словарь сокращений и альтернативных названий брендов
function getBrandAliases() {
    return [
        // Сокращения
        'VW' => 'Volkswagen',
        'Benz' => 'Mercedes-Benz',
        'Merc' => 'Mercedes-Benz',
        'MB' => 'Mercedes-Benz',
        'BMW' => 'BMW',
        'Audi' => 'Audi',
        'Volvo' => 'Volvo',
        'Ford' => 'Ford',
        'GM' => 'Chevrolet',
        'Chevy' => 'Chevrolet',
        'Lexus' => 'Lexus',
        'Acura' => 'Acura',
        'Infiniti' => 'Infiniti',
        'Caddy' => 'Cadillac',
        'Jag' => 'Jaguar',
        'LR' => 'Land Rover',
        'RR' => 'Rolls-Royce',

        // Альтернативные написания
        'Mercedes' => 'Mercedes-Benz',
        'Mercedes Benz' => 'Mercedes-Benz',
        'Land Rover' => 'Land Rover',
        'Landrover' => 'Land Rover',
        'Range Rover' => 'Land Rover',
        'Rolls Royce' => 'Rolls-Royce',
        'Alfa Romeo' => 'Alfa Romeo',
        'Alfa' => 'Alfa Romeo',

        // Корейские бренды
        'Hyundai' => 'Hyundai',
        'Kia' => 'Kia',
        'Genesis' => 'Genesis',
        'SsangYong' => 'SsangYong',
        'Daewoo' => 'Daewoo',

        // Японские бренды
        'Toyota' => 'Toyota',
        'Honda' => 'Honda',
        'Nissan' => 'Nissan',
        'Mazda' => 'Mazda',
        'Subaru' => 'Subaru',
        'Mitsubishi' => 'Mitsubishi',
        'Suzuki' => 'Suzuki',
        'Isuzu' => 'Isuzu'
    ];
}

// Функция нормализации названия бренда
function normalizeBrandName($brand) {
    if (empty($brand)) return '';

    // Убираем лишние пробелы и приводим к нижнему регистру для сравнения
    $normalized = trim(strtolower($brand));

    // Заменяем различные разделители на пробелы
    $normalized = preg_replace('/[-_\.]+/', ' ', $normalized);

    // Убираем множественные пробелы
    $normalized = preg_replace('/\s+/', ' ', $normalized);

    return $normalized;
}

// Функция разделения поискового запроса на марку и модель
function parseSearchQuery($searchTerm) {
    $result = ['brand' => '', 'model' => ''];

    if (empty($searchTerm)) {
        return $result;
    }

    $searchTerm = trim($searchTerm);
    logDebug("Parsing search query: '$searchTerm'");

    // Список известных брендов для точного распознавания
    $knownBrands = array_merge(
        array_keys(getBrandAliases()),
        array_values(getBrandAliases()),
        [
            'Acura', 'Alfa Romeo', 'Aston Martin', 'Bentley', 'Bugatti', 'Cadillac',
            'Chevrolet', 'Chrysler', 'Citroen', 'Dacia', 'Ferrari', 'Fiat', 'Genesis',
            'Hyundai', 'Infiniti', 'Jaguar', 'Jeep', 'Kia', 'Lamborghini', 'Lancia',
            'Land Rover', 'Lexus', 'Lincoln', 'Lotus', 'Maserati', 'Maybach', 'Mazda',
            'McLaren', 'Mercedes-Benz', 'MINI', 'Mitsubishi', 'Nissan', 'Opel', 'Peugeot',
            'Porsche', 'Renault', 'Rolls-Royce', 'Saab', 'SEAT', 'Skoda', 'Smart',
            'SsangYong', 'Subaru', 'Suzuki', 'Tesla', 'Toyota', 'Volkswagen', 'Volvo'
        ]
    );

    // Сортируем бренды по длине (сначала длинные, чтобы "Land Rover" находился раньше "Land")
    usort($knownBrands, function($a, $b) {
        return strlen($b) - strlen($a);
    });

    // Ищем совпадения с известными брендами
    $foundBrand = '';
    $remainingText = $searchTerm;

    foreach ($knownBrands as $brand) {
        $normalizedBrand = normalizeBrandName($brand);
        $normalizedSearch = normalizeBrandName($searchTerm);

        // Проверяем точное совпадение в начале строки
        if (strpos($normalizedSearch, $normalizedBrand) === 0) {
            $foundBrand = $brand;
            // Убираем найденный бренд из строки поиска
            $remainingText = trim(substr($searchTerm, strlen($brand)));
            break;
        }

        // Проверяем совпадение с учетом пробелов и разделителей
        $pattern = '/^' . preg_quote($normalizedBrand, '/') . '[\s\-_]*(.*)$/i';
        if (preg_match($pattern, $normalizedSearch, $matches)) {
            $foundBrand = $brand;
            $remainingText = trim($matches[1] ?? '');
            break;
        }
    }

    // Если бренд найден, оставшийся текст считаем моделью
    if (!empty($foundBrand)) {
        $result['brand'] = $foundBrand;
        $result['model'] = $remainingText;
    } else {
        // Если бренд не найден, весь текст считаем брендом
        $result['brand'] = $searchTerm;
    }

    logDebug("Parsed result: brand='{$result['brand']}', model='{$result['model']}'");
    return $result;
}

// Функция улучшенного поиска по бренду
function improvedBrandSearch($carMark, $searchBrand) {
    if (empty($searchBrand) || empty($carMark)) {
        return true; // Если поиск пустой, пропускаем фильтр
    }

    $aliases = getBrandAliases();

    // Нормализуем названия для сравнения
    $normalizedCarMark = normalizeBrandName($carMark);
    $normalizedSearchBrand = normalizeBrandName($searchBrand);

    // 1. Прямое совпадение
    if (strpos($normalizedCarMark, $normalizedSearchBrand) !== false) {
        return true;
    }

    // 2. Поиск через алиасы - проверяем, есть ли поисковый термин в алиасах
    $expandedBrand = '';
    foreach ($aliases as $alias => $fullName) {
        if (normalizeBrandName($alias) === $normalizedSearchBrand) {
            $expandedBrand = normalizeBrandName($fullName);
            break;
        }
    }

    if (!empty($expandedBrand) && strpos($normalizedCarMark, $expandedBrand) !== false) {
        return true;
    }

    // 3. Обратный поиск - проверяем, есть ли марка авто в алиасах поискового термина
    foreach ($aliases as $alias => $fullName) {
        if (normalizeBrandName($fullName) === $normalizedSearchBrand) {
            if (strpos($normalizedCarMark, normalizeBrandName($alias)) !== false) {
                return true;
            }
        }
    }

    // 4. Частичное совпадение для коротких запросов (2+ символа)
    if (strlen($normalizedSearchBrand) >= 2) {
        // Проверяем начало названия
        if (strpos($normalizedCarMark, $normalizedSearchBrand) === 0) {
            return true;
        }

        // Проверяем каждое слово в названии марки
        $carWords = explode(' ', $normalizedCarMark);
        foreach ($carWords as $word) {
            if (strpos($word, $normalizedSearchBrand) === 0) {
                return true;
            }
        }
    }

    return false;
}

// Функция улучшенного поиска по модели
function improvedModelSearch($carModel, $searchModel) {
    if (empty($searchModel) || empty($carModel)) {
        return true; // Если поиск пустой, пропускаем фильтр
    }

    // Нормализуем названия для сравнения
    $normalizedCarModel = normalizeBrandName($carModel); // Используем ту же функцию нормализации
    $normalizedSearchModel = normalizeBrandName($searchModel);

    // 1. Прямое совпадение
    if (strpos($normalizedCarModel, $normalizedSearchModel) !== false) {
        return true;
    }

    // 2. Поиск по началу слов
    $carWords = explode(' ', $normalizedCarModel);
    $searchWords = explode(' ', $normalizedSearchModel);

    foreach ($searchWords as $searchWord) {
        if (strlen($searchWord) >= 2) {
            foreach ($carWords as $carWord) {
                if (strpos($carWord, $searchWord) === 0) {
                    return true;
                }
            }
        }
    }

    return false;
}

// Get parameters - date теперь необязательный, используем текущую дату по умолчанию
$date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');

// Обрабатываем поисковый запрос для улучшенного поиска
$rawBrand = isset($_GET['brand']) ? $_GET['brand'] : '';
$rawModel = isset($_GET['model']) ? $_GET['model'] : '';

// Парсим комбинированный запрос, если модель не указана отдельно
if (!empty($rawBrand) && empty($rawModel)) {
    $parsedSearch = parseSearchQuery($rawBrand);
    $brand = $parsedSearch['brand'];
    $model = $parsedSearch['model'];
    logDebug("Parsed combined search: '$rawBrand' -> brand='$brand', model='$model'");
} else {
    $brand = $rawBrand;
    $model = $rawModel;
    logDebug("Using separate brand='$brand', model='$model'");
}
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;

// Для приоритетной сортировки увеличиваем лимит, чтобы показать премиум автомобили
$prioritySort = isset($_GET['priority_sort']) && $_GET['priority_sort'] === 'true';
if ($prioritySort) {
    // Для приоритетной сортировки разрешаем больший лимит
    if ($limit > 1000) {
        $limit = 1000; // Максимум 1000 для приоритетной сортировки
    }
} else {
    // Ограничиваем максимальный limit для производительности в обычном режиме
    if ($limit > 50) {
        $limit = 50;
    }
}
$offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
$featuredOnly = isset($_GET['featured']) && $_GET['featured'] == 1;
$showActive = !isset($_GET['active']) || $_GET['active'] != '0'; // По умолчанию показываем активные
$showRemoved = isset($_GET['removed']) && $_GET['removed'] == '1'; // По умолчанию не показываем removed

logDebug("Parameters: date=$date, brand=$brand, model=$model, limit=$limit, offset=$offset");

// Параметры фильтрации
$yearFrom = isset($_GET['year_from']) ? (int)$_GET['year_from'] : 0;
$yearTo = isset($_GET['year_to']) ? (int)$_GET['year_to'] : 0;
$priceFrom = isset($_GET['price_from']) ? (int)$_GET['price_from'] : 0;
$priceTo = isset($_GET['price_to']) ? (int)$_GET['price_to'] : 0;
$transmission = isset($_GET['transmission']) ? $_GET['transmission'] : '';

// Новые параметры фильтрации
$bodyType = isset($_GET['body_type']) ? $_GET['body_type'] : '';
$fuelType = isset($_GET['fuel_type']) ? $_GET['fuel_type'] : '';
$mileageFrom = isset($_GET['mileage_from']) ? (int)$_GET['mileage_from'] : 0;
$mileageTo = isset($_GET['mileage_to']) ? (int)$_GET['mileage_to'] : 0;
$driveType = isset($_GET['drive_type']) ? $_GET['drive_type'] : '';
$category = isset($_GET['category']) ? $_GET['category'] : '';

// Проверка параметра no_cache
$useCache = !isset($_GET['no_cache']) || $_GET['no_cache'] != '1';

// Путь к кэшу
$cacheDir = __DIR__ . '/cache/';
$cacheFileName = md5($date . '_' . $brand . '_' . $model . '_' . $limit . '_' . $offset . '_' . ($featuredOnly ? '1' : '0') . '_' . ($showActive ? '1' : '0') . '_' . ($showRemoved ? '1' : '0') . '_' . $yearFrom . '_' . $yearTo . '_' . $priceFrom . '_' . $priceTo . '_' . $transmission . '_' . $bodyType . '_' . $fuelType . '_' . $mileageFrom . '_' . $mileageTo . '_' . $driveType . '_' . $category) . '.json';
$cacheFile = $cacheDir . $cacheFileName;
$cacheExpire = 3600; // 1 час

// Проверяем кэш
if ($useCache && file_exists($cacheFile) && (time() - filemtime($cacheFile) < $cacheExpire)) {
    logDebug("Using cached data from: $cacheFile");

    // КРИТИЧНО: Устанавливаем заголовки перед выводом кэша
    if (!headers_sent()) {
        header('X-Cache: HIT');
    }

    // КРИТИЧНО: Читаем кэш и отправляем как JSON
    $cachedData = file_get_contents($cacheFile);
    if ($cachedData !== false) {
        // Очищаем буфер вывода перед отправкой
        if (ob_get_level()) {
            ob_clean();
        }
        echo $cachedData;
        exit;
    }

    logDebug("Cache file exists but failed to read, proceeding with fresh data");
}

logDebug("Cache miss or disabled. Processing data from local files.");
if (!headers_sent()) {
    header('X-Cache: MISS');
}

// Функция для генерации тестовых данных
function getTestData($params) {
    logDebug("Generating test data with filters");

    $testCars = [
        [
            'id' => '1001',
            'mark' => 'BMW',
            'model' => 'X5',
            'year' => '2020',
            'price' => '45000',
            'mileage' => '35000',
            'transmission_type' => 'Автомат',
            'body_type' => 'Внедорожник',
            'fuel_type' => 'Бензин',
            'drive_type' => 'Полный',
            'displacement' => '3000',
            'color' => 'Черный'
        ],
        [
            'id' => '1002',
            'mark' => 'BMW',
            'model' => '5 Series',
            'year' => '2019',
            'price' => '38000',
            'mileage' => '42000',
            'transmission_type' => 'Автомат',
            'body_type' => 'Седан',
            'fuel_type' => 'Бензин',
            'drive_type' => 'Задний',
            'displacement' => '2000',
            'color' => 'Белый'
        ],
        [
            'id' => '1003',
            'mark' => 'Mercedes-Benz',
            'model' => 'S-Class',
            'year' => '2021',
            'price' => '85000',
            'mileage' => '15000',
            'transmission_type' => 'Автомат',
            'body_type' => 'Седан',
            'fuel_type' => 'Бензин',
            'drive_type' => 'Задний',
            'displacement' => '3000',
            'color' => 'Серый'
        ],
        [
            'id' => '1004',
            'mark' => 'Audi',
            'model' => 'A6',
            'year' => '2018',
            'price' => '32000',
            'mileage' => '55000',
            'transmission_type' => 'Автомат',
            'body_type' => 'Седан',
            'fuel_type' => 'Дизель',
            'drive_type' => 'Полный',
            'displacement' => '2000',
            'color' => 'Синий'
        ],
        [
            'id' => '1005',
            'mark' => 'Porsche',
            'model' => '911',
            'year' => '2022',
            'price' => '120000',
            'mileage' => '8000',
            'transmission_type' => 'Автомат',
            'body_type' => 'Купе',
            'fuel_type' => 'Бензин',
            'drive_type' => 'Задний',
            'displacement' => '3000',
            'color' => 'Красный'
        ],
        [
            'id' => '1006',
            'mark' => 'Toyota',
            'model' => 'Camry',
            'year' => '2020',
            'price' => '28000',
            'mileage' => '25000',
            'transmission_type' => 'Автомат',
            'body_type' => 'Седан',
            'fuel_type' => 'Гибрид',
            'drive_type' => 'Передний',
            'displacement' => '2500',
            'color' => 'Белый'
        ],
        [
            'id' => '1007',
            'mark' => 'Hyundai',
            'model' => 'Santa Fe',
            'year' => '2021',
            'price' => '35000',
            'mileage' => '18000',
            'transmission_type' => 'Автомат',
            'body_type' => 'Внедорожник',
            'fuel_type' => 'Бензин',
            'drive_type' => 'Полный',
            'displacement' => '2400',
            'color' => 'Серебристый'
        ],
        [
            'id' => '1008',
            'mark' => 'Kia',
            'model' => 'Sportage',
            'year' => '2019',
            'price' => '24000',
            'mileage' => '38000',
            'transmission_type' => 'Автомат',
            'body_type' => 'Внедорожник',
            'fuel_type' => 'Бензин',
            'drive_type' => 'Передний',
            'displacement' => '2000',
            'color' => 'Черный'
        ],
        [
            'id' => '1009',
            'mark' => 'Lexus',
            'model' => 'RX',
            'year' => '2020',
            'price' => '55000',
            'mileage' => '22000',
            'transmission_type' => 'Автомат',
            'body_type' => 'Внедорожник',
            'fuel_type' => 'Гибрид',
            'drive_type' => 'Полный',
            'displacement' => '3500',
            'color' => 'Белый'
        ],
        [
            'id' => '1010',
            'mark' => 'Volkswagen',
            'model' => 'Passat',
            'year' => '2018',
            'price' => '22000',
            'mileage' => '48000',
            'transmission_type' => 'Автомат',
            'body_type' => 'Седан',
            'fuel_type' => 'Дизель',
            'drive_type' => 'Передний',
            'displacement' => '2000',
            'color' => 'Серый'
        ]
    ];

    $filteredCars = [];
    $count = 0;
    $processed = 0;

    foreach ($testCars as $car) {
        // Применяем фильтры
        if (!empty($params['brand'])) {
            if (!improvedBrandSearch($car['mark'], $params['brand'])) {
                continue;
            }
        }

        if (!empty($params['model'])) {
            if (!improvedModelSearch($car['model'], $params['model'])) {
                continue;
            }
        }

        // Фильтрация по году выпуска
        if (!empty($params['yearFrom']) && $params['yearFrom'] > 0) {
            $carYear = (int)$car['year'];
            if ($carYear < $params['yearFrom']) {
                continue;
            }
        }

        if (!empty($params['yearTo']) && $params['yearTo'] > 0) {
            $carYear = (int)$car['year'];
            if ($carYear > $params['yearTo']) {
                continue;
            }
        }

        // Фильтрация по цене
        if (!empty($params['priceFrom']) && $params['priceFrom'] > 0) {
            $carPrice = (int)$car['price'];
            if ($carPrice < $params['priceFrom']) {
                continue;
            }
        }

        if (!empty($params['priceTo']) && $params['priceTo'] > 0) {
            $carPrice = (int)$car['price'];
            if ($carPrice > $params['priceTo']) {
                continue;
            }
        }

        // Фильтрация по категории
        if (!empty($params['category'])) {
            $carCategory = determineCarCategory($car['mark'], $car['model']);
            if ($carCategory !== $params['category']) {
                continue;
            }
        }

        // Пропускаем до offset
        if ($processed < $params['offset']) {
            $processed++;
            continue;
        }

        // Проверяем лимит
        if ($count >= $params['limit']) break;

        // Форматируем данные
        $car['price_formatted'] = formatPrice($car['price']);
        $car['mileage_formatted'] = formatMileage($car['mileage']);
        $car['engine'] = formatEngine($car);
        $car['translated_mark'] = translateBrand($car['mark']);
        $car['brand'] = $car['translated_mark'];
        $car['mark'] = $car['translated_mark'];
        $car['translated_model'] = translateModel($car['brand'], $car['model']);
        $car['model'] = $car['translated_model'];
        $car['transmission'] = $car['transmission_type'];
        $car['is_active'] = true;
        $car['local_image'] = '';

        $filteredCars[] = $car;
        $count++;
        $processed++;
    }

    logDebug("Test data: processed $processed, returned $count cars");
    return $filteredCars;
}

// Функция для получения данных из локальных CSV файлов
function fetchDataFromLocalCSV($date, $params) {
    $dataDir = __DIR__ . '/encar_data/';
    $result = [];

    logDebug("Looking for CSV files in: $dataDir");

    // Сначала ищем файлы с точным именем для даты
    $activeFile = $dataDir . 'encar_active_' . $date . '.csv';
    $removedFile = $dataDir . 'encar_removed_' . $date . '.csv';

    // Проверяем наличие файлов
    if ($params['showActive'] && file_exists($activeFile)) {
        logDebug("Found active file: $activeFile");
        $activeData = processCSVFile($activeFile, $params);
        $result = array_merge($result, $activeData);
    }

    if ($params['showRemoved'] && file_exists($removedFile)) {
        logDebug("Found removed file: $removedFile");
        $removedData = processCSVFile($removedFile, $params);
        $result = array_merge($result, $removedData);
    }

    // Если не найдено ни одного файла, ищем самый свежий доступный файл
    if (empty($result)) {
        logDebug("No specific files found, searching for most recent available data");

        // Сначала ищем файлы за предыдущие дни (до 30 дней назад)
        $foundFile = false;
        for ($i = 1; $i <= 30; $i++) {
            $prevDate = date('Y-m-d', strtotime("-$i days"));
            $prevActiveFile = $dataDir . 'encar_active_' . $prevDate . '.csv';

            if (file_exists($prevActiveFile)) {
                logDebug("Found data from previous date: $prevDate (requested: $date)");
                $activeData = processCSVFile($prevActiveFile, $params);
                $result = array_merge($result, $activeData);
                $foundFile = true;
                break;
            }
        }

        // Если не найдено файлов за последние 30 дней, ищем любые CSV файлы
        if (!$foundFile) {
            logDebug("No recent files found, searching for any CSV files");
            $csvFiles = glob($dataDir . 'encar_active_*.csv');

            if (!empty($csvFiles)) {
                // Сортируем файлы по дате в имени (самые новые сначала)
                usort($csvFiles, function($a, $b) {
                    // Извлекаем дату из имени файла
                    preg_match('/encar_active_(\d{4}-\d{2}-\d{2})\.csv/', $a, $matchesA);
                    preg_match('/encar_active_(\d{4}-\d{2}-\d{2})\.csv/', $b, $matchesB);

                    $dateA = isset($matchesA[1]) ? $matchesA[1] : '1970-01-01';
                    $dateB = isset($matchesB[1]) ? $matchesB[1] : '1970-01-01';

                    return strcmp($dateB, $dateA); // Убывающий порядок (новые сначала)
                });

                // Берем самый свежий файл
                $csvFile = $csvFiles[0];
                preg_match('/encar_active_(\d{4}-\d{2}-\d{2})\.csv/', $csvFile, $matches);
                $fileDate = isset($matches[1]) ? $matches[1] : 'unknown';

                logDebug("Using most recent CSV file: $csvFile (date: $fileDate, requested: $date)");
                $result = processCSVFile($csvFile, $params);
                $foundFile = true;
            }
        }

        // Если все еще нет данных, возвращаем тестовые данные для демонстрации фильтров
        if (!$foundFile) {
            logDebug("No CSV files found, returning test data");
            $result = getTestData($params);
        }
    }

    logDebug("Total records found: " . count($result));
    return $result;
}

// Функция для обработки CSV файла
function processCSVFile($file, $params) {
    logDebug("Processing CSV file: $file");

    $handle = fopen($file, "r");
    if (!$handle) {
        logDebug("Failed to open file: $file");
        return [];
    }

    // Получаем курсы валют один раз для всего файла
    $exchangeRates = getExchangeRates();
    logDebug("Exchange rates loaded: KRW_to_RUB=" . $exchangeRates['KRW_to_RUB'] . ", USD_rate=" . $exchangeRates['USD_rate']);

    // Счетчик обработанных записей для отладки производительности
    $processedCount = 0;
    $startTime = microtime(true);

    // Определяем разделитель CSV файла
    $firstLine = fgets($handle);
    rewind($handle);

    $delimiter = "|"; // По умолчанию
    if (substr_count($firstLine, ",") > substr_count($firstLine, "|")) {
        $delimiter = ",";
        logDebug("Using comma delimiter");
    } else {
        logDebug("Using pipe delimiter");
    }

    // Получаем заголовки
    $headers = fgetcsv($handle, 0, $delimiter);
    if (!$headers) {
        fclose($handle);
        logDebug("Failed to read headers from file: $file");
        return [];
    }

    logDebug("Headers found: " . implode(", ", array_slice($headers, 0, 10)) . (count($headers) > 10 ? "..." : ""));
    logDebug("Total headers: " . count($headers));

    // Проверяем наличие ключевых полей
    $requiredFields = ['mark', 'model', 'price'];
    $missingFields = [];
    foreach ($requiredFields as $field) {
        if (!in_array($field, $headers)) {
            $missingFields[] = $field;
        }
    }

    if (!empty($missingFields)) {
        logDebug("Missing required fields: " . implode(", ", $missingFields));
        // Попробуем найти альтернативные названия
        $alternativeNames = [
            'mark' => ['brand', 'марка', 'бренд'],
            'model' => ['модель'],
            'price' => ['цена', 'стоимость']
        ];

        foreach ($missingFields as $field) {
            if (isset($alternativeNames[$field])) {
                foreach ($alternativeNames[$field] as $alt) {
                    if (in_array($alt, $headers)) {
                        logDebug("Found alternative for $field: $alt");
                        break;
                    }
                }
            }
        }
    }

    // Фильтруем и обрабатываем данные
    $result = [];
    $count = 0;
    $processed = 0;
    $reservedCarsSkipped = 0; // ИСПРАВЛЕНИЕ: Счетчик исключенных забронированных автомобилей
    
    // Определяем индексы для важных полей
    $mileageIndex = array_search('mileage', $headers);
    $kmAgeIndex = array_search('km_age', $headers); // Альтернативное поле пробега
    $kmIndex = array_search('km', $headers); // Еще одно альтернативное поле пробега
    
    logDebug("Mileage field indexes: mileage={$mileageIndex}, km_age={$kmAgeIndex}, km={$kmIndex}");
    
    while (($data = fgetcsv($handle, 0, $delimiter)) !== FALSE) {
        // Пропускаем строки с неправильным количеством полей
        if (count($data) !== count($headers)) continue;
        
        $car = array_combine($headers, $data);

        // ОТЛАДКА: Проверяем подозрительные данные
        $carPrice = (int)($car['price'] ?? 0);
        $carId = $car['id'] ?? '';
        $carInnerId = $car['inner_id'] ?? '';

        if ($carPrice > 50000 || $carInnerId === '38789466') {
            logDebug("SUSPICIOUS CAR DATA: ID=$carId, inner_id=$carInnerId, price_field='{$car['price']}', price_parsed=$carPrice");
            logDebug("Full car data: " . json_encode($car, JSON_UNESCAPED_UNICODE));
        }

        // Применяем улучшенные фильтры поиска
        if (!empty($params['brand'])) {
            if (!improvedBrandSearch($car['mark'] ?? '', $params['brand'])) {
                continue;
            }
        }

        if (!empty($params['model'])) {
            if (!improvedModelSearch($car['model'] ?? '', $params['model'])) {
                continue;
            }
        }

        // ИСПРАВЛЕНИЕ: Фильтрация забронированных и лизинговых автомобилей
        $carPriceKRW = (int)($car['price'] ?? 0);
        if ($carPriceKRW === 999999 || $carPriceKRW === 333333) {
            $reservedCarsSkipped++;
            logDebug("Skipping reserved/leasing car: ID=" . ($car['id'] ?? 'unknown') . ", price=" . $carPriceKRW);
            continue;
        }

        // Фильтрация по году выпуска
        if (!empty($params['yearFrom']) && $params['yearFrom'] > 0) {
            $carYear = (int)($car['year'] ?? 0);
            if ($carYear < $params['yearFrom']) {
                continue;
            }
        }
        
        if (!empty($params['yearTo']) && $params['yearTo'] > 0) {
            $carYear = (int)($car['year'] ?? 0);
            if ($carYear > $params['yearTo']) {
                continue;
            }
        }
        
        // Фильтрация по цене (конвертируем из вон в доллары)
        if ((!empty($params['priceFrom']) && $params['priceFrom'] > 0) || (!empty($params['priceTo']) && $params['priceTo'] > 0)) {
            $carPriceKRW = (int)($car['price'] ?? 0);

            // Отладка: логируем проблемные цены
            if ($carPriceKRW > 100000) {
                logDebug("DEBUG: Large price detected - ID: " . ($car['id'] ?? 'unknown') . ", inner_id: " . ($car['inner_id'] ?? 'unknown') . ", price field: '" . ($car['price'] ?? 'empty') . "', parsed as: $carPriceKRW");
            }

            $carPriceUSD = convertKRWToUSD($carPriceKRW, $exchangeRates);

            // Сохраняем конвертированную цену для дальнейшего использования
            $car['price_usd'] = $carPriceUSD;

            if (!empty($params['priceFrom']) && $params['priceFrom'] > 0) {
                if ($carPriceUSD < $params['priceFrom']) {
                    continue;
                }
            }

            if (!empty($params['priceTo']) && $params['priceTo'] > 0) {
                if ($carPriceUSD > $params['priceTo']) {
                    continue;
                }
            }
        }
        
        // Фильтрация по типу трансмиссии
        if (!empty($params['transmission'])) {
            $carTransmission = translateKorean($car['transmission_type'] ?? '');
            if (stripos($carTransmission, $params['transmission']) === false) {
                continue;
            }
        }
        
        // Фильтрация по типу кузова
        if (!empty($params['bodyType'])) {
            $carBodyType = translateKorean($car['body_type'] ?? '');
            if (stripos($carBodyType, $params['bodyType']) === false) {
                continue;
            }
        }
        
        // Фильтрация по типу топлива/двигателя
        if (!empty($params['fuelType'])) {
            $carFuelType = translateKorean($car['fuel_type'] ?? '');
            if (stripos($carFuelType, $params['fuelType']) === false) {
                continue;
            }
        }
        
        // Фильтрация по пробегу
        // Сначала определяем пробег авто (может быть в разных полях)
        $carMileage = 0;
        if (!empty($car['mileage'])) {
            $carMileage = (int)preg_replace('/[^0-9]/', '', $car['mileage']);
        } elseif (!empty($car['km_age'])) {
            $carMileage = (int)preg_replace('/[^0-9]/', '', $car['km_age']);
        } elseif (!empty($car['km'])) {
            $carMileage = (int)preg_replace('/[^0-9]/', '', $car['km']);
        }
        
        if (!empty($params['mileageFrom']) && $params['mileageFrom'] > 0) {
            if ($carMileage < $params['mileageFrom']) {
                continue;
            }
        }
        
        if (!empty($params['mileageTo']) && $params['mileageTo'] > 0) {
            if ($carMileage > $params['mileageTo']) {
                continue;
            }
        }
        
        // Фильтрация по типу привода
        if (!empty($params['driveType'])) {
            $carDriveType = translateKorean($car['drive_type'] ?? '');
            if (stripos($carDriveType, $params['driveType']) === false) {
                continue;
            }
        }

        // Фильтрация по категории (business, sport, suv)
        if (!empty($params['category'])) {
            $carCategory = determineCarCategory($car['mark'] ?? '', $car['model'] ?? '');
            if ($carCategory !== $params['category']) {
                continue;
            }
        }
        
        // Пропускаем до offset
        if ($processed < $params['offset']) {
            $processed++;
            continue;
        }
        
        // Проверяем лимит - раннее прерывание для производительности
        if ($count >= $params['limit']) break;
        
        // Проверяем наличие поля пробега в разных вариантах и обеспечиваем его наличие
        if (!isset($car['mileage']) || empty($car['mileage'])) {
            // Проверяем альтернативные поля
            if (isset($car['km_age']) && !empty($car['km_age'])) {
                $car['mileage'] = $car['km_age'];
            } elseif (isset($car['km']) && !empty($car['km'])) {
                $car['mileage'] = $car['km'];
            }
        }
        
        // Добавляем данные для отображения на сайте
        // Используем конвертированную цену если она есть, иначе конвертируем сейчас
        if (!isset($car['price_usd'])) {
            $carPriceKRW = (int)($car['price'] ?? 0);

            // Отладка: логируем проблемные цены
            if ($carPriceKRW > 100000) {
                logDebug("DEBUG: Large price detected in display - ID: " . ($car['id'] ?? 'unknown') . ", inner_id: " . ($car['inner_id'] ?? 'unknown') . ", price field: '" . ($car['price'] ?? 'empty') . "', parsed as: $carPriceKRW");
            }

            $car['price_usd'] = convertKRWToUSD($carPriceKRW, $exchangeRates);
        }
        $car['price_formatted'] = formatPrice($car['price_usd']);
        $car['mileage_formatted'] = formatMileage($car['mileage'] ?? 0);
        $car['year'] = $car['year'] ?? '';
        $car['engine'] = formatEngine($car);
        
        // Переводим марку и модель
        if (!empty($car['mark'])) {
            $car['translated_mark'] = translateBrand($car['mark']);
            $car['brand'] = $car['translated_mark']; // Устанавливаем brand как переведенную марку
            $car['mark'] = $car['translated_mark'];
        }
        
        if (!empty($car['model'])) {
            // Используем переведенную марку для правильного контекста при переводе модели
            $car['translated_model'] = translateModel($car['brand'] ?? $car['mark'], $car['model']);
            $car['model'] = $car['translated_model'];
        }
        
        // Переводим значения с корейского
        if (!empty($car['transmission_type'])) {
            $car['transmission_type'] = translateKorean($car['transmission_type']);
        }
        if (!empty($car['body_type'])) {
            $car['body_type'] = translateKorean($car['body_type']);
        }
        if (!empty($car['fuel_type'])) {
            $car['fuel_type'] = translateKorean($car['fuel_type']);
        }
        if (!empty($car['drive_type'])) {
            $car['drive_type'] = translateKorean($car['drive_type']);
        }
        if (!empty($car['options'])) {
            $car['options'] = translateKorean($car['options']);
        }
        
        $car['transmission'] = $car['transmission_type'] ?? '';
        $car['is_active'] = basename($file) === 'encar_active_' . $params['date'] . '.csv';
        
        // Добавляем локальный путь к изображению авто
        $car['local_image'] = getCarImageUrl($car);
        
        // Добавляем в результат
        $result[] = $car;
        $count++;
        $processed++;

        // Логирование производительности каждые 100 записей
        if ($processed % 100 == 0) {
            $elapsed = microtime(true) - $startTime;
            logDebug("Processed $processed records in " . round($elapsed, 2) . " seconds, found $count matching cars");
        }
    }

    fclose($handle);

    // НОВАЯ ФУНКЦИЯ: Приоритетная сортировка для режима "Все автомобили"
    if ($prioritySort) {
        logDebug("Applying priority sorting for 'All cars' mode");

        // Добавляем приоритеты к каждому автомобилю
        foreach ($result as &$car) {
            $brand = $car['brand'] ?? $car['mark'] ?? '';
            $brandPriority = getBrandPriority($brand);
            $modelPriority = getModelPriority($brand, $car['model'] ?? '');

            $car['brand_priority'] = $brandPriority;
            $car['model_priority'] = $modelPriority;

            // Отладка: логируем первые 5 автомобилей с их приоритетами
            static $debugCount = 0;
            if ($debugCount < 5) {
                error_log("DEBUG PRIORITY: Brand='$brand', Priority=$brandPriority, Model='{$car['model']}', Year={$car['year']}");
                $debugCount++;
            }
        }

        // Сортируем по приоритету: бренд → модель → год (убывание) → цена (возрастание)
        usort($result, function($a, $b) {
            // 1. Приоритет бренда (1 = высший приоритет, должен быть первым)
            if ($a['brand_priority'] !== $b['brand_priority']) {
                return $a['brand_priority'] - $b['brand_priority']; // 1 - 4 = -3 (a идет первым)
            }

            // 2. Приоритет модели (1 = высший приоритет)
            if ($a['model_priority'] !== $b['model_priority']) {
                return $a['model_priority'] - $b['model_priority'];
            }

            // 3. Год выпуска (новые сначала)
            $yearA = (int)($a['year'] ?? 0);
            $yearB = (int)($b['year'] ?? 0);
            if ($yearA !== $yearB) {
                return $yearB - $yearA; // Убывание (новые сначала)
            }

            // 4. Цена (дешевые сначала)
            $priceA = (int)($a['price'] ?? 0);
            $priceB = (int)($b['price'] ?? 0);
            return $priceA - $priceB; // Возрастание (дешевые сначала)
        });

        // Удаляем временные поля приоритетов
        foreach ($result as &$car) {
            unset($car['brand_priority'], $car['model_priority']);
        }

        logDebug("Priority sorting completed. First 3 cars: " .
            json_encode(array_slice(array_map(function($car) {
                return ($car['brand'] ?? $car['mark'] ?? '') . ' ' . ($car['model'] ?? '') . ' (' . ($car['year'] ?? '') . ')';
            }, $result), 0, 3), JSON_UNESCAPED_UNICODE));
    }

    logDebug("Processed $processed rows, returned $count cars, skipped $reservedCarsSkipped reserved cars");
    logDebug("FILTERING STATISTICS: Total processed: $processed, Valid cars: $count, Reserved cars excluded: $reservedCarsSkipped");
    return $result;
}

// Форматирование цены для отображения
function formatPrice($price) {
    if (empty($price)) return "Цена не указана";
    
    $price = (int)$price;
    if ($price < 1000) return "$" . $price;
    
    return "$" . number_format($price, 0, '.', ' ');
}

// Форматирование пробега
function formatMileage($mileage) {
    // Проверяем нулевое или пустое значение
    if (empty($mileage) || $mileage === '0' || $mileage === 0) {
        return "Без пробега";
    }
    
    // Сохраняем исходное значение
    $originalValue = $mileage;
    
    // Предобработка строковых значений
    if (is_string($mileage)) {
        // Если строка уже содержит "км", это уже отформатированное значение
        if (strpos($mileage, 'км') !== false) {
            return $mileage;
        }
        
        // Удаляем нечисловые символы
        $mileage = preg_replace('/[^0-9]/', '', $mileage);
    }
    
    // После очистки проверяем пустое значение
    if (empty($mileage) || intval($mileage) === 0) {
        // Если исходное значение не пустое, возвращаем его
        if (!empty($originalValue) && $originalValue !== '0') {
            // При необходимости добавляем "км"
            if (strpos($originalValue, 'км') === false) {
                return $originalValue . ' км';
            }
            return $originalValue;
        }
        return "Без пробега";
    }
    
    // Преобразуем в число
    $mileage = intval($mileage);
    
    // Если значение слишком большое (>1 млн), возможно это неправильные данные
    // Преобразуем в более реалистичное значение
    if ($mileage > 1000000) {
        $mileage = round($mileage / 1000);
    }
    
    // Форматируем с разделителями разрядов
    return number_format($mileage, 0, '.', ' ') . " км";
}

// Форматирование информации о двигателе
function formatEngine($car) {
    $engine = [];
    
    // Объем двигателя
    if (!empty($car['displacement'])) {
        $displacement = (float)$car['displacement'];
        if ($displacement > 0) {
            // Деление на 1000, если значение больше 100 (некоторые данные приходят в кубических сантиметрах)
            if ($displacement > 100) {
                $displacement = $displacement / 1000;
            }
            $engine[] = number_format($displacement, 1, '.', '') . " л";
        }
    }
    
    // Мощность двигателя
    if (!empty($car['horsepower'])) {
        $horsepower = (int)$car['horsepower'];
        if ($horsepower > 0) {
            $engine[] = $horsepower . " л.с.";
        }
    }
    
    // Тип топлива
    if (!empty($car['fuel_type'])) {
        $fuelType = translateKorean($car['fuel_type']);
        $engine[] = $fuelType;
    }
    
    // Если нет данных о двигателе, вернем базовую информацию
    if (empty($engine)) {
        if (!empty($car['engine_type'])) {
            return translateKorean($car['engine_type']);
        }
        return "Нет данных";
    }
    
    return implode(' • ', $engine);
}

// Проверка наличия локальных изображений автомобиля
function getCarImageUrl($car) {
    if (empty($car['id'])) {
        return '';
    }
    
    $imagesDir = '/Img/cars/'; // Путь относительно корня сайта
    $imageFile = 'car_' . $car['id'] . '.jpg';
    $thumbFile = 'car_' . $car['id'] . '_thumb.jpg';
    
    // Проверяем наличие миниатюры
    $thumbPath = __DIR__ . '/..' . $imagesDir . $thumbFile;
    
    if (file_exists($thumbPath)) {
        return $imagesDir . $thumbFile;
    }
    
    // Проверяем наличие оригинала
    $imagePath = __DIR__ . '/..' . $imagesDir . $imageFile;
    
    if (file_exists($imagePath)) {
        return $imagesDir . $imageFile;
    }
    
    // Возвращаем исходный URL, если нет локальной копии
    return $car['image_url'] ?? '';
}

// Перевод значений с корейского на русский
function translateKorean($text) {
    if (empty($text)) return "";
    
    $translations = [
        // Типы коробок передач
        '자동' => 'Автомат',
        '수동' => 'Механика',
        '더블클러치' => 'Робот (DCT)',
        '무단변속기' => 'Вариатор (CVT)',
        '수동변속기' => 'Механика',
        '자동변속기' => 'Автомат',
        '세미오토' => 'Полуавтомат',
        '오토' => 'Автомат',
        '오토매틱' => 'Автомат',
        'AT' => 'Автомат',
        'MT' => 'Механика',
        'DCT' => 'Робот (DCT)',
        'CVT' => 'Вариатор (CVT)',
        
        // Типы кузова
        '세단' => 'Седан',
        '해치백' => 'Хэтчбек',
        '왜건' => 'Универсал',
        '쿠페' => 'Купе',
        'SUV' => 'Внедорожник',
        '컨버터블' => 'Кабриолет',
        '픽업트럭' => 'Пикап',
        '밴' => 'Фургон',
        
        // Типы топлива
        '가솔린' => 'Бензин',
        '디젤' => 'Дизель',
        '하이브리드' => 'Гибрид',
        '전기' => 'Электро',
        '가스' => 'Газ',
        'LPG' => 'Газ (LPG)',
        
        // Привод
        '전륜구동' => 'Передний',
        '후륜구동' => 'Задний',
        '사륜구동' => 'Полный',
        
        // Прочее
        '신차급' => 'Как новый',
        '썬루프' => 'Люк',
        '네비게이션' => 'Навигация',
        '가죽시트' => 'Кожаный салон',
        '스마트키' => 'Smart-ключ'
    ];
    
    // Если есть прямой перевод
    if (isset($translations[$text])) {
        return $translations[$text];
    }
    
    // Если нет прямого перевода, ищем вхождения корейских слов в строке
    foreach ($translations as $korean => $russian) {
        if (strpos($text, $korean) !== false) {
            $text = str_replace($korean, $russian, $text);
        }
    }
    
    return $text;
}

// Перевод марок автомобилей
function translateBrand($brand) {
    if (empty($brand)) return '';
    
    $brandTranslations = [
        '현대' => 'Hyundai',
        '기아' => 'Kia',
        '쌍용' => 'SsangYong',
        '르노삼성' => 'Renault Samsung',
        '제네시스' => 'Genesis',
        '쉐보레' => 'Chevrolet',
        '대우' => 'Daewoo',
        '쉐보레대우' => 'Chevrolet',
        '토요타' => 'Toyota',
        '닛산' => 'Nissan',
        '혼다' => 'Honda',
        'BMW' => 'BMW',
        '벤츠' => 'Mercedes-Benz',
        '아우디' => 'Audi',
        '폭스바겐' => 'Volkswagen',
        '볼보' => 'Volvo',
        '포드' => 'Ford',
        '렉서스' => 'Lexus',
        '미니' => 'MINI',
        '포르쉐' => 'Porsche',
        '랜드로버' => 'Land Rover',
        '재규어' => 'Jaguar',
        '크라이슬러' => 'Chrysler',
        '지프' => 'Jeep',
        '마세라티' => 'Maserati',
        '벤틀리' => 'Bentley',
        '페라리' => 'Ferrari',
        '람보르기니' => 'Lamborghini'
    ];
    
    // Проверка на известные модели, требующие особой маркировки
    if (strpos(strtolower($brand), 'matiz') !== false) {
        return 'Chevrolet';
    }
    
    return $brandTranslations[$brand] ?? $brand;
}

// Перевод моделей автомобилей
function translateModel($brand, $model) {
    if (empty($model)) return '';
    
    // Словари моделей по брендам
    $modelTranslations = [
        'Hyundai' => [
            '쏘나타' => 'Sonata',
            '아반떼' => 'Avante/Elantra',
            '그랜저' => 'Grandeur/Azera',
            '싼타페' => 'Santa Fe',
            '투싼' => 'Tucson',
            '팰리세이드' => 'Palisade',
            '아이오닉' => 'Ioniq'
        ],
        'Kia' => [
            'K5' => 'K5/Optima',
            'K7' => 'K7/Cadenza',
            'K9' => 'K9/Quoris',
            '스포티지' => 'Sportage',
            '쏘렌토' => 'Sorento',
            '카니발' => 'Carnival/Sedona',
            '모닝' => 'Morning/Picanto'
        ],
        'Genesis' => [
            'G70' => 'G70',
            'G80' => 'G80',
            'G90' => 'G90',
            'GV70' => 'GV70',
            'GV80' => 'GV80'
        ]
    ];
    
    $translatedBrand = translateBrand($brand);
    
    // Если есть словарь моделей для этой марки
    if (isset($modelTranslations[$translatedBrand])) {
        return $modelTranslations[$translatedBrand][$model] ?? $model;
    }
    
    // Для непереведенных моделей возвращаем оригинал
    return $model;
}

// Функция для определения приоритета бренда (1-4, где 1 - высший приоритет)
function getBrandPriority($brand) {
    // Переводим марку для правильного сравнения
    $translatedBrand = translateBrand($brand);

    // 4-уровневая система приоритетов брендов
    $brandPriorities = [
        // 1 категория - Супер премиум люкс
        1 => [
            'Rolls-Royce', 'Bentley', 'Ferrari', 'Lamborghini', 'McLaren',
            'Bugatti', 'Koenigsegg', 'Pagani', 'Aston Martin', 'Maybach',
            'Mercedes-Maybach', 'Brabus', 'Alpina', 'Lucid Motors', 'Rimac',
            'Pininfarina', 'Spyker'
        ],
        // 2 категория - Премиум
        2 => [
            'Mercedes-Benz', 'BMW', 'Audi', 'Porsche', 'Lexus', 'Cadillac',
            'Maserati', 'Jaguar', 'Land Rover', 'Range Rover', 'Genesis',
            'Infiniti', 'Acura', 'Lincoln'
        ],
        // 3 категория - Средние
        3 => [
            'Toyota', 'Honda', 'Nissan', 'Mazda', 'Subaru', 'Mitsubishi',
            'Volvo', 'Jeep', 'Chrysler', 'Dodge', 'Buick', 'GMC'
        ],
        // 4 категория - Бюджетные
        4 => [
            'Kia', 'Hyundai', 'Chevrolet', 'ChevroletGMDaewoo', 'Daewoo',
            'Lada', 'Dacia', 'Skoda', 'SEAT', 'Ford', 'Renault', 'Peugeot',
            'Citroen', 'Fiat', 'Opel', 'Suzuki', 'Isuzu'
        ]
    ];

    // Ищем бренд в категориях приоритета
    foreach ($brandPriorities as $priority => $brands) {
        foreach ($brands as $priorityBrand) {
            if (strcasecmp($translatedBrand, $priorityBrand) === 0) {
                return $priority;
            }
        }
    }

    // Если бренд не найден, возвращаем самый низкий приоритет
    return 4;
}

// Функция для определения приоритета модели внутри бренда
function getModelPriority($brand, $model) {
    $translatedBrand = translateBrand($brand);

    // Приоритеты моделей для премиум брендов (чем меньше число, тем выше приоритет)
    $modelPriorities = [
        'Ferrari' => [
            'LaFerrari' => 1, 'SF90' => 2, '812' => 3, 'F8' => 4, 'Roma' => 5,
            'Portofino' => 6, '488' => 7, '458' => 8, 'California' => 9, 'F12' => 10
        ],
        'Lamborghini' => [
            'Revuelto' => 1, 'Aventador' => 2, 'Huracan' => 3, 'Urus' => 4,
            'Gallardo' => 5, 'Murcielago' => 6
        ],
        'Rolls-Royce' => [
            'Phantom' => 1, 'Cullinan' => 2, 'Ghost' => 3, 'Wraith' => 4,
            'Dawn' => 5, 'Spectre' => 6
        ],
        'Bentley' => [
            'Mulsanne' => 1, 'Flying Spur' => 2, 'Continental GT' => 3,
            'Bentayga' => 4, 'Continental' => 5
        ],
        'Mercedes-Benz' => [
            'AMG GT' => 1, 'S-Class' => 2, 'G-Class' => 3, 'E-Class' => 4,
            'C-Class' => 5, 'GLS' => 6, 'GLE' => 7, 'GLC' => 8
        ],
        'BMW' => [
            'M8' => 1, 'M5' => 2, 'M4' => 3, 'M3' => 4, 'M2' => 5,
            'X7' => 6, 'X6' => 7, 'X5' => 8, '7 Series' => 9, '5 Series' => 10
        ],
        'Audi' => [
            'R8' => 1, 'RS7' => 2, 'RS6' => 3, 'RS5' => 4, 'RS3' => 5,
            'Q8' => 6, 'Q7' => 7, 'A8' => 8, 'A7' => 9, 'A6' => 10
        ],
        'Porsche' => [
            '911' => 1, 'Taycan' => 2, 'Panamera' => 3, 'Cayenne' => 4,
            'Macan' => 5, '718' => 6, 'Boxster' => 7, 'Cayman' => 8
        ]
    ];

    if (isset($modelPriorities[$translatedBrand])) {
        foreach ($modelPriorities[$translatedBrand] as $priorityModel => $priority) {
            if (stripos($model, $priorityModel) !== false) {
                return $priority;
            }
        }
    }

    // Если модель не найдена в приоритетах, возвращаем средний приоритет
    return 50;
}

// Функция для определения категории автомобиля по марке и модели
function determineCarCategory($brand, $model) {
    // Переводим марку для правильного сравнения
    $translatedBrand = translateBrand($brand);

    // Определения категорий автомобилей
    $categories = [
        'business' => [
            // Ультра-премиум бренды - только топовые модели
            'Rolls-Royce' => ['Phantom', 'Ghost', 'Wraith', 'Dawn', 'Spectre', 'Cullinan', 'Silver Seraph', 'Silver Shadow', 'Corniche'],
            'Bentley' => ['Flying Spur', 'Continental GT', 'Continental', 'Flying', 'Mulsanne', 'Arnage', 'Azure', 'Brooklands', 'Bentayga'],
            'Mercedes-Maybach' => ['S-Class', 'Maybach S-Class', 'Maybach', 'GLS', 'S 680', 'S 580', 'GLS 600', 'EQS SUV'],
            'Maybach' => ['S-Class', 'S 680', 'S 580', 'GLS', 'GLS 600', 'EQS SUV'],
            'Maserati' => ['Quattroporte', 'Ghibli', 'Levante', 'MC12', 'GranTurismo'],
            'Cadillac' => ['CT6', 'XTS', 'Escalade', 'Escalade ESV', 'Celestiq', 'CT5-V', 'CTS-V', 'ELR'],
            'Bugatti' => ['Chiron', 'Veyron', 'Divo', 'Centodieci', 'La Voiture Noire'],
            'Koenigsegg' => ['Regera', 'Jesko', 'CC850', 'Gemera', 'Agera'],
            'Pagani' => ['Huayra', 'Zonda', 'Utopia'],
            'Alpina' => ['B7', 'B8', 'XB7', 'B5', 'B6'],
            'Brabus' => ['S-Class', 'Maybach', 'G-Class', '800', '900'],
            'Lucid Motors' => ['Air', 'Air Dream', 'Air Touring', 'Air Pure'],
            'Rimac' => ['Nevera', 'Concept One'],
            'Pininfarina' => ['Battista', 'PF0'],
            'Spyker' => ['C8', 'B6', 'C12'],
            'Aston Martin' => ['Rapide', 'Lagonda', 'DB12'],
            'McLaren' => ['GT', '720S Spider']
        ],
        'sport' => [
            'Ferrari' => ['458', '488', '812', 'F8', 'Roma', 'SF90', 'Portofino', 'LaFerrari', 'F12', 'California'],
            'Lamborghini' => ['Aventador', 'Huracan', 'Gallardo', 'Murcielago'],
            'Porsche' => ['911', '718 Boxster', '718 Cayman', 'Boxster', 'Cayman', 'GT2', 'GT3'],
            'McLaren' => ['570S', '720S', 'Artura', 'GT', 'Elva', 'Senna', 'Speedtail'],
            'Aston Martin' => ['DB11', 'DB12', 'Vantage', 'DBS', 'Valkyrie', 'Valhalla'],
            'BMW' => ['M2', 'M3', 'M4', 'M5', 'M8', 'i8', 'Z4'],
            'Audi' => ['R8', 'RS7', 'RS6', 'RS5', 'RS3', 'TT RS'],
            'Mercedes-Benz' => ['AMG GT', 'C63 AMG', 'E63 AMG', 'SL', 'SLK', 'AMG'],
            // ИСПРАВЛЕНИЕ: Исключен бюджетный бренд Chevrolet из премиум спортивной категории
            // 'Chevrolet' => ['Corvette', 'Camaro'], // Перенесено в бюджетную категорию
            'Ford' => ['Mustang', 'GT'],
            'Nissan' => ['GT-R', '370Z', 'GTR'],
            'Toyota' => ['GR Supra', 'GR86', 'Supra'],
            'Jaguar' => ['F-Type'],
            'Maserati' => ['MC20'],
            'Lotus' => ['Elise', 'Emira', 'Evija', 'Evora', 'Exige']
        ],
        'suv' => [
            'BMW' => ['X5', 'X6', 'X7', 'X3', 'X4', 'X1', 'X2', 'X5 M', 'X6 M', 'iX'],
            'Mercedes-Benz' => ['GLS', 'GLE', 'G-Class', 'GLC', 'GLA', 'GLB', 'G'],
            'Audi' => ['Q7', 'Q8', 'Q5', 'Q3', 'RS Q8', 'SQ7', 'SQ5'],
            'Range Rover' => ['Range Rover', 'Range Rover Sport', 'Range Rover Velar', 'Range Rover Evoque'],
            'Land Rover' => ['Defender', 'Discovery', 'Range Rover', 'Range Rover Sport', 'Range Rover Velar', 'Range Rover Evoque'],
            'Porsche' => ['Cayenne', 'Macan'],
            'Lamborghini' => ['Urus'],
            'Aston Martin' => ['DBX'],
            'Maserati' => ['Grecale'],
            'Ferrari' => ['Purosangue'],
            'Cadillac' => ['XT5', 'XT6', 'XT4'],
            'Lexus' => ['LX', 'RX', 'GX', 'NX', 'UX'],
            'Toyota' => ['Land Cruiser', 'Highlander', 'RAV4', '4Runner'],
            // ИСПРАВЛЕНИЕ: Исключены бюджетные бренды Hyundai и Kia из премиум категории SUV
            // 'Hyundai' => ['Palisade', 'Santa Fe', 'Tucson'], // Перенесено в бюджетную категорию
            // 'Kia' => ['Telluride', 'Sorento', 'Sportage'], // Перенесено в бюджетную категорию
            'Volvo' => ['XC90', 'XC60', 'XC40'],
            'Jeep' => ['Grand Cherokee', 'Cherokee', 'Wrangler', 'Compass']
        ]
    ];

    // Проверяем каждую категорию
    foreach ($categories as $categoryName => $brands) {
        if (isset($brands[$translatedBrand])) {
            $models = $brands[$translatedBrand];
            foreach ($models as $categoryModel) {
                // Проверяем точное совпадение (без учета регистра)
                if (strcasecmp($model, $categoryModel) === 0) {
                    return $categoryName;
                }
                // Проверяем, начинается ли модель с категории и следующий символ - пробел или конец строки
                // Например, "A6 2.0 TFSI" начинается с "A6", но "CTS" НЕ должен совпадать с "CTS-V"
                if (stripos($model, $categoryModel) === 0) {
                    $nextCharPos = strlen($categoryModel);
                    if ($nextCharPos >= strlen($model) || $model[$nextCharPos] === ' ') {
                        return $categoryName;
                    }
                }
            }
        }
    }

    // Если категория не определена, возвращаем пустую строку
    return '';
}

try {
    // КРИТИЧНО: Заголовки уже установлены в начале файла, не дублируем их
    // Убираем дублирование заголовков которые уже установлены

    // Формируем параметры для фильтрации
    $params = [
        'date' => $date,
        'limit' => $limit,
        'offset' => $offset,
        'brand' => $brand,
        'model' => $model,
        'featuredOnly' => $featuredOnly,
        'showActive' => $showActive,
        'showRemoved' => $showRemoved,
        'yearFrom' => $yearFrom,
        'yearTo' => $yearTo,
        'priceFrom' => $priceFrom,
        'priceTo' => $priceTo,
        'transmission' => $transmission,
        'bodyType' => $bodyType,
        'fuelType' => $fuelType,
        'mileageFrom' => $mileageFrom,
        'mileageTo' => $mileageTo,
        'driveType' => $driveType,
        'category' => $category
    ];

    logDebug("=== ENCAR PROXY START ===");
    logDebug("Request parameters: " . json_encode($params, JSON_UNESCAPED_UNICODE));
    logDebug("Memory usage: " . memory_get_usage(true) . " bytes");
    
    // Получаем данные из локальных CSV файлов
    logDebug("Calling fetchDataFromLocalCSV with date: $date");
    $data = fetchDataFromLocalCSV($date, $params);
    logDebug("fetchDataFromLocalCSV returned " . count($data) . " records");
    
    // Сохраняем в кэш
    if ($useCache && !empty($data)) {
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0777, true);
        }
        file_put_contents($cacheFile, json_encode($data, JSON_UNESCAPED_UNICODE));
    }
    
    // Отправляем ответ
    sendJsonResponse($data);
    
} catch (Exception $e) {
    handleError($e->getMessage(), 500, $debug);
}

// Функция для получения курсов валют
function getExchangeRates() {
    static $cachedRates = null;
    static $cacheTime = 0;

    // Кэшируем курсы на 10 минут для производительности
    if ($cachedRates !== null && (time() - $cacheTime) < 600) {
        logDebug("Using cached exchange rates");
        return $cachedRates;
    }

    try {
        // Получаем курсы из нашего API
        $url = 'http://localhost:8000/api/exchange-rates.php';
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 3);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200 && $response) {
            $data = json_decode($response, true);
            if ($data && $data['success']) {
                $krwToRub = $data['KRW_to_RUB'];
                $usdRate = $data['USD_rates']['usdtrub']['sell'] ?? 95; // Используем курс продажи

                $cachedRates = [
                    'KRW_to_RUB' => $krwToRub,
                    'USD_rate' => $usdRate
                ];
                $cacheTime = time();
                return $cachedRates;
            }
        }
    } catch (Exception $e) {
        logDebug("Error getting exchange rates: " . $e->getMessage());
    }

    // Fallback курсы если API недоступен
    $cachedRates = [
        'KRW_to_RUB' => 0.075, // Примерный курс воны к рублю
        'USD_rate' => 95       // Примерный курс доллара
    ];
    $cacheTime = time();
    return $cachedRates;
}

// Функция для конвертации цены из корейских вон в доллары
function convertKRWToUSD($priceInKRW, $rates) {
    if (empty($priceInKRW) || $priceInKRW <= 0) {
        return 0;
    }

    // ИСПРАВЛЕНИЕ: Исключаем забронированные и лизинговые автомобили
    if ($priceInKRW === 999999 || $priceInKRW === 333333) {
        logDebug("RESERVED CAR PRICE DETECTED: {$priceInKRW} - исключаем из конвертации");
        return 0;
    }

    // ВАЛИДАЦИЯ: Проверяем, не является ли цена ID автомобиля
    if ($priceInKRW > 50000) {
        logDebug("SUSPICIOUS PRICE DETECTED: {$priceInKRW} - это может быть ID автомобиля, а не цена!");
        // Возвращаем 0 для подозрительно высоких цен
        return 0;
    }

    // Формула: цена_в_сокращенном_виде × 10,000 → воны → рубли (через ЦБ) → доллары (через обменник)
    // 1. Умножаем на 10,000 (цены в CSV в сокращенном виде)
    $realPriceInKRW = $priceInKRW * 10000;

    // 2. Конвертируем воны в рубли
    $priceInRUB = $realPriceInKRW * $rates['KRW_to_RUB'];

    // 3. Конвертируем рубли в доллары
    $priceInUSD = $priceInRUB / $rates['USD_rate'];

    $result = round($priceInUSD, 0); // Округляем до целых долларов

    // ВАЛИДАЦИЯ: Проверяем результат конвертации
    if ($result > 500000) {
        logDebug("SUSPICIOUS CONVERTED PRICE: {$priceInKRW} → {$result} USD - слишком высокая цена!");
        return 0;
    }

    // Логируем только первые несколько конвертаций для отладки
    static $logCount = 0;
    if ($logCount < 3 || $priceInKRW > 10000) {
        logDebug("Price conversion: {$priceInKRW} → {$realPriceInKRW} KRW → {$priceInRUB} RUB → {$result} USD (rates: KRW={$rates['KRW_to_RUB']}, USD={$rates['USD_rate']})");
        $logCount++;
    }

    return $result;
}