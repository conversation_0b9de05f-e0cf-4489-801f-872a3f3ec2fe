# 🚀 Полное руководство по развертыванию SHMS Auto на Рег.ру

## 📋 Предварительные требования

### Что вам понадобится:
- Аккаунт в Рег.ру
- Домен (например: shms-auto.ru)
- VPS сервер на Рег.ру
- SSH клиент (PuTTY для Windows или терминал для Mac/Linux)
- SFTP клиент (FileZilla, WinSCP)

### Рекомендуемые характеристики VPS:
- **ОС**: Ubuntu 20.04 LTS или Ubuntu 22.04 LTS
- **RAM**: минимум 2GB (рекомендуется 4GB)
- **Диск**: мини<PERSON>ум 20GB SSD
- **CPU**: 1-2 ядра

## 🎯 Этап 1: Заказ и настройка VPS на Рег.ру

### 1.1 Заказ VPS
1. Войдите в личный кабинет Рег.ру
2. Перейдите в раздел "Серверы" → "VPS"
3. Выберите конфигурацию:
   - Ubuntu 20.04 LTS
   - 2GB RAM, 20GB SSD, 1 CPU (минимум)
4. Оформите заказ

### 1.2 Получение данных доступа
После создания VPS вы получите:
- IP адрес сервера
- Логин: root
- Пароль (на email)

### 1.3 Первое подключение к серверу
```bash
ssh root@ваш-ip-адрес
```

## 🔧 Этап 2: Подготовка сервера

### 2.1 Обновление системы
```bash
# Обновление пакетов
apt update && apt upgrade -y

# Установка базовых пакетов
apt install -y curl wget git nano htop unzip nginx certbot python3-certbot-nginx
```

### 2.2 Установка Node.js
```bash
# Установка Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs

# Проверка установки
node --version
npm --version
```

### 2.3 Установка PM2
```bash
# Глобальная установка PM2
npm install -g pm2

# Проверка установки
pm2 --version
```

### 2.4 Создание пользователя для приложения
```bash
# Создание пользователя shmsauto
adduser shmsauto --disabled-password --gecos ""

# Добавление в группу sudo
usermod -aG sudo shmsauto

# Переключение на пользователя
su - shmsauto
```

## 📁 Этап 3: Загрузка проекта на сервер

### 3.1 Создание директории проекта
```bash
# Находясь под пользователем shmsauto
cd /home/<USER>
mkdir shms-auto
cd shms-auto
```

### 3.2 Загрузка файлов проекта

#### Вариант A: Через Git (если есть репозиторий)
```bash
git clone https://github.com/ваш-username/shms-auto.git .
```

#### Вариант B: Через SFTP
1. Используйте FileZilla или WinSCP
2. Подключитесь к серверу:
   - Хост: ваш-ip-адрес
   - Пользователь: shmsauto
   - Пароль: пароль пользователя shmsauto
   - Порт: 22
3. Загрузите все файлы проекта в `/home/<USER>/shms-auto/`

#### Вариант C: Через архив
```bash
# Если у вас есть архив проекта
wget https://ваш-сайт.com/shms-auto.zip
unzip shms-auto.zip
```

## ⚙️ Этап 4: Настройка проекта

### 4.1 Установка зависимостей
```bash
cd /home/<USER>/shms-auto
npm install --production
```

### 4.2 Создание .env файла для продакшена
```bash
cp .env.example .env
nano .env
```

**Содержимое .env файла:**
```env
# Настройки сервера
NODE_ENV=production
PORT=3001
HOST=0.0.0.0

# Настройки домена (замените на ваш домен)
DOMAIN=ваш-домен.ru
SITE_URL=https://ваш-домен.ru

# Настройки почты Yandex (уже настроены)
MAIL_HOST=smtp.yandex.ru
MAIL_PORT=465
MAIL_SECURE=true
MAIL_USER=<EMAIL>
MAIL_PASS=SHMSAutogroup777!

# Куда отправлять заявки (замените на вашу почту)
CONTACT_EMAIL=ваша-почта@gmail.com
MANAGER_EMAIL=ваша-почта@gmail.com

# Сгенерируйте уникальный секретный ключ
SESSION_SECRET=shms-auto-2024-super-secret-key-$(openssl rand -hex 16)

# Настройки базы данных
DB_PATH=./data/stock.db

# Настройки загрузки файлов
UPLOAD_DIR=./data/uploads
MAX_FILE_SIZE=50mb

# Настройки безопасности для продакшена
HTTPS_REDIRECT=true
SECURE_COOKIES=true

# Настройки логирования
LOG_LEVEL=info
LOG_FILE=./logs/app.log
```

### 4.3 Создание необходимых директорий
```bash
mkdir -p logs data/uploads data/cache
chmod 755 data/uploads logs
```

### 4.4 Тестирование приложения
```bash
# Тест запуска
node server.js
# Если все работает, остановите Ctrl+C
```

## 🚀 Этап 5: Запуск через PM2

### 5.1 Запуск приложения
```bash
# Запуск в продакшн режиме
pm2 start ecosystem.config.js --env production

# Проверка статуса
pm2 list

# Просмотр логов
pm2 logs shms-auto
```

### 5.2 Настройка автозапуска
```bash
# Генерация скрипта автозапуска
pm2 startup

# Выполните команду, которую выдаст PM2 (примерно такую):
# sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u shmsauto --hp /home/<USER>

# Сохранение текущей конфигурации
pm2 save
```

## 🌐 Этап 6: Настройка Nginx

### 6.1 Создание конфигурации Nginx
```bash
sudo nano /etc/nginx/sites-available/shms-auto
```

**Содержимое файла (замените ваш-домен.ru на ваш домен):**
```nginx
server {
    listen 80;
    server_name ваш-домен.ru www.ваш-домен.ru;

    # Логи
    access_log /var/log/nginx/shms-auto.access.log;
    error_log /var/log/nginx/shms-auto.error.log;

    # Основное проксирование на Node.js
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300;
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
    }

    # Статические файлы загрузок
    location /uploads/ {
        alias /home/<USER>/shms-auto/data/uploads/;
        expires 30d;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Статические файлы сайта
    location /css/ {
        alias /home/<USER>/shms-auto/public/css/;
        expires 7d;
        add_header Cache-Control "public";
    }

    location /js/ {
        alias /home/<USER>/shms-auto/public/js/;
        expires 7d;
        add_header Cache-Control "public";
    }

    location /assets/ {
        alias /home/<USER>/shms-auto/public/assets/;
        expires 30d;
        add_header Cache-Control "public";
    }

    # Специальные файлы
    location = /favicon.ico {
        alias /home/<USER>/shms-auto/public/favicon.ico;
        expires 30d;
        access_log off;
    }

    location = /robots.txt {
        alias /home/<USER>/shms-auto/public/robots.txt;
        expires 7d;
        access_log off;
    }

    location = /sitemap.xml {
        alias /home/<USER>/shms-auto/public/sitemap.xml;
        expires 1d;
        access_log off;
    }

    # Безопасность
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Ограничение размера загружаемых файлов
    client_max_body_size 50M;
}
```

### 6.2 Активация конфигурации
```bash
# Создание символической ссылки
sudo ln -s /etc/nginx/sites-available/shms-auto /etc/nginx/sites-enabled/

# Удаление дефолтной конфигурации
sudo rm -f /etc/nginx/sites-enabled/default

# Проверка конфигурации
sudo nginx -t

# Перезапуск Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## 🔒 Этап 7: Настройка SSL сертификата

### 7.1 Установка SSL через Let's Encrypt
```bash
# Получение SSL сертификата (замените домен на ваш)
sudo certbot --nginx -d ваш-домен.ru -d www.ваш-домен.ru

# Следуйте инструкциям certbot:
# 1. Введите email для уведомлений
# 2. Согласитесь с условиями (Y)
# 3. Выберите опцию редиректа на HTTPS (рекомендуется 2)
```

### 7.2 Настройка автообновления сертификата
```bash
# Добавление задачи в cron
sudo crontab -e

# Добавьте строку (выберите редактор nano):
0 12 * * * /usr/bin/certbot renew --quiet
```

## 🌍 Этап 8: Настройка DNS в Рег.ру

### 8.1 Настройка A-записей
1. Войдите в личный кабинет Рег.ру
2. Перейдите в "Домены" → "Управление DNS"
3. Найдите ваш домен
4. Добавьте/измените записи:

```
Тип: A
Имя: @
Значение: ваш-ip-адрес-сервера
TTL: 3600

Тип: A
Имя: www
Значение: ваш-ip-адрес-сервера
TTL: 3600
```

5. Сохраните изменения

**Важно**: DNS изменения могут занять от 15 минут до 24 часов для полного распространения.

## ✅ Этап 9: Проверка работоспособности

### 9.1 Проверка сервисов
```bash
# Статус PM2
pm2 list

# Статус Nginx
sudo systemctl status nginx

# Проверка портов
sudo netstat -tulpn | grep -E ':(80|443|3001)'

# Проверка логов приложения
pm2 logs shms-auto --lines 50
```

### 9.2 Проверка в браузере
Откройте в браузере:
- `http://ваш-ip:3001/test` - прямой доступ к Node.js
- `http://ваш-домен.ru` - через Nginx (HTTP)
- `https://ваш-домен.ru` - через Nginx с SSL (HTTPS)

### 9.3 Проверка основных функций
1. **Главная страница** - должна загружаться полностью
2. **Админ-панель** - `https://ваш-домен.ru/admin`
3. **API тест** - `https://ваш-домен.ru/test`
4. **Загрузка файлов** - проверьте в админ-панели
5. **Отправка форм** - проверьте форму обратной связи

## 🔧 Этап 10: Настройка мониторинга и обслуживания

### 10.1 Настройка логирования
```bash
# Ротация логов Nginx
sudo nano /etc/logrotate.d/nginx

# Добавьте конфигурацию для логов приложения
sudo nano /etc/logrotate.d/shms-auto
```

### 10.2 Создание скриптов обслуживания
```bash
# Скрипт для проверки здоровья
nano /home/<USER>/shms-auto/health-check.sh
```

### 10.3 Настройка резервного копирования
```bash
# Создание скрипта бэкапа базы данных
nano /home/<USER>/backup.sh
chmod +x /home/<USER>/backup.sh

# Добавление в cron для ежедневного бэкапа
crontab -e
# Добавьте: 0 2 * * * /home/<USER>/backup.sh
```

## 🚨 Устранение неполадок

### Проблема: Сайт не открывается
```bash
# Проверьте статус сервисов
pm2 list
sudo systemctl status nginx

# Проверьте логи
pm2 logs shms-auto
sudo tail -f /var/log/nginx/error.log
```

### Проблема: SSL не работает
```bash
# Проверьте сертификат
sudo certbot certificates

# Обновите сертификат
sudo certbot renew --dry-run
```

### Проблема: Высокая нагрузка
```bash
# Мониторинг ресурсов
htop
pm2 monit

# Проверка дискового пространства
df -h
```

## 📞 Поддержка и дополнительная информация

### Полезные команды для администрирования:
```bash
# Перезапуск всех сервисов
sudo systemctl restart nginx
pm2 restart shms-auto

# Просмотр логов в реальном времени
pm2 logs shms-auto --lines 0 --raw

# Проверка использования ресурсов
pm2 monit
```

### Контакты для поддержки:
- Email: <EMAIL>
- Документация проекта: см. файлы README.md и PRODUCTION.md

---

**🎉 Поздравляем! Ваше приложение SHMS Auto успешно развернуто на продакшн сервере Рег.ру!**
