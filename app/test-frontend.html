<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест фронтенда - Категории и пагинация</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .category-tabs { margin: 10px 0; }
        .category-tab { 
            padding: 10px 15px; 
            margin: 5px; 
            background: #f0f0f0; 
            border: 1px solid #ccc; 
            cursor: pointer; 
            display: inline-block;
        }
        .category-tab.active { background: #007bff; color: white; }
        .cars-container { margin: 20px 0; }
        .car-card { 
            border: 1px solid #ddd; 
            padding: 10px; 
            margin: 10px 0; 
            background: #f9f9f9; 
        }
        .load-more-btn { 
            padding: 10px 20px; 
            background: #28a745; 
            color: white; 
            border: none; 
            cursor: pointer; 
        }
        .debug-info { 
            background: #f8f9fa; 
            padding: 10px; 
            margin: 10px 0; 
            border-left: 3px solid #007bff; 
            font-family: monospace; 
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Тест системы категорий и пагинации</h1>
    
    <div class="test-section">
        <h2>Категории автомобилей</h2>
        <div class="category-tabs">
            <div class="category-tab active" data-category="all">Все автомобили</div>
            <div class="category-tab" data-category="business">Бизнес-класс</div>
            <div class="category-tab" data-category="sport">Спортивные</div>
            <div class="category-tab" data-category="suv">SUV</div>
            <div class="category-tab" data-category="featured">Рекомендуемые</div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Отладочная информация</h2>
        <div class="debug-info" id="debug-info">
            Выберите категорию для просмотра отладочной информации
        </div>
    </div>
    
    <div class="test-section">
        <h2>Результаты</h2>
        <div class="cars-container" id="cars-container">
            Загрузка...
        </div>
        <button class="load-more-btn" id="load-more-btn" style="display: none;">Загрузить еще</button>
    </div>

    <script>
        // Симуляция переменных из основного скрипта
        let currentPage = 1;
        const pageSize = 12;
        let categoryFilter = "";
        let featuredOnly = false;
        let activeFilters = {};
        let cachedData = [];
        let isLoading = false;
        const apiCache = {};

        // Функция для отладочного вывода
        function debugLog(message) {
            const debugDiv = document.getElementById('debug-info');
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.innerHTML += `<br>[${timestamp}] ${message}`;
            console.log(message);
        }

        // Функция построения URL API
        function buildApiUrl(page = currentPage) {
            const apiUrl = new URL('/api/encar-proxy.php', window.location.href);
            
            apiUrl.searchParams.append('fileType', 'active_offer');
            apiUrl.searchParams.append('date', new Date().toISOString().split('T')[0]);
            apiUrl.searchParams.append('limit', pageSize);
            apiUrl.searchParams.append('offset', (page - 1) * pageSize);
            apiUrl.searchParams.append('debug', '1');

            if (featuredOnly) {
                apiUrl.searchParams.append('featured', '1');
            }

            if (activeFilters && activeFilters.category) {
                apiUrl.searchParams.append('category', activeFilters.category);
            } else if (categoryFilter && (!activeFilters || !activeFilters.category)) {
                apiUrl.searchParams.append('category', categoryFilter);
            }

            return apiUrl;
        }

        // Функция загрузки автомобилей
        async function loadCars(reset = false) {
            if (isLoading) return;
            isLoading = true;

            const container = document.getElementById('cars-container');
            const loadMoreBtn = document.getElementById('load-more-btn');

            if (reset) {
                container.innerHTML = 'Загрузка...';
                cachedData = [];
                loadMoreBtn.style.display = 'none';
            }

            try {
                const apiUrl = buildApiUrl();
                debugLog(`Запрос к API: ${apiUrl.toString()}`);

                const response = await fetch(apiUrl);
                const data = await response.json();

                debugLog(`Получено ${data.length} автомобилей для страницы ${currentPage}`);

                if (reset) {
                    cachedData = data;
                    displayCars(data);
                } else {
                    cachedData = [...cachedData, ...data];
                    displayCars(cachedData);
                }

                if (data.length >= pageSize) {
                    loadMoreBtn.style.display = 'block';
                } else {
                    loadMoreBtn.style.display = 'none';
                }

            } catch (error) {
                debugLog(`Ошибка: ${error.message}`);
                container.innerHTML = `Ошибка загрузки: ${error.message}`;
            } finally {
                isLoading = false;
            }
        }

        // Функция отображения автомобилей
        function displayCars(cars) {
            const container = document.getElementById('cars-container');
            
            const carsHTML = cars.map(car => `
                <div class="car-card">
                    <strong>${car.mark || car.brand || 'N/A'} ${car.model || 'N/A'}</strong><br>
                    Год: ${car.year || 'N/A'}<br>
                    Цена: ${car.price_formatted || car.price || 'N/A'}<br>
                    ID: ${car.id || 'N/A'}
                </div>
            `).join('');
            
            container.innerHTML = carsHTML;
            debugLog(`Отображено ${cars.length} автомобилей`);
        }

        // Инициализация категорий
        function initCategories() {
            const categoryTabs = document.querySelectorAll('.category-tab');
            
            categoryTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // Убираем активный класс у всех кнопок
                    categoryTabs.forEach(t => t.classList.remove('active'));
                    // Добавляем активный класс нажатой кнопке
                    this.classList.add('active');

                    const category = this.getAttribute('data-category');
                    debugLog(`Выбрана категория: ${category}`);

                    // Сбрасываем состояние
                    currentPage = 1;
                    Object.keys(apiCache).forEach(key => delete apiCache[key]);

                    if (category === 'featured') {
                        featuredOnly = true;
                        categoryFilter = '';
                        activeFilters.category = '';
                    } else if (category === 'all') {
                        featuredOnly = false;
                        categoryFilter = '';
                        activeFilters.category = '';
                    } else {
                        featuredOnly = false;
                        categoryFilter = category;
                        activeFilters.category = category;
                    }

                    debugLog(`Состояние: featuredOnly=${featuredOnly}, categoryFilter=${categoryFilter}, activeFilters.category=${activeFilters.category}`);
                    
                    loadCars(true);
                });
            });
        }

        // Инициализация кнопки "Загрузить еще"
        function initLoadMore() {
            const loadMoreBtn = document.getElementById('load-more-btn');
            loadMoreBtn.addEventListener('click', function() {
                if (isLoading) return;
                currentPage++;
                debugLog(`Загрузка страницы ${currentPage}`);
                loadCars(false);
            });
        }

        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('Страница загружена, инициализация...');
            initCategories();
            initLoadMore();
            loadCars(true);
        });
    </script>
</body>
</html>
