const express = require("express");
const router = express.Router();
const db = require("./database");
const multer = require("multer");
const path = require("path");
const fs = require("fs");
const sqlite3 = require("sqlite3").verbose();
const { generateCarDetailHTML } = require("./car-detail-api");

// Настройка multer для загрузки изображений
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Убедимся, что директория существует
    const uploadDir = path.join(__dirname, "../public/uploads");
    if (!fs.existsSync(uploadDir)) {
      console.log("Создаем директорию uploads");
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Генерируем уникальное имя файла
    const uniqueName =
      Date.now() +
      "-" +
      Math.round(Math.random() * 1e9) +
      path.extname(file.originalname);
    console.log("Генерируем имя файла:", uniqueName);
    cb(null, uniqueName);
  },
});

// Добавляем фильтрацию файлов
const fileFilter = function (req, file, cb) {
  // Принимаем только изображения
  if (file.mimetype.startsWith("image/")) {
    cb(null, true);
  } else {
    cb(new Error("Только изображения могут быть загружены!"), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 20 * 1024 * 1024, // 20MB
  },
});

// Получить все карточки
router.get("/cars", (req, res) => {
  const { search, sort } = req.query;
  let query = "SELECT * FROM stock_cars";
  const params = [];

  // Если есть параметр поиска
  if (search) {
    query += ` WHERE (title LIKE ? OR subtitle LIKE ? OR body_type LIKE ? OR engine LIKE ? OR price LIKE ?)`;
    const searchParam = `%${search}%`;
    params.push(
      searchParam,
      searchParam,
      searchParam,
      searchParam,
      searchParam
    );
  }

  // Сортировка
  if (sort) {
    switch (sort) {
      case "created_desc":
        query += " ORDER BY created_at DESC";
        break;
      case "created_asc":
        query += " ORDER BY created_at ASC";
        break;
      case "price_desc":
        query += ' ORDER BY CAST(REPLACE(price, ",", "") AS REAL) DESC';
        break;
      case "price_asc":
        query += ' ORDER BY CAST(REPLACE(price, ",", "") AS REAL) ASC';
        break;
      case "title_asc":
        query += " ORDER BY title ASC";
        break;
      case "title_desc":
        query += " ORDER BY title DESC";
        break;
      default:
        query += " ORDER BY created_at DESC";
    }
  } else {
    query += " ORDER BY created_at DESC";
  }

  db.all(query, params, (err, cars) => {
    if (err) {
      console.error("Ошибка при получении списка автомобилей:", err);
      return res
        .status(500)
        .json({ error: "Ошибка при получении списка автомобилей" });
    }
    res.json(cars);
  });
});

// Получить одну карточку
router.get("/cars/:id", (req, res) => {
  db.get(
    "SELECT * FROM stock_cars WHERE id = ?",
    [req.params.id],
    (err, car) => {
      if (err)
        return res
          .status(500)
          .json({ error: "Ошибка при получении автомобиля" });
      if (!car) return res.status(404).json({ error: "Автомобиль не найден" });
      res.json(car);
    }
  );
});

// Добавить карточку
router.post("/cars", upload.single("image"), (req, res) => {
  const {
    title,
    subtitle,
    power,
    mileage,
    body_type,
    engine,
    transmission,
    interior_colors,
    consumption,
    capacity,
    price,
  } = req.body;
  // Сохраняем только относительный путь для фронта
  const image_path = req.file ? "/uploads/" + req.file.filename : "";
  db.run(
    `INSERT INTO stock_cars (title, subtitle, image_path, power, mileage, body_type, engine, transmission, interior_colors, consumption, capacity, price)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
    [
      title,
      subtitle,
      image_path,
      power,
      mileage,
      body_type,
      engine,
      transmission,
      interior_colors,
      consumption,
      capacity,
      price,
    ],
    function (err) {
      if (err) {
        console.error("Ошибка при добавлении автомобиля:", err);
        return res
          .status(500)
          .json({ error: "Ошибка при добавлении автомобиля" });
      }
      res.json({ id: this.lastID });
    }
  );
});

// Обновить карточку
router.put("/cars/:id", upload.single("image"), (req, res) => {
  const {
    title,
    subtitle,
    power,
    mileage,
    body_type,
    engine,
    transmission,
    interior_colors,
    consumption,
    capacity,
    price,
  } = req.body;
  const id = req.params.id;
  db.get("SELECT * FROM stock_cars WHERE id = ?", [id], (err, car) => {
    if (err || !car)
      return res.status(404).json({ error: "Автомобиль не найден" });
    let image_path = car.image_path;
    if (req.file) {
      // Удаляем старое изображение, если оно есть
      if (
        image_path &&
        fs.existsSync(path.join(__dirname, "../public", image_path))
      ) {
        fs.unlinkSync(path.join(__dirname, "../public", image_path));
      }
      image_path = "/uploads/" + req.file.filename;
    }
    db.run(
      `UPDATE stock_cars SET title=?, subtitle=?, image_path=?, power=?, mileage=?, body_type=?, engine=?, transmission=?, interior_colors=?, consumption=?, capacity=?, price=? WHERE id=?`,
      [
        title,
        subtitle,
        image_path,
        power,
        mileage,
        body_type,
        engine,
        transmission,
        interior_colors,
        consumption,
        capacity,
        price,
        id,
      ],
      function (err) {
        if (err)
          return res
            .status(500)
            .json({ error: "Ошибка при обновлении автомобиля" });
        res.json({ success: true });
      }
    );
  });
});

// Удалить карточку
router.delete("/cars/:id", (req, res) => {
  const id = req.params.id;
  db.get("SELECT * FROM stock_cars WHERE id = ?", [id], (err, car) => {
    if (err || !car)
      return res.status(404).json({ error: "Автомобиль не найден" });
    // Удаляем изображение, если оно есть
    if (
      car.image_path &&
      fs.existsSync(path.join(__dirname, "../public", car.image_path))
    ) {
      fs.unlinkSync(path.join(__dirname, "../public", car.image_path));
    }
    db.run("DELETE FROM stock_cars WHERE id = ?", [id], function (err) {
      if (err)
        return res
          .status(500)
          .json({ error: "Ошибка при удалении автомобиля" });
      res.json({ success: true });
    });
  });
});

// Импорт из HTML
router.post("/import", (req, res) => {
  const { path: htmlPath } = req.body;

  if (!htmlPath) {
    return res.status(400).json({ error: "Путь к HTML файлу не указан" });
  }

  const fullPath = path.join(__dirname, "..", htmlPath);

  if (!fs.existsSync(fullPath)) {
    return res.status(404).json({ error: "Файл не найден" });
  }

  try {
    const cheerio = require("cheerio");
    const html = fs.readFileSync(fullPath, "utf8");
    const $ = cheerio.load(html);
    const imgDir = path.join(__dirname, "../public/uploads");

    if (!fs.existsSync(imgDir)) {
      fs.mkdirSync(imgDir, { recursive: true });
    }

    let importCount = 0;
    let importPromises = [];

    $(".car-card").each((i, el) => {
      const title = $(el).find(".car-card__title").text().trim();
      const subtitle = $(el).find(".car-card__subtitle").text().trim();
      const imgSrc = $(el).find(".car-card__image img").attr("src");
      const power = $(el).find(".car-card__spec-main").first().text().trim();
      const mileage = $(el).find(".car-card__spec-main").last().text().trim();
      const body_type = $(el)
        .find(".car-card__info-list div")
        .eq(0)
        .text()
        .replace("Тип кузова", "")
        .trim();
      const engine = $(el)
        .find(".car-card__info-list div")
        .eq(1)
        .text()
        .replace("Двигатель", "")
        .trim();
      const transmission = $(el)
        .find(".car-card__info-list div")
        .eq(2)
        .text()
        .replace("Трансмиссия", "")
        .trim();

      // Преобразуем формат цветов из "dot dot-color" или "dot--color" в JSON
      let colorsArray = [];
      const colorDots = $(el)
        .find(".car-card__info-list div")
        .eq(3)
        .find(".dot");

      if (colorDots.length > 0) {
        // Получаем цвета из HTML-элементов
        colorDots.each((i, dot) => {
          const className = $(dot).attr("class") || "";
          const colorClass = className
            .split(" ")
            .find((c) => c.startsWith("dot--"));
          if (colorClass) {
            const colorName = colorClass.replace("dot--", "");
            let hexColor = "#000000"; // Черный по умолчанию

            // Преобразуем название цвета в HEX
            switch (colorName.toLowerCase()) {
              case "black":
                hexColor = "#000000";
                break;
              case "white":
                hexColor = "#ffffff";
                break;
              case "orange":
                hexColor = "#ff7700";
                break;
              case "red":
                hexColor = "#d20000";
                break;
              case "blue":
                hexColor = "#0066cc";
                break;
              case "brown":
                hexColor = "#8B4513";
                break;
              case "beige":
                hexColor = "#F5F5DC";
                break;
              case "gray":
                hexColor = "#808080";
                break;
              default:
                hexColor = "#000000";
            }

            colorsArray.push({
              hex: hexColor,
              name: colorName,
            });
          }
        });
      } else {
        // Если на странице цвета в текстовом формате "Цвета: dot dot-black, dot dot-orange"
        const colorText = $(el)
          .find(".car-card__info-list div")
          .eq(3)
          .text()
          .replace("Цвета:", "")
          .trim();
        if (colorText) {
          const colorParts = colorText.split(", ");
          colorParts.forEach((part) => {
            let colorName = part.trim();
            if (colorName.startsWith("dot dot-")) {
              colorName = colorName.replace("dot dot-", "");
            } else if (colorName.startsWith("dot--")) {
              colorName = colorName.replace("dot--", "");
            }

            let hexColor = "#000000"; // Черный по умолчанию

            // Преобразуем название цвета в HEX
            switch (colorName.toLowerCase()) {
              case "black":
                hexColor = "#000000";
                break;
              case "white":
                hexColor = "#ffffff";
                break;
              case "orange":
                hexColor = "#ff7700";
                break;
              case "red":
                hexColor = "#d20000";
                break;
              case "blue":
                hexColor = "#0066cc";
                break;
              case "brown":
                hexColor = "#8B4513";
                break;
              case "beige":
                hexColor = "#F5F5DC";
                break;
              case "gray":
                hexColor = "#808080";
                break;
              default:
                hexColor = "#000000";
            }

            colorsArray.push({
              hex: hexColor,
              name: colorName,
            });
          });
        }
      }

      // Преобразуем массив цветов в JSON строку
      const interior_colors = JSON.stringify(colorsArray);

      const consumption = $(el)
        .find(".car-card__info-list div")
        .eq(4)
        .text()
        .replace("Расход", "")
        .trim();
      const capacity = $(el)
        .find(".car-card__info-list div")
        .eq(5)
        .text()
        .replace("Вместимость", "")
        .trim();
      const price = $(el).find(".car-card__price").text().trim();

      // Копируем картинку в uploads если есть
      let image_path = "";
      if (imgSrc) {
        const srcPath = path.join(__dirname, "..", imgSrc);
        const ext = path.extname(srcPath) || ".png";
        const destName = `imported_${Date.now()}_${i}${ext}`;
        const destPath = path.join(imgDir, destName);

        if (fs.existsSync(srcPath)) {
          fs.copyFileSync(srcPath, destPath);
          image_path = "/uploads/" + destName;
        }
      }

      // Создаем промис для вставки в базу
      const importPromise = new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO stock_cars (title, subtitle, image_path, power, mileage, body_type, engine, transmission, interior_colors, consumption, capacity, price)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            title,
            subtitle,
            image_path,
            power,
            mileage,
            body_type,
            engine,
            transmission,
            interior_colors,
            consumption,
            capacity,
            price,
          ],
          function (err) {
            if (err) {
              reject(err);
            } else {
              importCount++;
              resolve();
            }
          }
        );
      });

      importPromises.push(importPromise);
    });

    // Ждем завершения всех вставок
    Promise.all(importPromises)
      .then(() => {
        res.json({ success: true, count: importCount });
      })
      .catch((err) => {
        console.error("Ошибка при импорте:", err);
        res
          .status(500)
          .json({ error: "Ошибка при импорте данных", details: err.message });
      });
  } catch (error) {
    console.error("Ошибка при импорте:", error);
    res
      .status(500)
      .json({ error: "Ошибка при импорте данных", details: error.message });
  }
});

// Маршрут для исправления цветов
router.post("/fix-colors", (req, res) => {
  // Сначала получим все карточки с их текущими цветами
  db.all("SELECT id, interior_colors FROM stock_cars", [], (err, cars) => {
    if (err) {
      console.error("Ошибка при получении списка автомобилей:", err);
      return res
        .status(500)
        .json({ error: "Ошибка при получении списка автомобилей" });
    }

    let count = 0;
    const updatePromises = cars.map((car) => {
      return new Promise((resolve, reject) => {
        // Проверяем, если уже JSON формат - ничего не делаем
        try {
          const parsed = JSON.parse(car.interior_colors);
          if (
            Array.isArray(parsed) &&
            parsed.length > 0 &&
            typeof parsed[0] === "object" &&
            parsed[0].hex
          ) {
            resolve(); // Уже в правильном формате
            return;
          }
        } catch (e) {
          // Не JSON - нужно конвертировать
        }

        let colorsArray = [];

        if (
          car.interior_colors.includes("dot--") ||
          car.interior_colors.includes("dot dot-")
        ) {
          // Конвертируем старый формат (dot--color или dot dot-color)
          let colorNames = [];

          if (car.interior_colors.includes("dot dot-")) {
            // Формат "dot dot-color, dot dot-color2"
            const colorTexts = car.interior_colors.split(", ");
            colorNames = colorTexts.map((text) =>
              text.replace("dot dot-", "").trim()
            );
          } else {
            // Формат "dot--color,dot--color2"
            colorNames = car.interior_colors
              .split(",")
              .map((cls) => cls.replace("dot--", "").trim());
          }

          // Конвертируем имена цветов в HEX-значения
          colorNames.forEach((name) => {
            let hex = "#000000"; // Черный по умолчанию

            // Преобразуем название цвета в HEX
            switch (name.toLowerCase()) {
              case "black":
                hex = "#000000";
                break;
              case "white":
                hex = "#ffffff";
                break;
              case "orange":
                hex = "#ff7700";
                break;
              case "red":
                hex = "#d20000";
                break;
              case "blue":
                hex = "#0066cc";
                break;
              case "brown":
                hex = "#8B4513";
                break;
              case "beige":
                hex = "#F5F5DC";
                break;
              case "gray":
                hex = "#808080";
                break;
              default:
                hex =
                  "#" +
                  Math.floor(Math.random() * 16777215)
                    .toString(16)
                    .padStart(6, "0");
            }

            colorsArray.push({
              hex: hex,
              name: name,
            });
          });
        }

        // Если нет цветов или неизвестный формат, добавим хотя бы один цвет
        if (colorsArray.length === 0) {
          colorsArray.push({
            hex: "#000000",
            name: "black",
          });
        }

        // Преобразуем массив цветов в JSON строку
        const newColors = JSON.stringify(colorsArray);

        // Обновляем запись в базе данных
        db.run(
          "UPDATE stock_cars SET interior_colors = ? WHERE id = ?",
          [newColors, car.id],
          function (err) {
            if (err) {
              reject(err);
            } else {
              count++;
              resolve();
            }
          }
        );
      });
    });

    Promise.all(updatePromises)
      .then(() => {
        res.json({ success: true, count });
      })
      .catch((err) => {
        console.error("Ошибка при обновлении цветов:", err);
        res.status(500).json({ error: "Ошибка при обновлении цветов" });
      });
  });
});

// Получить список избранных автомобилей для главной страницы
router.get("/featured", (req, res) => {
  db.all(
    `
        SELECT f.id as featured_id, f.display_order, c.* 
        FROM featured_cars f 
        JOIN stock_cars c ON f.car_id = c.id 
        ORDER BY f.display_order ASC
    `,
    [],
    (err, cars) => {
      if (err) {
        console.error("Ошибка при получении избранных автомобилей:", err);
        return res
          .status(500)
          .json({ error: "Ошибка при получении списка избранных автомобилей" });
      }
      res.json(cars);
    }
  );
});

// Добавить автомобиль в избранное для главной страницы
router.post("/featured/:carId", (req, res) => {
  const carId = req.params.carId;
  const displayOrder = req.body.displayOrder || 0;

  // Проверяем, существует ли такой автомобиль
  db.get("SELECT id FROM stock_cars WHERE id = ?", [carId], (err, car) => {
    if (err) {
      console.error("Ошибка при проверке автомобиля:", err);
      return res
        .status(500)
        .json({ error: "Ошибка при добавлении в избранное" });
    }

    if (!car) {
      return res.status(404).json({ error: "Автомобиль не найден" });
    }

    // Проверяем, не добавлен ли уже этот автомобиль в избранное
    db.get(
      "SELECT id FROM featured_cars WHERE car_id = ?",
      [carId],
      (err, featured) => {
        if (err) {
          console.error("Ошибка при проверке избранного:", err);
          return res
            .status(500)
            .json({ error: "Ошибка при добавлении в избранное" });
        }

        if (featured) {
          return res
            .status(400)
            .json({ error: "Автомобиль уже добавлен в избранное" });
        }

        // Добавляем автомобиль в избранное
        db.run(
          "INSERT INTO featured_cars (car_id, display_order) VALUES (?, ?)",
          [carId, displayOrder],
          function (err) {
            if (err) {
              console.error("Ошибка при добавлении в избранное:", err);
              return res
                .status(500)
                .json({ error: "Ошибка при добавлении в избранное" });
            }

            res.json({
              id: this.lastID,
              success: true,
              message: "Автомобиль успешно добавлен в избранное",
            });
          }
        );
      }
    );
  });
});

// Удалить автомобиль из избранного для главной страницы
router.delete("/featured/:id", (req, res) => {
  const id = req.params.id;

  db.run("DELETE FROM featured_cars WHERE id = ?", [id], function (err) {
    if (err) {
      console.error("Ошибка при удалении из избранного:", err);
      return res
        .status(500)
        .json({ error: "Ошибка при удалении из избранного" });
    }

    if (this.changes === 0) {
      return res.status(404).json({ error: "Избранный автомобиль не найден" });
    }

    res.json({
      success: true,
      message: "Автомобиль успешно удален из избранного",
    });
  });
});

// Обновить порядок отображения избранного автомобиля
router.put("/featured/:id/order", (req, res) => {
  const id = req.params.id;
  const { displayOrder } = req.body;

  if (displayOrder === undefined || displayOrder === null) {
    return res.status(400).json({ error: "Параметр displayOrder обязателен" });
  }

  db.run(
    "UPDATE featured_cars SET display_order = ? WHERE id = ?",
    [displayOrder, id],
    function (err) {
      if (err) {
        console.error("Ошибка при обновлении порядка:", err);
        return res
          .status(500)
          .json({ error: "Ошибка при обновлении порядка отображения" });
      }

      if (this.changes === 0) {
        return res
          .status(404)
          .json({ error: "Избранный автомобиль не найден" });
      }

      res.json({
        success: true,
        message: "Порядок отображения успешно обновлен",
      });
    }
  );
});

// Публичный API для получения избранных автомобилей для главной страницы
router.get("/api/featured", (req, res) => {
  db.all(
    `
        SELECT c.* 
        FROM featured_cars f 
        JOIN stock_cars c ON f.car_id = c.id 
        ORDER BY f.display_order ASC
        LIMIT 6
    `,
    [],
    (err, cars) => {
      if (err) {
        console.error("Ошибка при получении избранных автомобилей:", err);
        return res
          .status(500)
          .json({ error: "Ошибка при получении списка избранных автомобилей" });
      }
      res.json(cars);
    }
  );
});

// ========== Car Details Routes ==========

// Получить детальную информацию для всех автомобилей
router.get("/car-details", (req, res) => {
  db.all(
    `
        SELECT cd.*, sc.title as car_title, sc.image_path as car_image 
        FROM car_details cd
        JOIN stock_cars sc ON cd.car_id = sc.id
        ORDER BY cd.created_at DESC
    `,
    [],
    (err, details) => {
      if (err) {
        console.error("Ошибка при получении детальной информации:", err);
        return res
          .status(500)
          .json({ error: "Ошибка при получении детальной информации" });
      }
      res.json(details);
    }
  );
});

// Получить детальную информацию для конкретного автомобиля
router.get("/car-details/car/:carId", (req, res) => {
  const carId = req.params.carId;
  db.get(
    "SELECT * FROM car_details WHERE car_id = ?",
    [carId],
    (err, detail) => {
      if (err) {
        console.error("Ошибка при получении детальной информации:", err);
        return res
          .status(500)
          .json({ error: "Ошибка при получении детальной информации" });
      }
      res.json(detail || null);
    }
  );
});

// Получить детальную информацию по ID
router.get("/car-details/:id", (req, res) => {
  const id = req.params.id;
  db.get("SELECT * FROM car_details WHERE id = ?", [id], (err, detail) => {
    if (err) {
      console.error("Ошибка при получении детальной информации:", err);
      return res
        .status(500)
        .json({ error: "Ошибка при получении детальной информации" });
    }
    if (!detail) {
      return res.status(404).json({ error: "Детальная информация не найдена" });
    }

    // Парсим JSON поля перед отправкой клиенту
    try {
      if (detail.images && typeof detail.images === "string") {
        detail.images = JSON.parse(detail.images);
      }
      if (!Array.isArray(detail.images)) {
        detail.images = [];
      }
    } catch (e) {
      console.error("Ошибка при парсинге изображений:", e);
      detail.images = [];
    }

    res.json(detail);
  });
});

// Добавить детальную информацию об автомобиле
router.post("/car-details", upload.array("detail_images", 10), (req, res) => {
  const {
    car_id,
    full_title,
    price,
    moscow_price,
    description,
    specs,
    best_features,
    similar_cars,
    images,
  } = req.body;

  // Проверяем, есть ли уже детальная информация для этого автомобиля
  db.get(
    "SELECT * FROM car_details WHERE car_id = ?",
    [car_id],
    (err, existingDetail) => {
      if (err) {
        console.error("Ошибка при проверке детальной информации:", err);
        return res
          .status(500)
          .json({ error: "Ошибка при проверке детальной информации" });
      }

      if (existingDetail) {
        return res
          .status(400)
          .json({
            error: "Детальная информация для этого автомобиля уже существует",
          });
      }

      // Обработка загруженных изображений
      let uploadedImages = [];
      if (req.files && req.files.length > 0) {
        uploadedImages = req.files.map((file) => ({
          path: "/uploads/" + file.filename,
          filename: file.filename,
        }));
      }

      // Объединяем загруженные изображения с существующими (если они переданы в JSON)
      let allImages = uploadedImages;
      if (images) {
        try {
          const existingImages = JSON.parse(images);
          allImages = [...existingImages, ...uploadedImages];
        } catch (e) {
          console.error("Ошибка при парсинге JSON изображений:", e);
        }
      }

      // Получаем статус из запроса или устанавливаем по умолчанию
      const status = req.body.status || "draft";

      db.run(
        `INSERT INTO car_details (car_id, full_title, price, moscow_price, description, specs, best_features, similar_cars, images, status)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          car_id,
          full_title,
          price,
          moscow_price,
          description,
          specs,
          best_features,
          similar_cars || null,
          JSON.stringify(allImages),
          status,
        ],
        function (err) {
          if (err) {
            console.error("Ошибка при добавлении детальной информации:", err);
            return res
              .status(500)
              .json({ error: "Ошибка при добавлении детальной информации" });
          }
          res.json({ id: this.lastID });
        }
      );
    }
  );
});

// Обновить детальную информацию об автомобиле
router.put(
  "/car-details/:id",
  upload.array("detail_images", 10),
  (req, res) => {
    const id = req.params.id;
    const {
      car_id,
      full_title,
      price,
      moscow_price,
      description,
      specs,
      best_features,
      similar_cars,
      images,
    } = req.body;

    // Получаем текущую запись
    db.get("SELECT * FROM car_details WHERE id = ?", [id], (err, detail) => {
      if (err || !detail) {
        return res
          .status(404)
          .json({ error: "Детальная информация не найдена" });
      }

      // Обработка загруженных изображений
      let uploadedImages = [];
      if (req.files && req.files.length > 0) {
        uploadedImages = req.files.map((file) => ({
          path: "/uploads/" + file.filename,
          filename: file.filename,
        }));
      }

      // Объединяем загруженные изображения с существующими (если они переданы в JSON)
      let allImages = uploadedImages;
      if (images) {
        try {
          const existingImages = JSON.parse(images);
          allImages = [...existingImages, ...uploadedImages];
        } catch (e) {
          console.error("Ошибка при парсинге JSON изображений:", e);
        }
      }

      // Проверяем, есть ли статус в запросе
      const status = req.body.status || "draft";

      db.run(
        `UPDATE car_details 
             SET car_id=?, full_title=?, price=?, moscow_price=?, description=?, specs=?, best_features=?, similar_cars=?, images=?, status=?
             WHERE id=?`,
        [
          car_id,
          full_title,
          price,
          moscow_price,
          description,
          specs,
          best_features,
          similar_cars || null,
          JSON.stringify(allImages),
          status,
          id,
        ],
        function (err) {
          if (err) {
            console.error("Ошибка при обновлении детальной информации:", err);
            return res
              .status(500)
              .json({ error: "Ошибка при обновлении детальной информации" });
          }
          res.json({ success: true });
        }
      );
    });
  }
);

// Удалить детальную информацию об автомобиле
router.delete("/car-details/:id", (req, res) => {
  const id = req.params.id;

  // Получаем текущую запись, чтобы удалить связанные изображения
  db.get("SELECT * FROM car_details WHERE id = ?", [id], (err, detail) => {
    if (err || !detail) {
      return res.status(404).json({ error: "Детальная информация не найдена" });
    }

    // Удаляем загруженные изображения, если они есть
    try {
      const images = JSON.parse(detail.images || "[]");
      images.forEach((img) => {
        if (
          img.path &&
          img.path.startsWith("/uploads/") &&
          fs.existsSync(path.join(__dirname, "../public", img.path))
        ) {
          fs.unlinkSync(path.join(__dirname, "../public", img.path));
        }
      });
    } catch (e) {
      console.error("Ошибка при удалении изображений:", e);
    }

    db.run("DELETE FROM car_details WHERE id = ?", [id], function (err) {
      if (err) {
        console.error("Ошибка при удалении детальной информации:", err);
        return res
          .status(500)
          .json({ error: "Ошибка при удалении детальной информации" });
      }
      res.json({ success: true });
    });
  });
});

// Маршрут для предпросмотра детальной страницы
router.post("/car-details/preview", (req, res) => {
  try {
    const detail = req.body;

    // Генерация HTML для предпросмотра
    let images = [];
    try {
      if (detail.images) {
        images =
          typeof detail.images === "string"
            ? JSON.parse(detail.images)
            : detail.images;
      }
    } catch (e) {
      console.error("Ошибка при парсинге JSON изображений:", e);
    }

    // Обработка описания (поддержка markdown)
    const description = detail.description
      ? detail.description
          .replace(/\*\*(.*?)\*\*/g, "<b>$1</b>") // Замена **text** на <b>text</b>
          .replace(/\*(.*?)\*/g, "<b>$1</b>") // Замена *text* на <b>text</b> (однозвездочная версия)
          .replace(/\n\n/g, "<br><br>") // Замена двойных переносов строк
          .replace(/\n/g, "<br>")
      : ""; // Замена одиночных переносов строк

    // Обработка спецификаций
    const specsLines = detail.specs ? detail.specs.split("\n") : [];

    // Извлекаем значения для характеристик из спецификаций
    let previewDriveTrain = "AWD",
      previewEngine = "",
      previewPower = "",
      previewTransmission = "",
      previewExteriorColor = "",
      previewMileage = "0 км",
      previewBodyType = "Тип кузова";

    // Затем ищем данные в спецификациях
    specsLines.forEach((line) => {
      const lineLower = line.toLowerCase();
      const parts = lineLower.split(":");
      if (parts.length >= 2) {
        const key = parts[0].trim();
        const value = parts[1].trim();

        if (key.includes("привод")) {
          previewDriveTrain = value;
        } else if (key.includes("кузов") || key.includes("body")) {
          previewBodyType = value;
        } else if (key.includes("пробег") || key.includes("mileage")) {
          previewMileage = value;
        } else if (key.includes("мощность") || key.includes("power")) {
          previewPower = value;
        } else if (key.includes("двигатель") || key.includes("engine")) {
          previewEngine = value;
        } else if (
          key.includes("трансмиссия") ||
          key.includes("transmission")
        ) {
          previewTransmission = value;
        } else if (
          key.includes("цвет экстерьера") ||
          key.includes("exterior")
        ) {
          previewExteriorColor = value;
        }
      }
    });

    // Обработка цветов интерьера
    let colorsHTML = "";
    try {
      // Проверяем, есть ли информация о цветах в параметре car_interior_colors
      if (detail.car_interior_colors) {
        const interiorColors =
          typeof detail.car_interior_colors === "string"
            ? JSON.parse(detail.car_interior_colors)
            : detail.car_interior_colors;

        if (Array.isArray(interiorColors)) {
          colorsHTML = interiorColors
            .map((color) => {
              const colorClass = color.name
                ? color.name.toLowerCase()
                : "black";
              return `<span class="dot dot--${colorClass}"></span>`;
            })
            .join(" ");
        }
      } else {
        // Попробуем найти информацию о цветах в спецификациях
        const colorsLine = specsLines.find(
          (line) =>
            line.toLowerCase().includes("цвета интерьера") ||
            line.toLowerCase().includes("цвет интерьера")
        );

        if (colorsLine) {
          const colorNames = colorsLine
            .split(":")[1]
            .trim()
            .split(",")
            .map((c) => c.trim());
          colorsHTML = colorNames
            .map((name) => {
              const colorClass = name.toLowerCase();
              return `<span class="dot dot--${colorClass}"></span>`;
            })
            .join(" ");
        }
      }
    } catch (e) {
      console.error("Ошибка при обработке цветов интерьера:", e);
      colorsHTML = "";
    }

    // Обработка лучших характеристик
    let bestFeatures = detail.best_features
      ? detail.best_features.split("\n").filter((f) => f.trim())
      : [];

    // Форматируем секцию "Лучшее в авто" с "Основные опции" и "Дополнительные"
    let firstColumnFeatures = [];
    let secondColumnFeatures = [];
    let firstColumnTitle = "Основные опции";
    let secondColumnTitle = "Дополнительные";

    // Проверяем, есть ли заголовки разделов
    const hasMainSection = bestFeatures.some(
      (line) =>
        line.toLowerCase().includes("основные опции") ||
        line.toLowerCase().includes("основное") ||
        line.toLowerCase().includes("основные")
    );

    const hasAdditionalSection = bestFeatures.some(
      (line) =>
        line.toLowerCase().includes("дополнительные") ||
        line.toLowerCase().includes("дополнительно")
    );

    if (hasMainSection || hasAdditionalSection) {
      // Есть явные заголовки разделов
      let sections = [];
      let currentSection = { title: "", items: [] };
      let foundMainSection = false;
      let foundAdditionalSection = false;

      bestFeatures.forEach((line) => {
        const lowerLine = line.toLowerCase();

        if (
          lowerLine.includes("основные опции") ||
          (lowerLine.includes("основные") && !foundMainSection) ||
          (lowerLine.includes("основное") && !foundMainSection)
        ) {
          if (currentSection.title) {
            sections.push(currentSection);
          }
          currentSection = { title: "Основные опции", items: [] };
          foundMainSection = true;
        } else if (
          lowerLine.includes("дополнительные") ||
          lowerLine.includes("дополнительно")
        ) {
          if (currentSection.title) {
            sections.push(currentSection);
          }
          currentSection = { title: "Дополнительные", items: [] };
          foundAdditionalSection = true;
        } else if (
          line.trim() &&
          !lowerLine.endsWith(":") &&
          !lowerLine.endsWith("опции")
        ) {
          // Это пункт списка, добавляем в текущую секцию
          currentSection.items.push(line.trim().replace(/^[•-]\s*/, ""));
        }
      });

      if (currentSection.title) {
        sections.push(currentSection);
      }

      // Находим разделы "Основные опции" и "Дополнительные"
      const mainSection = sections.find((s) =>
        s.title.toLowerCase().includes("основные")
      );
      const additionalSection = sections.find((s) =>
        s.title.toLowerCase().includes("дополнительные")
      );

      if (mainSection) {
        firstColumnTitle = mainSection.title;
        firstColumnFeatures = mainSection.items;
      }

      if (additionalSection) {
        secondColumnTitle = additionalSection.title;
        secondColumnFeatures = additionalSection.items;
      }

      // Если одна из секций не найдена, разделяем все характеристики
      if (!mainSection && !additionalSection) {
        const allFeatures = bestFeatures
          .filter(
            (f) =>
              f.trim() &&
              !f.toLowerCase().includes("основные") &&
              !f.toLowerCase().includes("дополнительные")
          )
          .map((f) => f.replace(/^[•-]\s*/, ""));

        const middleIndex = Math.ceil(allFeatures.length / 2);
        firstColumnFeatures = allFeatures.slice(0, middleIndex);
        secondColumnFeatures = allFeatures.slice(middleIndex);
      } else if (!mainSection) {
        firstColumnFeatures = [];
      } else if (!additionalSection) {
        secondColumnFeatures = [];
      }
    } else {
      // Нет явных заголовков, разделяем по половинам
      const cleanFeatures = bestFeatures
        .filter((f) => f.trim())
        .map((f) => f.replace(/^[•-]\s*/, ""));

      const middleIndex = Math.ceil(cleanFeatures.length / 2);
      firstColumnFeatures = cleanFeatures.slice(0, middleIndex);
      secondColumnFeatures = cleanFeatures.slice(middleIndex);
    }

    const firstColumnHTML = firstColumnFeatures
      .map((feature) => `<li>${feature}</li>`)
      .join("");
    const secondColumnHTML = secondColumnFeatures
      .map((feature) => `<li>${feature}</li>`)
      .join("");

    // Получаем первые изображения для галереи
    const mainImagesHTML =
      images.length > 0
        ? images
            .map(
              (image, index) => `
                <img src="${image.path}" alt="${
                detail.full_title || "Изображение автомобиля"
              } ${index + 1}" class="car-detail__main-img slider-img ${
                index === 0 ? "active" : ""
              }">
            `
            )
            .join("")
        : '<img src="../PNG/SHMS_Logo_Black.png" alt="Нет изображения" class="car-detail__main-img slider-img active">';

    // Получаем изображения для секции описания (первые 2)
    const descGalleryHTML =
      images.length >= 2
        ? `<div class="car-detail__desc-gallery">
                <img src="${images[0].path}" alt="${
            detail.full_title || "Авто"
          }" class="car-detail__desc-img">
                <img src="${images[1].path}" alt="${
            detail.full_title || "Авто"
          } интерьер" class="car-detail__desc-img">
              </div>`
        : "";

    // Получаем изображения для секции характеристик (3-й и 4-й)
    const specsGalleryHTML =
      images.length >= 4
        ? `<div class="car-detail__specs-gallery">
                <img src="${images[2].path}" alt="${
            detail.full_title || "Авто"
          }" class="car-detail__specs-img">
                <img src="${images[3].path}" alt="${
            detail.full_title || "Авто"
          } задняя часть" class="car-detail__specs-img">
              </div>`
        : images.length >= 2
        ? `<div class="car-detail__specs-gallery">
                    <img src="${images[0].path}" alt="${
            detail.full_title || "Авто"
          }" class="car-detail__specs-img">
                    <img src="${images[1].path}" alt="${
            detail.full_title || "Авто"
          }" class="car-detail__specs-img">
                  </div>`
        : "";

    // Извлекаем значения для инфоблоков из спецификаций
    let bodyType = "",
      mileage = "",
      power = "";

    specsLines.forEach((line) => {
      const lineLower = line.toLowerCase();
      const parts = lineLower.split(":");
      if (parts.length >= 2) {
        const key = parts[0].trim();
        const value = parts[1].trim();

        if (key.includes("кузов") || key.includes("body") || key === "привод") {
          bodyType = value;
        } else if (key.includes("пробег") || key.includes("mileage")) {
          mileage = value;
        } else if (key.includes("мощность") || key.includes("power")) {
          power = value;
        }
      }
    });

    // Сгенерированный HTML для предпросмотра
    const html = `
<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${
      detail.full_title || "Детальная страница автомобиля"
    } | SHMS</title>
    <link rel="stylesheet" href="/car-detail.css">
    <link rel="stylesheet" href="/specs-fix.css">
    <link rel="stylesheet" href="/footer.css">
    <link rel="stylesheet" href="/car-detail-styles.css">
</head>

<body>
    <!-- Header как на stock.html -->
    <header class="mobile-header">
        <button class="burger" id="burgerBtn" aria-label="Открыть меню">
            <span></span>
            <span></span>
            <span></span>
        </button>
        <a href="/index.html" class="mobile-header__logo">
            <img src="/PNG/SHMS_Logo_Black.png" alt="SHMS" class="mobile-header__logo-img">
        </a>
    </header>
    <div class="mobile-header-spacer"></div>
    <section class="hero-stock">
        <div class="hero-header-group">
            <a href="#" class="header__back-link" onclick="window.history.back(); return false;">Назад</a>
            <div class="header__center-group">
                <nav class="header__nav">
                    <a href="/index.html" class="header__logo-link">
                        <img src="/PNG/SHMS_Logo_Black.png" alt="SHMS" class="header__logo-img">
                    </a>
                    <a href="/stock.html" class="header__nav-link">Авто в наличии</a>
                    <a href="/order.html" class="header__nav-link">Заказать авто</a>
                    <a href="/contacts.html" class="header__nav-link">Наши контакты</a>
                </nav>
            </div>
        </div>
    </section>

    <!-- Навигационные табы -->
    <div class="car-detail__tabs">
        <a href="#desc" class="car-detail__tab active">Описание</a>
        <a href="#specs" class="car-detail__tab">Характеристики</a>
        <a href="#best" class="car-detail__tab">Лучшее в авто</a>
    </div>

    <!-- ======= Заголовок и кнопка ======= -->
    <div class="car-detail__headline-row">
        <div>
            <h1 class="car-detail__title">${
              detail.full_title || "Название автомобиля"
            }</h1>
            <span class="car-detail__price-label">Цена в Москве: <span class="car-detail__price">${
              detail.price || "$0"
            }</span></span>
        </div>
        <button class="car-detail__buy-btn">Купить</button>
    </div>

    <!-- ======= Галерея ======= -->
    <div class="car-detail__gallery">
        <div class="slider" id="carSlider">
            <button class="slider-arrow slider-arrow--left" id="sliderPrev" aria-label="Назад">❮</button>
            ${mainImagesHTML}
            <button class="slider-arrow slider-arrow--right" id="sliderNext" aria-label="Вперед">❯</button>
        </div>
    </div>

    <!-- Модальное окно для полноэкранного просмотра -->
    <div id="imgModal" class="img-modal">
        <span class="img-modal__close" id="imgModalClose">&times;</span>
        <img class="img-modal__content" id="imgModalImg" src="" alt="Фото">
    </div>

    <!-- ======= Описание и инфо ======= -->
    <div class="car-detail__desc-row" id="desc">
        <div class="car-detail__desc-col">
            <div class="car-detail__desc-title">Описание</div>
            <div class="car-detail__desc-text">
                ${description}
            </div>
            ${descGalleryHTML}
        </div>
        <div class="car-detail__info-col">
            <div class="car-detail__info-price">${
              detail.moscow_price || "$0"
            }</div>
            <div class="car-detail__info-label">Цена в Москве со всеми документами</div>
            <div class="car-detail__info-blocks">
                <div class="car-detail__info-block">
                    <div class="car-detail__info-block-icon">
                        <img src="/PNG/car.png" alt="Кузов">
                    </div>
                    <div class="car-detail__info-block-text">
                        ${
                          bodyType || "Тип кузова"
                        } <span class="car-detail__info-block-label">Кузов</span>
                    </div>
                </div>
                <div class="car-detail__info-block">
                    <div class="car-detail__info-block-icon">
                        <img src="/PNG/gas_pump.png" alt="Пробег">
                    </div>
                    <div class="car-detail__info-block-text">
                        ${
                          mileage || "0 км"
                        } <span class="car-detail__info-block-label">Пробег</span>
                    </div>
                </div>
                <div class="car-detail__info-block">
                    <div class="car-detail__info-block-icon">
                        <img src="/PNG/speed.png" alt="Мощность">
                    </div>
                    <div class="car-detail__info-block-text">
                        ${
                          power || "0 л.с."
                        } <span class="car-detail__info-block-label">Мощность</span>
                    </div>
                </div>
            </div>
            <div class="car-detail__priority">
                <b>Приоритет</b>
                Вы станете первым владельцем автомобиля, так как его пробег обусловлен исключительно мобилизационной
                эксплуатацией (погрузка, выгрузка и транспортировка)
            </div>
            <div class="car-detail__info-buy">
                <button class="car-detail__buy-btn">Купить</button>
            </div>
        </div>
    </div>

    <!-- ======= Характеристики ======= -->
    <section class="car-detail__specs-section" id="specs">
        <div class="car-detail__specs-title">Характеристики</div>
        <div class="car-detail__specs-content">
            <div class="specs-table-container">
                <table class="car-detail__specs-table">
                    <tr>
                        <td>Привод</td>
                        <td>${previewDriveTrain || "AWD"}</td>
                    </tr>
                    <tr>
                        <td>Двигатель</td>
                        <td>${previewEngine || "4л. / V8 twin-turbo"}</td>
                    </tr>
                    <tr>
                        <td>Мощность</td>
                        <td>${previewPower || "585 л.с."}</td>
                    </tr>
                    <tr>
                        <td>Трансмиссия</td>
                        <td>${previewTransmission || "8-скоростная АКПП"}</td>
                    </tr>
                    <tr>
                        <td>Цвет экстерьера</td>
                        <td>${
                          previewExteriorColor || "Sebring Black Crystal Effect"
                        }</td>
                    </tr>
                    <tr>
                        <td>Цвет интерьера</td>
                        <td>${colorsHTML || ""}</td>
                    </tr>
                </table>
            </div>
            ${specsGalleryHTML}
        </div>
    </section>

    <!-- ======= Лучшее в авто ======= -->
    <section class="car-detail__best-section" id="best">
        <div class="car-detail__best-title">Лучшее в авто</div>
        <div class="car-detail__best-columns">
            <div class="car-detail__best-column">
                <h4>${firstColumnTitle}</h4>
                <ul>
                    ${firstColumnHTML}
                </ul>
            </div>
            <div class="car-detail__best-column">
                <h4>${secondColumnTitle}</h4>
                <ul>
                    ${secondColumnHTML}
                </ul>
            </div>
        </div>
    </section>

    <!-- ======= Контакты ======= -->
    <section class="car-detail__contact-section">
        <div class="car-detail__contact-icon">→</div>
        <div class="car-detail__contact-label">Напишите нам</div>
        <div class="car-detail__contact-title">Остались вопросы?<br>Свяжитесь с нами!</div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer__info">
            <div class="footer__left">
                <div class="footer__logo"><img src="/PNG/SHMS_Logo.png" alt="SHMS" class="footer__logo-img"></div>
                <div class="footer__address">
                    Наш адрес:<br>
                    Москва,<br>
                    Пресненская набережная 12<br>
                    Башня "Федерация"<br>
                    12 этаж, офис К2
                </div>
            </div>
            <div class="footer__right">
                <nav class="footer__nav">
                    <a href="/order.html">Под заказ</a>
                    <a href="/contacts.html">Контакты</a>
                    <a href="/stock.html">Авто в наличии</a>
                </nav>
                <div class="footer__requisites">
                    <div class="footer__requisites-title">Реквизиты организации</div>
                    <div class="footer__requisites-info">
                        ИП Шамаев Мансур Махмудович<br>
                        ИНН 201578554480, ОГРН 324200000020490<br>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer__copyright">© ${new Date().getFullYear()} Все права защищены</div>
    </footer>

    <script src="/car-detail-carousel.js"></script>
    <script src="/menu.js"></script>
</body>

</html>
`;

    res.json({ success: true, html });
  } catch (error) {
    console.error("Ошибка при генерации предпросмотра:", error);
    res
      .status(500)
      .json({
        error: "Не удалось сгенерировать предпросмотр: " + error.message,
      });
  }
});

// Маршрут для генерации HTML страницы автомобиля
router.post("/car-details/:id/generate", (req, res) => {
  const id = req.params.id;
  const { outputPath } = req.body;

  // Логирование для отладки
  console.log(`Запрос на генерацию HTML для ID ${id}, путь: ${outputPath}`);

  try {
    // Получаем детальную информацию о машине
    db.get(
      `SELECT cd.*, sc.title as car_title, sc.subtitle as car_subtitle, sc.image_path as car_image,
                    sc.mileage as car_mileage, sc.interior_colors as car_interior_colors
             FROM car_details cd
             JOIN stock_cars sc ON cd.car_id = sc.id
             WHERE cd.id = ?`,
      [id],
      async (err, detail) => {
        if (err) {
          console.error("Ошибка при запросе в базу данных:", err);
          return res
            .status(500)
            .json({ error: "Ошибка базы данных: " + err.message });
        }

        if (!detail) {
          console.error("Детальная информация не найдена для ID:", id);
          return res
            .status(404)
            .json({ error: "Детальная информация не найдена" });
        }

        try {
          // Парсим JSON изображений
          let images = [];
          try {
            if (detail.images) {
              images =
                typeof detail.images === "string"
                  ? JSON.parse(detail.images)
                  : detail.images;
            }
          } catch (e) {
            console.error("Ошибка при парсинге JSON изображений:", e);
          }

          // Генерируем HTML с использованием нашего модуля
          const result = generateCarDetailHTML(detail, images, outputPath);

          // Сохраняем информацию о сгенерированной странице в базе данных
          // Сначала проверяем, существует ли уже запись для этого detail_id
          db.get(
            "SELECT id FROM page_html WHERE detail_id = ?",
            [id],
            (checkErr, row) => {
              if (checkErr) {
                console.error(
                  "Ошибка при проверке существования записи:",
                  checkErr
                );
              }

              // Определяем SQL запрос в зависимости от наличия существующей записи
              const sql = row
                ? "UPDATE page_html SET file_path = ?, generated_at = CURRENT_TIMESTAMP WHERE detail_id = ?"
                : "INSERT INTO page_html (file_path, detail_id) VALUES (?, ?)";

              db.run(sql, [result.filePath, id], function (err) {
                if (err) {
                  console.error(
                    "Ошибка при сохранении информации о странице:",
                    err
                  );
                } else {
                  console.log(
                    `Информация о странице ${result.filePath} успешно сохранена для ID ${id}`
                  );
                }

                // Обновляем поле html_page для обратной совместимости
                db.run(
                  `UPDATE car_details SET html_page = ? WHERE id = ?`,
                  [result.filePath, id],
                  function (updateErr) {
                    if (updateErr) {
                      console.error(
                        "Ошибка при сохранении пути к HTML:",
                        updateErr
                      );
                    }
                    // Возвращаем успех и путь к файлу
                    res.json(result);
                  }
                );
              });
            }
          );
        } catch (error) {
          console.error("Ошибка при генерации HTML страницы:", error);
          console.error("Стек ошибки:", error.stack);
          res
            .status(500)
            .json({
              error: "Не удалось сгенерировать HTML страницу: " + error.message,
            });
        }
      }
    );
  } catch (outerError) {
    console.error("Критическая ошибка в route handler:", outerError);
    console.error("Стек ошибки:", outerError.stack);
    res
      .status(500)
      .json({ error: "Критическая ошибка: " + outerError.message });
  }
});

// Получить список всех опубликованных страниц деталей автомобилей
router.get("/car-details/published", (req, res) => {
  db.all(
    `
        SELECT cd.id, cd.full_title as title, cd.car_id, cd.status, cd.created_at,
               COALESCE(ph.file_path, '') as filePath,
               COALESCE(cd.html_page, '') as htmlPage
        FROM car_details cd
        LEFT JOIN page_html ph ON cd.id = ph.detail_id
        WHERE cd.status = 'published' OR (cd.status IS NULL AND cd.html_page IS NOT NULL)
        ORDER BY cd.created_at DESC
    `,
    [],
    (err, pages) => {
      if (err) {
        console.error("Ошибка при получении опубликованных страниц:", err);
        return res
          .status(500)
          .json({ error: "Ошибка при получении опубликованных страниц" });
      }

      // Преобразуем пути файлов для веб-доступа
      pages.forEach((page) => {
        // Сначала проверяем filePath из таблицы page_html
        if (page.filePath) {
          page.filePath = "/" + page.filePath.replace(/\\/g, "/");
        }

        // Если нет filePath, то проверяем htmlPage (обратная совместимость)
        else if (page.htmlPage) {
          page.filePath = "/" + page.htmlPage.replace(/\\/g, "/");
        }
      });

      // Фильтруем только страницы с путями
      const pagesWithPaths = pages.filter(
        (page) => page.filePath && page.filePath !== "/"
      );
      res.json(pagesWithPaths);
    }
  );
});

module.exports = router;
