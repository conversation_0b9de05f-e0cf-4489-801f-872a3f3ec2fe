const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

console.log('🗑️  Сброс базы данных SHMS Auto');
console.log('===============================\n');

// Путь к базе данных
const dbPath = path.join(__dirname, 'data/stock.db');

// Удаляем старую базу данных
if (fs.existsSync(dbPath)) {
    console.log('🗑️  Удаление старой базы данных...');
    fs.unlinkSync(dbPath);
    console.log('✅ Старая база данных удалена');
} else {
    console.log('ℹ️  База данных не существует');
}

// Проверяем существование директории data
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
    console.log('📁 Создание директории data...');
    fs.mkdirSync(dataDir, { recursive: true });
}

// Создаем новую базу данных
console.log('🔧 Создание новой базы данных...');

const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('❌ Ошибка создания базы данных:', err.message);
        process.exit(1);
    }
    console.log('✅ Новая база данных создана\n');
});

// SQL запросы для создания таблиц
const createAdminsTable = `
CREATE TABLE IF NOT EXISTS admins (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100),
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME,
    reset_token VARCHAR(255),
    reset_token_expires DATETIME
);
`;

const createSecurityLogsTable = `
CREATE TABLE IF NOT EXISTS security_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    admin_id INTEGER,
    action VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    details TEXT,
    success BOOLEAN,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(id)
);
`;

const createLoginAttemptsTable = `
CREATE TABLE IF NOT EXISTS login_attempts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ip_address VARCHAR(45) NOT NULL,
    username VARCHAR(50),
    success BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
`;

// Функция для выполнения SQL запроса
function runQuery(sql, description) {
    return new Promise((resolve, reject) => {
        db.run(sql, (err) => {
            if (err) {
                console.error(`❌ Ошибка при ${description}:`, err.message);
                reject(err);
            } else {
                console.log(`✅ ${description} выполнено успешно`);
                resolve();
            }
        });
    });
}

// Основная функция
async function resetDatabase() {
    try {
        console.log('🔧 Создание таблиц...');
        
        await runQuery(createAdminsTable, 'создание таблицы admins');
        await runQuery(createSecurityLogsTable, 'создание таблицы security_logs');
        await runQuery(createLoginAttemptsTable, 'создание таблицы login_attempts');

        console.log('\n✅ База данных успешно создана!');
        console.log('📋 Созданные таблицы:');
        console.log('   - admins (администраторы)');
        console.log('   - security_logs (логи безопасности)');
        console.log('   - login_attempts (попытки входа)');
        
        console.log('\n🎉 Готово!');
        console.log('💡 Теперь создайте администратора: node create-admin.js');

    } catch (error) {
        console.error('\n❌ Ошибка при создании базы данных:', error.message);
        process.exit(1);
    } finally {
        db.close((err) => {
            if (err) {
                console.error('❌ Ошибка при закрытии базы данных:', err.message);
            } else {
                console.log('\n🔒 Соединение с базой данных закрыто');
            }
        });
    }
}

// Запуск
resetDatabase();
