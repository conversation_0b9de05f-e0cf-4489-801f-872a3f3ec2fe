// Функция для загрузки информации о детальных страницах
async function loadDetailPages() {
  try {
    const response = await fetch("/api/published");
    if (!response.ok) {
      console.warn("Не удалось загрузить детальные страницы");
      return [];
    }
    const pages = await response.json();
    console.log("Loaded detail pages:", pages);
    return pages;
  } catch (error) {
    console.error("Ошибка при загрузке детальных страниц:", error);
    return [];
  }
}

// Функция для загрузки избранных автомобилей
document.addEventListener("DOMContentLoaded", () => {
  loadFeaturedCars();
});

// Загрузка избранных автомобилей
async function loadFeaturedCars() {
  console.log("Загрузка избранных автомобилей...");
  const stockCardsContainer = document.querySelector(
    ".stock__cards, .stock-cards"
  );

  if (!stockCardsContainer) {
    console.error(
      "Контейнер для карточек автомобилей не найден! Проверьте наличие элемента с классом stock__cards или stock-cards"
    );
    return;
  }

  // Показываем сообщение о загрузке
  stockCardsContainer.innerHTML =
    '<div class="loading">Загрузка автомобилей...</div>';

  try {
    // Запрос к API для получения избранных автомобилей
    const response = await fetch("/api/featured");

    if (!response.ok) {
      throw new Error(`Ошибка HTTP: ${response.status}`);
    }

    const cars = await response.json();
    console.log("Получены избранные автомобили:", cars);

    // Загружаем информацию о детальных страницах
    const detailPages = await loadDetailPages();

    // Фильтруем только те автомобили, которые не помечены как проданные (sold)
    const availableCars = cars.filter((car) => !car.is_sold);

    // Добавляем ссылки на детальные страницы к карточкам автомобилей
    const carsWithDetails = availableCars.map((car) => {
      const detailPage = detailPages.find((page) => page.car_id === car.id);
      return {
        ...car,
        detailPage: detailPage ? detailPage.filePath : null,
      };
    });

    // Очистка контейнера
    stockCardsContainer.innerHTML = "";

    // Если автомобилей нет, показываем заглушку
    if (carsWithDetails.length === 0) {
      stockCardsContainer.innerHTML =
        '<div class="no-cars">Автомобили в наличии отсутствуют</div>';
      return;
    }

    // Ограничиваем количество отображаемых автомобилей (например, первые 4)
    const carsToShow = carsWithDetails.slice(0, 4);

    // Отображаем автомобили
    carsToShow.forEach((car) => {
      try {
        // Создаем HTML для карточки автомобиля
        const carCard = createCarCard(car);
        if (stockCardsContainer) {
          stockCardsContainer.appendChild(carCard);

          // Оптимизация изображений для разных моделей
          optimizeCarImage(carCard, car.title || car.mark + " " + car.model);
        } else {
          console.error("Контейнер для карточек исчез после начала загрузки!");
        }
      } catch (cardError) {
        console.error("Ошибка при создании карточки:", cardError);
      }
    });
  } catch (error) {
    console.error("Ошибка при загрузке избранных автомобилей:", error);

    // В случае ошибки загрузки показываем сообщение об ошибке
    if (stockCardsContainer) {
      stockCardsContainer.innerHTML =
        '<div class="error">Не удалось загрузить автомобили. Пожалуйста, повторите попытку позже. <br><br>Ошибка: ' +
        error.message +
        "</div>";
    }
  }
}

// Создание карточки автомобиля из данных API
function createCarCard(car) {
  if (!car) {
    console.error("Попытка создать карточку для пустых данных");
    return document.createElement("div"); // Возвращаем пустой div чтобы избежать null
  }

  const defaultImage = "/assets/img/cars/default-car.jpg";
  const safeGetValue = (value, defaultVal = "") => (value ? value : defaultVal);

  const card = document.createElement("div");
  card.className = "car-card";

  // Проверяем, есть ли детальная страница для этого автомобиля
  const detailPage = car.detailPage || null;

  // Если есть детальная страница, делаем карточку кликабельной
  if (detailPage) {
    card.style.cursor = "pointer";
    card.addEventListener("click", () => {
      window.location.href = detailPage;
    });
  }

  card.innerHTML = `
        <div class="car-card__header">
          <div>
            <div class="car-card__title">${safeGetValue(
              car.title || (car.mark ? car.mark + " " + car.model : "")
            )}</div>
            <div class="car-card__subtitle">${safeGetValue(
              car.subtitle ||
                (car.year
                  ? car.year + (car.engine_type ? " • " + car.engine_type : "")
                  : "")
            )}</div>
          </div>
        </div>
        <div class="car-card__image">
          <img src="${safeGetValue(
            car.image_path,
            defaultImage
          )}" alt="${safeGetValue(
    car.title || (car.mark ? car.mark + " " + car.model : ""),
    "Автомобиль"
  )}" onerror="this.src='${defaultImage}'">
        </div>
        <div class="car-card__specs-row">
          <div class="car-card__spec">
            <span class="car-card__spec-main">${safeGetValue(car.power)}</span>
            <div class="car-card__spec-label">Мощность</div>
          </div>
          <div class="car-card__spec-divider"></div>
          <div class="car-card__spec">
            <span class="car-card__spec-main">${safeGetValue(
              car.mileage
            )}</span>
            <div class="car-card__spec-label">Пробег</div>
          </div>
        </div>
        <div class="car-card__info">
          <div class="car-card__info-list">
            <div>Тип кузова <b>${safeGetValue(car.body_type)}</b></div>
            <div>Двигатель <b>${safeGetValue(car.engine)}</b></div>
            <div>Трансмиссия <b>${safeGetValue(car.transmission)}</b></div>
            <div>Цвета интерьера ${formatInteriorColors(
              car.interior_colors
            )}</div>
            <div>Расход <b>${safeGetValue(car.consumption)}</b></div>
            <div>Вместимость <b>${safeGetValue(car.capacity)}</b></div>
          </div>
          <div class="car-card__price-block">
            <div class="car-card__price">${formatPrice(
              safeGetValue(car.price)
            )}</div>
            <div class="car-card__in-stock">В наличии в Москве</div>
          </div>
        </div>
    `;
  return card;
}

function formatInteriorColors(colors) {
  if (!colors) return "";
  try {
    const colorObjects =
      typeof colors === "string" ? JSON.parse(colors) : colors;
    if (Array.isArray(colorObjects)) {
      let dotsHtml = '<div class="color-dots-container">';
      colorObjects.forEach((color) => {
        if (color.hex) {
          dotsHtml += `<span class="dot" style="background-color: ${color.hex};" title="${color.name}"></span>`;
        }
      });
      dotsHtml += "</div>";
      return dotsHtml;
    }
  } catch (e) {
    // Старый формат (через запятую)
    return String(colors)
      .split(",")
      .map((color) => {
        const colorClass = color.trim().toLowerCase();
        return `<span class="dot dot--${colorClass}"></span>`;
      })
      .join("");
  }
}

function formatPrice(price) {
  if (!price) return "";

  // Для главной страницы цены уже в долларах
  const numericPrice = String(price).replace(/[^\d.]/g, "");
  return `$${Number(numericPrice).toLocaleString("en-US", {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  })}`;
}

// Функция для оптимизации отображения изображений автомобилей
function optimizeCarImage(cardElement, carTitle) {
  const imgElement = cardElement.querySelector(".car-card__image img");
  if (!imgElement) return;

  // Оставляем стили как есть в CSS - не переопределяем
}

// Демо-данные для машин (замените на реальные при необходимости)
const cars = [
  {
    class: "business",
    name: "Mercedes-Benz S-Class",
    img: "/Img/maibax.png",
    link: "/stock/mercedes-s-class",
  },
  {
    class: "business",
    name: "BMW 7 Series",
    img: "/Img/bmw7.png",
    link: "/order/bmw-7-series",
  },
  {
    class: "sport",
    name: "Porsche 911",
    img: "/Img/porsche911.png",
    link: "/stock/porsche-911",
  },
  {
    class: "sport",
    name: "Lamborghini Huracan",
    img: "/Img/huracan.png",
    link: "/order/lamborghini-huracan",
  },
  { class: "suv", name: "BMW X5", img: "/Img/bmw.jpg", link: "/stock/bmw-x5" },
  {
    class: "suv",
    name: "Mercedes-Benz GLE",
    img: "/Img/gle.png",
    link: "/order/mercedes-gle",
  },
];

const classTitles = {
  business: "Бизнес-класс",
  sport: "Спортивные Авто",
  suv: "SUV",
};

// --- Категории и ключевые слова для фильтрации ---
const classKeywords = {
  business: [
    "S-Class",
    "7 Series",
    "Maybach",
    "A8",
    "Flying Spur",
    "EQS",
    "LS",
  ],
  sport: [
    "911",
    "Huracan",
    "AMG GT",
    "M4",
    "Roma",
    "RS6",
    "Supra",
    "Corvette",
    "Cayman",
    "F8",
    "GTR",
  ],
  suv: [
    "X5",
    "GLE",
    "Q7",
    "Cayenne",
    "Urus",
    "Range Rover",
    "GLS",
    "G63",
    "Defender",
    "X7",
  ],
};

// --- Массив "под заказ" (название и ссылка) ---
const orderCars = [
  { name: "Ferrari Roma", link: "/order/ferrari-roma" },
  { name: "Lamborghini Huracan Technica", link: "/order/lamborghini-huracan" },
  { name: "Bentley Flying Spur", link: "/order/bentley-flying-spur" },
  { name: "Aston Martin DB12", link: "/order/aston-martin-db12" },
  { name: "BMW M4 Competition", link: "/order/bmw-m4-competition" },
  { name: "Range Rover Vogue", link: "/order/range-rover-vogue" },
  { name: "Porsche 911 Turbo S", link: "/order/porsche-911-turbo-s" },
  {
    name: "Mercedes-Benz GLS 600 Maybach",
    link: "/order/mercedes-gls-maybach",
  },
];

// --- Функция фильтрации по ключевым словам ---
function carMatchesClass(title, carClass) {
  if (!title) return false;
  const keywords = classKeywords[carClass] || [];
  return keywords.some((word) =>
    title.toLowerCase().includes(word.toLowerCase())
  );
}

// --- Модалка по категориям ---
document.addEventListener("DOMContentLoaded", function () {
  const modal = document.getElementById("carClassModal");
  const modalTitle = document.getElementById("carClassModalTitle");
  const modalList = document.getElementById("carClassModalList");
  const closeBtn = document.getElementById("carClassModalClose");
  const cardBtns = document.querySelectorAll(".category[data-class]");

  // --- Грузим "в наличии" через API один раз ---
  let stockCars = [];
  Promise.all([fetch("/api/featured").then((r) => r.json()), loadDetailPages()])
    .then(([cars, detailPages]) => {
      // Добавляем ссылки на детальные страницы к автомобилям
      stockCars = cars.map((car) => {
        const detailPage = detailPages.find((page) => page.car_id === car.id);
        return {
          ...car,
          detailPage: detailPage ? detailPage.filePath : null,
        };
      });
    })
    .catch(() => {
      stockCars = [];
    });

  cardBtns.forEach((btn) => {
    btn.addEventListener("click", function () {
      const carClass = btn.getAttribute("data-class");
      // Фильтруем "в наличии"
      const stockFiltered = (stockCars || []).filter((car) =>
        carMatchesClass(car.title, carClass)
      );
      // Фильтруем "под заказ"
      const orderFiltered = orderCars.filter((car) =>
        carMatchesClass(car.name, carClass)
      );
      // Формируем HTML
      let html = "";
      html += `<div style="font-weight:700;font-size:1.1em;margin-bottom:0.7em;">В наличии:</div>`;
      if (stockFiltered.length) {
        html += stockFiltered
          .map((car) => {
            const linkHref = car.detailPage || "stock.html";
            return `
          <div class="car-class-modal__item">
            <img class="car-class-modal__item-img" src="${
              car.image_path || "/assets/img/cars/default-car.jpg"
            }" alt="${car.title}">
            <a class="car-class-modal__item-link" href="${linkHref}">${
              car.title
            }</a>
          </div>
        `;
          })
          .join("");
      } else {
        html +=
          '<div style="color:#888;margin-bottom:1em;">Нет машин в наличии</div>';
      }
      html += `<div style="font-weight:700;font-size:1.1em;margin:1.2em 0 0.7em 0;">Под заказ:</div>`;
      if (orderFiltered.length) {
        html += orderFiltered
          .map(
            (car) => `
          <div class="car-class-modal__item">
            <a class="car-class-modal__item-link" href="${car.link}">${car.name}</a>
          </div>
        `
          )
          .join("");
      } else {
        html += '<div style="color:#888;">Нет машин под заказ</div>';
      }
      modalTitle.textContent =
        btn.querySelector(".category__title").textContent;
      modalList.innerHTML = html;
      modal.style.display = "flex";
      setTimeout(() => {
        modal.classList.add("active");
      }, 10);
    });
  });

  closeBtn.addEventListener("click", function () {
    modal.style.display = "none";
    modal.classList.remove("active");
  });
  modal.addEventListener("click", function (e) {
    if (e.target === modal) {
      modal.style.display = "none";
      modal.classList.remove("active");
    }
  });
});

// --- Popup-поиск только для index.html ---
document.addEventListener("DOMContentLoaded", function () {
  const searchForm = document.querySelector(".search__form");
  const searchInput = document.querySelector(".search__input");
  const searchPopup = document.getElementById("search-popup");
  // Проверяем, что popup есть только на главной
  if (!searchForm || !searchInput || !searchPopup) return;
  let stockCars = [];
  // Массив orderCars должен быть определён выше в вашем коде!

  // Грузим "в наличии" через API один раз
  fetch("/api/featured")
    .then((r) => r.json())
    .then((data) => {
      stockCars = data;
    })
    .catch(() => {
      stockCars = [];
    });

  function renderPopupResults(query) {
    if (!query) {
      searchPopup.style.display = "none";
      searchPopup.innerHTML = "";
      return;
    }
    // Фильтруем "в наличии"
    const stockFiltered = (stockCars || []).filter(
      (car) =>
        (car.title || "").toLowerCase().includes(query) ||
        (car.mark || "").toLowerCase().includes(query) ||
        (car.model || "").toLowerCase().includes(query)
    );
    // Фильтруем "под заказ"
    const orderFiltered = (
      typeof orderCars !== "undefined" ? orderCars : []
    ).filter((car) => (car.name || "").toLowerCase().includes(query));
    // Формируем HTML
    let html =
      '<div style="background:#fff;border-radius:12px;box-shadow:0 4px 24px rgba(0,0,0,0.13);padding:18px 18px 10px 18px;max-height:350px;overflow-y:auto;">';
    html += `<div style=\"font-weight:700;font-size:1.1em;margin-bottom:0.7em;\">В наличии:</div>`;
    if (stockFiltered.length) {
      html += stockFiltered
        .map((car) => {
          const defaultImage = "/Img/default-car.jpg";
          return `<div style=\"display:flex;align-items:center;gap:12px;margin-bottom:10px;\"><img src=\"${
            car.image_path || defaultImage
          }\" alt=\"${
            car.title || car.mark + " " + car.model
          }\" style=\"width:54px;height:36px;object-fit:cover;border-radius:6px;\"><span>${
            car.title || car.mark + " " + car.model
          }</span></div>`;
        })
        .join("");
    } else {
      html +=
        '<div style="color:#888;margin-bottom:1em;">Нет машин в наличии</div>';
    }
    html += `<div style=\"font-weight:700;font-size:1.1em;margin:1.2em 0 0.7em 0;\">Под заказ:</div>`;
    if (orderFiltered.length) {
      html += orderFiltered
        .map(
          (car) =>
            `<div style=\"margin-bottom:10px;\"><a href=\"${car.link}\" target=\"_blank\" style=\"color:#1a4d6a;text-decoration:none;\">${car.name}</a></div>`
        )
        .join("");
    } else {
      html += '<div style="color:#888;">Нет машин под заказ</div>';
    }
    html += "</div>";
    searchPopup.innerHTML = html;
    searchPopup.style.display = "block";
  }

  searchInput.addEventListener("input", function () {
    const query = searchInput.value.trim().toLowerCase();
    renderPopupResults(query);
  });
  searchInput.addEventListener("focus", function () {
    const query = searchInput.value.trim().toLowerCase();
    renderPopupResults(query);
  });
  searchInput.addEventListener("blur", function () {
    setTimeout(() => {
      searchPopup.style.display = "none";
    }, 180); // Даем время кликнуть по ссылке
  });
  // Отключаем submit формы и обработку Enter
  searchForm.addEventListener("submit", function (e) {
    e.preventDefault();
    const query = searchInput.value.trim().toLowerCase();
    renderPopupResults(query);
  });
  // По Enter в input
  searchInput.addEventListener("keydown", function (e) {
    if (e.key === "Enter") {
      e.preventDefault();
      const query = searchInput.value.trim().toLowerCase();
      renderPopupResults(query);
    }
  });
  // По клику на кнопку поиска
  const searchBtn = searchForm.querySelector(".search__btn");
  if (searchBtn) {
    searchBtn.addEventListener("click", function (e) {
      e.preventDefault();
      searchInput.focus();
      const query = searchInput.value.trim().toLowerCase();
      renderPopupResults(query);
    });
  }
});
