document.addEventListener("DOMContentLoaded", () => {
  const carForm = document.getElementById("car-form");
  const carsContainer = document.getElementById("cars-container");
  const submitBtn = document.getElementById("submit-btn");
  const cancelBtn = document.getElementById("cancel-btn");
  const currentImage = document.getElementById("current-image");
  const searchInput = document.getElementById("search-input");
  const searchBtn = document.getElementById("search-btn");
  const resetSearchBtn = document.getElementById("reset-search-btn");
  const sortSelect = document.getElementById("sort-select");
  const importForm = document.getElementById("import-form");
  const colorPicker = document.getElementById("color-picker");
  const colorNameInput = document.getElementById("color-name");
  const addColorBtn = document.getElementById("add-color-btn");
  const selectedColorsList = document.getElementById("selected-colors-list");
  const interiorColorsInput = document.getElementById("interior_colors");
  const toggleViewBtn = document.getElementById("toggle-view-btn");

  // Состояние вида (по умолчанию табличный)
  let isTableView = true;

  // Инициализация вида
  initializeView();

  // Обработчик переключения вида
  toggleViewBtn.addEventListener("click", () => {
    isTableView = !isTableView;
    updateView();
    // Сохраняем предпочтение в localStorage
    localStorage.setItem("adminTableView", isTableView);
  });

  // Инициализация вида из localStorage
  function initializeView() {
    const savedView = localStorage.getItem("adminTableView");
    if (savedView !== null) {
      isTableView = savedView === "true";
    }
    updateView();
  }

  // Обновление вида
  function updateView() {
    const adminContainer = document.querySelector(".admin-container");

    if (isTableView) {
      adminContainer.classList.add("cars-table-view");
      toggleViewBtn.innerHTML = '<i class="fas fa-th"></i> Сетка';
      toggleViewBtn.title = "Переключить на вид сетки";
    } else {
      adminContainer.classList.remove("cars-table-view");
      toggleViewBtn.innerHTML = '<i class="fas fa-list"></i> Список';
      toggleViewBtn.title = "Переключить на табличный вид";
    }
  }

  // Создаем и добавляем кнопку "Fix All Colors"
  const fixAllColorsBtn = document.createElement("button");
  fixAllColorsBtn.id = "fix-all-colors-btn";
  fixAllColorsBtn.textContent = "Исправить все цвета";
  fixAllColorsBtn.className = "fix-all-btn";
  document.querySelector(".admin-section").appendChild(fixAllColorsBtn);

  // Обработчик для кнопки исправления цветов
  fixAllColorsBtn.addEventListener("click", async () => {
    if (
      !confirm(
        "Это действие преобразует все цвета в новый формат JSON. Продолжить?"
      )
    )
      return;

    try {
      const response = await fetch("/api/fix-colors", {
        method: "POST",
      });

      const result = await response.json();

      if (response.ok) {
        alert(`Успешно обновлено ${result.count} автомобилей`);
        loadCars();
      } else {
        alert(result.error || "Ошибка при обновлении цветов");
      }
    } catch (error) {
      console.error("Ошибка при обновлении цветов:", error);
      alert("Ошибка при обновлении цветов: " + error.message);
    }
  });

  let isEditing = false;
  let allCars = []; // Храним все полученные карточки
  let selectedColors = []; // Массив для хранения выбранных цветов

  // Инициализация обработчиков цветов
  initColorPicker();

  // Инициализация выбора цветов
  function initColorPicker() {
    // Обработчик кнопки добавления цвета
    addColorBtn.addEventListener("click", addSelectedColor);

    // Обновляем скрытое поле при изменении цветов
    updateInteriorColorsValue();
  }

  // Добавление выбранного цвета
  function addSelectedColor() {
    const colorHex = colorPicker.value;
    const colorName = colorNameInput.value.trim() || colorHex;

    // Создаем уникальный идентификатор для цвета
    const colorId = "color-" + Date.now();

    // Добавляем цвет в массив выбранных цветов
    selectedColors.push({
      id: colorId,
      hex: colorHex,
      name: colorName,
    });

    // Обновляем отображение выбранных цветов и скрытое поле
    renderSelectedColors();
    updateInteriorColorsValue();

    // Очищаем поле названия цвета
    colorNameInput.value = "";
  }

  // Удаление выбранного цвета
  function removeSelectedColor(colorId) {
    // Удаляем цвет из массива
    selectedColors = selectedColors.filter((color) => color.id !== colorId);

    // Обновляем отображение и скрытое поле
    renderSelectedColors();
    updateInteriorColorsValue();
  }

  // Отображение выбранных цветов
  function renderSelectedColors() {
    selectedColorsList.innerHTML = "";

    if (selectedColors.length === 0) {
      selectedColorsList.innerHTML =
        '<div class="no-colors">Цвета не выбраны</div>';
      return;
    }

    selectedColors.forEach((color) => {
      const colorEl = document.createElement("div");
      colorEl.className = "color-preview";
      colorEl.style.backgroundColor = color.hex;

      // Определяем контрастный цвет для текста
      const textColor = getContrastColor(color.hex);

      colorEl.innerHTML = `
                <div class="color-preview-swatch"></div>
                <div class="color-preview-name" style="color: ${textColor}">${color.name}</div>
                <button type="button" class="color-preview-remove" data-id="${color.id}">&times;</button>
            `;

      // Добавляем обработчик удаления
      const removeBtn = colorEl.querySelector(".color-preview-remove");
      removeBtn.addEventListener("click", () => removeSelectedColor(color.id));

      selectedColorsList.appendChild(colorEl);
    });
  }

  // Получение контрастного цвета для текста
  function getContrastColor(hexColor) {
    // Преобразуем HEX в RGB
    const r = parseInt(hexColor.slice(1, 3), 16);
    const g = parseInt(hexColor.slice(3, 5), 16);
    const b = parseInt(hexColor.slice(5, 7), 16);

    // Вычисляем яркость цвета
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;

    // Возвращаем белый для темных цветов и черный для светлых
    return brightness < 128 ? "#ffffff" : "#000000";
  }

  // Обновление скрытого поля со значениями выбранных цветов
  function updateInteriorColorsValue() {
    // Форматируем значение как JSON строку с цветами
    const colorsValue = selectedColors.map((color) => ({
      hex: color.hex,
      name: color.name,
    }));

    interiorColorsInput.value = JSON.stringify(colorsValue);
  }

  // Установка выбранных цветов при редактировании
  function setSelectedColors(colorsValue) {
    // Очищаем текущие выбранные цвета
    selectedColors = [];

    if (!colorsValue) {
      renderSelectedColors();
      return;
    }

    try {
      // Пытаемся распарсить JSON строку
      const parsed = JSON.parse(colorsValue);
      if (Array.isArray(parsed)) {
        // Новый формат - массив объектов с hex и name
        selectedColors = parsed.map((color) => ({
          id: "color-" + Date.now() + Math.random().toString(36).substr(2, 5),
          hex: color.hex,
          name: color.name,
        }));
      } else {
        throw new Error("Invalid format");
      }
    } catch (e) {
      // Если не удалось распарсить JSON, пробуем старые форматы
      if (colorsValue.includes("dot--") || colorsValue.includes("dot dot-")) {
        // Конвертируем старый формат (dot--color или dot dot-color)
        let colorNames = [];

        if (colorsValue.includes("dot dot-")) {
          // Формат "dot dot-color, dot dot-color2"
          const colorTexts = colorsValue.split(", ");
          colorNames = colorTexts.map((text) =>
            text.replace("dot dot-", "").trim()
          );
        } else {
          // Формат "dot--color,dot--color2"
          colorNames = colorsValue
            .split(",")
            .map((cls) => cls.replace("dot--", "").trim());
        }

        // Конвертируем имена цветов в HEX-значения
        selectedColors = colorNames.map((name) => {
          let hex = "#000000"; // Черный по умолчанию

          // Преобразуем название цвета в HEX
          switch (name.toLowerCase()) {
            case "black":
              hex = "#000000";
              break;
            case "white":
              hex = "#ffffff";
              break;
            case "orange":
              hex = "#ff7700";
              break;
            case "red":
              hex = "#d20000";
              break;
            case "blue":
              hex = "#0066cc";
              break;
            case "brown":
              hex = "#8B4513";
              break;
            case "beige":
              hex = "#F5F5DC";
              break;
            case "gray":
              hex = "#808080";
              break;
            default:
              hex =
                "#" +
                Math.floor(Math.random() * 16777215)
                  .toString(16)
                  .padStart(6, "0");
          }

          return {
            id: "color-" + Date.now() + Math.random().toString(36).substr(2, 5),
            hex: hex,
            name: name,
          };
        });
      }
    }

    renderSelectedColors();
    updateInteriorColorsValue();
  }

  // Отображение цветов в списке карточек
  function renderColorDots(colorsValue) {
    if (!colorsValue) return "";

    try {
      // Пытаемся распарсить JSON строку
      const colors = JSON.parse(colorsValue);
      let dotsHtml = '<div class="color-dots-container">';

      colors.forEach((color) => {
        dotsHtml += `<span class="color-dot" style="background-color: ${color.hex};" title="${color.name}"></span>`;
      });

      dotsHtml += "</div>";
      dotsHtml += `<div class="color-codes">${colors
        .map((c) => c.name)
        .join(", ")}</div>`;
      return dotsHtml;
    } catch (e) {
      // Если не удалось распарсить JSON, пробуем старые форматы
      if (colorsValue.includes("dot--") || colorsValue.includes("dot dot-")) {
        // Конвертируем старый формат (dot--color или dot dot-color)
        let colorNames = [];

        if (colorsValue.includes("dot dot-")) {
          // Формат "dot dot-color, dot dot-color2"
          const colorTexts = colorsValue.split(", ");
          colorNames = colorTexts.map((text) =>
            text.replace("dot dot-", "").trim()
          );
        } else {
          // Формат "dot--color,dot--color2"
          colorNames = colorsValue
            .split(",")
            .map((cls) => cls.replace("dot--", "").trim());
        }

        let dotsHtml = '<div class="color-dots-container">';

        colorNames.forEach((name) => {
          let hexColor = "#000000"; // Черный по умолчанию

          // Преобразуем название цвета в HEX
          switch (name.toLowerCase()) {
            case "black":
              hexColor = "#000000";
              break;
            case "white":
              hexColor = "#ffffff";
              break;
            case "orange":
              hexColor = "#ff7700";
              break;
            case "red":
              hexColor = "#d20000";
              break;
            case "blue":
              hexColor = "#0066cc";
              break;
            case "brown":
              hexColor = "#8B4513";
              break;
            case "beige":
              hexColor = "#F5F5DC";
              break;
            case "gray":
              hexColor = "#808080";
              break;
          }

          dotsHtml += `<span class="color-dot" style="background-color: ${hexColor};" title="${name}"></span>`;
        });

        dotsHtml += "</div>";
        dotsHtml += `<div class="color-codes">${colorNames.join(", ")}</div>`;
        return dotsHtml;
      }

      return colorsValue; // В крайнем случае выводим как есть
    }
  }

  // Загрузка карточек при старте
  loadCars();

  // Обработчик формы импорта
  importForm.addEventListener("submit", async (e) => {
    e.preventDefault();
    const htmlPath = document.getElementById("html-path").value.trim();

    if (!htmlPath) {
      alert("Введите путь к HTML файлу");
      return;
    }

    if (
      confirm(`Вы уверены, что хотите импортировать данные из ${htmlPath}?`)
    ) {
      try {
        const response = await fetch("/api/import", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ path: htmlPath }),
        });

        const result = await response.json();

        if (response.ok) {
          alert(`Импорт успешно завершен, добавлено ${result.count} элементов`);
          loadCars();
        } else {
          alert(result.error || "Ошибка при импорте");
        }
      } catch (error) {
        alert("Ошибка при импорте: " + error.message);
      }
    }
  });

  // Поиск
  searchBtn.addEventListener("click", () => {
    filterAndSortCars();
  });

  // Сброс поиска
  resetSearchBtn.addEventListener("click", () => {
    searchInput.value = "";
    filterAndSortCars();
  });

  // Сортировка
  sortSelect.addEventListener("change", () => {
    filterAndSortCars();
  });

  // Обработка формы
  carForm.addEventListener("submit", async (e) => {
    e.preventDefault();

    // Обновляем значение цветов перед отправкой
    updateInteriorColorsValue();

    // Проверяем, выбран ли хотя бы один цвет
    if (selectedColors.length === 0) {
      alert("Выберите хотя бы один цвет интерьера");
      return;
    }

    const formData = new FormData(carForm);
    const carId = document.getElementById("car-id").value;
    const url = isEditing ? `/api/cars/${carId}` : "/api/cars";
    const method = isEditing ? "PUT" : "POST";

    try {
      const response = await fetch(url, {
        method,
        body: formData,
      });
      const data = await response.json();
      if (response.ok) {
        carForm.reset();
        resetForm();
        loadCars();
        alert(isEditing ? "Карточка обновлена" : "Карточка добавлена");
      } else {
        alert(data.error || "Ошибка");
      }
    } catch (error) {
      alert("Ошибка при сохранении");
    }
  });

  // Кнопка отмены
  cancelBtn.addEventListener("click", () => {
    resetForm();
  });

  // Загрузка карточек
  async function loadCars() {
    try {
      const searchTerm = searchInput.value.trim();
      const sortValue = sortSelect.value;

      let url = "/api/cars";
      const params = new URLSearchParams();

      if (searchTerm) {
        params.append("search", searchTerm);
      }

      if (sortValue) {
        params.append("sort", sortValue);
      }

      if (params.toString()) {
        url += "?" + params.toString();
      }

      const response = await fetch(url);
      allCars = await response.json();
      displayCars(allCars);
    } catch (error) {
      carsContainer.innerHTML = "<div>Ошибка загрузки</div>";
    }
  }

  // Фильтрация и сортировка карточек
  function filterAndSortCars() {
    // Вместо локальной фильтрации и сортировки используем загрузку с сервера
    loadCars();
  }

  // Отображение списка автомобилей
  function displayCars(cars) {
    if (!carsContainer) return;

    if (cars.length === 0) {
      carsContainer.innerHTML = `
                <div class="no-results">
                    <h3>Автомобили не найдены</h3>
                    <p>Попробуйте изменить параметры поиска или добавьте новый автомобиль</p>
                </div>
            `;
      return;
    }

    carsContainer.innerHTML = "";

    cars.forEach((car) => {
      const carElement = createCarElement(car);
      carsContainer.appendChild(carElement);
    });
  }

  // Создание элемента автомобиля
  function createCarElement(car) {
    const carCard = document.createElement("div");
    carCard.className = "car-card";
    carCard.dataset.id = car.id;

    const imagePath =
      car.image_path && car.image_path.startsWith("/")
        ? car.image_path
        : `/${car.image_path || "uploads/default-car.jpg"}`;

    carCard.innerHTML = `
            <div class="car-card-image">
                <img src="${imagePath}" alt="${
      car.title
    }" onerror="this.src='/uploads/default-car.jpg'">
            </div>
            <div class="car-card-content">
                <div class="car-card-info">
                    <div class="car-card-title">${car.title}</div>
                    <div class="car-card-subtitle">${car.subtitle || ""}</div>
                </div>
                <div class="car-card-price">${car.price || ""}</div>

                <div class="car-card-specs">
                    <div class="spec-item">
                        <span class="spec-label">Мощность:</span>
                        <span class="spec-value">${car.power || "-"}</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Пробег:</span>
                        <span class="spec-value">${car.mileage || "-"}</span>
                    </div>
                </div>

                <div class="car-card-actions">
                    <button class="edit-btn" data-id="${
                      car.id
                    }">Редактировать</button>
                    <button class="delete-btn" data-id="${
                      car.id
                    }">Удалить</button>
                    <button class="copy-btn" data-id="${
                      car.id
                    }">Копировать</button>
                    <button class="detail-btn" data-id="${
                      car.id
                    }">Создать детальную страницу</button>
                </div>
            </div>
        `;

    // Добавляем обработчики событий
    carCard
      .querySelector(".edit-btn")
      .addEventListener("click", () => editCar(car.id));
    carCard
      .querySelector(".delete-btn")
      .addEventListener("click", () => deleteCar(car.id, carCard));
    carCard
      .querySelector(".copy-btn")
      .addEventListener("click", () => copyCarData(car));
    carCard
      .querySelector(".detail-btn")
      .addEventListener("click", () => createDetailPage(car.id));

    return carCard;
  }

  // Копирование данных карточки
  function copyCarData(car) {
    let colorInfo = "Неизвестные цвета";

    try {
      const colors = JSON.parse(car.interior_colors);
      colorInfo = colors.map((c) => `${c.name} (${c.hex})`).join(", ");
    } catch (e) {
      colorInfo = car.interior_colors;
    }

    const carData = `
Название: ${car.title}
Подзаголовок: ${car.subtitle}
Мощность: ${car.power}
Пробег: ${car.mileage}
Тип кузова: ${car.body_type}
Двигатель: ${car.engine}
Трансмиссия: ${car.transmission}
Цвета интерьера: ${colorInfo}
Расход: ${car.consumption}
Вместимость: ${car.capacity}
Цена: ${car.price}
        `;

    navigator.clipboard
      .writeText(carData.trim())
      .then(() => {
        alert("Данные карточки скопированы в буфер обмена");
      })
      .catch((err) => {
        console.error("Ошибка при копировании: ", err);
        alert("Не удалось скопировать данные");
      });
  }

  // Редактирование
  async function editCar(id) {
    try {
      const response = await fetch(`/api/cars/${id}`);
      const car = await response.json();
      if (response.ok) {
        document.getElementById("car-id").value = car.id;
        document.getElementById("title").value = car.title;
        document.getElementById("subtitle").value = car.subtitle;
        document.getElementById("power").value = car.power;
        document.getElementById("mileage").value = car.mileage;
        document.getElementById("body_type").value = car.body_type;
        document.getElementById("engine").value = car.engine;
        document.getElementById("transmission").value = car.transmission;
        setSelectedColors(car.interior_colors);
        document.getElementById("consumption").value = car.consumption;
        document.getElementById("capacity").value = car.capacity;
        document.getElementById("price").value = car.price;
        currentImage.innerHTML = `<img src="${car.image_path}" alt="Фото">`;
        submitBtn.textContent = "Сохранить";
        cancelBtn.style.display = "block";
        isEditing = true;
      } else {
        alert("Ошибка загрузки данных");
      }
    } catch (error) {
      alert("Ошибка загрузки данных");
    }
  }

  // Удаление
  async function deleteCar(id, div) {
    if (!confirm("Удалить карточку?")) return;
    try {
      const response = await fetch(`/api/cars/${id}`, {
        method: "DELETE",
      });
      if (response.ok) {
        div.remove();
        // Обновляем список allCars
        allCars = allCars.filter((car) => car.id !== id);
      } else {
        alert("Ошибка удаления");
      }
    } catch (error) {
      alert("Ошибка удаления");
    }
  }

  // Создание детальной страницы
  function createDetailPage(carId) {
    // Перенаправляем на упрощенный редактор с предзаполненным ID автомобиля
    window.location.href = `car-detail-simple.html?car=${carId}`;
  }

  // Сброс формы
  function resetForm() {
    carForm.reset();
    document.getElementById("car-id").value = "";
    currentImage.innerHTML = "";
    submitBtn.textContent = "Добавить";
    cancelBtn.style.display = "none";
    isEditing = false;

    // Очищаем выбранные цвета
    selectedColors = [];
    renderSelectedColors();
    updateInteriorColorsValue();
  }
});
