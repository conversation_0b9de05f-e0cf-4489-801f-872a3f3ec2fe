// Список премиальных автомобилей для поиска
const premiumCars = [
  {
    brand: "Ferrari",
    model: "Roma",
    year: 2024,
  },
  {
    brand: "Lamborghini",
    model: "Huracan",
    year: 2024,
  },
  {
    brand: "Bentley",
    model: "Flying Spur",
    year: 2024,
  },
  {
    brand: "Aston Martin",
    model: "DB12",
    year: 2024,
  },
  {
    brand: "BMW",
    model: "M5",
    year: 2024,
  },
  {
    brand: "Audi",
    model: "RS7",
    year: 2024,
  },
  {
    brand: "Alfa Romeo",
    model: "Giulia Quadrifoglio",
    year: 2024,
  },
];

// Функция для загрузки данных о премиальных автомобилях
async function loadPremiumCars() {
  console.log("Загрузка данных о премиальных автомобилях...");

  const cars = [];

  // Проверяем доступность прокси
  try {
    const proxyCheckResponse = await fetch("/api/encar?check=1");
    if (!proxyCheckResponse.ok) {
      throw new Error("Прокси-сервер недоступен");
    }
  } catch (error) {
    console.error("Ошибка при проверке прокси:", error);
    return getFallbackPremiumCars();
  }

  // Загружаем данные для каждого автомобиля
  for (const car of premiumCars) {
    try {
      // Пытаемся найти в активных объявлениях
      let carData = await fetchEncarData("active_offer", car.brand, car.model);

      // Если не нашли в активных, ищем в снятых с продажи
      if (!carData || carData.length === 0) {
        carData = await fetchEncarData("inactive_offer", car.brand, car.model);
      }

      if (carData && carData.length > 0) {
        // Берем первый найденный автомобиль
        const foundCar = carData[0];
        cars.push({
          mark: car.brand,
          model: car.model,
          year: car.year,
          power: foundCar.power || 0,
          engine_volume: foundCar.engine_volume || 0,
          fuel_type: foundCar.fuel_type || "",
          price: foundCar.price || 0,
          km_age: foundCar.km_age || 0,
          images: foundCar.images || [],
        });
      } else {
        // Если не нашли в API, используем локальные данные
        cars.push(getFallbackCarData(car));
      }
    } catch (error) {
      console.error(
        `Ошибка при загрузке данных для ${car.brand} ${car.model}:`,
        error
      );
      cars.push(getFallbackCarData(car));
    }
  }

  return cars;
}

// Функция для получения локальных данных об автомобиле
function getFallbackCarData(car) {
  const fallbackData = {
    "Ferrari Roma": {
      power: 612,
      engine_volume: 3.9,
      fuel_type: "V8 twin-turbocharged",
      price: 436195,
      images: ["Img/ferrari.jpg"],
    },
    "Lamborghini Huracan": {
      power: 631,
      engine_volume: 5.2,
      fuel_type: "V10 атмосферный",
      price: 420325,
      images: ["Img/lamborghini.jpg"],
    },
    "Bentley Flying Spur": {
      power: 542,
      engine_volume: 4.0,
      fuel_type: "V8 twin-turbo",
      price: 400000,
      images: ["Img/bentley-flying-spur.jpg"],
    },
    "Aston Martin DB12": {
      power: 671,
      engine_volume: 4.0,
      fuel_type: "V8 twin-turbo",
      price: 284800,
      images: ["Img/aston-martin.jpg"],
    },
    "BMW M5": {
      power: 748,
      engine_volume: 4.4,
      fuel_type: "V8 twin-turbo + электро",
      price: 270000,
      images: ["Img/bmw-m5.jpg"],
    },
    "Audi RS7": {
      power: 600,
      engine_volume: 4.0,
      fuel_type: "V8 twin-turbo",
      price: 208524,
      images: ["Img/audi-rs7.jpg"],
    },
    "Alfa Romeo Giulia Quadrifoglio": {
      power: 510,
      engine_volume: 2.9,
      fuel_type: "V6 twin-turbo",
      price: 150100,
      images: ["Img/placeholder-car.jpg"],
    },
  };

  const key = `${car.brand} ${car.model}`;
  const data = fallbackData[key] || {};

  return {
    mark: car.brand,
    model: car.model,
    year: car.year,
    power: data.power || 0,
    engine_volume: data.engine_volume || 0,
    fuel_type: data.fuel_type || "",
    price: data.price || 0,
    km_age: 0,
    images: data.images || ["Img/placeholder-car.jpg"],
  };
}

// Функция для форматирования цены
function formatPrice(price) {
  return new Intl.NumberFormat("ru-RU", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
}

// Функция для форматирования пробега
function formatMileage(km) {
  return km === 0
    ? "Без пробега"
    : new Intl.NumberFormat("ru-RU").format(km) + " км";
}

// Функция для создания карточки автомобиля
function createCarCard(car) {
  const card = document.createElement("div");
  card.className = "order-card";
  card.setAttribute("data-brand", car.mark);

  // Получаем первое изображение из массива или используем заглушку
  const imageUrl =
    Array.isArray(car.images) && car.images.length > 0
      ? car.images[0]
      : "/assets/img/cars/default-car.jpg";

  // Маппинг названий марок к файлам логотипов
  const logoMap = {
    Ferrari: "ferrari-logo.png",
    Lamborghini: "lamborghini-logo.png",
    Bentley: "bentley-logo.png",
    "Aston Martin": "aston-martin-logo.png",
    BMW: "bmw-logo.png",
    Audi: "audi-logo.png",
    "Alfa Romeo": "default-logo.png",
  };

  const logoFile = logoMap[car.mark] || "default-logo.png";

  card.innerHTML = `
        <div class="order-card-image">
            <img src="${imageUrl}" alt="${car.mark} ${
    car.model
  }" onerror="this.src='Img/placeholder-car.jpg'">
        </div>
        <div class="order-card-content">
            <div class="order-card-header">
                <div class="order-card-logo">
                    <img src="PNG/${logoFile}" alt="${
    car.mark
  }" onerror="this.src='PNG/default-logo.png'">
                </div>
                <h3 class="order-card-title order-card-title--small">${
                  car.mark
                } ${car.model} ${car.year}</h3>
            </div>
            <div class="order-card-specs">
                <div class="order-card-spec">
                    ${car.power ? car.power + " л.с. • " : ""}
                    ${car.engine_volume ? car.engine_volume + "л " : ""}
                    ${car.fuel_type || ""}
                </div>
            </div>
            <div class="order-card-footer">
                <div class="order-card-mileage">${formatMileage(
                  car.km_age
                )}</div>
                <div class="order-card-price">${formatPrice(car.price)}</div>
            </div>
        </div>
    `;

  return card;
}

// Функция для отображения автомобилей
async function displayPremiumCars(searchQuery = "") {
  console.log("displayPremiumCars вызвана с запросом:", searchQuery);

  const cardsContainer = document.querySelector(".order-cards");
  console.log("Контейнер для карточек:", cardsContainer);

  if (!cardsContainer) {
    console.error("Контейнер .order-cards не найден!");
    return;
  }

  // Показываем индикатор загрузки
  cardsContainer.innerHTML =
    '<div class="loading">Загрузка премиальных автомобилей...</div>';

  try {
    const cars = await loadPremiumCars();
    console.log("Загружено автомобилей:", cars.length);

    // Фильтруем автомобили по поисковому запросу
    const filteredCars = searchQuery
      ? cars.filter((car) => {
          const searchStr = `${car.mark} ${car.model}`.toLowerCase();
          return searchStr.includes(searchQuery.toLowerCase());
        })
      : cars;

    console.log("Отфильтровано автомобилей:", filteredCars.length);

    // Очищаем контейнер
    cardsContainer.innerHTML = "";

    if (filteredCars.length === 0) {
      cardsContainer.innerHTML =
        '<div class="no-cars">Автомобили не найдены</div>';
      return;
    }

    // Добавляем карточки автомобилей
    filteredCars.forEach((car) => {
      const card = createCarCard(car);
      console.log("Создана карточка для:", car.mark, car.model);
      cardsContainer.appendChild(card);
    });
  } catch (error) {
    console.error("Ошибка при загрузке автомобилей:", error);
    cardsContainer.innerHTML =
      '<div class="error">Произошла ошибка при загрузке автомобилей</div>';
  }
}

// Инициализация при загрузке страницы
document.addEventListener("DOMContentLoaded", () => {
  console.log("DOMContentLoaded сработал");

  // Отображаем все автомобили при загрузке
  displayPremiumCars();

  // Обработка поиска
  const searchInput = document.querySelector(".search-input");
  const searchButton = document.querySelector("#search-btn");

  console.log("Поле поиска:", searchInput);
  console.log("Кнопка поиска:", searchButton);

  if (searchInput && searchButton) {
    // Поиск по клику на кнопку
    searchButton.addEventListener("click", () => {
      console.log("Клик по кнопке поиска");
      displayPremiumCars(searchInput.value);
    });

    // Поиск по Enter
    searchInput.addEventListener("keypress", (e) => {
      if (e.key === "Enter") {
        console.log("Нажат Enter в поле поиска");
        displayPremiumCars(searchInput.value);
      }
    });
  } else {
    console.error("Не найдены элементы поиска!");
  }
});
