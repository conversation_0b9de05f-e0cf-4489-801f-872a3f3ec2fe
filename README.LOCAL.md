# 🚀 Локальная разработка SHMS Авто

## 🔐 Система авторизации админки

### ⚡ Быстрая настройка (3 шага)

1. **Создание администратора:**

   ```bash
   node create-admin.js
   ```

   Введите логин, имя и пароль

2. **Запуск сервера:**

   ```bash
   npm run local
   ```

3. **Вход в админку:**
   - Откройте: http://localhost:3000/admin
   - Введите созданные логин и пароль

### 🛠️ Управление администраторами

```bash
node create-admin.js   # Создать администратора
node list-admins.js    # Показать список
node delete-admin.js   # Удалить/деактивировать
```

### 📚 Документация

- [QUICK_SETUP.md](./QUICK_SETUP.md) - Быстрая настройка
- [ADMIN_AUTH_SETUP.md](./ADMIN_AUTH_SETUP.md) - Подробная документация

## 📋 Быстрый старт

### 1. Установка зависимостей

```bash
npm install
```

### 2. Запуск локального сервера

#### Простой запуск:

```bash
npm run local
```

#### Запуск с автоперезагрузкой (рекомендуется для разработки):

```bash
npm run dev:local
```

#### Запуск через PM2:

```bash
pm2 start ecosystem.local.config.js
```

### 3. Доступ к приложению

- **Главная страница**: http://localhost:3000/
- **Авто в наличии**: http://localhost:3000/stock
- **Авто под заказ**: http://localhost:3000/order
- **Контакты**: http://localhost:3000/contacts
- **Админ-панель**: http://localhost:3000/admin
- **API тест**: http://localhost:3000/test

## 🔧 Конфигурация

### Файлы конфигурации:

- **`.env`** - настройки для локальной разработки
- **`.env.production`** - настройки для продакшена
- **`server.local.js`** - локальный сервер
- **`ecosystem.local.config.js`** - PM2 конфигурация для разработки

### Основные настройки (.env):

```env
NODE_ENV=development
PORT=3000
HOST=localhost
SITE_URL=http://localhost:3000
LOG_LEVEL=debug
```

## 🛠️ Особенности локальной разработки

### ✅ Включено:

- Подробное логирование всех запросов
- CORS для всех источников
- Отключено кеширование статических файлов
- Email заявки логируются в консоль (не отправляются)
- Детальная информация об ошибках
- Автоперезагрузка при изменениях (с nodemon)

### ❌ Отключено:

- HTTPS редиректы
- Строгие заголовки безопасности
- Кеширование статических файлов
- Отправка email уведомлений

## 📁 Структура проекта

```
shms-auto/
├── server.local.js          # Локальный сервер
├── server.js               # Продакшн сервер
├── .env                    # Локальные настройки
├── .env.production         # Продакшн настройки
├── ecosystem.local.config.js # PM2 для разработки
├── ecosystem.config.js     # PM2 для продакшена
├── public/                 # Статические файлы
├── admin/                  # Админ-панель
├── data/                   # База данных и загрузки
│   ├── uploads/           # Загруженные файлы
│   ├── stock.db          # SQLite база
│   └── cache/            # Кеш
└── logs/                  # Логи
```

## 🔍 Отладка

### Логи в консоли:

- 📡 Все HTTP запросы
- 📦 Тело запросов (POST/PUT)
- 🔍 Query параметры
- 📤 Ответы сервера
- 📧 Email заявки (вместо отправки)

### PM2 логи:

```bash
pm2 logs shms-auto-local
```

### Остановка PM2:

```bash
pm2 stop shms-auto-local
pm2 delete shms-auto-local
```

## 🚨 Важные команды

### Проверка синтаксиса:

```bash
npm run lint
```

### Тестирование:

```bash
npm test
```

### Проверка здоровья:

```bash
npm run health-check
```

## 📧 Тестирование форм

В локальной разработке все email заявки логируются в консоль вместо отправки.
Для тестирования отправьте POST запрос на `/api/send-review`:

```json
{
  "name": "Тест",
  "contact": "<EMAIL>",
  "message": "Тестовое сообщение",
  "formType": "review"
}
```

## 🔄 Переключение между режимами

### На локальную разработку:

```bash
cp .env.production .env.backup
cp .env.local .env  # если есть
npm run dev:local
```

### На продакшн:

```bash
cp .env.production .env
npm run production
```

## 🆘 Решение проблем

### Порт занят:

```bash
# Найти процесс на порту 3000
netstat -ano | findstr :3000
# Убить процесс
taskkill /PID <PID> /F
```

### Проблемы с базой данных:

- Проверьте существование файла `data/stock.db`
- Убедитесь в правах доступа к директории `data/`

### Проблемы с загрузками:

- Проверьте существование директории `data/uploads/`
- Убедитесь в правах записи

---

**Удачной разработки! 🚀**
