const sqlite3 = require("sqlite3").verbose();
const path = require("path");

// Путь к базе данных (используем ту же, что и сервер)
const dbPath = path.join(__dirname, "../../admin/js/stock.db");

console.log("Обновление схемы базы данных...");
console.log("Путь к БД:", dbPath);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error("Ошибка подключения к БД:", err);
    return;
  }
  console.log("Подключение к БД успешно");
});

// Функция для проверки существования колонки
function checkColumnExists(tableName, columnName) {
  return new Promise((resolve, reject) => {
    db.all(`PRAGMA table_info(${tableName})`, (err, rows) => {
      if (err) {
        reject(err);
        return;
      }
      const columnExists = rows.some((row) => row.name === columnName);
      resolve(columnExists);
    });
  });
}

// Функция для добавления колонки
function addColumn(tableName, columnName, columnType) {
  return new Promise((resolve, reject) => {
    const sql = `ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnType}`;
    db.run(sql, (err) => {
      if (err) {
        reject(err);
        return;
      }
      console.log(`✅ Колонка ${columnName} добавлена в таблицу ${tableName}`);
      resolve();
    });
  });
}

// Основная функция обновления
async function updateSchema() {
  try {
    console.log("Проверка и обновление схемы таблицы car_details...");

    // Сначала проверим текущую структуру таблицы
    const tableInfo = await new Promise((resolve, reject) => {
      db.all(`PRAGMA table_info(car_details)`, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log("Текущие колонки в таблице car_details:");
    tableInfo.forEach((col) => console.log(`- ${col.name} (${col.type})`));

    // Проверяем и добавляем main_features
    const mainFeaturesExists = await checkColumnExists(
      "car_details",
      "main_features"
    );
    if (!mainFeaturesExists) {
      await addColumn("car_details", "main_features", "TEXT");
    } else {
      console.log("ℹ️  Колонка main_features уже существует");
    }

    // Проверяем и добавляем additional_features
    const additionalFeaturesExists = await checkColumnExists(
      "car_details",
      "additional_features"
    );
    if (!additionalFeaturesExists) {
      await addColumn("car_details", "additional_features", "TEXT");
    } else {
      console.log("ℹ️  Колонка additional_features уже существует");
    }

    // Проверяем и добавляем mileage
    const mileageExists = await checkColumnExists("car_details", "mileage");
    if (!mileageExists) {
      await addColumn("car_details", "mileage", "TEXT");
    } else {
      console.log("ℹ️  Колонка mileage уже существует");
    }

    // Проверяем и добавляем body_type
    const bodyTypeExists = await checkColumnExists("car_details", "body_type");
    if (!bodyTypeExists) {
      await addColumn("car_details", "body_type", "TEXT");
    } else {
      console.log("ℹ️  Колонка body_type уже существует");
    }

    // Проверим финальную структуру
    const finalTableInfo = await new Promise((resolve, reject) => {
      db.all(`PRAGMA table_info(car_details)`, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log("\nФинальные колонки в таблице car_details:");
    finalTableInfo.forEach((col) => console.log(`- ${col.name} (${col.type})`));

    console.log("✅ Обновление схемы завершено успешно!");
  } catch (error) {
    console.error("❌ Ошибка при обновлении схемы:", error);
  } finally {
    db.close((err) => {
      if (err) {
        console.error("Ошибка при закрытии БД:", err);
      } else {
        console.log("Соединение с БД закрыто");
      }
    });
  }
}

// Запускаем обновление
updateSchema();
