const readline = require("readline");
const Admin = require("../server/models/Admin");
const securityUtils = require("../server/utils/security");

// Создаем интерфейс для ввода данных
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// Функция для скрытого ввода пароля
function hiddenInput(prompt) {
  return new Promise((resolve) => {
    const stdin = process.stdin;
    const stdout = process.stdout;

    stdout.write(prompt);
    stdin.setRawMode(true);
    stdin.resume();
    stdin.setEncoding("utf8");

    let input = "";

    stdin.on("data", function (char) {
      char = char + "";

      switch (char) {
        case "\n":
        case "\r":
        case "\u0004":
          stdin.setRawMode(false);
          stdin.pause();
          stdout.write("\n");
          resolve(input);
          break;
        case "\u0003":
          process.exit();
          break;
        case "\u007f": // Backspace
          if (input.length > 0) {
            input = input.slice(0, -1);
            stdout.write("\b \b");
          }
          break;
        default:
          input += char;
          stdout.write("*");
          break;
      }
    });
  });
}

// Функция для обычного ввода
function normalInput(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, (answer) => {
      resolve(answer.trim());
    });
  });
}

// Основная функция создания администратора
async function createFirstAdmin() {
  console.log("🔧 Создание первого администратора SHMS Auto");
  console.log("==========================================\n");

  try {
    // Проверяем, есть ли уже администраторы
    const adminModel = new Admin();
    const existingAdmins = await adminModel.getAll();

    if (existingAdmins.length > 0) {
      console.log("⚠️  В системе уже есть администраторы:");
      existingAdmins.forEach((admin) => {
        console.log(
          `   - ${admin.username} (${admin.email}) - ${
            admin.is_active ? "активен" : "неактивен"
          }`
        );
      });

      const confirm = await normalInput(
        "\nВы уверены, что хотите создать еще одного администратора? (y/N): "
      );
      if (confirm.toLowerCase() !== "y" && confirm.toLowerCase() !== "yes") {
        console.log("Отменено пользователем.");
        process.exit(0);
      }
    }

    console.log("Введите данные нового администратора:\n");

    // Ввод логина
    let username;
    while (true) {
      username = await normalInput("Логин: ");

      if (!username) {
        console.log("❌ Логин не может быть пустым");
        continue;
      }

      const validation = securityUtils.validateUsername(username);
      if (!validation.isValid) {
        console.log(`❌ ${validation.error}`);
        continue;
      }

      // Проверяем уникальность
      const existingAdmin = await adminModel.findByUsername(username);
      if (existingAdmin) {
        console.log("❌ Пользователь с таким логином уже существует");
        continue;
      }

      break;
    }

    // Ввод email
    let email;
    while (true) {
      email = await normalInput("Email: ");

      if (!email) {
        console.log("❌ Email не может быть пустым");
        continue;
      }

      if (!securityUtils.validateEmail(email)) {
        console.log("❌ Введите корректный email адрес");
        continue;
      }

      // Проверяем уникальность
      const existingAdmin = await adminModel.findByEmail(email);
      if (existingAdmin) {
        console.log("❌ Пользователь с таким email уже существует");
        continue;
      }

      break;
    }

    // Ввод полного имени
    const fullName = await normalInput("Полное имя (необязательно): ");

    // Ввод пароля
    let password;
    while (true) {
      password = await hiddenInput("Пароль: ");

      if (!password) {
        console.log("❌ Пароль не может быть пустым");
        continue;
      }

      const validation = securityUtils.validatePassword(password);
      if (!validation.isValid) {
        console.log("❌ Пароль не соответствует требованиям безопасности:");
        validation.errors.forEach((error) => {
          console.log(`   - ${error}`);
        });
        continue;
      }

      // Подтверждение пароля
      const confirmPassword = await hiddenInput("Подтвердите пароль: ");

      if (password !== confirmPassword) {
        console.log("❌ Пароли не совпадают");
        continue;
      }

      break;
    }

    console.log("\n📋 Проверьте введенные данные:");
    console.log(`   Логин: ${username}`);
    console.log(`   Email: ${email}`);
    console.log(`   Полное имя: ${fullName || "не указано"}`);
    console.log(`   Пароль: ${"*".repeat(password.length)}`);

    const confirm = await normalInput("\nВсе данные верны? (Y/n): ");
    if (confirm.toLowerCase() === "n" || confirm.toLowerCase() === "no") {
      console.log("Отменено пользователем.");
      rl.close();
      process.exit(0);
    }

    // Создаем администратора
    console.log("\n🔄 Создание администратора...");

    const newAdmin = await adminModel.create({
      username,
      email,
      password,
      fullName: fullName || null,
    });

    console.log("✅ Администратор успешно создан!");
    console.log(`   ID: ${newAdmin.id}`);
    console.log(`   Логин: ${newAdmin.username}`);
    console.log(`   Email: ${newAdmin.email}`);
    console.log(`   Создан: ${newAdmin.createdAt}`);

    console.log("\n🎉 Теперь вы можете войти в админку по адресу:");
    console.log("   http://localhost:3000/admin/login.html");
    console.log("\n💡 Не забудьте настроить базу данных командой:");
    console.log("   node scripts/setup-auth-db.js");
  } catch (error) {
    console.error("❌ Ошибка при создании администратора:", error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Обработка сигналов завершения
process.on("SIGINT", () => {
  console.log("\n\nОтменено пользователем.");
  rl.close();
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.log("\n\nПроцесс завершен.");
  rl.close();
  process.exit(0);
});

// Запуск скрипта
if (require.main === module) {
  createFirstAdmin()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error("Критическая ошибка:", error);
      process.exit(1);
    });
}

module.exports = { createFirstAdmin };
