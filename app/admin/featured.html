<!DOCTYPE html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Автомобили на главной | SHMS Админка</title>
    <link rel="stylesheet" href="css/styles.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"
    />
  </head>

  <body>
    <!-- Mobile Header -->
    <header class="admin-mobile-header">
      <button
        class="admin-burger"
        id="adminBurgerBtn"
        aria-label="Открыть меню"
      >
        <span></span>
        <span></span>
        <span></span>
      </button>
      <a href="index.html" class="admin-mobile-header__logo"> SHMS Admin </a>
    </header>

    <!-- Пустой раздел для отступа -->
    <div class="admin-mobile-header-spacer"></div>

    <!-- Mobile Menu -->
    <nav class="admin-dropdown-menu" id="adminDropdownMenu">
      <a href="index.html">Автомобили в наличии</a>
      <a href="featured.html" class="active">Автомобили на главной</a>
      <a href="car-detail-simple.html">Детальные страницы</a>
      <a href="reviews.html">Отзывы</a>
    </nav>

    <!-- Hero / Title Section -->
    <section class="admin-hero-stock">
      <div class="admin-hero-header-group">
        <div class="admin-header__left">
          <a
            href="#"
            class="admin-header__back-link"
            onclick="window.history.back(); return false;"
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19 12H5M5 12L12 19M5 12L12 5"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            Назад
          </a>
        </div>
        <div class="admin-header__center-group">
          <nav class="admin-header__nav">
            <a href="index.html" class="admin-header__logo-link">SHMS Admin</a>
            <div class="admin-header__nav-links">
              <a href="index.html" class="admin-header__nav-link"
                >Автомобили в наличии</a
              >
              <a href="featured.html" class="admin-header__nav-link active"
                >Автомобили на главной</a
              >
              <a href="car-detail-simple.html" class="admin-header__nav-link"
                >Детальные страницы</a
              >
              <a href="reviews.html" class="admin-header__nav-link">Отзывы</a>
            </div>
          </nav>
        </div>
      </div>
    </section>

    <div class="admin-container">
      <div class="admin-section featured-section">
        <div class="section-header">
          <h2>Автомобили на главной странице</h2>
          <div class="section-counter" id="featured-counter">
            <i class="fas fa-star"></i>
            <span class="counter-value">0</span>
          </div>
        </div>
        <p class="alert alert-info" id="featured-status"></p>
        <div id="featured-container" class="featured-container">
          <!-- Здесь будут автомобили, выбранные для главной -->
        </div>
      </div>

      <div class="admin-section available-section">
        <div class="section-header">
          <h2>Доступные автомобили</h2>
          <div class="section-counter" id="available-counter">
            <i class="fas fa-car"></i>
            <span class="counter-value">0</span>
          </div>
        </div>
        <div class="controls">
          <div class="search-box">
            <input type="text" id="search-available" placeholder="Поиск..." />
            <button id="search-available-btn">
              <i class="fas fa-search"></i> Найти
            </button>
            <button id="reset-available-search-btn">
              <i class="fas fa-times"></i> Сбросить
            </button>
          </div>
        </div>
        <div id="available-status" class="search-status"></div>
        <div id="available-container" class="car-grid">
          <!-- Здесь будут доступные автомобили -->
        </div>
      </div>
    </div>

    <!-- Диалоговое окно для изменения порядка отображения -->
    <div id="order-dialog" class="order-dialog">
      <div class="order-dialog-content">
        <div class="order-dialog-title">
          <i class="fas fa-sort"></i> Изменить порядок отображения
        </div>
        <div class="order-dialog-form">
          <label for="display-order"
            >Порядок (меньше значение = выше в списке):</label
          >
          <input type="number" id="display-order" min="0" value="0" />
          <input type="hidden" id="featured-id" />
        </div>
        <div class="order-dialog-actions">
          <button class="dialog-close-btn" id="dialog-close">
            <i class="fas fa-times"></i> Отмена
          </button>
          <button class="dialog-save-btn" id="dialog-save">
            <i class="fas fa-save"></i> Сохранить
          </button>
        </div>
      </div>
    </div>

    <!-- Уведомления -->
    <div id="notification" class="notification">
      <div class="notification-content">
        <div class="notification-message">
          <div class="notification-title"></div>
          <div class="notification-text"></div>
        </div>
        <button class="notification-close">&times;</button>
      </div>
    </div>

    <script src="js/featured.js"></script>

    <!-- Mobile Menu JavaScript -->
    <script>
      // Mobile menu functionality
      document.addEventListener("DOMContentLoaded", function () {
        const burgerBtn = document.getElementById("adminBurgerBtn");
        const dropdownMenu = document.getElementById("adminDropdownMenu");

        if (burgerBtn && dropdownMenu) {
          burgerBtn.addEventListener("click", function () {
            burgerBtn.classList.toggle("open");
            dropdownMenu.classList.toggle("active");
          });

          // Close menu when clicking outside
          document.addEventListener("click", function (e) {
            if (
              !burgerBtn.contains(e.target) &&
              !dropdownMenu.contains(e.target)
            ) {
              burgerBtn.classList.remove("open");
              dropdownMenu.classList.remove("active");
            }
          });
        }
      });
    </script>
  </body>
</html>
