// Encar API Integration for Order Page
// This script handles fetching and displaying car data from Encar API with pagination

// Функция для определения базового URL в зависимости от окружения
function getBaseUrl() {
  // Используем текущий домен (JavaScript сервер проксирует запросы к PHP)
  console.log(
    "Используем текущий домен с проксированием:",
    window.location.origin
  );
  return window.location.origin;
}

// Константа с базовым URL
const BASE_URL = getBaseUrl();

// Global variables
let currentPage = 1;
const pageSize = 12;
let cachedData = [];
let isLoading = false;
let searchQuery = "";
let useProxy = true; // Toggle between real API and mock data
let featuredOnly = false; // Показывать только главные карточки
let sortByPrice = "asc"; // По умолчанию сортировка от дешевого к дорогому
let categoryFilter = ""; // Фильтр по категории: business, sport, suv

// Кэш для хранения результатов API запросов
const apiCache = {};
// Индикатор предзагрузки следующей страницы
let nextPagePreloaded = false;
// Таймер для отложенной загрузки
let debounceTimer = null;

// Новые глобальные переменные для фильтров и автодополнения
let carBrands = []; // Список доступных марок
let carModels = {}; // Модели по маркам

// Марки автомобилей, состоящие из нескольких слов
const multiWordBrands = [
  "Aston Martin",
  "Land Rover",
  "Mercedes-Benz",
  "Alfa Romeo",
  "Rolls-Royce",
];

let activeFilters = {
  brand: "",
  model: "",
  yearFrom: "",
  yearTo: "",
  priceFrom: "",
  priceTo: "",
  transmission: "",
  bodyType: "",
  fuelType: "",
  mileageFrom: "",
  mileageTo: "",
  driveType: "",
  category: "", // Добавляем категорию автомобиля
};

// Имя ключа для localStorage
const STORAGE_KEY = "shms_car_filters";

// Document ready function
document.addEventListener("DOMContentLoaded", function () {
  console.log("Encar Order integration loaded");

  // Загрузка списка автомобильных марок и моделей
  loadCarBrandsAndModels();

  // Загружаем сохраненные фильтры из localStorage
  loadSavedFilters();

  // Initialize search functionality
  initSearch();

  // Инициализация фильтров
  initFilters();

  // Инициализация категорий
  initCategories();

  // По умолчанию загружаем все автомобили из CSV через API
  featuredOnly = false;
  categoryFilter = "";

  // Активируем соответствующую вкладку "Все автомобили"
  const allTab = document.querySelector('.category-tab[data-category="all"]');
  if (allTab) {
    // Убираем активный класс со всех вкладок
    document
      .querySelectorAll(".category-tab")
      .forEach((tab) => tab.classList.remove("active"));
    // Делаем активной вкладку "Все автомобили"
    allTab.classList.add("active");
  }

  // Initialize the page
  loadCars();

  // Добавляем обработчик для кнопки "Загрузить еще"
  const loadMoreBtn = document.getElementById("load-more-btn");
  if (loadMoreBtn) {
    loadMoreBtn.addEventListener("click", function () {
      // Если загрузка уже идет, не делаем ничего
      if (isLoading) return;

      // Увеличиваем номер страницы и загружаем следующую порцию авто
      currentPage++;
      loadCars(false); // false = append (добавить к существующим)
    });

    // Изначально скрываем кнопку, она будет показана только если есть еще авто
    loadMoreBtn.style.display = "none";
  }

  // Add modal to page
  addModalToPage();
});

// Функция для загрузки сохраненных фильтров из localStorage
function loadSavedFilters() {
  try {
    const savedFilters = localStorage.getItem(STORAGE_KEY);
    if (savedFilters) {
      const parsedFilters = JSON.parse(savedFilters);

      // Проверяем на валидность и актуальность (например, не устарели ли года)
      const currentYear = getCurrentYear();

      // Если сохраненный максимальный год больше текущего, сбрасываем его
      if (parsedFilters.yearTo > currentYear) {
        parsedFilters.yearTo = currentYear;
      }

      // Применяем сохраненные фильтры
      activeFilters = parsedFilters;

      console.log("Загружены сохраненные фильтры:", activeFilters);
    }
  } catch (error) {
    console.warn("Ошибка при загрузке сохраненных фильтров:", error);
  }
}

// Функция для сохранения фильтров в localStorage
function saveFilters() {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(activeFilters));
    console.log("Фильтры сохранены в localStorage");
  } catch (error) {
    console.warn("Ошибка при сохранении фильтров:", error);
  }
}

// Функция для получения текущего года
function getCurrentYear() {
  return new Date().getFullYear();
}

// Функция для загрузки списка автомобильных марок и моделей
function loadCarBrandsAndModels() {
  // Марки автомобилей (можно получать динамически через API)
  carBrands = [
    "Acura",
    "Alfa Romeo",
    "Aston Martin",
    "Audi",
    "Bentley",
    "BMW",
    "Bugatti",
    "Cadillac",
    "Chevrolet",
    "Chrysler",
    "Citroen",
    "Dodge",
    "Ferrari",
    "Fiat",
    "Ford",
    "Genesis",
    "GMC",
    "Honda",
    "Hummer",
    "Hyundai",
    "Infiniti",
    "Jaguar",
    "Jeep",
    "Kia",
    "Koenigsegg",
    "Lamborghini",
    "Land Rover",
    "Lexus",
    "Lincoln",
    "Lotus",
    "Maserati",
    "Maybach",
    "Mazda",
    "McLaren",
    "Mercedes-Benz",
    "MINI",
    "Mitsubishi",
    "Nissan",
    "Pagani",
    "Peugeot",
    "Porsche",
    "Renault",
    "Rolls-Royce",
    "Skoda",
    "Subaru",
    "Suzuki",
    "Tesla",
    "Toyota",
    "Volkswagen",
    "Volvo",
  ].sort();

  // Модели по маркам (можно получать динамически через API)
  carModels = {
    Acura: ["MDX", "NSX", "RDX", "TLX", "ILX", "RLX"],
    "Alfa Romeo": ["Giulia", "Stelvio", "4C", "8C", "Giulietta"],
    "Aston Martin": [
      "DB11",
      "DB12",
      "Vantage",
      "DBS",
      "Valkyrie",
      "Valhalla",
      "DBX",
    ],
    Audi: [
      "A3",
      "A4",
      "A5",
      "A6",
      "A7",
      "A8",
      "Q3",
      "Q5",
      "Q7",
      "Q8",
      "e-tron",
      "RS3",
      "RS4",
      "RS6",
      "RS7",
      "S3",
      "S4",
      "S5",
      "S6",
      "S7",
      "S8",
      "TT",
      "R8",
    ],
    Bentley: [
      "Bentayga",
      "Continental GT",
      "Flying Spur",
      "Mulsanne",
      "Bacalar",
    ],
    BMW: [
      "1 Series",
      "2 Series",
      "3 Series",
      "4 Series",
      "5 Series",
      "6 Series",
      "7 Series",
      "8 Series",
      "X1",
      "X2",
      "X3",
      "X4",
      "X5",
      "X6",
      "X7",
      "Z4",
      "i3",
      "i4",
      "i7",
      "i8",
      "iX",
      "M2",
      "M3",
      "M4",
      "M5",
      "M8",
      "X3 M",
      "X5 M",
      "X6 M",
    ],
    Bugatti: ["Chiron", "Veyron", "Divo", "Mistral", "Centodieci"],
    Cadillac: ["CT4", "CT5", "Escalade", "XT4", "XT5", "XT6"],
    Chevrolet: [
      "Camaro",
      "Corvette",
      "Cruze",
      "Impala",
      "Malibu",
      "Silverado",
      "Suburban",
      "Tahoe",
      "Trailblazer",
      "Traverse",
    ],
    Chrysler: ["300", "Pacifica", "Voyager"],
    Citroen: ["C3", "C4", "C5", "Berlingo", "Jumper", "SpaceTourer"],
    Dodge: ["Challenger", "Charger", "Durango", "RAM"],
    Ferrari: [
      "458",
      "488",
      "812",
      "F8",
      "Roma",
      "SF90",
      "Portofino",
      "Purosangue",
      "LaFerrari",
    ],
    Fiat: ["500", "Panda", "Tipo", "Ducato"],
    Ford: [
      "Bronco",
      "EcoSport",
      "Edge",
      "Escape",
      "Explorer",
      "F-150",
      "Focus",
      "Mustang",
      "Ranger",
      "Transit",
    ],
    Genesis: ["G70", "G80", "G90", "GV70", "GV80"],
    GMC: ["Acadia", "Canyon", "Sierra", "Terrain", "Yukon"],
    Honda: [
      "Accord",
      "Civic",
      "CR-V",
      "Element",
      "Fit",
      "HR-V",
      "Odyssey",
      "Pilot",
      "Ridgeline",
    ],
    Hummer: ["H1", "H2", "H3", "EV"],
    Hyundai: [
      "Accent",
      "Elantra",
      "Ioniq",
      "Kona",
      "Palisade",
      "Santa Cruz",
      "Santa Fe",
      "Sonata",
      "Staria",
      "Tucson",
      "Venue",
    ],
    Infiniti: ["Q50", "Q60", "QX50", "QX55", "QX60", "QX80"],
    Jaguar: ["E-Pace", "F-Pace", "F-Type", "I-Pace", "XE", "XF", "XJ"],
    Jeep: [
      "Cherokee",
      "Compass",
      "Gladiator",
      "Grand Cherokee",
      "Renegade",
      "Wagoneer",
      "Wrangler",
    ],
    Kia: [
      "Carnival",
      "Ceed",
      "Cerato",
      "EV6",
      "Forte",
      "K5",
      "Niro",
      "Optima",
      "Rio",
      "Seltos",
      "Sorento",
      "Soul",
      "Sportage",
      "Stinger",
      "Telluride",
    ],
    Koenigsegg: ["Agera", "Jesko", "Regera", "Gemera"],
    Lamborghini: [
      "Aventador",
      "Huracan",
      "Urus",
      "Revuelto",
      "Countach",
      "Sian",
    ],
    "Land Rover": [
      "Defender",
      "Discovery",
      "Range Rover",
      "Range Rover Evoque",
      "Range Rover Sport",
      "Range Rover Velar",
    ],
    Lexus: [
      "ES",
      "GS",
      "GX",
      "IS",
      "LC",
      "LS",
      "LX",
      "NX",
      "RX",
      "UX",
      "RC",
      "RZ",
    ],
    Lincoln: ["Aviator", "Continental", "Corsair", "Navigator", "Nautilus"],
    Lotus: ["Elise", "Emira", "Evija", "Evora", "Exige"],
    Maserati: ["Ghibli", "Grecale", "Levante", "MC20", "Quattroporte"],
    Maybach: ["S-Class", "GLS"],
    Mazda: [
      "CX-3",
      "CX-30",
      "CX-5",
      "CX-9",
      "MX-5",
      "Mazda3",
      "Mazda6",
      "MX-30",
    ],
    McLaren: ["570S", "720S", "Artura", "GT", "Elva", "Senna", "Speedtail"],
    "Mercedes-Benz": [
      "A-Class",
      "B-Class",
      "C-Class",
      "CLA",
      "CLS",
      "E-Class",
      "EQA",
      "EQB",
      "EQC",
      "EQE",
      "EQS",
      "G-Class",
      "GLA",
      "GLB",
      "GLC",
      "GLE",
      "GLS",
      "S-Class",
      "SL",
      "AMG GT",
      "Maybach S-Class",
    ],
    MINI: ["Cooper", "Clubman", "Countryman", "Convertible", "Electric"],
    Mitsubishi: [
      "ASX",
      "Eclipse Cross",
      "L200",
      "Lancer",
      "Outlander",
      "Pajero",
      "Space Star",
    ],
    Nissan: [
      "370Z",
      "Altima",
      "Armada",
      "GT-R",
      "Juke",
      "Leaf",
      "Maxima",
      "Micra",
      "Murano",
      "Navara",
      "Note",
      "Pathfinder",
      "Qashqai",
      "Rogue",
      "Sentra",
      "Titan",
      "X-Trail",
    ],
    Pagani: ["Huayra", "Zonda", "Utopia"],
    Peugeot: [
      "108",
      "208",
      "308",
      "408",
      "508",
      "2008",
      "3008",
      "5008",
      "Partner",
      "Rifter",
    ],
    Porsche: [
      "718 Boxster",
      "718 Cayman",
      "911",
      "Cayenne",
      "Macan",
      "Panamera",
      "Taycan",
    ],
    Renault: [
      "Arkana",
      "Captur",
      "Clio",
      "Duster",
      "Kadjar",
      "Kangoo",
      "Koleos",
      "Megane",
      "Scenic",
      "Trafic",
      "Twingo",
      "Zoe",
    ],
    "Rolls-Royce": [
      "Cullinan",
      "Dawn",
      "Ghost",
      "Phantom",
      "Wraith",
      "Spectre",
    ],
    Skoda: [
      "Citigo",
      "Fabia",
      "Kamiq",
      "Karoq",
      "Kodiaq",
      "Octavia",
      "Rapid",
      "Scala",
      "Superb",
    ],
    Subaru: [
      "BRZ",
      "Crosstrek",
      "Forester",
      "Impreza",
      "Legacy",
      "Outback",
      "WRX",
      "WRX STI",
    ],
    Suzuki: ["Ignis", "Jimny", "S-Cross", "Swift", "Vitara"],
    Tesla: [
      "Model 3",
      "Model S",
      "Model X",
      "Model Y",
      "Cybertruck",
      "Roadster",
    ],
    Toyota: [
      "4Runner",
      "86",
      "Avalon",
      "Camry",
      "Corolla",
      "C-HR",
      "GR86",
      "GR Supra",
      "Highlander",
      "Land Cruiser",
      "Mirai",
      "Prius",
      "RAV4",
      "Sequoia",
      "Sienna",
      "Supra",
      "Tacoma",
      "Tundra",
      "Venza",
      "Yaris",
    ],
    Volkswagen: [
      "Arteon",
      "Atlas",
      "Caddy",
      "Golf",
      "ID.3",
      "ID.4",
      "Jetta",
      "Passat",
      "Polo",
      "Taos",
      "Tiguan",
      "Touareg",
      "Transporter",
      "T-Cross",
      "T-Roc",
    ],
    Volvo: ["C40", "S60", "S90", "V60", "V90", "XC40", "XC60", "XC90"],
  };

  // Заполняем селект марок
  populateBrandSelect();

  // Заполняем селекты годов
  populateYearSelects();
}

// Заполнение селекта марок
function populateBrandSelect() {
  const brandSelect = document.getElementById("brand-filter");
  if (!brandSelect) return;

  // Очищаем список, оставляя пустой элемент
  const emptyOption = brandSelect.querySelector('option[value=""]');
  brandSelect.innerHTML = "";
  if (emptyOption) brandSelect.appendChild(emptyOption);

  // Добавляем марки
  carBrands.forEach((brand) => {
    const option = document.createElement("option");
    option.value = brand;
    option.textContent = brand;
    brandSelect.appendChild(option);
  });
}

// Заполнение селектов годов
function populateYearSelects() {
  const yearFrom = document.getElementById("year-from");
  const yearTo = document.getElementById("year-to");
  if (!yearFrom || !yearTo) return;

  // Получаем текущий год
  const currentYear = getCurrentYear();

  // Создаем список годов от текущего до 1990
  const years = [];
  for (let year = currentYear; year >= 1990; year--) {
    years.push(year);
  }

  // Функция для заполнения одного селекта
  function fillYearSelect(select, years) {
    // Сохраняем пустой option
    const emptyOption = select.querySelector('option[value=""]');
    select.innerHTML = "";
    if (emptyOption) select.appendChild(emptyOption);

    // Заполняем годами
    years.forEach((year) => {
      const option = document.createElement("option");
      option.value = year;
      option.textContent = year;
      select.appendChild(option);
    });
  }

  // Заполняем оба селекта
  fillYearSelect(yearFrom, years);
  fillYearSelect(yearTo, years);
}

// Initialize search functionality с интеграцией с фильтрами
function initSearch() {
  const searchInput = document.querySelector(".search-input");
  const searchButton = document.getElementById("search-btn");

  if (searchInput && searchButton) {
    // Устанавливаем значение поиска из активных фильтров, если есть
    if (activeFilters.brand) {
      let searchValue = activeFilters.brand;
      if (activeFilters.model) {
        searchValue += " " + activeFilters.model;
      }
      searchInput.value = searchValue;
    }

    // Search on button click - ТОЛЬКО по клику на кнопку "Найти"
    searchButton.addEventListener("click", function () {
      const searchValue = searchInput.value.trim();
      searchQuery = searchValue;

      // При поиске сбрасываем фильтр категории и переключаемся на "Все автомобили"
      if (searchValue) {
        featuredOnly = false;
        categoryFilter = "";

        // Активируем вкладку "Все автомобили"
        const allTab = document.querySelector(
          '.category-tab[data-category="all"]'
        );
        if (allTab) {
          document
            .querySelectorAll(".category-tab")
            .forEach((tab) => tab.classList.remove("active"));
          allTab.classList.add("active");
        }

        // Сбрасываем категорию в фильтрах
        if (activeFilters) {
          activeFilters.category = "";
        }

        // Очищаем кэш для нового поиска
        Object.keys(apiCache).forEach((key) => delete apiCache[key]);
      }

      // Синхронизируем поиск с фильтрами
      syncSearchWithFilters(searchValue);

      currentPage = 1;
      loadCars(true); // true = reset
    });

    // Search on Enter key
    searchInput.addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        const searchValue = searchInput.value.trim();
        searchQuery = searchValue;

        // При поиске сбрасываем фильтр категории и переключаемся на "Все автомобили"
        if (searchValue) {
          featuredOnly = false;
          categoryFilter = "";

          // Активируем вкладку "Все автомобили"
          const allTab = document.querySelector(
            '.category-tab[data-category="all"]'
          );
          if (allTab) {
            document
              .querySelectorAll(".category-tab")
              .forEach((tab) => tab.classList.remove("active"));
            allTab.classList.add("active");
          }

          // Сбрасываем категорию в фильтрах
          if (activeFilters) {
            activeFilters.category = "";
          }

          // Очищаем кэш для нового поиска
          Object.keys(apiCache).forEach((key) => delete apiCache[key]);
        }

        // Синхронизируем поиск с фильтрами
        syncSearchWithFilters(searchValue);

        currentPage = 1;
        loadCars(true); // true = reset
      }
    });
  }
}

// Функция для нормализации поисковой строки
function normalizeSearchString(str) {
  if (!str) return "";
  return str.trim().replace(/\s+/g, " "); // Убираем лишние пробелы
}

// Функция для нечеткого поиска марки
function fuzzyBrandMatch(searchBrand, availableBrands) {
  if (!searchBrand || !availableBrands) return null;

  const normalizedSearch = searchBrand.toLowerCase();

  // Точное совпадение
  for (const brand of availableBrands) {
    if (brand.toLowerCase() === normalizedSearch) {
      return brand;
    }
  }

  // Поиск по началу строки
  for (const brand of availableBrands) {
    if (brand.toLowerCase().startsWith(normalizedSearch)) {
      return brand;
    }
  }

  // Поиск вхождения
  for (const brand of availableBrands) {
    if (brand.toLowerCase().includes(normalizedSearch)) {
      return brand;
    }
  }

  return null;
}

// Функция для умного разделения марки и модели
function smartBrandModelSplit(searchValue) {
  if (!searchValue) return { brand: "", model: "" };

  const normalized = normalizeSearchString(searchValue);
  console.log("Разбираем поисковый запрос:", normalized);

  // Сначала проверяем марки из нескольких слов
  for (const multiWordBrand of multiWordBrands) {
    if (normalized.toLowerCase().startsWith(multiWordBrand.toLowerCase())) {
      const remainingText = normalized.substring(multiWordBrand.length).trim();
      console.log(
        `Найдена многословная марка: ${multiWordBrand}, модель: ${remainingText}`
      );
      return {
        brand: multiWordBrand,
        model: remainingText,
      };
    }
  }

  // Если нет пробелов, это только марка
  if (!normalized.includes(" ")) {
    return {
      brand: normalized,
      model: "",
    };
  }

  // Разделяем по первому пробелу
  const spaceIndex = normalized.indexOf(" ");
  const potentialBrand = normalized.substring(0, spaceIndex);
  const potentialModel = normalized.substring(spaceIndex + 1);

  console.log(
    `Потенциальная марка: ${potentialBrand}, модель: ${potentialModel}`
  );

  return {
    brand: potentialBrand,
    model: potentialModel,
  };
}

// Улучшенная функция для синхронизации поиска с фильтрами
function syncSearchWithFilters(searchValue) {
  try {
    // Если поиск пуст, не меняем фильтры
    if (!searchValue) {
      console.log("Пустой поисковый запрос, пропускаем синхронизацию");
      return;
    }

    console.log("Синхронизируем поиск с фильтрами:", searchValue);

    // Умно разделяем марку и модель
    const { brand: searchBrand, model: searchModel } =
      smartBrandModelSplit(searchValue);

    if (!searchBrand) {
      console.log("Не удалось определить марку из поискового запроса");
      return;
    }

    // Ищем подходящую марку с нечетким поиском
    const matchedBrand = fuzzyBrandMatch(searchBrand, carBrands);

    if (matchedBrand) {
      console.log(`Найдена марка: ${matchedBrand}`);

      // Устанавливаем марку в фильтр
      const brandSelect = document.getElementById("brand-filter");
      if (brandSelect) brandSelect.value = matchedBrand;
      activeFilters.brand = matchedBrand;

      // Обновляем список моделей для выбранной марки
      updateModelSelect(matchedBrand);

      // Проверяем модель, если она указана
      if (searchModel) {
        const modelSelect = document.getElementById("model-filter");
        if (modelSelect && carModels[matchedBrand]) {
          // Ищем подходящую модель
          const matchedModel = fuzzyBrandMatch(
            searchModel,
            carModels[matchedBrand]
          );
          if (matchedModel) {
            console.log(`Найдена модель: ${matchedModel}`);
            modelSelect.value = matchedModel;
            activeFilters.model = matchedModel;
          } else {
            console.log(
              `Модель "${searchModel}" не найдена для марки ${matchedBrand}`
            );
            activeFilters.model = "";
          }
        }
      } else {
        // Если модель не указана, сбрасываем выбранную модель
        activeFilters.model = "";
      }
    } else {
      console.log(`Марка "${searchBrand}" не найдена в списке доступных марок`);
      // Если марка не найдена, все равно сохраняем поисковый запрос для передачи на сервер
      activeFilters.brand = searchBrand;
      activeFilters.model = searchModel;
    }

    // Сохраняем обновленные фильтры
    saveFilters();
  } catch (error) {
    console.error("Ошибка при синхронизации поиска с фильтрами:", error);
  }
}

// Функция для сортировки автомобилей по цене
function sortCarsByPrice(cars, order = "desc") {
  if (!cars || !Array.isArray(cars)) return cars;

  return [...cars].sort((a, b) => {
    const priceA = parseFloat(a.price || 0);
    const priceB = parseFloat(b.price || 0);

    if (order === "desc") {
      return priceB - priceA; // От дорогого к дешевому
    } else {
      return priceA - priceB; // От дешевого к дорогому
    }
  });
}

// Функция для корректного расчета цены в USD
function calculateUSDPrice(koreanWonPrice) {
  // Если цена не указана, возвращаем 0
  if (!koreanWonPrice) return 0;

  // Текущий курс USD к KWN (Korean Won)
  const usdToKwnRate = 1320; // Это примерное значение, в реальности нужно получать актуальный курс

  // Применяем формулу: USD = (цена с сайта в вонах × 10,000) ÷ Курс_USD_к_KWN
  return Math.round((koreanWonPrice * 10000) / usdToKwnRate);
}

// Инициализация фильтров
function initFilters() {
  // Получаем элементы фильтров
  const brandFilter = document.getElementById("brand-filter");
  const modelFilter = document.getElementById("model-filter");
  const bodyFilter = document.getElementById("body-filter");
  const transmissionFilter = document.getElementById("transmission-filter");
  const fuelFilter = document.getElementById("fuel-filter");
  const driveFilter = document.getElementById("drive-filter");
  const categoryFilter = document.getElementById("category-filter"); // Новый фильтр категории
  const sortFilter = document.getElementById("sort-filter");

  // Устанавливаем сохраненные значения из activeFilters
  if (brandFilter) brandFilter.value = activeFilters.brand || "";
  if (activeFilters.brand) {
    updateModelSelect(activeFilters.brand);
    if (modelFilter) modelFilter.value = activeFilters.model || "";
  }

  if (bodyFilter) bodyFilter.value = activeFilters.bodyType || "";
  if (transmissionFilter)
    transmissionFilter.value = activeFilters.transmission || "";
  if (fuelFilter) fuelFilter.value = activeFilters.fuelType || "";
  if (driveFilter) driveFilter.value = activeFilters.driveType || "";
  if (categoryFilter) categoryFilter.value = activeFilters.category || ""; // Устанавливаем категорию

  // Элементы ползунков
  const yearFromSlider = document.getElementById("year-from-slider");
  const yearToSlider = document.getElementById("year-to-slider");
  const priceFromSlider = document.getElementById("price-from-slider");
  const priceToSlider = document.getElementById("price-to-slider");
  const mileageFromSlider = document.getElementById("mileage-from-slider");
  const mileageToSlider = document.getElementById("mileage-to-slider");

  // Элементы отображения текущих значений
  const yearDisplay = document.getElementById("year-display");
  const priceDisplay = document.getElementById("price-display");
  const mileageDisplay = document.getElementById("mileage-display");

  const applyButton = document.getElementById("apply-filters");
  const resetButton = document.getElementById("reset-filters");

  // Получаем текущий год
  const currentYear = getCurrentYear();

  // Устанавливаем сохраненные значения для ползунков
  if (yearFromSlider && yearToSlider) {
    yearToSlider.max = currentYear;
    yearFromSlider.value = activeFilters.yearFrom || yearFromSlider.min;
    yearToSlider.value = activeFilters.yearTo || currentYear;
    initRangeSlider(yearFromSlider, yearToSlider, yearDisplay, "г.", "");
  }

  // Увеличиваем максимальную цену до 10 млн
  if (priceFromSlider && priceToSlider) {
    priceToSlider.max = "10000000"; // Увеличиваем максимум до 10,000,000
    priceFromSlider.value = activeFilters.priceFrom || priceFromSlider.min;
    priceToSlider.value = activeFilters.priceTo || priceToSlider.max;
    initRangeSlider(priceFromSlider, priceToSlider, priceDisplay, "$", true);
  }

  if (mileageFromSlider && mileageToSlider) {
    mileageFromSlider.value =
      activeFilters.mileageFrom || mileageFromSlider.min;
    mileageToSlider.value = activeFilters.mileageTo || mileageToSlider.max;
    initRangeSlider(
      mileageFromSlider,
      mileageToSlider,
      mileageDisplay,
      " км",
      true
    );
  }

  // Обработчик изменения сортировки
  if (sortFilter) {
    sortFilter.value = sortByPrice;
    sortFilter.addEventListener("change", function () {
      sortByPrice = this.value;
      currentPage = 1;
      loadCars(true); // Перезагрузить авто с новой сортировкой
    });
  }

  // Применение фильтров
  if (applyButton) {
    applyButton.addEventListener("click", applyFilters);
  }

  // Сброс фильтров
  if (resetButton) {
    resetButton.addEventListener("click", resetFilters);
  }

  // При изменении бренда обновляем список моделей и подсказки для поиска
  if (brandFilter) {
    brandFilter.addEventListener("change", function () {
      const selectedBrand = this.value;
      updateModelSelect(selectedBrand);

      // Синхронизируем с полем поиска
      syncFiltersWithSearch();
    });
  }

  // При изменении модели также синхронизируем с полем поиска
  if (modelFilter) {
    modelFilter.addEventListener("change", function () {
      syncFiltersWithSearch();
    });
  }

  // Функция инициализации ползунков диапазона
  function initRangeSlider(
    fromSlider,
    toSlider,
    displayElement,
    suffix,
    formatNumbers
  ) {
    // Создаем трек для слайдера
    const sliderContainer = fromSlider.parentElement;
    const sliderTrack = document.createElement("div");
    sliderTrack.className = "slider-track";
    const sliderColor = document.createElement("div");
    sliderColor.className = "slider-color";
    sliderTrack.appendChild(sliderColor);

    // Убеждаемся, что track стоит первым, чтобы не перекрывать управляющие элементы
    if (sliderContainer.firstChild) {
      sliderContainer.insertBefore(sliderTrack, sliderContainer.firstChild);
    } else {
      sliderContainer.appendChild(sliderTrack);
    }

    // Функция для обновления позиции и цвета трека
    function updateTrack() {
      const fromValue = parseInt(fromSlider.value);
      const toValue = parseInt(toSlider.value);

      const min = parseInt(fromSlider.min);
      const max = parseInt(toSlider.max);
      const range = max - min;

      // Рассчитываем позиции в процентах
      const fromPosition = ((fromValue - min) / range) * 100;
      const toPosition = ((toValue - min) / range) * 100;

      // Обновляем стили трека
      sliderColor.style.left = fromPosition + "%";
      sliderColor.style.width = toPosition - fromPosition + "%";
    }

    // Обновление значения при изменении левого ползунка
    fromSlider.addEventListener("input", function () {
      // Убеждаемся, что левый ползунок не больше правого
      if (parseInt(fromSlider.value) > parseInt(toSlider.value)) {
        fromSlider.value = toSlider.value;
      }
      updateTrack();
      updateRangeDisplay(
        fromSlider,
        toSlider,
        displayElement,
        suffix,
        formatNumbers
      );
    });

    // Обновление значения при изменении правого ползунка
    toSlider.addEventListener("input", function () {
      // Убеждаемся, что правый ползунок не меньше левого
      if (parseInt(toSlider.value) < parseInt(fromSlider.value)) {
        toSlider.value = fromSlider.value;
      }
      updateTrack();
      updateRangeDisplay(
        fromSlider,
        toSlider,
        displayElement,
        suffix,
        formatNumbers
      );
    });

    // Инициализация отображения и трека
    updateTrack();
    updateRangeDisplay(
      fromSlider,
      toSlider,
      displayElement,
      suffix,
      formatNumbers
    );
  }

  // Функция обновления отображения диапазона
  function updateRangeDisplay(
    fromSlider,
    toSlider,
    displayElement,
    suffix,
    formatNumbers
  ) {
    let fromValue = fromSlider.value;
    let toValue = toSlider.value;

    // Применяем форматирование чисел, если требуется
    if (formatNumbers) {
      fromValue = new Intl.NumberFormat("ru-RU").format(fromValue);
      toValue = new Intl.NumberFormat("ru-RU").format(toValue);
    }

    displayElement.textContent = `${fromValue}${suffix} - ${toValue}${suffix}`;
  }

  // Синхронизация фильтров с полем поиска
  function syncFiltersWithSearch() {
    const brandSelect = document.getElementById("brand-filter");
    const modelSelect = document.getElementById("model-filter");
    const searchInput = document.querySelector(".search-input");

    if (!searchInput) return;

    // Формируем строку поиска на основе выбранных фильтров
    let searchString = "";

    if (brandSelect && brandSelect.value) {
      searchString = brandSelect.value;

      if (modelSelect && modelSelect.value) {
        searchString += " " + modelSelect.value;
      }
    }

    // Обновляем поле поиска только если в нем есть изменения
    if (searchString && searchString !== searchInput.value) {
      searchInput.value = searchString;
      searchQuery = searchString;
    }
  }
}

// Функция обновления списка моделей при выборе марки
function updateModelSelect(brand) {
  const modelFilter = document.getElementById("model-filter");
  if (!modelFilter) return;

  // Очищаем текущий список моделей
  modelFilter.innerHTML = '<option value="">Все модели</option>';

  // Если марка не выбрана, ничего не делаем
  if (!brand) return;

  // Если есть модели для выбранной марки, добавляем их в список
  if (carModels[brand]) {
    carModels[brand].forEach((model) => {
      const option = document.createElement("option");
      option.value = model;
      option.textContent = model;
      modelFilter.appendChild(option);
    });
  }
}

// Функция применения фильтров
function applyFilters() {
  // Получаем элементы фильтров
  const brandFilter = document.getElementById("brand-filter");
  const modelFilter = document.getElementById("model-filter");
  const bodyFilter = document.getElementById("body-filter");
  const transmissionFilter = document.getElementById("transmission-filter");
  const fuelFilter = document.getElementById("fuel-filter");
  const driveFilter = document.getElementById("drive-filter");
  const categoryFilter = document.getElementById("category-filter"); // Добавляем категорию
  const sortFilter = document.getElementById("sort-filter"); // Добавляем сортировку

  // Элементы ползунков
  const yearFromSlider = document.getElementById("year-from-slider");
  const yearToSlider = document.getElementById("year-to-slider");
  const priceFromSlider = document.getElementById("price-from-slider");
  const priceToSlider = document.getElementById("price-to-slider");
  const mileageFromSlider = document.getElementById("mileage-from-slider");
  const mileageToSlider = document.getElementById("mileage-to-slider");

  // Собираем значения фильтров
  activeFilters = {
    brand: brandFilter ? brandFilter.value : "",
    model: modelFilter ? modelFilter.value : "",
    bodyType: bodyFilter ? bodyFilter.value : "",
    transmission: transmissionFilter ? transmissionFilter.value : "",
    fuelType: fuelFilter ? fuelFilter.value : "",
    driveType: driveFilter ? driveFilter.value : "",
    category: categoryFilter ? categoryFilter.value : "", // Устанавливаем категорию

    // Значения из ползунков
    yearFrom: yearFromSlider ? yearFromSlider.value : "",
    yearTo: yearToSlider ? yearToSlider.value : "",
    priceFrom: priceFromSlider ? priceFromSlider.value : "",
    priceTo: priceToSlider ? priceToSlider.value : "",
    mileageFrom: mileageFromSlider ? mileageFromSlider.value : "",
    mileageTo: mileageToSlider ? mileageToSlider.value : "",
  };

  // Устанавливаем сортировку
  if (sortFilter) {
    sortByPrice = sortFilter.value;
  }

  // Синхронизируем поле поиска с фильтрами
  const searchInput = document.querySelector(".search-input");
  if (searchInput && activeFilters.brand) {
    let searchValue = activeFilters.brand;
    if (activeFilters.model) {
      searchValue += " " + activeFilters.model;
    }
    searchInput.value = searchValue;
    searchQuery = searchValue;
  }

  // Сохраняем фильтры в localStorage
  saveFilters();

  // Обновляем поиск с учетом фильтров
  currentPage = 1;
  loadCars(true);

  // Закрываем панель фильтров
  const filterDropdown = document.querySelector(".filter-dropdown");
  if (filterDropdown) filterDropdown.removeAttribute("open");
}

// Функция сброса фильтров
function resetFilters() {
  // Получаем элементы фильтров
  const brandFilter = document.getElementById("brand-filter");
  const modelFilter = document.getElementById("model-filter");
  const bodyFilter = document.getElementById("body-filter");
  const transmissionFilter = document.getElementById("transmission-filter");
  const fuelFilter = document.getElementById("fuel-filter");
  const driveFilter = document.getElementById("drive-filter");
  const categoryFilter = document.getElementById("category-filter"); // Новый фильтр категории
  const sortFilter = document.getElementById("sort-filter"); // Новый элемент для сортировки

  // Элементы ползунков
  const yearFromSlider = document.getElementById("year-from-slider");
  const yearToSlider = document.getElementById("year-to-slider");
  const priceFromSlider = document.getElementById("price-from-slider");
  const priceToSlider = document.getElementById("price-to-slider");
  const mileageFromSlider = document.getElementById("mileage-from-slider");
  const mileageToSlider = document.getElementById("mileage-to-slider");

  // Элементы отображения
  const yearDisplay = document.getElementById("year-display");
  const priceDisplay = document.getElementById("price-display");
  const mileageDisplay = document.getElementById("mileage-display");

  // Получаем текущий год
  const currentYear = getCurrentYear();

  // Сбрасываем значения в элементах фильтров
  if (brandFilter) brandFilter.value = "";
  if (modelFilter) {
    modelFilter.innerHTML = '<option value="">Все модели</option>';
  }
  if (bodyFilter) bodyFilter.value = "";
  if (transmissionFilter) transmissionFilter.value = "";
  if (fuelFilter) fuelFilter.value = "";
  if (driveFilter) driveFilter.value = "";
  if (categoryFilter) categoryFilter.value = ""; // Устанавливаем категорию

  // Сбрасываем ползунки на исходные значения
  if (yearFromSlider) yearFromSlider.value = yearFromSlider.min;
  if (yearToSlider) yearToSlider.value = currentYear; // Используем текущий год
  if (yearDisplay)
    yearDisplay.textContent = `${yearFromSlider.min} г. - ${currentYear} г.`;

  if (priceFromSlider) priceFromSlider.value = priceFromSlider.min;
  if (priceToSlider) priceToSlider.value = priceToSlider.max;
  if (priceDisplay)
    priceDisplay.textContent = `$0 - $${new Intl.NumberFormat("ru-RU").format(
      priceToSlider.max
    )}`;

  if (mileageFromSlider) mileageFromSlider.value = mileageFromSlider.min;
  if (mileageToSlider) mileageToSlider.value = mileageToSlider.max;
  if (mileageDisplay)
    mileageDisplay.textContent = `0 - ${new Intl.NumberFormat("ru-RU").format(
      mileageToSlider.max
    )} км`;

  // Сбрасываем активные фильтры
  activeFilters = {
    brand: "",
    model: "",
    bodyType: "",
    transmission: "",
    fuelType: "",
    yearFrom: "",
    yearTo: "",
    priceFrom: "",
    priceTo: "",
    mileageFrom: "",
    mileageTo: "",
    driveType: "",
    category: "",
  };

  // Удаляем сохраненные фильтры из localStorage
  localStorage.removeItem(STORAGE_KEY);

  // Сбрасываем поле поиска
  const searchInput = document.querySelector(".search-input");
  if (searchInput) searchInput.value = "";
  searchQuery = "";

  // Обновляем поиск
  currentPage = 1;
  loadCars(true);
}

// ... existing code ...
// Остальной код оставляем без изменений, функции handleScroll, preloadNextPage, formatPrice, formatMileage, getLogo и т.д.

// Handle scroll for infinite scrolling
function handleScroll() {
  // Функция отключена, чтобы использовать кнопку "Загрузить еще" вместо бесконечной прокрутки
  return;
}

// Предзагрузка следующей страницы данных
function preloadNextPage() {
  const nextPage = currentPage + 1;
  const apiUrl = buildApiUrl(nextPage);
  const cacheKey = apiUrl.toString();

  console.log(`Preloading page ${nextPage} from: ${cacheKey}`);

  // Если данные уже в кэше, не делаем запрос
  if (apiCache[cacheKey]) {
    console.log(`Page ${nextPage} already in cache`);
    return;
  }

  // Выполняем запрос без обновления интерфейса
  fetch(apiUrl)
    .then((response) => response.json())
    .then((data) => {
      // Сохраняем данные в кэш
      apiCache[cacheKey] = Array.isArray(data)
        ? data
        : data.cars
        ? data.cars
        : [];
      console.log(`Page ${nextPage} preloaded and cached`);
    })
    .catch((error) => {
      console.warn(`Failed to preload page ${nextPage}:`, error);
    });
}

// Format price in USD
function formatPrice(price) {
  if (!price) return "$0";

  // Пересчитываем цену по новой формуле
  const usdPrice = calculateUSDPrice(price);

  return new Intl.NumberFormat("ru-RU", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(usdPrice);
}

// Format mileage
function formatMileage(km) {
  // Проверяем пустое значение или значение равное нулю
  if (!km || km === "0" || km === 0) return "Без пробега";

  // Проверяем, является ли значение уже отформатированной строкой
  if (typeof km === "string" && km.includes("км")) {
    return km;
  }

  // Если значение является строкой, извлекаем только цифры
  if (typeof km === "string") {
    const originalValue = km;
    // Удаляем все нецифровые символы
    km = km.replace(/[^0-9]/g, "");

    // Если после фильтрации получили пустую строку или ноль
    if (!km || km === "0") {
      // Если исходное значение не было пустым, возвращаем его
      if (originalValue && originalValue !== "0") {
        // Добавляем единицы измерения, если их нет
        if (!originalValue.includes("км")) {
          return originalValue + " км";
        }
        return originalValue;
      }
      return "Без пробега";
    }
  }

  // Преобразуем в число
  const mileageNumber = parseInt(km, 10);

  // Проверка на очень большие значения (возможно, ошибка)
  if (mileageNumber > 1000000) {
    // Если значение слишком большое, возможно это записано в метрах, конвертируем в км
    return (
      new Intl.NumberFormat("ru-RU").format(Math.round(mileageNumber / 1000)) +
      " км"
    );
  }

  // Преобразуем в число и форматируем с разделителями
  return new Intl.NumberFormat("ru-RU").format(mileageNumber) + " км";
}

// Get logo for car brand
function getLogo(brand) {
  if (!brand) return "PNG/default-logo.png";

  const brandMap = {
    ferrari: "PNG/ferrari-logo.png",
    lamborghini: "PNG/lamborghini-logo.png",
    bentley: "PNG/bentley-logo.png",
    "aston martin": "PNG/aston-martin-logo.png",
    bmw: "PNG/bmw-logo.png",
    audi: "PNG/audi-logo.png",
    mercedes: "PNG/default-logo.png",
    "mercedes-benz": "PNG/default-logo.png",
    porsche: "PNG/default-logo.png",
    "alfa romeo": "PNG/default-logo.png",
    maserati: "PNG/default-logo.png",
  };

  return brandMap[brand.toLowerCase()] || "PNG/default-logo.png";
}

// Build API URL with current parameters
function buildApiUrl(page = currentPage) {
  console.log("=== ОТЛАДКА buildApiUrl ===");
  console.log("Текущая страница:", page);
  console.log("useProxy:", useProxy);
  console.log("searchQuery:", searchQuery);
  console.log("activeFilters:", activeFilters);

  // Build base URL depending on useProxy flag (используем проксирование через JavaScript сервер)
  const apiBase = useProxy
    ? `${BASE_URL}/api/encar` // Проксирование к encar-proxy.php через JavaScript сервер
    : `${BASE_URL}/api/mock-data`; // Проксирование к mock-data.php через JavaScript сервер

  console.log("API base URL:", apiBase);

  // Create URL with parameters
  const apiUrl = new URL(apiBase);

  // Add common parameters
  apiUrl.searchParams.append("fileType", "active_offer");
  apiUrl.searchParams.append("date", new Date().toISOString().split("T")[0]);
  apiUrl.searchParams.append("limit", pageSize);
  apiUrl.searchParams.append("offset", (page - 1) * pageSize);

  // Add featured flag if needed
  if (featuredOnly) {
    apiUrl.searchParams.append("featured", "1");
  }

  // Добавляем фильтры, если они активны
  if (activeFilters) {
    // Фильтр по марке (приоритетнее чем поиск, если заполнены оба)
    if (activeFilters.brand) {
      apiUrl.searchParams.append("brand", activeFilters.brand);

      // Если также выбрана модель, добавляем её
      if (activeFilters.model) {
        apiUrl.searchParams.append("model", activeFilters.model);
      }
    }
    // Если марка не выбрана в фильтрах, но есть поисковый запрос
    else if (searchQuery) {
      // Используем умное разделение марки и модели
      const { brand: searchBrand, model: searchModel } =
        smartBrandModelSplit(searchQuery);

      if (searchBrand) {
        apiUrl.searchParams.append("brand", searchBrand);
        if (searchModel) {
          apiUrl.searchParams.append("model", searchModel);
        }
      }
    }

    // Фильтры по году
    if (activeFilters.yearFrom) {
      apiUrl.searchParams.append("year_from", activeFilters.yearFrom);
    }

    if (activeFilters.yearTo) {
      apiUrl.searchParams.append("year_to", activeFilters.yearTo);
    }

    // Фильтры по цене
    if (activeFilters.priceFrom) {
      apiUrl.searchParams.append("price_from", activeFilters.priceFrom);
    }

    if (activeFilters.priceTo) {
      apiUrl.searchParams.append("price_to", activeFilters.priceTo);
    }

    // Фильтр по трансмиссии
    if (activeFilters.transmission) {
      apiUrl.searchParams.append("transmission", activeFilters.transmission);
    }

    // Фильтр по типу кузова
    if (activeFilters.bodyType) {
      apiUrl.searchParams.append("body_type", activeFilters.bodyType);
    }

    // Фильтр по типу топлива/двигателя
    if (activeFilters.fuelType) {
      apiUrl.searchParams.append("fuel_type", activeFilters.fuelType);
    }

    // Фильтры по пробегу
    if (activeFilters.mileageFrom) {
      apiUrl.searchParams.append("mileage_from", activeFilters.mileageFrom);
    }

    if (activeFilters.mileageTo) {
      apiUrl.searchParams.append("mileage_to", activeFilters.mileageTo);
    }

    // Фильтр по приводу
    if (activeFilters.driveType) {
      apiUrl.searchParams.append("drive_type", activeFilters.driveType);
    }

    // Фильтр по категории (приоритет у activeFilters.category)
    if (activeFilters.category) {
      apiUrl.searchParams.append("category", activeFilters.category);
    }
  }

  // Добавляем категорию из categoryFilter, если она не была добавлена через activeFilters
  if (categoryFilter && (!activeFilters || !activeFilters.category)) {
    console.log("Добавляем categoryFilter:", categoryFilter);
    apiUrl.searchParams.append("category", categoryFilter);
  }

  // Если нет активных фильтров, но есть поисковый запрос
  if (searchQuery && (!activeFilters || !activeFilters.brand)) {
    console.log("Обрабатываем поисковый запрос:", searchQuery);
    // Используем умное разделение марки и модели
    const { brand: searchBrand, model: searchModel } =
      smartBrandModelSplit(searchQuery);

    console.log(
      "Разделенный поиск - марка:",
      searchBrand,
      "модель:",
      searchModel
    );

    if (searchBrand) {
      apiUrl.searchParams.append("brand", searchBrand);
      if (searchModel) {
        apiUrl.searchParams.append("model", searchModel);
      }
    }
  }

  console.log("Итоговый URL:", apiUrl.toString());
  console.log("=== КОНЕЦ ОТЛАДКИ buildApiUrl ===");

  return apiUrl;
}

// Load cars from API
async function loadCars(reset = false) {
  if (isLoading) return;
  isLoading = true;

  const cardsContainer = document.querySelector(".order-cards");
  const loadMoreBtn = document.getElementById("load-more-btn");
  if (!cardsContainer) {
    isLoading = false;
    return;
  }

  // Скрываем кнопку "Загрузить еще" во время загрузки
  if (loadMoreBtn) {
    loadMoreBtn.style.display = "none";
  }

  // Show loading indicator
  if (reset) {
    cardsContainer.innerHTML =
      '<div class="loading">Загрузка автомобилей...</div>';
    cachedData = []; // Clear cached data on reset
  } else {
    // Add loading indicator at the bottom
    const loadingIndicator = document.createElement("div");
    loadingIndicator.className = "loading";
    loadingIndicator.textContent = "Загрузка дополнительных автомобилей...";
    cardsContainer.appendChild(loadingIndicator);
  }

  try {
    // Устанавливаем таймаут для проверки длительной загрузки
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error("Timeout")), 10000); // 10 секунд на загрузку
    });

    // Fetch cars data with timeout
    const dataPromise = fetchCarsData();
    const data = await Promise.race([dataPromise, timeoutPromise]);

    if (data.length === 0 && currentPage === 1) {
      // No results found
      cardsContainer.innerHTML = `
        <div class="no-results">
          <h3>По вашему запросу ничего не найдено</h3>
          <p>Такого авто в наличии нет. Попробуйте изменить параметры поиска или выбрать другую марку/модель.</p>
          <button class="reset-search-button">Сбросить поиск</button>
        </div>
      `;

      const resetButton = cardsContainer.querySelector(".reset-search-button");
      if (resetButton) {
        resetButton.addEventListener("click", () => {
          const searchInput = document.querySelector(".search-input");
          if (searchInput) searchInput.value = "";
          searchQuery = "";
          featuredOnly = false;
          const featuredToggle = document.getElementById("featured-toggle");
          if (featuredToggle) featuredToggle.checked = false;

          // Сбросить все фильтры
          const resetFiltersButton = document.getElementById("reset-filters");
          if (resetFiltersButton) {
            resetFiltersButton.click();
          } else {
            currentPage = 1;
            loadCars(true);
          }
        });
      }

      isLoading = false;
      return;
    }

    // Add new data to cached data
    cachedData = reset ? data : [...cachedData, ...data];

    // Display cars
    displayCars(cachedData, reset);

    // Показываем кнопку "Загрузить еще", если количество полученных данных равно размеру страницы
    // (это значит, что могут быть еще данные)
    if (loadMoreBtn && data.length >= pageSize) {
      loadMoreBtn.style.display = "block";
    } else if (loadMoreBtn) {
      loadMoreBtn.style.display = "none";
    }

    // Отключаем предзагрузку следующей страницы, т.к. теперь используем кнопку
    /*
    if (!nextPagePreloaded) {
      setTimeout(() => {
        preloadNextPage();
        nextPagePreloaded = true;
      }, 300);
    }
    */
  } catch (error) {
    console.error("Error loading cars:", error);

    if (reset) {
      // Показываем сообщение о том, что авто не найдены, если ошибка таймаута
      if (error.message === "Timeout") {
        cardsContainer.innerHTML = `
          <div class="no-results">
            <h3>Автомобили не найдены</h3>
            <p>Такого авто в наличии нет. Попробуйте изменить параметры поиска или выбрать другую марку/модель.</p>
            <button class="reset-search-button">Сбросить поиск</button>
          </div>
        `;

        const resetButton = cardsContainer.querySelector(
          ".reset-search-button"
        );
        if (resetButton) {
          resetButton.addEventListener("click", () => {
            const searchInput = document.querySelector(".search-input");
            if (searchInput) searchInput.value = "";
            searchQuery = "";

            // Сбросить все фильтры
            const resetFiltersButton = document.getElementById("reset-filters");
            if (resetFiltersButton) {
              resetFiltersButton.click();
            } else {
              currentPage = 1;
              loadCars(true);
            }
          });
        }
      } else {
        // Show error message with retry button for other errors
        cardsContainer.innerHTML = `
          <div class="error">
            Произошла ошибка при загрузке данных: ${error.message}
            <br><br>
            <button class="retry-button">Повторить попытку</button>
            <button class="use-mock-button">Использовать тестовые данные</button>
          </div>
        `;

        // Add retry button handler
        const retryButton = cardsContainer.querySelector(".retry-button");
        if (retryButton) {
          retryButton.addEventListener("click", () => loadCars(true));
        }

        // Add mock data button handler
        const mockButton = cardsContainer.querySelector(".use-mock-button");
        if (mockButton) {
          mockButton.addEventListener("click", () => {
            useProxy = false; // Switch to mock data
            loadCars(true);
          });
        }
      }
    } else {
      // Remove loading indicator if appending
      const loadingIndicator = cardsContainer.querySelector(".loading");
      if (loadingIndicator) {
        cardsContainer.removeChild(loadingIndicator);
      }
    }

    // Скрываем кнопку "Загрузить еще" в случае ошибки
    if (loadMoreBtn) {
      loadMoreBtn.style.display = "none";
    }
  } finally {
    isLoading = false;
  }
}

// Модифицируем функцию fetchCarsData для работы с главными карточками
async function fetchCarsData() {
  // Используем константу BASE_URL вместо жесткой строки

  // Если выбраны главные карточки (featured) или первая загрузка, загружаем данные из JSON
  if (featuredOnly) {
    return loadFeaturedCarsFromJson();
  }

  // Если выбрана конкретная категория, устанавливаем фильтр
  if (categoryFilter && (!activeFilters || !activeFilters.category)) {
    // Устанавливаем фильтр категории для API только если он еще не установлен
    if (!activeFilters) activeFilters = {};
    activeFilters.category = categoryFilter;
    console.log(`Setting category filter: ${categoryFilter}`);
  }

  // Обычный поиск через API...
  // Строим URL API с текущими параметрами
  const apiUrl = buildApiUrl();
  const cacheKey = apiUrl.toString();

  console.log(
    `Fetching cars from: ${cacheKey} (${useProxy ? "Real API" : "Mock Data"})`
  );

  // Проверяем кэш перед запросом к серверу
  if (apiCache[cacheKey]) {
    console.log(`Using cached data for page ${currentPage}: ${cacheKey}`);
    return apiCache[cacheKey];
  }

  // Set timeout for fetch
  const timeoutMs = 60000; // 60 seconds (увеличен с 30 до 60 секунд)

  // Create the fetch promise
  const fetchPromise = fetch(apiUrl);

  // Create a timeout promise
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error("Request timed out")), timeoutMs);
  });

  try {
    // Race the fetch against the timeout
    const response = await Promise.race([fetchPromise, timeoutPromise]);

    if (!response.ok) {
      // Если API не отвечает, возвращаем ошибку вместо переключения на mock-data
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Handle empty results
    if (Array.isArray(data) && data.length === 0) {
      console.log("No results found");
      return [];
    }

    // Обрабатываем ответ и кэшируем
    let result = [];

    // If we get an object with cars array
    if (data.cars) {
      result = data.cars;
    }
    // If we get an array directly
    else if (Array.isArray(data)) {
      result = data;
    } else {
      // Handle unknown response format
      console.warn("Unknown response format:", data);
      return [];
    }

    // Фильтруем полученные автомобили, чтобы удалить дубликаты (по ID)
    // Но только если это не первая страница (currentPage > 1)
    if (cachedData.length > 0 && currentPage > 1) {
      const existingIds = cachedData.map((car) => car.id);
      const originalLength = result.length;
      result = result.filter((car) => !existingIds.includes(car.id));
      console.log(
        `Page ${currentPage}: Filtered ${originalLength} cars to ${
          result.length
        } unique cars (removed ${originalLength - result.length} duplicates)`
      );
    } else {
      console.log(
        `Page ${currentPage}: Loaded ${result.length} cars (no duplicate filtering needed)`
      );
    }

    // Сохраняем результат в кэше
    apiCache[cacheKey] = result;

    return result;
  } catch (error) {
    console.error("Error fetching data:", error);
    // Если данные нельзя получить, возвращаем пустой массив
    return [];
  }
}

// Функция для загрузки данных о главных карточках из JSON
async function loadFeaturedCarsFromJson() {
  console.log("Loading featured cars from JSON");

  try {
    // Загружаем данные из файла featured-cars.json
    const response = await fetch(`${BASE_URL}/data/featured-cars.json`);
    if (!response.ok) {
      console.error("Failed to fetch featured cars data:", response.status);
      console.log("Falling back to API data for featured cars");
      // Если файл не найден, используем обычный API с параметром featured
      return loadFeaturedCarsFromAPI();
    }

    const cars = await response.json();
    console.log(`Loaded ${cars.length} featured cars from JSON`);
    return cars;
  } catch (error) {
    console.error("Error loading featured cars from JSON:", error);
    console.log("Falling back to API data for featured cars");
    // Если произошла ошибка, используем обычный API
    return loadFeaturedCarsFromAPI();
  }
}

// Функция для загрузки рекомендуемых автомобилей через API
async function loadFeaturedCarsFromAPI() {
  console.log("Loading featured cars from API");

  try {
    // Используем только реальный API через JavaScript сервер
    const apiBase = `${BASE_URL}/api/encar`; // Проксирование к encar-proxy.php через JavaScript сервер

    const apiUrl = new URL(apiBase);
    apiUrl.searchParams.append("fileType", "active_offer");
    apiUrl.searchParams.append("date", new Date().toISOString().split("T")[0]);
    apiUrl.searchParams.append("limit", "12");
    apiUrl.searchParams.append("offset", "0");

    const response = await fetch(apiUrl);
    if (!response.ok) {
      console.error("Failed to fetch featured cars from API:", response.status);
      return [];
    }

    const cars = await response.json();
    console.log(`Loaded ${cars.length} featured cars from API`);
    return Array.isArray(cars) ? cars : [];
  } catch (error) {
    console.error("Error loading featured cars from API:", error);
    return [];
  }
}

// Функция для загрузки данных о категориях из JSON
async function loadCategoryFilteredCarsFromJson(category) {
  console.log(`Loading ${category} cars from JSON`);

  try {
    // Загружаем данные из файла featured-cars.json
    const response = await fetch(`${BASE_URL}/data/featured-cars.json`);
    if (!response.ok) {
      console.error("Failed to fetch featured cars data:", response.status);
      console.log(`Falling back to API data for ${category} cars`);
      // Если файл не найден, используем обычный API
      return loadFeaturedCarsFromAPI();
    }

    // Получаем все автомобили и фильтруем их по категории
    const allCars = await response.json();
    const filteredCars = allCars.filter((car) => car.category === category);
    console.log(`Loaded ${filteredCars.length} ${category} cars from JSON`);

    return filteredCars;
  } catch (error) {
    console.error(`Error loading ${category} cars from JSON:`, error);
    console.log(`Falling back to API data for ${category} cars`);
    // Если произошла ошибка, используем обычный API
    return loadFeaturedCarsFromAPI();
  }
}

// Display cars in the container
function displayCars(cars, reset = true) {
  console.log("=== ОТЛАДКА displayCars ===");
  console.log("Получено автомобилей:", cars ? cars.length : 0);
  console.log("Данные автомобилей:", cars);
  console.log("Reset:", reset);

  const cardsContainer = document.querySelector(".order-cards");
  console.log("Контейнер найден:", !!cardsContainer);
  if (!cardsContainer) {
    console.error("Контейнер .order-cards не найден!");
    return;
  }

  // Remove loading indicator
  const loadingIndicator = cardsContainer.querySelector(".loading");
  if (loadingIndicator) {
    console.log("Удаляем индикатор загрузки");
    cardsContainer.removeChild(loadingIndicator);
  }

  if (!cars || cars.length === 0) {
    console.log("Нет автомобилей для отображения");
    cardsContainer.innerHTML =
      '<div class="no-cars">Автомобили не найдены</div>';
    return;
  }

  // Сортируем автомобили по цене
  console.log("Сортируем автомобили...");
  const sortedCars = sortCarsByPrice(cars, sortByPrice);
  console.log("Отсортировано автомобилей:", sortedCars.length);

  // Create HTML for cars
  console.log("Создаем HTML для карточек...");
  const carsHTML = sortedCars
    .map((car, index) => {
      console.log(
        `Создаем карточку ${index + 1}:`,
        car.mark || car.brand,
        car.model
      );
      try {
        const cardHTML = createCarCard(car);
        console.log(`Карточка ${index + 1} создана успешно`);
        return cardHTML;
      } catch (error) {
        console.error(`Ошибка создания карточки ${index + 1}:`, error);
        return `<div class="error-card">Ошибка загрузки автомобиля</div>`;
      }
    })
    .join("");

  console.log("HTML карточек создан, длина:", carsHTML.length);

  // Update the container
  if (reset) {
    console.log("Заменяем содержимое контейнера");
    cardsContainer.innerHTML = carsHTML;
  } else {
    console.log("Добавляем к существующему содержимому");
    // Append new cars (without replacing existing ones)
    cardsContainer.insertAdjacentHTML("beforeend", carsHTML);
  }

  console.log("Содержимое контейнера обновлено");
  console.log(
    "Итоговое содержимое контейнера:",
    cardsContainer.innerHTML.substring(0, 200) + "..."
  );
  console.log("=== КОНЕЦ ОТЛАДКИ displayCars ===");

  // Add car count summary
  const statusContainer = document.querySelector(".order-status");
  if (statusContainer) {
    statusContainer.innerHTML = `
      <div class="order-found">
        <span>Найдено автомобилей: ${cars.length}</span>
        ${searchQuery ? `<span> | Поиск: ${searchQuery}</span>` : ""}
      </div>
    `;
  }

  // Инициализируем галерею изображений для карточек
  initCardImageGallery();
}

// Инициализация галереи изображений в карточках
function initCardImageGallery() {
  // Находим все кнопки навигации
  const prevButtons = document.querySelectorAll(".prev-img");
  const nextButtons = document.querySelectorAll(".next-img");

  // Добавляем обработчики для кнопок "предыдущее изображение"
  prevButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      e.stopPropagation();
      navigateCardImage(this.getAttribute("data-car-id"), -1);
    });
  });

  // Добавляем обработчики для кнопок "следующее изображение"
  nextButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      e.stopPropagation();
      navigateCardImage(this.getAttribute("data-car-id"), 1);
    });
  });
}

// Функция навигации по изображениям в карточке
function navigateCardImage(carId, direction) {
  // Находим карточку по ID автомобиля
  const carCard = document.querySelector(`.order-card[data-id="${carId}"]`);
  if (!carCard) return;

  // Находим текущее изображение
  const img = carCard.querySelector("img[data-car-id]");
  if (!img) return;

  // Получаем текущий индекс изображения
  let currentIndex = parseInt(img.getAttribute("data-img-index") || "0");

  // Находим автомобиль в кеше
  const car = cachedData.find((c) => c.id == carId);
  if (!car || !car.images) return;

  // Получаем массив изображений
  let images;
  try {
    // Проверяем, является ли car.images массивом или строкой JSON
    images =
      typeof car.images === "string" ? JSON.parse(car.images) : car.images;
  } catch (e) {
    console.warn("Ошибка при разборе изображений:", e);
    return;
  }

  if (!Array.isArray(images) || images.length <= 1) return;

  // Вычисляем новый индекс
  let newIndex = currentIndex + direction;

  // Применяем круговую навигацию
  if (newIndex < 0) newIndex = images.length - 1;
  if (newIndex >= images.length) newIndex = 0;

  // Обновляем изображение с красивой анимацией
  img.style.opacity = "0";
  img.style.transform = "scale(0.95)";

  setTimeout(() => {
    img.src = images[newIndex];
    img.setAttribute("data-img-index", newIndex.toString());

    setTimeout(() => {
      img.style.opacity = "1";
      img.style.transform = "scale(1)";
    }, 50);
  }, 250);
}

// Create HTML for a single car card
function createCarCard(car) {
  console.log("=== ОТЛАДКА createCarCard ===");
  console.log("Данные автомобиля:", car);
  console.log("ID:", car.id);
  console.log("Марка:", car.brand || car.mark);
  console.log("Модель:", car.model);
  console.log("Год:", car.year);
  console.log("Цена:", car.price);
  console.log("Изображения:", car.images);

  // Extract first image or use default
  let imageUrl = "/assets/img/cars/default-car.jpg";
  if (car.images) {
    try {
      // Проверяем, является ли car.images массивом или строкой JSON
      const images =
        typeof car.images === "string" ? JSON.parse(car.images) : car.images;
      if (images && images.length > 0) {
        imageUrl = images[0];
        console.log("Используем изображение:", imageUrl);
      } else {
        console.log(
          "Массив изображений пуст, используем изображение по умолчанию"
        );
      }
    } catch (e) {
      console.warn("Ошибка при разборе изображений:", e);
      console.log("Используем изображение по умолчанию из-за ошибки");
    }
  } else {
    console.log("Изображения не найдены, используем изображение по умолчанию");
  }

  // Проверяем, имеется ли несколько изображений
  const hasMultipleImages = false;
  let imagesArray = [];
  try {
    imagesArray =
      typeof car.images === "string" ? JSON.parse(car.images) : car.images;
    if (imagesArray && imagesArray.length > 1) {
      hasMultipleImages = true;
    }
  } catch (e) {
    // Если ошибка при парсинге, считаем что изображение одно
  }

  // Переводим характеристики, если они на другом языке
  const engineType = translateCarFeature(car.engine_type || "Не указан");
  const transmissionType = translateCarFeature(
    car.transmission_type || "Не указан"
  );
  const mileageFormatted = formatMileage(car.mileage);

  // Переводим название модели и бренда, если необходимо
  const brandName = translateCarFeature(car.brand || "");
  const modelName = translateCarFeature(car.model || "");

  // Определяем категорию автомобиля
  let carCategory = car.category || "";
  let categoryName = "";

  // Если категория не указана напрямую, пытаемся определить
  if (!carCategory) {
    // Проходим по всем категориям и проверяем соответствие бренда и модели
    Object.keys(carCategories).forEach((catKey) => {
      const category = carCategories[catKey];
      category.models.forEach((brandInfo) => {
        if (brandInfo.brand.toLowerCase() === brandName.toLowerCase()) {
          // Если марка соответствует, проверяем модель
          if (
            brandInfo.models.some((model) =>
              modelName.toLowerCase().includes(model.toLowerCase())
            )
          ) {
            carCategory = catKey;
            categoryName = category.name;
          }
        }
      });
    });
  } else {
    // Если категория уже указана, получаем её название
    categoryName = carCategories[carCategory]
      ? carCategories[carCategory].name
      : "";
  }

  // Определяем CSS класс для категории
  let categoryClass = "";
  if (carCategory) {
    categoryClass = `car-category-${carCategory}`;
  }

  // Добавляем бейдж категории
  const categoryBadge = carCategory
    ? `<div class="car-category-badge ${categoryClass}">${categoryName}</div>`
    : "";

  // Добавляем стрелки для навигации, если есть несколько изображений
  const navigationArrows = hasMultipleImages
    ? `
    <div class="card-nav prev-img" data-car-id="${car.id || ""}">❮</div>
    <div class="card-nav next-img" data-car-id="${car.id || ""}">❯</div>
  `
    : "";

  // Return card HTML
  const cardHTML = `
    <div class="order-card ${categoryClass}" data-id="${car.id || ""}">
      ${
        car.featured
          ? '<div class="featured-badge"><span>Премиум</span></div>'
          : ""
      }
      ${categoryBadge}
      <div class="order-card-img">
        <img src="${imageUrl}" alt="${brandName} ${modelName}" data-car-id="${
    car.id || ""
  }" data-img-index="0" loading="lazy">
        ${navigationArrows}
        <div class="order-card-overlay">
          <div class="order-card-year">${car.year}</div>
        </div>
      </div>
      <div class="order-card-content">
        <div class="order-card-title">
          <h3>${brandName} ${modelName}</h3>
        </div>
        <div class="order-card-specs">
          <div class="spec-item">
            <span class="spec-label">Двигатель</span>
            <span class="spec-value">${engineType}</span>
          </div>
          <div class="spec-item">
            <span class="spec-label">КПП</span>
            <span class="spec-value">${transmissionType}</span>
          </div>
          <div class="spec-item">
            <span class="spec-label">Пробег</span>
            <span class="spec-value">${mileageFormatted}</span>
          </div>
        </div>
        <div class="order-card-footer">
          <div class="order-card-price">${formatPrice(car.price)}</div>
          <button class="order-card-details-btn" data-car-id="${
            car.id || ""
          }" onclick="openCarDetails(${car.id || "0"})">Подробнее</button>
        </div>
      </div>
    </div>
  `;

  console.log("HTML карточки создан:");
  console.log("Длина HTML:", cardHTML.length);
  console.log("Первые 200 символов:", cardHTML.substring(0, 200));
  console.log("=== КОНЕЦ ОТЛАДКИ createCarCard ===");

  return cardHTML;
}

// Функция для перевода характеристик автомобиля
function translateCarFeature(text) {
  if (!text) return "Не указан";

  // Словарь с наиболее распространенными корейскими словами для автомобилей
  const koreanToRussian = {
    // Двигатели
    가솔린: "Бензин",
    디젤: "Дизель",
    하이브리드: "Гибрид",
    전기: "Электро",
    가스: "Газ",

    // Трансмиссии
    자동: "Автомат",
    수동: "Механика",
    듀얼클러치: "Робот (DCT)",
    무단변속기: "Вариатор (CVT)",

    // Привод
    전륜구동: "Передний",
    후륜구동: "Задний",
    사륜구동: "Полный",

    // Типы кузова
    세단: "Седан",
    해치백: "Хэтчбек",
    왜건: "Универсал",
    쿠페: "Купе",
    SUV: "Внедорожник",
    컨버터블: "Кабриолет",

    // Цвета
    흰색: "Белый",
    검정색: "Черный",
    빨간색: "Красный",
    파란색: "Синий",
    은색: "Серебристый",
    회색: "Серый",

    // Марки
    현대: "Hyundai",
    기아: "Kia",
    쌍용: "SsangYong",
    르노삼성: "Renault Samsung",
    제네시스: "Genesis",
    쉐보레: "Chevrolet",

    // Общие слова
    신차: "Новый",
    중고: "Б/у",
    키로미터: "км",
    년식: "год выпуска",
  };

  // Проверяем, содержит ли текст корейские символы
  if (
    /[\uAC00-\uD7AF\u1100-\u11FF\u3130-\u318F\uA960-\uA97F\uD7B0-\uD7FF]/.test(
      text
    )
  ) {
    // Пытаемся заменить корейские слова на русские
    let translated = text;

    Object.keys(koreanToRussian).forEach((korean) => {
      // Создаем регулярное выражение для поиска корейского слова в тексте
      const regex = new RegExp(korean, "g");
      translated = translated.replace(regex, koreanToRussian[korean]);
    });

    return translated;
  }

  return text;
}

// Add modal HTML to the page
function addModalToPage() {
  const modal = document.createElement("div");
  modal.id = "car-modal";
  modal.className = "car-modal";
  modal.innerHTML = `
    <div class="car-modal-content">
      <span class="close-modal">&times;</span>
      <div class="car-modal-body">
        <div class="car-slider">
          <div class="car-slider-container"></div>
          <button class="slider-nav prev">&#10094;</button>
          <button class="slider-nav next">&#10095;</button>
          <div class="slider-dots"></div>
        </div>
        <div class="car-modal-info">
          <div class="car-modal-header">
            <h2 class="car-title"></h2>
            <div class="car-price"></div>
          </div>
          <div class="car-details"></div>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  // Add event listener to close button
  modal.querySelector(".close-modal").addEventListener("click", () => {
    modal.style.display = "none";
    document.body.classList.remove("modal-open");
  });

  // Close modal on click outside
  window.addEventListener("click", (event) => {
    if (event.target === modal) {
      modal.style.display = "none";
      document.body.classList.remove("modal-open");
    }
  });

  // Add slider navigation
  const prevBtn = modal.querySelector(".slider-nav.prev");
  const nextBtn = modal.querySelector(".slider-nav.next");
  prevBtn.addEventListener("click", () => navigateSlider(-1));
  nextBtn.addEventListener("click", () => navigateSlider(1));
}

// Current slide index
let currentSlide = 0;
let totalSlides = 0;

// Функция для перевода текста с Google Translate API
async function translateWithGoogleApi(
  text,
  targetLang = "ru",
  sourceLang = "ko"
) {
  // Если текст пустой или не содержит корейских символов, не переводим
  if (!text || !/[\u1100-\u11FF\u3130-\u318F\uAC00-\uD7AF]/.test(text)) {
    return text;
  }

  // Проверяем, есть ли кэшированный перевод
  const cacheKey = `translate_${sourceLang}_${targetLang}_${encodeURIComponent(
    text.substring(0, 100)
  )}`;
  const cachedTranslation = localStorage.getItem(cacheKey);

  if (cachedTranslation) {
    return wrapTranslatedText(cachedTranslation);
  }

  try {
    // Используем бесплатный Google Translate API
    const encodedText = encodeURIComponent(text);
    const url = `https://translate.googleapis.com/translate_a/single?client=gtx&sl=${sourceLang}&tl=${targetLang}&dt=t&q=${encodedText}`;

    const response = await fetch(url);
    const data = await response.json();

    if (data && Array.isArray(data[0])) {
      // Собираем переведенный текст из всех частей
      let translatedText = "";
      data[0].forEach((part) => {
        if (part[0]) {
          translatedText += part[0];
        }
      });

      // Сохраняем в кэш
      if (translatedText) {
        localStorage.setItem(cacheKey, translatedText);
      }

      return wrapTranslatedText(translatedText || text);
    }

    return text;
  } catch (error) {
    console.warn("Ошибка при переводе:", error);
    return text;
  }
}

// Добавляем специальное форматирование для переведенного текста
function wrapTranslatedText(text) {
  // Просто возвращаем текст без обертки, чтобы не замедлять страницу
  return text;
  // Старый вариант:
  // return `<span class="translated-text" title="Переведено автоматически">${text}</span>`;
}

// Open car details modal
async function openCarDetails(carId) {
  // Reset slider
  currentSlide = 0;

  // Find car data
  const car = cachedData.find((c) => c.id == carId);
  if (!car) return;

  // Get modal
  const modal = document.getElementById("car-modal");

  // Переводим данные
  const brand = translateCarFeature(car.brand);
  const model = translateCarFeature(car.model);
  const engine_type = translateCarFeature(car.engine_type || "Не указан");
  const transmission_type = translateCarFeature(
    car.transmission_type || "Не указан"
  );

  // Показываем индикатор загрузки для полей, которые будут переводиться
  const loadingText = '<em style="color:#999">Загрузка перевода...</em>';
  let color = translateCarFeature(car.color || "Не указан");
  let body_type = translateCarFeature(car.body_type || "Не указан");
  let seller = translateCarFeature(car.seller || "Не указан");
  let seller_type = translateCarFeature(car.seller_type || "Не указан");
  let address = translateCarFeature(car.address || "Не указан");
  let description = car.description ? loadingText : "—";

  // Set title and price
  modal.querySelector(
    ".car-title"
  ).textContent = `${brand} ${model} ${car.year}`;
  modal.querySelector(".car-price").textContent = formatPrice(car.price);

  // Create car details HTML
  let detailsHtml = `
    <div class="car-detail-section">
      <h3>Характеристики</h3>
      <table class="car-specs-table">
        <tr>
          <td>Марка:</td>
          <td>${brand}</td>
        </tr>
        <tr>
          <td>Модель:</td>
          <td>${model}</td>
        </tr>
        <tr>
          <td>Год:</td>
          <td>${car.year}</td>
        </tr>
        <tr>
          <td>Пробег:</td>
          <td>${formatMileage(car.mileage)}</td>
        </tr>
        <tr>
          <td>Двигатель:</td>
          <td>${engine_type}</td>
        </tr>
        <tr>
          <td>Коробка передач:</td>
          <td>${transmission_type}</td>
        </tr>
        <tr>
          <td>Цвет:</td>
          <td id="car-color-${carId}">${color}</td>
        </tr>
        <tr>
          <td>Тип кузова:</td>
          <td id="car-body-${carId}">${body_type}</td>
        </tr>
      </table>
    </div>
  `;

  // Add seller info if available
  if (car.seller || car.address) {
    detailsHtml += `
      <div class="car-detail-section">
        <h3>Информация о продавце</h3>
        <table class="car-specs-table">
          ${
            car.seller
              ? `<tr><td>Продавец:</td><td id="car-seller-${carId}">${seller}</td></tr>`
              : ""
          }
          ${
            car.seller_type
              ? `<tr><td>Тип продавца:</td><td id="car-seller-type-${carId}">${seller_type}</td></tr>`
              : ""
          }
          ${
            car.address
              ? `<tr><td>Адрес:</td><td id="car-address-${carId}">${address}</td></tr>`
              : ""
          }
        </table>
      </div>
    `;
  }

  // Add description if available
  if (car.description) {
    detailsHtml += `
      <div class="car-detail-section">
        <h3>Описание</h3>
        <p class="car-description" id="car-description-${carId}">${description}</p>
      </div>
    `;
  }

  // Создаем URL для перехода на форму обратной связи с параметрами автомобиля
  const feedbackUrl = `feedback-mockup.html?type=order&brand=${encodeURIComponent(
    car.brand
  )}&model=${encodeURIComponent(car.model)}&year=${encodeURIComponent(
    car.year
  )}&price=${encodeURIComponent(car.price)}`;

  // Add contact button with link to feedback form
  detailsHtml += `
    <div class="car-actions">
      <a href="${feedbackUrl}" class="car-contact-btn">Связаться</a>
      ${
        car.url
          ? `<a href="${car.url}" target="_blank" class="car-source-btn">Источник</a>`
          : ""
      }
    </div>
  `;

  // Update details
  modal.querySelector(".car-details").innerHTML = detailsHtml;

  // Create slider
  createSlider(car);

  // Show modal with animation effect
  modal.style.display = "block";
  setTimeout(() => {
    modal.classList.add("modal-open");
    document.body.classList.add("modal-open");
  }, 10);

  // Асинхронно переводим детали после отображения модального окна
  setTimeout(async () => {
    // Переводим описание
    if (car.description) {
      const translatedDescription = await translateWithGoogleApi(
        car.description
      );
      const descElement = document.getElementById(`car-description-${carId}`);
      if (descElement) {
        descElement.innerHTML = translatedDescription;
      }
    }

    // Переводим цвет
    if (
      car.color &&
      /[\u1100-\u11FF\u3130-\u318F\uAC00-\uD7AF]/.test(car.color)
    ) {
      const translatedColor = await translateWithGoogleApi(car.color);
      const colorElement = document.getElementById(`car-color-${carId}`);
      if (colorElement) {
        colorElement.innerHTML = translatedColor;
      }
    }

    // Переводим тип кузова
    if (
      car.body_type &&
      /[\u1100-\u11FF\u3130-\u318F\uAC00-\uD7AF]/.test(car.body_type)
    ) {
      const translatedBodyType = await translateWithGoogleApi(car.body_type);
      const bodyElement = document.getElementById(`car-body-${carId}`);
      if (bodyElement) {
        bodyElement.innerHTML = translatedBodyType;
      }
    }

    // Переводим продавца
    if (
      car.seller &&
      /[\u1100-\u11FF\u3130-\u318F\uAC00-\uD7AF]/.test(car.seller)
    ) {
      const translatedSeller = await translateWithGoogleApi(car.seller);
      const sellerElement = document.getElementById(`car-seller-${carId}`);
      if (sellerElement) {
        sellerElement.innerHTML = translatedSeller;
      }
    }

    // Переводим тип продавца
    if (
      car.seller_type &&
      /[\u1100-\u11FF\u3130-\u318F\uAC00-\uD7AF]/.test(car.seller_type)
    ) {
      const translatedSellerType = await translateWithGoogleApi(
        car.seller_type
      );
      const sellerTypeElement = document.getElementById(
        `car-seller-type-${carId}`
      );
      if (sellerTypeElement) {
        sellerTypeElement.innerHTML = translatedSellerType;
      }
    }

    // Переводим адрес
    if (
      car.address &&
      /[\u1100-\u11FF\u3130-\u318F\uAC00-\uD7AF]/.test(car.address)
    ) {
      const translatedAddress = await translateWithGoogleApi(car.address);
      const addressElement = document.getElementById(`car-address-${carId}`);
      if (addressElement) {
        addressElement.innerHTML = translatedAddress;
      }
    }
  }, 100);
}

// Create image slider
function createSlider(car) {
  const sliderContainer = document.querySelector(".car-slider-container");
  const dotsContainer = document.querySelector(".slider-dots");
  sliderContainer.innerHTML = "";
  dotsContainer.innerHTML = "";

  try {
    let images = [];
    if (car.images) {
      // Ограничиваем количество изображений для улучшения производительности
      try {
        // Проверяем, является ли car.images массивом или строкой JSON
        const parsedImages =
          typeof car.images === "string" ? JSON.parse(car.images) : car.images;
        images = Array.isArray(parsedImages) ? parsedImages.slice(0, 5) : [];
      } catch (e) {
        images = [];
      }
    }

    if (!images || images.length === 0) {
      // Add default image if no images available
      images = ["Img/default-car.jpg"];
    }

    totalSlides = images.length;

    // Create slides
    images.forEach((image, index) => {
      const slide = document.createElement("div");
      slide.className = "slide";
      slide.style.display = index === 0 ? "block" : "none";

      const img = document.createElement("img");
      img.src = image;
      img.alt = `${car.brand} ${car.model} - Изображение ${index + 1}`;

      // Оптимизируем загрузку изображений
      img.loading = "lazy";

      slide.appendChild(img);
      sliderContainer.appendChild(slide);

      // Create dot
      const dot = document.createElement("span");
      dot.className = "dot";
      dot.addEventListener("click", () => {
        currentSlide = index;
        showSlide(currentSlide);
      });

      if (index === 0) {
        dot.classList.add("active");
      }

      dotsContainer.appendChild(dot);
    });
  } catch (e) {
    console.error("Error creating slider:", e);
    sliderContainer.innerHTML = `<div class="slide"><img src="Img/default-car.jpg" alt="Default Image"></div>`;
    totalSlides = 1;
  }
}

// Show specific slide
function showSlide(n) {
  const slides = document.querySelectorAll(".slide");
  const dots = document.querySelectorAll(".dot");

  if (n >= slides.length) {
    currentSlide = 0;
  }

  if (n < 0) {
    currentSlide = slides.length - 1;
  }

  // Hide all slides
  for (let i = 0; i < slides.length; i++) {
    slides[i].style.display = "none";
    dots[i].classList.remove("active");
  }

  // Show current slide
  slides[currentSlide].style.display = "block";
  dots[currentSlide].classList.add("active");
}

// Navigate to next/previous slide
function navigateSlider(n) {
  showSlide((currentSlide += n));
}

// Add CSS styles for modal and cards
function addStyles() {
  const style = document.createElement("style");
  style.innerHTML = `
    /* Container styles */
    .order-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 15px;
    }

    /* Контейнер для ползунка */
    .range-slider {
      position: relative;
      height: 30px; /* Увеличиваем высоту контейнера */
      display: flex;
      align-items: center; /* Центрируем по вертикали */
    }

    /* Новые стили для ползунков */
    input[type=range] {
      -webkit-appearance: none;
      width: 100%;
      height: 2px;
      background: transparent;
      outline: none;
      position: absolute;
      pointer-events: none;
      z-index: 2;
      margin: 0;
      top: 50%; /* Центрируем по вертикали */
      transform: translateY(-50%); /* Точное центрирование */
    }

    input[type=range]::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #000;
      cursor: pointer;
      pointer-events: auto;
      border: 2px solid white;
      box-shadow: 0 0 2px rgba(0,0,0,0.3);
      z-index: 3;
    }

    input[type=range]::-moz-range-thumb {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #000;
      cursor: pointer;
      pointer-events: auto;
      border: 2px solid white;
      box-shadow: 0 0 2px rgba(0,0,0,0.3);
      z-index: 3;
    }

    /* Дорожка для ползунка */
    .slider-track {
      width: 100%;
      height: 2px;
      background: #e0e0e0;
      position: absolute;
      top: 50%; /* Центрируем по вертикали */
      transform: translateY(-50%); /* Точное центрирование */
    }

    .slider-color {
      height: 100%;
      background: #333;
      position: absolute;
      top: 0;
      z-index: 1;
    }

    /* Card styles */
    .order-cards {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      margin: 20px auto;
      max-width: 1200px;
      width: 100%;
    }

    /* Упрощенные стили для переведенного текста - просто обычный текст */
    .translated-text {
      /* Убираем все эффекты для переведенного текста */
    }

    /* Убираем псевдоэлемент с глобусом */
    .translated-text::after {
      content: none;
    }

    /* Упрощаем эффекты наведения для повышения производительности */
    .order-card:hover {
      /* Убираем анимацию поднятия карточки */
      transform: none;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    /* Отключаем анимацию при наведении на изображение */
    .order-card:hover .order-card-img img {
      transform: none;
    }

    .order-card {
      background: #ffffff;
      border-radius: 8px;
      overflow: hidden;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      position: relative;
      color: #333;
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
      border: 1px solid #eaeaea;
    }

    .featured-badge {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 2;
      background: #f8f8f8;
      border: 1px solid #e0e0e0;
      border-radius: 20px;
      padding: 3px 10px;
      color: #666;
      font-size: 11px;
      font-weight: 600;
    }

    .order-card-img {
      height: 200px;
      overflow: hidden;
      position: relative;
    }

    .order-card-img img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: opacity 0.25s ease, transform 0.25s ease;
    }

    .order-card-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 10px;
      pointer-events: none;
    }

    .order-card-year {
      display: inline-block;
      background: rgba(255, 255, 255, 0.85);
      color: #555;
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 13px;
      font-weight: 500;
      border: 1px solid #e5e5e5;
    }

    /* Стрелки навигации для изображений */
    .card-nav {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      background-color: rgba(255, 255, 255, 0.7);
      color: #333;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 18px;
      opacity: 0;
      transition: opacity 0.2s ease, background-color 0.2s ease;
      z-index: 5;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .prev-img {
      left: 8px;
    }

    .next-img {
      right: 8px;
    }

    .order-card:hover .card-nav {
      opacity: 0.8;
    }

    .card-nav:hover {
      opacity: 1 !important;
      background-color: rgba(255, 255, 255, 0.9);
    }

    .order-card-content {
      padding: 15px;
    }

    .order-card-title h3 {
      margin: 0 0 15px 0;
      font-size: 17px;
      font-weight: 600;
      color: #333;
      letter-spacing: 0.3px;
    }

    .order-card-specs {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 15px;
      border-left: 2px solid #e0e0e0;
      padding-left: 10px;
    }

    .spec-item {
      display: flex;
      justify-content: space-between;
    }

    .spec-label {
      color: #777;
      font-size: 13px;
    }

    .spec-value {
      color: #333;
      font-size: 13px;
      font-weight: 500;
    }

    .order-card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 15px;
      border-top: 1px solid #f0f0f0;
      padding-top: 15px;
    }

    .order-card-price {
      font-size: 18px;
      font-weight: 700;
      color: #444;
      letter-spacing: 0.3px;
    }

    .order-card-details-btn {
      background: #f5f5f5;
      color: #555;
      border: 1px solid #e0e0e0;
      padding: 7px 14px;
      border-radius: 20px;
      cursor: pointer;
      font-size: 13px;
      transition: all 0.3s ease;
      font-weight: 500;
    }

    .order-card-details-btn:hover {
      background: #ebebeb;
      color: #333;
    }

    /* Modal styles */
    .car-modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.95);
      z-index: 1000;
      overflow-y: auto;
      backdrop-filter: blur(5px);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .car-modal.modal-open {
      opacity: 1;
    }

    .car-modal-content {
      background: #ffffff;
      margin: 50px auto;
      max-width: 900px;
      width: 90%;
      border-radius: 8px;
      position: relative;
      overflow: hidden;
      box-shadow: 0 5px 30px rgba(0,0,0,0.15);
      color: #333;
      border: 1px solid #eaeaea;
    }

    .close-modal {
      position: absolute;
      top: 15px;
      right: 20px;
      font-size: 28px;
      color: #666;
      cursor: pointer;
      z-index: 10;
    }

    .car-modal-body {
      display: flex;
      flex-direction: column;
    }

    .car-slider {
      position: relative;
      height: 400px;
    }

    .car-slider-container {
      height: 100%;
    }

    .slide {
      width: 100%;
      height: 100%;
      display: none;
    }

    .slide img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .slider-nav {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(255, 255, 255, 0.8);
      color: #555;
      border: 1px solid #e0e0e0;
      font-size: 18px;
      padding: 15px;
      cursor: pointer;
      border-radius: 50%;
      width: 45px;
      height: 45px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background 0.3s ease;
      z-index: 10;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .slider-nav:hover {
      background: #ffffff;
      color: #333;
    }

    .prev {
      left: 15px;
    }

    .next {
      right: 15px;
    }

    .slider-dots {
      position: absolute;
      bottom: 20px;
      left: 0;
      right: 0;
      display: flex;
      justify-content: center;
      gap: 8px;
      z-index: 10;
    }

    .dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.6);
      cursor: pointer;
      border: 1px solid #e0e0e0;
    }

    .dot.active {
      background: #ffffff;
      transform: scale(1.2);
    }

    .car-modal-info {
      padding: 25px;
    }

    .car-modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 15px;
    }

    .car-title {
      margin: 0;
      font-size: 22px;
      color: #333;
      letter-spacing: 0.3px;
      font-weight: 600;
    }

    .car-price {
      font-size: 24px;
      font-weight: bold;
      color: #444;
    }

    .car-detail-section {
      margin-bottom: 25px;
    }

    .car-detail-section h3 {
      margin-top: 0;
      margin-bottom: 15px;
      font-size: 17px;
      color: #555;
      position: relative;
      padding-left: 12px;
      font-weight: 600;
    }

    .car-detail-section h3::before {
      content: '';
      position: absolute;
      left: 0;
      top: 5px;
      bottom: 5px;
      width: 3px;
      background: #e0e0e0;
      border-radius: 3px;
    }

    .car-specs-table {
      width: 100%;
      border-collapse: collapse;
    }

    .car-specs-table tr {
      transition: background 0.2s ease;
    }

    .car-specs-table tr:nth-child(odd) {
      background-color: #f9f9f9;
    }

    .car-specs-table tr:hover {
      background-color: #f5f5f5;
    }

    .car-specs-table td {
      padding: 10px;
      border-bottom: 1px solid #f0f0f0;
    }

    .car-specs-table td:first-child {
      font-weight: 500;
      width: 35%;
      color: #666;
    }

    .car-description {
      line-height: 1.6;
      color: #555;
      background: #f9f9f9;
      padding: 15px;
      border-radius: 6px;
      border-left: 3px solid #e0e0e0;
    }

    .car-actions {
      display: flex;
      gap: 15px;
      margin-top: 25px;
    }

    .car-contact-btn, .car-source-btn {
      padding: 10px 22px;
      border: 1px solid #e0e0e0;
      border-radius: 25px;
      font-weight: 500;
      text-decoration: none;
      text-align: center;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .car-contact-btn {
      background: #e8f4ff;
      color: #333;
      flex: 1;
    }

    .car-contact-btn:hover {
      background: #d1e9ff;
      color: #1c6ca2;
    }

    .car-source-btn {
      background: #ffffff;
      color: #666;
    }

    .car-source-btn:hover {
      background: #f9f9f9;
      color: #333;
    }

    /* Loading and error states */
    .loading, .error, .no-results {
      padding: 30px 20px;
      text-align: center;
      background: #f9f9f9;
      border-radius: 8px;
      margin: 20px 0;
      color: #666;
      grid-column: 1 / -1;
    }

    .error {
      border-left: 3px solid #e74c3c;
    }

    .no-results {
      border-left: 3px solid #e0e0e0;
    }

    .retry-button, .use-mock-button, .reset-search-button {
      background: #f5f5f5;
      border: 1px solid #e0e0e0;
      color: #555;
      padding: 8px 16px;
      border-radius: 20px;
      cursor: pointer;
      margin-top: 15px;
      font-size: 14px;
      transition: all 0.2s ease;
    }

    .retry-button:hover, .use-mock-button:hover, .reset-search-button:hover {
      background: #ebebeb;
      color: #333;
    }

    @media (max-width: 768px) {
      .car-modal-content {
        width: 95%;
        margin: 30px auto;
      }

      .car-slider {
        height: 250px;
      }

      .order-cards {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      }

      .card-nav {
        opacity: 0.8;
        width: 28px;
        height: 28px;
        font-size: 16px;
      }
    }
  `;

  document.head.appendChild(style);
}

// Add styles when document is loaded
document.addEventListener("DOMContentLoaded", function () {
  // Add other initialization code
  addStyles();
});

// Объект для хранения категорий автомобилей
const carCategories = {
  business: {
    name: "Бизнес-класс",
    models: [
      {
        brand: "Mercedes-Benz",
        models: ["S-Class", "E-Class", "Maybach S-Class"],
      },
      { brand: "BMW", models: ["7 Series", "5 Series"] },
      { brand: "Audi", models: ["A8", "A7", "A6"] },
      { brand: "Bentley", models: ["Flying Spur", "Continental GT"] },
      { brand: "Rolls-Royce", models: ["Phantom", "Ghost"] },
      { brand: "Lexus", models: ["LS", "ES"] },
      { brand: "Genesis", models: ["G90", "G80"] },
      { brand: "Jaguar", models: ["XJ", "XF"] },
      { brand: "Maserati", models: ["Quattroporte", "Ghibli"] },
      { brand: "Porsche", models: ["Panamera"] },
    ],
  },
  sport: {
    name: "Спортивные",
    models: [
      { brand: "Ferrari", models: ["Roma", "F8", "SF90", "458", "488"] },
      { brand: "Lamborghini", models: ["Huracan", "Aventador", "Revuelto"] },
      { brand: "Porsche", models: ["911", "718 Cayman", "718 Boxster"] },
      { brand: "McLaren", models: ["720S", "570S", "Artura"] },
      { brand: "Aston Martin", models: ["DB11", "Vantage", "DBS"] },
      { brand: "BMW", models: ["M4", "M3", "M5", "M8"] },
      { brand: "Audi", models: ["R8", "RS7", "RS6", "RS5", "RS3"] },
      { brand: "Mercedes-Benz", models: ["AMG GT", "C63 AMG", "E63 AMG"] },
      { brand: "Chevrolet", models: ["Corvette", "Camaro"] },
      { brand: "Ford", models: ["Mustang"] },
      { brand: "Nissan", models: ["GT-R", "370Z"] },
      { brand: "Toyota", models: ["GR Supra", "GR86"] },
      { brand: "Jaguar", models: ["F-Type"] },
      { brand: "Maserati", models: ["MC20"] },
    ],
  },
  suv: {
    name: "SUV",
    models: [
      { brand: "BMW", models: ["X5", "X6", "X7", "X5 M", "X6 M"] },
      {
        brand: "Mercedes-Benz",
        models: ["GLS", "GLE", "G-Class", "Maybach GLS"],
      },
      { brand: "Audi", models: ["Q7", "Q8", "RS Q8"] },
      {
        brand: "Range Rover",
        models: ["Range Rover", "Range Rover Sport", "Range Rover Velar"],
      },
      { brand: "Porsche", models: ["Cayenne", "Macan"] },
      { brand: "Bentley", models: ["Bentayga"] },
      { brand: "Rolls-Royce", models: ["Cullinan"] },
      { brand: "Lamborghini", models: ["Urus"] },
      { brand: "Aston Martin", models: ["DBX"] },
      { brand: "Maserati", models: ["Levante"] },
      { brand: "Ferrari", models: ["Purosangue"] },
      { brand: "Cadillac", models: ["Escalade"] },
      { brand: "Lexus", models: ["LX", "RX"] },
    ],
  },
};

// Список "главных" автомобилей для отображения по умолчанию
const featuredCars = [
  {
    brand: "Lamborghini",
    model: "Huracan",
    year: 2023,
    variant: "Technica",
    category: "sport",
  },
  { brand: "Ferrari", model: "Roma", year: 2023, category: "sport" },
  {
    brand: "BMW",
    model: "M5",
    year: 2025,
    variant: "(G90)",
    category: "sport",
  },
  {
    brand: "Audi",
    model: "RS7",
    year: 2023,
    variant: "4.0",
    category: "sport",
  },
  {
    brand: "Aston Martin",
    model: "DB11",
    year: 2023,
    variant: "4.0",
    category: "sport",
  },
  {
    brand: "BMW",
    model: "M4",
    year: 2024,
    variant: "Competition",
    category: "sport",
  },
  {
    brand: "Bentley",
    model: "Flying Spur",
    year: 2023,
    variant: "3",
    category: "business",
  },
  {
    brand: "Bentley",
    model: "Continental GT",
    year: 2020,
    category: "business",
  },
  {
    brand: "Bentley",
    model: "Bentayga",
    year: 2023,
    variant: "S",
    category: "suv",
  },
  {
    brand: "Mercedes-Benz",
    model: "GLS600 Maybach",
    year: 2022,
    category: "suv",
  },
  {
    brand: "Rolls-Royce",
    model: "Cullinan",
    year: 2023,
    variant: "Black",
    category: "suv",
  },
  { brand: "Range Rover", model: "5 generation", year: 2023, category: "suv" },
  {
    brand: "Porsche",
    model: "Macan",
    year: 2022,
    variant: "2.9 GTS",
    category: "suv",
  },
];

// Функция для инициализации категорий и кнопок
function initCategories() {
  const categoryTabs = document.querySelectorAll(".category-tab");
  if (!categoryTabs.length) return;

  // Добавляем обработчики событий для всех кнопок категорий
  categoryTabs.forEach((tab) => {
    tab.addEventListener("click", function () {
      // Убираем активный класс у всех кнопок
      categoryTabs.forEach((t) => t.classList.remove("active"));
      // Добавляем активный класс нажатой кнопке
      this.classList.add("active");

      // Получаем значение категории
      const category = this.getAttribute("data-category");
      console.log(`Category tab clicked: ${category}`);

      // Если нажали "Рекомендуемые", включаем режим featured
      if (category === "featured") {
        console.log("Switching to featured mode");
        featuredOnly = true;
        categoryFilter = ""; // Сбрасываем фильтр категории
      }
      // Если нажали "Все автомобили", используем обычный API без фильтров
      else if (category === "all") {
        console.log("Switching to all cars mode (API)");
        featuredOnly = false;
        categoryFilter = ""; // Сбрасываем фильтр категории для загрузки всех автомобилей
      }
      // Иначе включаем фильтрацию по категории
      else {
        console.log(`Switching to category filter: ${category}`);
        featuredOnly = false;
        categoryFilter = category;
      }

      // Обновляем выбранную категорию в селекте
      const categorySelect = document.getElementById("category-filter");
      if (categorySelect && category !== "featured" && category !== "all") {
        categorySelect.value = category;
      } else if (categorySelect) {
        categorySelect.value = "";
      }

      // Обновляем активные фильтры
      if (!activeFilters) activeFilters = {};
      activeFilters.category =
        category !== "featured" && category !== "all" ? category : "";

      // Сохраняем фильтры
      saveFilters();

      // Сбрасываем кэш для новой категории
      Object.keys(apiCache).forEach((key) => delete apiCache[key]);
      console.log("Cache cleared for category switch");

      // Загружаем автомобили с новыми параметрами
      currentPage = 1;
      console.log(
        `Loading cars for category: ${category}, featuredOnly: ${featuredOnly}, categoryFilter: ${categoryFilter}`
      );
      loadCars(true);
    });
  });
}

// Функция для тестирования улучшенного поиска (только для отладки)
function testSearchImprovements() {
  console.log("=== ТЕСТИРОВАНИЕ УЛУЧШЕННОГО ПОИСКА ===");

  const testCases = [
    "BMW X5",
    "Mercedes-Benz S-Class",
    "Aston Martin DB11",
    "Land Rover Range Rover",
    "Toyota Camry",
    "Rolls-Royce Phantom",
    "BMW",
    "Mercedes",
    "Aston",
  ];

  testCases.forEach((testCase) => {
    console.log(`\nТест: "${testCase}"`);
    const result = smartBrandModelSplit(testCase);
    console.log(`Результат: марка="${result.brand}", модель="${result.model}"`);
  });

  console.log("=== КОНЕЦ ТЕСТИРОВАНИЯ ===");
}

// Раскомментируйте следующую строку для запуска тестов в консоли браузера
// testSearchImprovements();

// Функция testCardDisplay удалена - показываем только данные из API

// Тестовый код отключен
// setTimeout(() => {
//   console.log("Запускаем тест отображения карточек...");
//   testCardDisplay();
// }, 3000);
