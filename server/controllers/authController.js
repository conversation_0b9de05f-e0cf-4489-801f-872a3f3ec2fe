const Admin = require("../models/Admin");
const securityUtils = require("../utils/security");
const logger = require("../utils/logger");
const { sendContactForm } = require("../../config/mail-config");

class AuthController {
  // Вход в систему
  async login(req, res) {
    try {
      const { username, password } = req.body;
      const ipAddress = securityUtils.getClientIp(req);
      const userAgent = req.get("User-Agent") || "";

      // Валидация входных данных
      if (!username || !password) {
        await logger.logLoginAttempt(
          null,
          username || "unknown",
          ipAddress,
          userAgent,
          false,
          "Отсутствуют логин или пароль"
        );
        return res.status(400).json({
          error: "Логин и пароль обязательны для заполнения",
        });
      }

      // Проверяем блокировку IP
      const isBlocked = await securityUtils.isIpBlocked(ipAddress);
      if (isBlocked) {
        const attempts = await securityUtils.getFailedAttempts(ipAddress);
        await logger.logIpBlocked(ipAddress, userAgent, attempts);
        return res.status(429).json({
          error:
            "IP адрес временно заблокирован из-за множественных неудачных попыток входа",
        });
      }

      // Поиск администратора
      const adminModel = new Admin();
      const admin = await adminModel.findByUsername(username);

      if (!admin) {
        await securityUtils.recordLoginAttempt(ipAddress, username, false);
        await logger.logLoginAttempt(
          null,
          username,
          ipAddress,
          userAgent,
          false,
          "Пользователь не найден"
        );
        return res.status(401).json({
          error: "Неверный логин или пароль",
        });
      }

      // Проверка пароля
      const isPasswordValid = await securityUtils.verifyPassword(
        password,
        admin.password_hash
      );

      if (!isPasswordValid) {
        await securityUtils.recordLoginAttempt(ipAddress, username, false);
        await logger.logLoginAttempt(
          admin.id,
          username,
          ipAddress,
          userAgent,
          false,
          "Неверный пароль"
        );
        return res.status(401).json({
          error: "Неверный логин или пароль",
        });
      }

      // Успешный вход
      await securityUtils.recordLoginAttempt(ipAddress, username, true);
      await securityUtils.clearSuccessfulAttempts(ipAddress);
      await adminModel.updateLastLogin(admin.id);
      await logger.logLoginAttempt(
        admin.id,
        username,
        ipAddress,
        userAgent,
        true,
        "Успешный вход"
      );

      // Создаем сессию
      req.session.adminId = admin.id;
      req.session.username = admin.username;
      req.session.fullName = admin.full_name;
      req.session.loginTime = new Date().toISOString();

      // Генерируем CSRF токен
      req.session.csrfToken = securityUtils.generateCSRFToken();

      console.log("🔐 Сессия создана:", {
        sessionId: req.sessionID,
        adminId: req.session.adminId,
        username: req.session.username,
        hasSession: !!req.session
      });

      res.json({
        success: true,
        message: "Успешный вход в систему",
        admin: {
          id: admin.id,
          username: admin.username,
          email: admin.email,
          fullName: admin.full_name,
        },
        csrfToken: req.session.csrfToken,
      });
    } catch (error) {
      console.error("Ошибка при входе в систему:", error.message);
      res.status(500).json({
        error: "Ошибка сервера при входе в систему",
      });
    }
  }

  // Выход из системы
  async logout(req, res) {
    try {
      const ipAddress = securityUtils.getClientIp(req);
      const userAgent = req.get("User-Agent") || "";
      const adminId = req.session?.adminId;
      const username = req.session?.username || "unknown";

      if (adminId) {
        await logger.logLogout(adminId, username, ipAddress, userAgent);
      }

      // Уничтожаем сессию
      req.session.destroy((err) => {
        if (err) {
          console.error("Ошибка при уничтожении сессии:", err.message);
          return res.status(500).json({
            error: "Ошибка при выходе из системы",
          });
        }

        res.json({
          success: true,
          message: "Успешный выход из системы",
        });
      });
    } catch (error) {
      console.error("Ошибка при выходе из системы:", error.message);
      res.status(500).json({
        error: "Ошибка сервера при выходе из системы",
      });
    }
  }

  // Проверка статуса авторизации
  async checkAuth(req, res) {
    try {
      console.log("🔍 Проверка авторизации:", {
        hasSession: !!req.session,
        sessionId: req.sessionID,
        adminId: req.session?.adminId,
        username: req.session?.username
      });

      if (!req.session || !req.session.adminId) {
        console.log("❌ Сессия не найдена или adminId отсутствует");
        return res.json({
          authenticated: false,
        });
      }

      const adminModel = new Admin();
      const admin = await adminModel.findById(req.session.adminId);

      if (!admin) {
        console.log("❌ Администратор не найден в БД, уничтожаем сессию");
        req.session.destroy();
        return res.json({
          authenticated: false,
        });
      }

      console.log("✅ Авторизация подтверждена для:", admin.username);
      res.json({
        authenticated: true,
        admin: {
          id: admin.id,
          username: admin.username,
          email: admin.email,
          fullName: admin.full_name,
        },
        csrfToken: req.session.csrfToken,
      });
    } catch (error) {
      console.error("Ошибка при проверке авторизации:", error.message);
      res.status(500).json({
        error: "Ошибка сервера при проверке авторизации",
      });
    }
  }

  // Запрос на восстановление пароля
  async forgotPassword(req, res) {
    try {
      const { email } = req.body;
      const ipAddress = securityUtils.getClientIp(req);
      const userAgent = req.get("User-Agent") || "";

      // Валидация email
      if (!email || !securityUtils.validateEmail(email)) {
        return res.status(400).json({
          error: "Введите корректный email адрес",
        });
      }

      const adminModel = new Admin();
      const admin = await adminModel.findByEmail(email);

      if (!admin) {
        await logger.logPasswordReset(
          null,
          email,
          ipAddress,
          userAgent,
          "request",
          false,
          "Email не найден"
        );
        // Не раскрываем информацию о существовании email
        return res.json({
          success: true,
          message:
            "Если указанный email существует, на него будет отправлена ссылка для восстановления пароля",
        });
      }

      // Генерируем токен сброса
      const resetToken = await adminModel.generateResetToken(email);

      // Отправляем email с токеном
      const resetUrl = `${req.protocol}://${req.get(
        "host"
      )}/admin/reset-password.html?token=${resetToken}`;

      const emailData = {
        name: admin.full_name || admin.username,
        email: admin.email,
        message: `Для сброса пароля перейдите по ссылке: ${resetUrl}\n\nСсылка действительна в течение 1 часа.`,
        formType: "password-reset",
        resetUrl: resetUrl,
      };

      await sendContactForm(emailData);
      await logger.logPasswordReset(
        admin.id,
        admin.username,
        ipAddress,
        userAgent,
        "request",
        true,
        "Токен отправлен на email"
      );

      res.json({
        success: true,
        message:
          "Ссылка для восстановления пароля отправлена на указанный email",
      });
    } catch (error) {
      console.error("Ошибка при запросе восстановления пароля:", error.message);
      res.status(500).json({
        error: "Ошибка сервера при обработке запроса",
      });
    }
  }

  // Сброс пароля
  async resetPassword(req, res) {
    try {
      const { token, newPassword, confirmPassword } = req.body;
      const ipAddress = securityUtils.getClientIp(req);
      const userAgent = req.get("User-Agent") || "";

      // Валидация входных данных
      if (!token || !newPassword || !confirmPassword) {
        return res.status(400).json({
          error: "Все поля обязательны для заполнения",
        });
      }

      if (newPassword !== confirmPassword) {
        return res.status(400).json({
          error: "Пароли не совпадают",
        });
      }

      // Валидация пароля
      const passwordValidation = securityUtils.validatePassword(newPassword);
      if (!passwordValidation.isValid) {
        return res.status(400).json({
          error: "Пароль не соответствует требованиям безопасности",
          details: passwordValidation.errors,
        });
      }

      const adminModel = new Admin();
      const admin = await adminModel.findByResetToken(token);

      if (!admin) {
        await logger.logPasswordReset(
          null,
          "unknown",
          ipAddress,
          userAgent,
          "reset",
          false,
          "Недействительный токен"
        );
        return res.status(400).json({
          error: "Недействительный или истекший токен",
        });
      }

      // Сбрасываем пароль
      await adminModel.resetPassword(token, newPassword);
      await logger.logPasswordReset(
        admin.id,
        admin.username,
        ipAddress,
        userAgent,
        "reset",
        true,
        "Пароль успешно сброшен"
      );

      res.json({
        success: true,
        message:
          "Пароль успешно изменен. Теперь вы можете войти с новым паролем.",
      });
    } catch (error) {
      console.error("Ошибка при сбросе пароля:", error.message);
      res.status(500).json({
        error: "Ошибка сервера при сбросе пароля",
      });
    }
  }

  // Смена пароля (для авторизованных пользователей)
  async changePassword(req, res) {
    try {
      const { currentPassword, newPassword, confirmPassword } = req.body;
      const ipAddress = securityUtils.getClientIp(req);
      const userAgent = req.get("User-Agent") || "";
      const adminId = req.admin.id;
      const username = req.admin.username;

      // Валидация входных данных
      if (!currentPassword || !newPassword || !confirmPassword) {
        return res.status(400).json({
          error: "Все поля обязательны для заполнения",
        });
      }

      if (newPassword !== confirmPassword) {
        return res.status(400).json({
          error: "Новые пароли не совпадают",
        });
      }

      // Валидация нового пароля
      const passwordValidation = securityUtils.validatePassword(newPassword);
      if (!passwordValidation.isValid) {
        return res.status(400).json({
          error: "Новый пароль не соответствует требованиям безопасности",
          details: passwordValidation.errors,
        });
      }

      const adminModel = new Admin();
      const admin = await adminModel.findById(adminId);

      // Проверяем текущий пароль
      const isCurrentPasswordValid = await securityUtils.verifyPassword(
        currentPassword,
        admin.password_hash
      );
      if (!isCurrentPasswordValid) {
        await logger.logPasswordChange(
          adminId,
          username,
          ipAddress,
          userAgent,
          false,
          "Неверный текущий пароль"
        );
        return res.status(400).json({
          error: "Неверный текущий пароль",
        });
      }

      // Обновляем пароль
      await adminModel.updatePassword(adminId, newPassword);

      await logger.logPasswordChange(
        adminId,
        username,
        ipAddress,
        userAgent,
        true,
        "Пароль успешно изменен"
      );

      res.json({
        success: true,
        message: "Пароль успешно изменен",
      });
    } catch (error) {
      console.error("Ошибка при смене пароля:", error.message);
      res.status(500).json({
        error: "Ошибка сервера при смене пароля",
      });
    }
  }
}

module.exports = new AuthController();
