module.exports = {
  apps: [{
    name: 'shms-auto',
    script: 'server.js',
    
    // Настройки окружения
    env: {
      NODE_ENV: 'development',
      PORT: 3001
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    
    // Настройки PM2
    instances: 1, // Количество экземпляров (можно увеличить для нагрузки)
    exec_mode: 'fork', // или 'cluster' для множественных экземпляров
    
    // Автоперезапуск
    watch: false, // Отключаем в продакшене
    ignore_watch: ['node_modules', 'logs', 'data/uploads'],
    
    // Логирование
    log_file: './logs/combined.log',
    out_file: './logs/out.log',
    error_file: './logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    
    // Автоперезапуск при ошибках
    autorestart: true,
    max_restarts: 10,
    min_uptime: '10s',
    
    // Ограничения ресурсов
    max_memory_restart: '500M',
    
    // Переменные окружения из файла
    env_file: '.env',
    
    // Настройки для продакшена
    merge_logs: true,
    time: true
  }],

  // Настройки деплоя (опционально)
  deploy: {
    production: {
      user: 'root',
      host: 'your-server-ip',
      ref: 'origin/main',
      repo: '**************:username/shms-auto.git',
      path: '/var/www/shms-auto',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
