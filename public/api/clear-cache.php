<?php
// <PERSON>ript to clear API cache

header('Content-Type: text/html; charset=utf-8');
echo "<h1>Очистка кэша</h1>";

// Path to cache directory
$cacheDir = __DIR__ . '/cache/';

if (!is_dir($cacheDir)) {
    echo "<p>Директория кэша не существует: $cacheDir</p>";
    exit;
}

// Get all files in cache directory
$files = glob($cacheDir . '*.json');
$count = count($files);

echo "<p>Найдено файлов кэша: $count</p>";

// Delete each file
$deleted = 0;
foreach ($files as $file) {
    if (unlink($file)) {
        $deleted++;
        echo "<p>Удален файл: " . basename($file) . "</p>";
    } else {
        echo "<p>Не удалось удалить файл: " . basename($file) . "</p>";
    }
}

echo "<h2>Результат</h2>";
echo "<p>Удалено файлов: $deleted из $count</p>";
echo "<p><a href='../order.html?no_cache=1'>Вернуться на страницу автомобилей</a></p>"; 