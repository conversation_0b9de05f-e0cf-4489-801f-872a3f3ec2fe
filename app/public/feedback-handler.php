<?php
/**
 * Обработчик форм обратной связи для SHMS Авто
 * Адаптирован для работы с почтой Рег.ру
 */

// Устанавливаем CORS заголовки для работы между портами
header('Access-Control-Allow-Origin: http://localhost:3002');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

// Обрабатываем preflight запросы
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Устанавливаем заголовки для JSON ответов
header('Content-Type: text/plain; charset=utf-8');

// Включаем отображение ошибок для отладки
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Логируем начало обработки
error_log("Feedback handler started. Method: " . $_SERVER['REQUEST_METHOD']);

// Настройки почты (можно вынести в отдельный конфиг файл)
$mail_config = [
    'smtp_host' => 'smtp.yandex.ru',
    'smtp_port' => 465,
    'smtp_user' => '<EMAIL>', // Замените на ваш email
    'smtp_pass' => 'SHMSAutogroup777!',   // Замените на ваш пароль
    'from_email' => '<EMAIL>',
    'from_name' => 'SHMS Авто',
    'to_email' => '<EMAIL>'   // Замените на вашу почту для получения заявок
];

// Настройки подключения к базе (если используется)
$db_config = [
    'host' => 'localhost',
    'user' => 'your_db_user',
    'pass' => 'your_db_pass',
    'name' => 'your_db_name'
];

// Получение данных из формы (поддержка JSON и POST)
$input_data = [];

// Проверяем, отправлены ли данные как JSON
$content_type = $_SERVER['CONTENT_TYPE'] ?? '';
error_log("Content-Type: " . $content_type);

if (strpos($content_type, 'application/json') !== false) {
    $json_input = file_get_contents('php://input');
    error_log("JSON input: " . $json_input);
    $input_data = json_decode($json_input, true) ?? [];
    error_log("Parsed JSON data: " . print_r($input_data, true));
} else {
    // Обычные POST данные
    $input_data = $_POST;
    error_log("POST data: " . print_r($input_data, true));
}

$name    = $input_data['name']    ?? '';
$region  = $input_data['region']  ?? '';
$carType = $input_data['carType'] ?? '';
$model   = $input_data['model']   ?? '';
$phone   = $input_data['phone']   ?? '';
$email   = $input_data['email']   ?? '';
$contact = $input_data['contact'] ?? '';
$message = $input_data['message'] ?? '';
$promo   = $input_data['promo']   ?? '';
$consent = isset($input_data['consent']) ? 'Да' : 'Нет';
$formType = $input_data['formType'] ?? 'Обратная связь';

// Валидация данных
if (empty($name) && empty($contact) && empty($message) && empty($phone) && empty($email)) {
    http_response_code(400);
    echo 'Необходимо заполнить хотя бы одно поле';
    exit;
}

// Сохранение в базу данных (опционально)
try {
    if (!empty($db_config['host'])) {
        $mysqli = new mysqli($db_config['host'], $db_config['user'], $db_config['pass'], $db_config['name']);
        $mysqli->set_charset('utf8');

        if ($promo !== '') {
            $stmt = $mysqli->prepare("INSERT INTO promocodes (code, created_at) VALUES (?, NOW())");
            $stmt->bind_param("s", $promo);
            $stmt->execute();
            $stmt->close();
        }

        // Сохранение заявки в БД (если нужно)
        $stmt = $mysqli->prepare("INSERT INTO contact_forms (name, region, car_type, model, phone, email, contact_method, message, promo, consent, form_type, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");
        $stmt->bind_param("sssssssssss", $name, $region, $carType, $model, $phone, $email, $contact, $message, $promo, $consent, $formType);
        $stmt->execute();
        $stmt->close();

        $mysqli->close();
    }
} catch (Exception $e) {
    error_log("Ошибка сохранения в БД: " . $e->getMessage());
}

// Формирование сообщения
$mail_message = "Новая заявка с сайта SHMS Авто\n\n";
$mail_message .= "Тип формы: $formType\n";
$mail_message .= "Имя: $name\n";
$mail_message .= "Регион: $region\n";
$mail_message .= "Тип авто: $carType\n";
$mail_message .= "Модель: $model\n";
$mail_message .= "Телефон: $phone\n";
$mail_message .= "Email: $email\n";
$mail_message .= "Способ связи: $contact\n";
$mail_message .= "Сообщение: $message\n";
$mail_message .= "Промокод: $promo\n";
$mail_message .= "Согласие на обработку: $consent\n";
$mail_message .= "Дата: " . date('d.m.Y H:i:s') . "\n";

// Отправка через обычную функцию mail() (работает на большинстве хостингов)
$to = $mail_config['to_email'];
$subject = "Новая заявка с сайта SHMS Авто - $formType";
$headers = "From: {$mail_config['from_name']} <{$mail_config['from_email']}>\r\n";
$headers .= "Reply-To: {$mail_config['from_email']}\r\n";
$headers .= "Content-type: text/plain; charset=utf-8\r\n";
$headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";

// Подключаем функцию отправки почты
require_once 'send-mail.php';

// Функция отправки уведомления в Telegram
function sendTelegramNotification($message) {
    // Настройки Telegram бота (замените на ваши)
    $bot_token = ''; // Токен вашего бота
    $chat_id = '';   // ID чата для уведомлений

    if (empty($bot_token) || empty($chat_id)) {
        error_log("Telegram не настроен - пропускаем отправку");
        return false;
    }

    $telegram_message = "🚗 НОВАЯ ЗАЯВКА SHMS АВТО\n\n" . $message;

    $url = "https://api.telegram.org/bot{$bot_token}/sendMessage";
    $data = [
        'chat_id' => $chat_id,
        'text' => $telegram_message,
        'parse_mode' => 'HTML'
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    $success = $http_code == 200;
    error_log("Telegram notification " . ($success ? 'sent' : 'failed') . ": HTTP $http_code");

    return $success;
}

// Включаем отправку email
error_log("Attempting to send email to: " . $to);
error_log("Subject: " . $subject);
error_log("Message: " . $mail_message);

// Используем нашу функцию отправки
// 'file' - сохранение в файлы (для локальной разработки)
// 'smtp' - отправка через SMTP (для продакшена)
// 'webhook' - отправка через внешний сервис
$mail_sent = sendEmail($to, $subject, $mail_message, $mail_config, 'file');

// Дополнительно отправляем уведомление в Telegram (если настроен)
sendTelegramNotification($mail_message);

error_log("Mail sent result: " . ($mail_sent ? 'SUCCESS' : 'FAILED'));

if ($mail_sent) {
    http_response_code(200);
    echo 'Спасибо! Ваше сообщение отправлено. Мы свяжемся с вами в ближайшее время.';

    // Логируем заявку в файл для отладки
    error_log("=== НОВАЯ ЗАЯВКА ===");
    error_log($mail_message);
    error_log("===================");
} else {
    http_response_code(500);
    echo 'Ошибка отправки сообщения. Попробуйте позже или свяжитесь с нами по телефону.';
}
?>