# Переменные окружения (содержат пароли и ключи)
.env
app/.env
.env.local
.env.production
.env.staging

# Логи
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Зависимости
node_modules/
npm-debug.log*

# Кэш
.cache/
.npm/
.yarn/

# Временные файлы
.tmp/
temp/
*.tmp
*.temp

# Загруженные файлы пользователей
uploads/
data/uploads/

# Данные и загрузки
data/uploads/
data/cache/
data/*.db

# База данных (если содержит реальные данные)
*.db
*.sqlite
*.sqlite3

# Системные файлы
.DS_Store
Thumbs.db
desktop.ini

# IDE файлы
.vscode/
.idea/
*.swp
*.swo
*~

# PM2 файлы
ecosystem.config.js.bak
.pm2/

# Архивы и бэкапы
*.zip
*.tar.gz
*.rar
backup/
archive/
backup*/

# Сертификаты
*.pem
*.key
*.crt

# Большие файлы данных
app/public/api/encar_data/*.csv
