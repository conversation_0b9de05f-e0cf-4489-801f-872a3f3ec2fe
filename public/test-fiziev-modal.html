<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Тест модального окна Физиева</title>
  <link rel="stylesheet" href="css/fiziev-modal.css">
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 50px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      margin: 0;
    }
    
    .test-container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 40px;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    h1 {
      text-align: center;
      color: #333;
      margin-bottom: 30px;
    }
    
    .test-buttons {
      display: flex;
      gap: 20px;
      justify-content: center;
      flex-wrap: wrap;
      margin-bottom: 30px;
    }
    
    .test-btn {
      padding: 15px 30px;
      background: #333;
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .test-btn:hover {
      background: #555;
      transform: translateY(-2px);
    }
    
    .info-section {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 10px;
      margin-top: 30px;
    }
    
    .info-section h3 {
      color: #333;
      margin-bottom: 15px;
    }
    
    .info-section ul {
      color: #666;
      line-height: 1.6;
    }
    
    .back-link {
      display: inline-block;
      margin-top: 20px;
      color: #667eea;
      text-decoration: none;
      font-weight: 600;
    }
    
    .back-link:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>Тестирование модального окна промо-страницы Физиева</h1>
    
    <div class="test-buttons">
      <button class="test-btn" onclick="openFizievModal()">
        Открыть модальное окно
      </button>
      
      <button class="test-btn" onclick="window.location.href='/?fiziev-modal=true'">
        Открыть на главной странице
      </button>
      
      <button class="test-btn" onclick="window.location.href='/Fiziev'">
        Тест: /Fiziev
      </button>

      <button class="test-btn" onclick="window.location.href='/fiziev'">
        Тест: /fiziev
      </button>

      <button class="test-btn" onclick="window.location.href='/Fiziev.html'">
        Тест: /Fiziev.html
      </button>
    </div>
    
    <div class="info-section">
      <h3>Особенности реализации:</h3>
      <ul>
        <li><strong>Настоящее модальное окно:</strong> Открывается поверх основного сайта как overlay/popup</li>
        <li><strong>Полупрозрачный фон:</strong> За модальным окном виден основной сайт с затемнением</li>
        <li><strong>Ограниченные размеры:</strong> Окно не занимает весь экран, а имеет фиксированные размеры</li>
        <li><strong>Адаптивный дизайн:</strong> Корректно отображается на десктопе и мобильных устройствах</li>
        <li><strong>Закрытие:</strong> По кнопке X, клавише Escape или клику вне окна</li>
        <li><strong>Автоматическое открытие:</strong> При переходе по ссылке с параметром ?fiziev-modal=true</li>
        <li><strong>Перенаправление:</strong> Прямая ссылка Fiziev.html перенаправляет на главную с открытием модального окна</li>
      </ul>
    </div>
    
    <div class="info-section">
      <h3>Способы открытия:</h3>
      <ul>
        <li><strong>Программно:</strong> <code>openFizievModal()</code> или <code>window.fizievModal.open()</code></li>
        <li><strong>URL параметр:</strong> <code>/?fiziev-modal=true</code></li>
        <li><strong>Прямые ссылки:</strong> <code>/Fiziev</code>, <code>/fiziev</code>, <code>/Fiziev.html</code> (все автоматически перенаправляют)</li>
      </ul>
    </div>
    
    <a href="/" class="back-link">← Вернуться на главную страницу</a>
  </div>
  
  <script src="js/fiziev-modal.js"></script>
</body>
</html>
