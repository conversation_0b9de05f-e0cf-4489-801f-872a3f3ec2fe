/* === Hero Section Styles === */

/* Base hero section */
.hero-stock {
  position: relative;
  width: 100%;
  background: #fff;
}

/* Hero header group */
.hero-header-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100;
  height: 48px;
  min-height: 48px;
}

.header__left {
  display: flex;
  align-items: center;
}

.header__back-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #333;
  text-decoration: none;
  font-size: 1rem;
  transition: all 0.3s ease;
  padding: 0;
  border-radius: 4px;
  height: 48px;
  line-height: 48px;
}

.header__back-link:hover {
  color: #000;
  background: rgba(0, 0, 0, 0.05);
  transform: translateX(-2px);
}

.header__back-link svg {
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;
}

.header__back-link:hover svg {
  transform: translateX(-2px);
}

.header__center-group {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header__nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.header__logo-link {
  font-family: "Inter", Arial, sans-serif;
  font-size: 1.5rem;
  font-weight: 400;
  letter-spacing: 4px;
  color: #000;
  text-transform: uppercase;
  text-decoration: none;
  margin-right: 2rem;
  transition: all 0.3s ease;
  position: relative;
  line-height: 48px;
  padding: 0;
}

.header__logo-link:hover {
  transform: translateY(-2px);
  color: #333;
}

.header__logo-link::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 2px;
  background: #000;
  transform: scaleX(0);
  transition: transform 0.3s ease;
  transform-origin: right;
}

.header__logo-link:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

.header__nav-links {
  display: flex;
  gap: 1.5rem;
}

.header__nav-link {
  color: #333;
  text-decoration: none;
  font-size: 1rem;
  transition: all 0.3s ease;
  position: relative;
  padding: 0 0.5rem;
  font-weight: 400;
  line-height: 48px;
  height: 48px;
  display: flex;
  align-items: center;
}

.header__nav-link:hover {
  color: #000;
  transform: translateY(-2px);
}

.header__nav-link.active {
  color: #000;
  font-weight: 500;
}

/* Удаляем подчеркивание */
.header__nav-link::after,
.header__nav-link.active::after {
  display: none !important;
}

/* Mobile styles */
@media (max-width: 768px) {
  .hero-header-group {
    padding: 1rem;
    height: auto;
  }

  .header__nav-links {
    display: none;
  }

  .header__logo-link {
    margin-right: 0;
  }

  .header__center-group {
    justify-content: flex-end;
  }

  .hero-stock__title-main {
    font-size: 2rem;
  }

  .hero-stock__subtitle-main {
    font-size: 1rem;
    padding: 0 1rem;
  }
}

/* Hero custom section */
.hero-stock--custom {
  background: #f8f8f8;
  padding: 3rem 0;
  position: relative;
  min-height: 200px;
  display: flex;
  align-items: center;
}

.hero-stock__visual {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
  width: 100%;
}

.hero-stock__center {
  text-align: center;
  padding: 2rem 0;
}

.hero-stock__title-main {
  font-size: 2.5rem;
  font-weight: 600;
  color: #000;
  margin: 0 0 1rem 0;
  transition: all 0.3s ease;
}

.hero-stock__subtitle-main {
  font-size: 1.2rem;
  color: #666;
  margin: 0;
  max-width: 800px;
  margin: 0 auto;
  transition: all 0.3s ease;
}

.hero-stock__car-img {
  position: absolute;
  right: 0;
  bottom: 0;
  max-width: 44vw;
  height: auto;
  z-index: 2;
  pointer-events: none;
}

.hero-stock__gradient {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  min-height: 260px;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.2) 10%,
    rgba(0, 0, 0, 0.3) 20%,
    rgba(0, 0, 0, 0.4) 30%,
    rgba(0, 0, 0, 0.5) 40%,
    rgba(0, 0, 0, 0.6) 50%,
    rgba(0, 0, 0, 0.7) 60%,
    rgba(0, 0, 0, 0.8) 70%,
    rgba(0, 0, 0, 0.9) 80%,
    #222 100%
  );
  display: flex;
  align-items: flex-end;
  z-index: 3;
}

.hero-stock__gradient-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 2.5rem 0 2.5rem 4vw;
  max-width: 900px;
}

.hero-stock__gradient-title {
  font-size: 2.2rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 0.7rem;
  margin-top: 0;
}

.hero-stock__gradient-desc {
  font-size: 1.15rem;
  color: #fff;
  margin-bottom: 1.2rem;
  font-weight: 400;
  max-width: 900px;
}

.hero-stock__gradient-btn {
  display: inline-block;
  background: #111;
  color: #fff;
  font-weight: 700;
  border-radius: 12px;
  padding: 0.7em 2em;
  font-size: 1.1rem;
  border: none;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  margin-top: 0.7rem;
  text-decoration: none;
}

.hero-stock__gradient-btn:hover {
  background: #fff;
  color: #111;
}

/* Media Queries */
@media (max-width: 1200px) {
  .hero-stock__car-img {
    max-width: 60vw;
  }

  .hero-stock__title-main {
    font-size: 3rem;
  }
}

@media (max-width: 900px) {
  .hero-header-group {
    height: 60px;
    padding: 0 20px;
  }

  .header__logo-link {
    font-size: 22px;
    letter-spacing: 4px;
  }

  .hero-header-group .header__center-group {
    gap: 1.5rem;
  }

  .hero-header-group .header__nav {
    gap: 1.5rem;
  }

  .hero-stock__visual {
    min-height: 400px;
  }

  .hero-stock__car-img {
    max-width: 90vw;
    right: 0;
  }

  .hero-stock__title-main {
    font-size: 2rem;
  }

  .hero-stock__gradient {
    min-height: 140px;
  }

  .hero-stock__gradient-content {
    padding: 1.2rem 0 1.2rem 2vw;
    max-width: 98vw;
  }

  .hero-stock__center {
    top: 2%;
  }

  .hero-stock__subtitle-main {
    font-size: 0.95rem;
  }
}

@media (max-width: 768px) {
  .hero-header-group {
    display: none;
  }

  .mobile-header {
    display: flex;
  }

  .mobile-header-spacer {
    display: block;
  }
}

@media (min-width: 769px) {
  .hero-header-group {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 0 2rem !important;
    width: 100% !important;
    height: 56px !important;
    background: #fff !important;
    position: relative !important;
    box-sizing: border-box !important;
  }
  .header__left {
    display: flex !important;
    align-items: center !important;
    margin-left: 0 !important;
  }
  .header__center-group {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex: 1 1 auto !important;
  }
  .header__logo-link {
    margin-right: 2rem !important;
    margin-left: 0 !important;
    padding: 0 !important;
  }
  .burger,
  .dropdown-menu {
    display: none !important;
  }

  .mobile-header,
  .mobile-header-spacer {
    display: none !important;
  }
}

.header__logo-img {
  height: 30px !important;
  width: 100px !important;
  object-fit: contain;
  display: block;
}
