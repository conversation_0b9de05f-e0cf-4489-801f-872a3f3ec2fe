<!DOCTYPE html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Админка STOCK | SHMS</title>
    <link rel="stylesheet" href="css/styles.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"
    />
  </head>

  <body>
    <!-- Mobile Header -->
    <header class="admin-mobile-header">
      <button
        class="admin-burger"
        id="adminBurgerBtn"
        aria-label="Открыть меню"
      >
        <span></span>
        <span></span>
        <span></span>
      </button>
      <a href="index.html" class="admin-mobile-header__logo"> SHMS Admin </a>
    </header>

    <!-- Пустой раздел для отступа -->
    <div class="admin-mobile-header-spacer"></div>

    <!-- Mobile Menu -->
    <nav class="admin-dropdown-menu" id="adminDropdownMenu">
      <a href="index.html" class="active">Автомобили в наличии</a>
      <a href="featured.html">Автомобили на главной</a>
      <a href="car-detail-simple.html">Детальные страницы</a>
      <a href="reviews.html">Отзывы</a>
    </nav>

    <!-- Hero / Title Section -->
    <section class="admin-hero-stock">
      <div class="admin-hero-header-group">
        <div class="admin-header__left">
          <a
            href="#"
            class="admin-header__back-link"
            onclick="window.history.back(); return false;"
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19 12H5M5 12L12 19M5 12L12 5"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            Назад
          </a>
        </div>
        <div class="admin-header__center-group">
          <nav class="admin-header__nav">
            <a href="index.html" class="admin-header__logo-link">SHMS Admin</a>
            <div class="admin-header__nav-links">
              <a href="index.html" class="admin-header__nav-link active"
                >Автомобили в наличии</a
              >
              <a href="featured.html" class="admin-header__nav-link"
                >Автомобили на главной</a
              >
              <a href="car-detail-simple.html" class="admin-header__nav-link"
                >Детальные страницы</a
              >
              <a href="reviews.html" class="admin-header__nav-link">Отзывы</a>
            </div>
          </nav>
        </div>
      </div>
    </section>

    <div class="admin-container">
      <div class="admin-section">
        <div class="section-header">
          <h2>Управление карточками "в наличии"</h2>
          <div class="section-counter" id="cars-counter">
            <i class="fas fa-car"></i>
            <span class="counter-value">0</span>
          </div>
        </div>
        <form id="car-form">
          <input type="hidden" id="car-id" name="id" />
          <div class="form-group">
            <label for="title">Название:</label>
            <input type="text" id="title" name="title" required />
          </div>
          <div class="form-group">
            <label for="subtitle">Подзаголовок:</label>
            <input type="text" id="subtitle" name="subtitle" required />
          </div>
          <div class="form-group">
            <label for="image">Фото:</label>
            <input type="file" id="image" name="image" accept="image/*" />
            <div id="current-image" class="current-file"></div>
          </div>
          <div class="form-group">
            <label for="power">Мощность:</label>
            <input type="text" id="power" name="power" required />
          </div>
          <div class="form-group">
            <label for="mileage">Пробег:</label>
            <input type="text" id="mileage" name="mileage" required />
          </div>
          <div class="form-group">
            <label for="body_type">Тип кузова:</label>
            <input type="text" id="body_type" name="body_type" required />
          </div>
          <div class="form-group">
            <label for="engine">Двигатель:</label>
            <input type="text" id="engine" name="engine" required />
          </div>
          <div class="form-group">
            <label for="transmission">Трансмиссия:</label>
            <input type="text" id="transmission" name="transmission" required />
          </div>
          <div class="form-group">
            <label for="interior_colors">Цвета интерьера:</label>
            <div class="color-picker-container">
              <div class="selected-colors">
                <p>Выбранные цвета:</p>
                <div id="selected-colors-list" class="color-preview-list"></div>
              </div>
              <div class="add-color-section">
                <div class="color-input-group">
                  <input
                    type="color"
                    id="color-picker"
                    class="color-picker"
                    value="#000000"
                  />
                  <input
                    type="text"
                    id="color-name"
                    class="color-name-input"
                    placeholder="Название цвета (опционально)"
                  />
                  <button
                    type="button"
                    id="add-color-btn"
                    class="add-color-button"
                  >
                    <i class="fas fa-plus"></i> Добавить цвет
                  </button>
                </div>
              </div>
            </div>
            <input type="hidden" id="interior_colors" name="interior_colors" />
          </div>
          <div class="form-group">
            <label for="consumption">Расход:</label>
            <input type="text" id="consumption" name="consumption" required />
          </div>
          <div class="form-group">
            <label for="capacity">Вместимость:</label>
            <input type="text" id="capacity" name="capacity" required />
          </div>
          <div class="form-group">
            <label for="price">Цена:</label>
            <input type="text" id="price" name="price" required />
          </div>
          <div class="form-buttons">
            <button type="submit" id="submit-btn">
              <i class="fas fa-save"></i> Добавить
            </button>
            <button type="button" id="cancel-btn" style="display: none">
              <i class="fas fa-times"></i> Отмена
            </button>
          </div>
        </form>
      </div>
      <div class="admin-section">
        <h3>Импорт данных</h3>
        <form id="import-form" class="import-form">
          <div class="form-group">
            <label for="html-path">Путь к HTML файлу:</label>
            <input
              type="text"
              id="html-path"
              name="html-path"
              placeholder="../public/stock.html"
            />
          </div>
          <button type="submit" id="import-btn">
            <i class="fas fa-file-import"></i> Импортировать
          </button>
        </form>
      </div>
      <div class="admin-section">
        <div class="section-header">
          <h3>Список автомобилей</h3>
          <div class="section-counter" id="results-counter">
            <i class="fas fa-list"></i>
            <span class="counter-value">0</span>
          </div>
        </div>
        <div class="controls">
          <div class="search-box">
            <input type="text" id="search-input" placeholder="Поиск..." />
            <button id="search-btn"><i class="fas fa-search"></i> Найти</button>
            <button id="reset-search-btn">
              <i class="fas fa-times"></i> Сбросить
            </button>
          </div>
          <div class="sort-controls">
            <label>Сортировка:</label>
            <select id="sort-select">
              <option value="created_desc">Новые сначала</option>
              <option value="created_asc">Старые сначала</option>
              <option value="price_desc">По цене (дороже)</option>
              <option value="price_asc">По цене (дешевле)</option>
              <option value="title_asc">По названию (А-Я)</option>
              <option value="title_desc">По названию (Я-А)</option>
            </select>
          </div>
          <div class="view-controls">
            <button id="toggle-view-btn" type="button" title="Переключить вид">
              <i class="fas fa-list"></i> Список
            </button>
          </div>
        </div>
        <div id="search-status" class="search-status"></div>
        <div id="cars-container"></div>
      </div>
      <div class="admin-section">
        <div class="admin-card">
          <div class="admin-card-icon">📊</div>
          <h3>Аналитика</h3>
          <p>Просмотр статистики посещений и интересов</p>
          <a href="analytics" class="admin-card-link">Перейти</a>
        </div>
      </div>
    </div>

    <!-- Уведомления -->
    <div id="notification" class="notification">
      <div class="notification-content">
        <div class="notification-message">
          <div class="notification-title"></div>
          <div class="notification-text"></div>
        </div>
        <button class="notification-close">&times;</button>
      </div>
    </div>

    <script src="js/admin.js"></script>

    <!-- Mobile Menu JavaScript -->
    <script>
      // Mobile menu functionality
      document.addEventListener("DOMContentLoaded", function () {
        const burgerBtn = document.getElementById("adminBurgerBtn");
        const dropdownMenu = document.getElementById("adminDropdownMenu");

        if (burgerBtn && dropdownMenu) {
          burgerBtn.addEventListener("click", function () {
            burgerBtn.classList.toggle("open");
            dropdownMenu.classList.toggle("active");
          });

          // Close menu when clicking outside
          document.addEventListener("click", function (e) {
            if (
              !burgerBtn.contains(e.target) &&
              !dropdownMenu.contains(e.target)
            ) {
              burgerBtn.classList.remove("open");
              dropdownMenu.classList.remove("active");
            }
          });
        }
      });
    </script>
  </body>
</html>
