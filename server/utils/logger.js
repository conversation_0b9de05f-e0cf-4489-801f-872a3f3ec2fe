const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class SecurityLogger {
    constructor() {
        this.dbPath = path.join(__dirname, '../../data/stock.db');
        this.logFilePath = path.join(__dirname, '../../logs/security.log');
        
        // Создаем директорию для логов если не существует
        this.ensureLogDirectory();
    }

    // Создание директории для логов
    ensureLogDirectory() {
        const logDir = path.dirname(this.logFilePath);
        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }
    }

    // Получение соединения с базой данных
    getDatabase() {
        return new sqlite3.Database(this.dbPath, (err) => {
            if (err) {
                console.error('Ошибка подключения к базе данных для логирования:', err.message);
            }
        });
    }

    // Запись лога в базу данных
    async logToDatabase(adminId, action, ipAddress, userAgent, details, success) {
        return new Promise((resolve, reject) => {
            const db = this.getDatabase();
            const sql = `
                INSERT INTO security_logs (admin_id, action, ip_address, user_agent, details, success, created_at)
                VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            `;

            db.run(sql, [adminId, action, ipAddress, userAgent, details, success ? 1 : 0], function(err) {
                db.close();
                if (err) {
                    console.error('Ошибка записи лога в БД:', err.message);
                    return reject(err);
                }
                resolve(this.lastID);
            });
        });
    }

    // Запись лога в файл
    async logToFile(level, message, data = {}) {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            level,
            message,
            ...data
        };

        const logLine = JSON.stringify(logEntry) + '\n';

        return new Promise((resolve, reject) => {
            fs.appendFile(this.logFilePath, logLine, (err) => {
                if (err) {
                    console.error('Ошибка записи в файл лога:', err.message);
                    return reject(err);
                }
                resolve();
            });
        });
    }

    // Логирование попытки входа
    async logLoginAttempt(adminId, username, ipAddress, userAgent, success, details = '') {
        try {
            const action = success ? 'LOGIN_SUCCESS' : 'LOGIN_FAILED';
            const message = success ? 
                `Успешный вход пользователя ${username}` : 
                `Неудачная попытка входа для ${username}`;

            // Записываем в БД
            await this.logToDatabase(adminId, action, ipAddress, userAgent, details, success);

            // Записываем в файл
            await this.logToFile(success ? 'INFO' : 'WARN', message, {
                username,
                ipAddress,
                userAgent,
                details
            });

            console.log(`[SECURITY] ${message} (IP: ${ipAddress})`);
        } catch (error) {
            console.error('Ошибка логирования попытки входа:', error.message);
        }
    }

    // Логирование выхода
    async logLogout(adminId, username, ipAddress, userAgent) {
        try {
            const message = `Выход пользователя ${username}`;

            await this.logToDatabase(adminId, 'LOGOUT', ipAddress, userAgent, '', true);
            await this.logToFile('INFO', message, {
                username,
                ipAddress,
                userAgent
            });

            console.log(`[SECURITY] ${message} (IP: ${ipAddress})`);
        } catch (error) {
            console.error('Ошибка логирования выхода:', error.message);
        }
    }

    // Логирование смены пароля
    async logPasswordChange(adminId, username, ipAddress, userAgent, success, details = '') {
        try {
            const action = success ? 'PASSWORD_CHANGE_SUCCESS' : 'PASSWORD_CHANGE_FAILED';
            const message = success ? 
                `Успешная смена пароля для ${username}` : 
                `Неудачная попытка смены пароля для ${username}`;

            await this.logToDatabase(adminId, action, ipAddress, userAgent, details, success);
            await this.logToFile(success ? 'INFO' : 'WARN', message, {
                username,
                ipAddress,
                userAgent,
                details
            });

            console.log(`[SECURITY] ${message} (IP: ${ipAddress})`);
        } catch (error) {
            console.error('Ошибка логирования смены пароля:', error.message);
        }
    }

    // Логирование сброса пароля
    async logPasswordReset(adminId, username, ipAddress, userAgent, action, success, details = '') {
        try {
            const actionType = `PASSWORD_RESET_${action.toUpperCase()}`;
            const message = `${action} сброса пароля для ${username}`;

            await this.logToDatabase(adminId, actionType, ipAddress, userAgent, details, success);
            await this.logToFile(success ? 'INFO' : 'WARN', message, {
                username,
                ipAddress,
                userAgent,
                details
            });

            console.log(`[SECURITY] ${message} (IP: ${ipAddress})`);
        } catch (error) {
            console.error('Ошибка логирования сброса пароля:', error.message);
        }
    }

    // Логирование блокировки IP
    async logIpBlocked(ipAddress, userAgent, attempts) {
        try {
            const message = `IP адрес заблокирован из-за множественных неудачных попыток входа`;
            const details = `Количество попыток: ${attempts}`;

            await this.logToDatabase(null, 'IP_BLOCKED', ipAddress, userAgent, details, true);
            await this.logToFile('WARN', message, {
                ipAddress,
                userAgent,
                attempts
            });

            console.log(`[SECURITY] ${message} (IP: ${ipAddress}, попыток: ${attempts})`);
        } catch (error) {
            console.error('Ошибка логирования блокировки IP:', error.message);
        }
    }

    // Логирование подозрительной активности
    async logSuspiciousActivity(adminId, username, ipAddress, userAgent, activity, details = '') {
        try {
            const message = `Подозрительная активность: ${activity}`;

            await this.logToDatabase(adminId, 'SUSPICIOUS_ACTIVITY', ipAddress, userAgent, details, false);
            await this.logToFile('ERROR', message, {
                username,
                ipAddress,
                userAgent,
                activity,
                details
            });

            console.log(`[SECURITY] ${message} (Пользователь: ${username}, IP: ${ipAddress})`);
        } catch (error) {
            console.error('Ошибка логирования подозрительной активности:', error.message);
        }
    }

    // Логирование действий администратора
    async logAdminAction(adminId, username, ipAddress, userAgent, action, details = '') {
        try {
            const message = `Действие администратора: ${action}`;

            await this.logToDatabase(adminId, `ADMIN_${action.toUpperCase()}`, ipAddress, userAgent, details, true);
            await this.logToFile('INFO', message, {
                username,
                ipAddress,
                userAgent,
                action,
                details
            });

            console.log(`[ADMIN] ${message} (Пользователь: ${username}, IP: ${ipAddress})`);
        } catch (error) {
            console.error('Ошибка логирования действия администратора:', error.message);
        }
    }

    // Получение логов безопасности
    async getSecurityLogs(limit = 100, offset = 0, adminId = null, action = null) {
        return new Promise((resolve, reject) => {
            const db = this.getDatabase();
            let sql = `
                SELECT sl.*, a.username, a.full_name
                FROM security_logs sl
                LEFT JOIN admins a ON sl.admin_id = a.id
                WHERE 1=1
            `;
            const params = [];

            if (adminId) {
                sql += ' AND sl.admin_id = ?';
                params.push(adminId);
            }

            if (action) {
                sql += ' AND sl.action = ?';
                params.push(action);
            }

            sql += ' ORDER BY sl.created_at DESC LIMIT ? OFFSET ?';
            params.push(limit, offset);

            db.all(sql, params, (err, rows) => {
                db.close();
                if (err) {
                    return reject(new Error('Ошибка получения логов: ' + err.message));
                }
                resolve(rows || []);
            });
        });
    }

    // Очистка старых логов
    async cleanupOldLogs(daysToKeep = 90) {
        return new Promise((resolve, reject) => {
            const db = this.getDatabase();
            const cutoffDate = new Date(Date.now() - (daysToKeep * 24 * 60 * 60 * 1000)).toISOString();
            
            const sql = 'DELETE FROM security_logs WHERE created_at < ?';

            db.run(sql, [cutoffDate], function(err) {
                db.close();
                if (err) {
                    console.error('Ошибка очистки старых логов:', err.message);
                    return reject(err);
                }
                console.log(`Очищено ${this.changes} старых записей логов`);
                resolve(this.changes);
            });
        });
    }

    // Получение статистики безопасности
    async getSecurityStats(days = 7) {
        return new Promise((resolve, reject) => {
            const db = this.getDatabase();
            const cutoffDate = new Date(Date.now() - (days * 24 * 60 * 60 * 1000)).toISOString();
            
            const sql = `
                SELECT 
                    action,
                    success,
                    COUNT(*) as count
                FROM security_logs 
                WHERE created_at > ?
                GROUP BY action, success
                ORDER BY count DESC
            `;

            db.all(sql, [cutoffDate], (err, rows) => {
                db.close();
                if (err) {
                    return reject(new Error('Ошибка получения статистики: ' + err.message));
                }
                resolve(rows || []);
            });
        });
    }
}

module.exports = new SecurityLogger();
