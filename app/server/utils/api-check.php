<?php
// Скрипт для проверки API из командной строки
// Увеличиваем лимит памяти
ini_set('memory_limit', '256M');

// API настройки
$api_host = 'https://autobase-wade.auto-parser.ru';
$api_username = 'admin';
$api_password = 'n2Q8ewyLft9qgPmim5ng';

// Используем текущую дату
$date = date('Y-m-d');
$file_type = 'active_offer';

// Строим URL
$url = "{$api_host}/encar/{$date}/{$file_type}.csv";

echo "Проверка API Encar\n";
echo "URL: {$url}\n";
echo "Дата: {$date}\n\n";

// Проверка наличия необходимых расширений PHP
if (!extension_loaded('curl')) {
    echo "ОШИБКА: Расширение cURL не установлено!\n";
    echo "Следуйте инструкциям в README-setup.md для установки cURL.\n";
    exit(1);
}

// Проверка файла сертификата
$cert_file = __DIR__ . '/cacert.pem';
if (!file_exists($cert_file)) {
    echo "ОШИБКА: Файл сертификата не найден: {$cert_file}\n";
    echo "Скачайте файл сертификатов с https://curl.se/docs/caextract.html\n";
    exit(1);
}

// Инициализируем сессию cURL
$ch = curl_init();

// Устанавливаем параметры cURL
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
curl_setopt($ch, CURLOPT_USERPWD, "{$api_username}:{$api_password}");
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_CAINFO, $cert_file);
curl_setopt($ch, CURLOPT_VERBOSE, true);
curl_setopt($ch, CURLOPT_HEADER, true); // Получаем заголовки

// Для отладки
$verbose = fopen('php://temp', 'w+');
curl_setopt($ch, CURLOPT_STDERR, $verbose);

// Выполняем запрос - только получаем HTTP-заголовки
curl_setopt($ch, CURLOPT_NOBODY, true);
$header_response = curl_exec($ch);
$status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$info = curl_getinfo($ch);

// Проверяем ошибки cURL
if (curl_errno($ch)) {
    echo "ОШИБКА cURL: " . curl_error($ch) . "\n\n";
    
    // Выводим подробную информацию
    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);
    echo "Подробный лог:\n";
    echo $verboseLog . "\n";
    
    echo "\nИнформация о запросе:\n";
    print_r($info);
    
    curl_close($ch);
    exit(1);
}

echo "Статус ответа: " . $status_code . "\n";

if ($status_code >= 200 && $status_code < 300) {
    echo "Успешное соединение с API!\n";
    
    // Проверяем тип данных
    $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    echo "Тип содержимого: " . $content_type . "\n";
    
    // Получаем размер контента
    $content_length = curl_getinfo($ch, CURLINFO_CONTENT_LENGTH_DOWNLOAD);
    echo "Размер данных: " . ($content_length > 0 ? number_format($content_length) . " байт" : "не указан") . "\n";
    
    // Теперь получаем только начало файла для анализа
    curl_setopt($ch, CURLOPT_NOBODY, false);
    curl_setopt($ch, CURLOPT_HEADER, false);
    curl_setopt($ch, CURLOPT_RANGE, '0-2048'); // Получаем только первые 2KB
    
    $sample_data = curl_exec($ch);
    
    // Анализируем CSV
    $lines = explode("\n", $sample_data);
    $count = count($lines) - 1; // -1 для учета последней строки
    
    echo "Получено строк в примере: " . $count . "\n";
    
    if ($count > 0) {
        echo "Заголовки: " . $lines[0] . "\n";
        
        if (count($lines) > 1) {
            echo "Пример данных: " . $lines[1] . "\n";
        }
    } else {
        echo "Данные отсутствуют или имеют неправильный формат.\n";
    }
}

// Закрываем сессию cURL
curl_close($ch);

// Проверка директорий кэширования
echo "\nПроверка структуры директорий:\n";
$cache_dir = 'cache/images/';
if (is_dir($cache_dir)) {
    echo "✓ Директория кэширования существует: {$cache_dir}\n";
    if (is_writable($cache_dir)) {
        echo "✓ Директория доступна для записи\n";
    } else {
        echo "✗ Директория НЕ доступна для записи!\n";
    }
} else {
    echo "✗ Директория кэширования НЕ существует: {$cache_dir}\n";
}

// Проверка файлов заглушек
$placeholder_files = [
    'Img/placeholder-car.jpg',
    'PNG/default-logo.png',
    'PNG/bmw-logo.png',
    'Img/bmw-m5.jpg'
];

echo "\nПроверка файлов заглушек:\n";
foreach ($placeholder_files as $file) {
    if (file_exists($file)) {
        echo "✓ Файл существует: {$file}\n";
    } else {
        echo "✗ Файл НЕ существует: {$file}\n";
    }
}

echo "\nПроверка завершена.\n";
?> 