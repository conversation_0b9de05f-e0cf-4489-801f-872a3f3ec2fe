#!/usr/bin/env node

/**
 * Базовые тесты для приложения SHMS Auto
 * Проверяет основные функции и API endpoints
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

// Настройки тестирования
const TEST_CONFIG = {
  host: process.env.HOST || 'localhost',
  port: process.env.PORT || 3001,
  timeout: 5000
};

// Цвета для вывода
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}[${new Date().toISOString()}] ${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, 'green');
}

function error(message) {
  log(`❌ ${message}`, 'red');
}

function info(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Счетчики тестов
let testsRun = 0;
let testsPassed = 0;
let testsFailed = 0;

// Функция для HTTP запросов
function makeRequest(options) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.on('error', reject);
    req.setTimeout(TEST_CONFIG.timeout);
    req.end();
  });
}

// Тест функция
async function runTest(testName, testFunction) {
  testsRun++;
  info(`Тест: ${testName}`);
  
  try {
    await testFunction();
    testsPassed++;
    success(`PASS: ${testName}`);
  } catch (err) {
    testsFailed++;
    error(`FAIL: ${testName} - ${err.message}`);
  }
}

// Тест доступности главной страницы
async function testHomePage() {
  const response = await makeRequest({
    hostname: TEST_CONFIG.host,
    port: TEST_CONFIG.port,
    path: '/',
    method: 'GET'
  });

  if (response.statusCode !== 200) {
    throw new Error(`Expected status 200, got ${response.statusCode}`);
  }

  if (!response.data.includes('SHMS')) {
    throw new Error('Home page does not contain expected content');
  }
}

// Тест API endpoint /test
async function testApiTest() {
  const response = await makeRequest({
    hostname: TEST_CONFIG.host,
    port: TEST_CONFIG.port,
    path: '/test',
    method: 'GET'
  });

  if (response.statusCode !== 200) {
    throw new Error(`Expected status 200, got ${response.statusCode}`);
  }

  const data = JSON.parse(response.data);
  if (!data.message) {
    throw new Error('Test API response missing message field');
  }
}

// Тест страницы stock
async function testStockPage() {
  const response = await makeRequest({
    hostname: TEST_CONFIG.host,
    port: TEST_CONFIG.port,
    path: '/stock',
    method: 'GET'
  });

  if (response.statusCode !== 200) {
    throw new Error(`Expected status 200, got ${response.statusCode}`);
  }
}

// Тест страницы contacts
async function testContactsPage() {
  const response = await makeRequest({
    hostname: TEST_CONFIG.host,
    port: TEST_CONFIG.port,
    path: '/contacts',
    method: 'GET'
  });

  if (response.statusCode !== 200) {
    throw new Error(`Expected status 200, got ${response.statusCode}`);
  }
}

// Тест админ панели
async function testAdminPage() {
  const response = await makeRequest({
    hostname: TEST_CONFIG.host,
    port: TEST_CONFIG.port,
    path: '/admin',
    method: 'GET'
  });

  if (response.statusCode !== 200) {
    throw new Error(`Expected status 200, got ${response.statusCode}`);
  }
}

// Тест 404 страницы
async function test404Page() {
  const response = await makeRequest({
    hostname: TEST_CONFIG.host,
    port: TEST_CONFIG.port,
    path: '/nonexistent-page',
    method: 'GET'
  });

  if (response.statusCode !== 404) {
    throw new Error(`Expected status 404, got ${response.statusCode}`);
  }
}

// Тест структуры файлов
async function testFileStructure() {
  const requiredFiles = [
    'server.js',
    'package.json',
    'ecosystem.config.js',
    '.env.example'
  ];

  const requiredDirs = [
    'public',
    'admin',
    'data',
    'scripts'
  ];

  // Проверка файлов
  for (const file of requiredFiles) {
    if (!fs.existsSync(path.resolve(file))) {
      throw new Error(`Required file missing: ${file}`);
    }
  }

  // Проверка директорий
  for (const dir of requiredDirs) {
    if (!fs.existsSync(path.resolve(dir))) {
      throw new Error(`Required directory missing: ${dir}`);
    }
  }
}

// Тест конфигурации package.json
async function testPackageJson() {
  const packagePath = path.resolve('package.json');
  if (!fs.existsSync(packagePath)) {
    throw new Error('package.json not found');
  }

  const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  if (!packageData.name) {
    throw new Error('package.json missing name field');
  }

  if (!packageData.scripts || !packageData.scripts.start) {
    throw new Error('package.json missing start script');
  }

  if (!packageData.dependencies) {
    throw new Error('package.json missing dependencies');
  }
}

// Основная функция тестирования
async function runAllTests() {
  console.log('🧪 Запуск тестов SHMS Auto');
  console.log('='.repeat(40));

  // Тесты файловой структуры (не требуют запущенного сервера)
  await runTest('Структура файлов', testFileStructure);
  await runTest('Конфигурация package.json', testPackageJson);

  // Проверка доступности сервера
  try {
    await makeRequest({
      hostname: TEST_CONFIG.host,
      port: TEST_CONFIG.port,
      path: '/test',
      method: 'GET'
    });
    
    info('Сервер доступен, запускаем HTTP тесты...');

    // HTTP тесты
    await runTest('Главная страница', testHomePage);
    await runTest('API /test', testApiTest);
    await runTest('Страница stock', testStockPage);
    await runTest('Страница contacts', testContactsPage);
    await runTest('Админ панель', testAdminPage);
    await runTest('404 страница', test404Page);

  } catch (err) {
    error(`Сервер недоступен: ${err.message}`);
    error('HTTP тесты пропущены');
  }

  // Результаты
  console.log('='.repeat(40));
  console.log(`📊 Результаты тестирования:`);
  console.log(`   Всего тестов: ${testsRun}`);
  console.log(`   Пройдено: ${testsPassed}`);
  console.log(`   Провалено: ${testsFailed}`);

  if (testsFailed === 0) {
    success('Все тесты пройдены! 🎉');
    process.exit(0);
  } else {
    error(`${testsFailed} тестов провалено! ❌`);
    process.exit(1);
  }
}

// Запуск тестов
if (require.main === module) {
  runAllTests().catch(err => {
    error(`Критическая ошибка при тестировании: ${err.message}`);
    process.exit(1);
  });
}

module.exports = {
  runAllTests,
  makeRequest,
  testHomePage,
  testApiTest,
  testFileStructure
};
