<?php
// Увеличиваем лимиты для обработки больших файлов
ini_set('max_execution_time', 300); // 5 минут
ini_set('memory_limit', '512M');    // Увеличиваем до 512MB для больших файлов

// Включаем отображение ошибок
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Настройка заголовков ответа

// Обработка OPTIONS запроса
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Включаем отладку
$debug = isset($_GET['debug']) && $_GET['debug'] == 1;
if ($debug) {
    ini_set('display_errors', 1);
}

// Логирование запросов
function logDebug($message) {
    global $debug;
    if ($debug) {
        error_log("[API Debug] $message");
    }
}

// Функция для отправки JSON ответа
function sendJsonResponse($data, $statusCode = 200) {
    if (!headers_sent()) {
        header('Content-Type: application/json; charset=utf-8');
        http_response_code($statusCode);
    }
    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

// Функция для обработки ошибок
function handleError($message, $statusCode = 500, $debug = false) {
    $response = ['error' => $message];
    if ($debug) {
        $response['debug'] = [
            'file' => __FILE__,
            'line' => __LINE__,
            'time' => date('Y-m-d H:i:s')
        ];
    }
    sendJsonResponse($response, $statusCode);
}

// Обработка ошибок PHP
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    handleError("PHP Error: $errstr", 500, true);
});

// Обработка исключений
set_exception_handler(function($e) {
    handleError($e->getMessage(), 500, true);
});

logDebug("API request started");

// Get parameters - date теперь необязательный, используем текущую дату по умолчанию
$date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
$brand = isset($_GET['brand']) ? $_GET['brand'] : '';
$model = isset($_GET['model']) ? $_GET['model'] : '';
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
$offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
$featuredOnly = isset($_GET['featured']) && $_GET['featured'] == 1;
$showActive = !isset($_GET['active']) || $_GET['active'] != '0'; // По умолчанию показываем активные
$showRemoved = isset($_GET['removed']) && $_GET['removed'] == '1'; // По умолчанию не показываем removed

logDebug("Parameters: date=$date, brand=$brand, model=$model, limit=$limit, offset=$offset");

// Параметры фильтрации
$yearFrom = isset($_GET['year_from']) ? (int)$_GET['year_from'] : 0;
$yearTo = isset($_GET['year_to']) ? (int)$_GET['year_to'] : 0;
$priceFrom = isset($_GET['price_from']) ? (int)$_GET['price_from'] : 0;
$priceTo = isset($_GET['price_to']) ? (int)$_GET['price_to'] : 0;
$transmission = isset($_GET['transmission']) ? $_GET['transmission'] : '';

// Новые параметры фильтрации
$bodyType = isset($_GET['body_type']) ? $_GET['body_type'] : '';
$fuelType = isset($_GET['fuel_type']) ? $_GET['fuel_type'] : '';
$mileageFrom = isset($_GET['mileage_from']) ? (int)$_GET['mileage_from'] : 0;
$mileageTo = isset($_GET['mileage_to']) ? (int)$_GET['mileage_to'] : 0;
$driveType = isset($_GET['drive_type']) ? $_GET['drive_type'] : '';
$category = isset($_GET['category']) ? $_GET['category'] : '';

// Проверка параметра no_cache
$useCache = !isset($_GET['no_cache']) || $_GET['no_cache'] != '1';

// Путь к кэшу
$cacheDir = __DIR__ . '/cache/';
$cacheFileName = md5($date . '_' . $brand . '_' . $model . '_' . $limit . '_' . $offset . '_' . ($featuredOnly ? '1' : '0') . '_' . ($showActive ? '1' : '0') . '_' . ($showRemoved ? '1' : '0') . '_' . $yearFrom . '_' . $yearTo . '_' . $priceFrom . '_' . $priceTo . '_' . $transmission . '_' . $bodyType . '_' . $fuelType . '_' . $mileageFrom . '_' . $mileageTo . '_' . $driveType . '_' . $category) . '.json';
$cacheFile = $cacheDir . $cacheFileName;
$cacheExpire = 3600; // 1 час

// Проверяем кэш
if ($useCache && file_exists($cacheFile) && (time() - filemtime($cacheFile) < $cacheExpire)) {
    logDebug("Using cached data from: $cacheFile");
    header('X-Cache: HIT');
    readfile($cacheFile);
    exit;
} else {
    logDebug("Cache miss or disabled. Processing data from local files.");
    header('X-Cache: MISS');
}

// Функция для получения данных из локальных CSV файлов
function fetchDataFromLocalCSV($date, $params) {
    $dataDir = __DIR__ . '/encar_data/';
    $result = [];

    logDebug("Looking for CSV files in: $dataDir");

    // Сначала ищем файлы с точным именем для даты
    $activeFile = $dataDir . 'encar_active_' . $date . '.csv';
    $removedFile = $dataDir . 'encar_removed_' . $date . '.csv';

    // Проверяем наличие файлов
    if ($params['showActive'] && file_exists($activeFile)) {
        logDebug("Found active file: $activeFile");
        $activeData = processCSVFile($activeFile, $params);
        $result = array_merge($result, $activeData);
    }

    if ($params['showRemoved'] && file_exists($removedFile)) {
        logDebug("Found removed file: $removedFile");
        $removedData = processCSVFile($removedFile, $params);
        $result = array_merge($result, $removedData);
    }

    // Если не найдено ни одного файла, ищем любые CSV файлы в директории
    if (empty($result)) {
        logDebug("No specific files found, searching for any CSV files");
        $csvFiles = glob($dataDir . '*.csv');

        if (!empty($csvFiles)) {
            // Берем первый найденный CSV файл
            $csvFile = $csvFiles[0];
            logDebug("Using CSV file: $csvFile");
            $result = processCSVFile($csvFile, $params);
        } else {
            // Если CSV файлов нет, ищем файлы за предыдущие дни
            for ($i = 1; $i <= 7; $i++) {
                $prevDate = date('Y-m-d', strtotime("-$i days"));
                $prevActiveFile = $dataDir . 'encar_active_' . $prevDate . '.csv';

                if (file_exists($prevActiveFile)) {
                    logDebug("Using data from previous date: $prevDate");
                    $activeData = processCSVFile($prevActiveFile, $params);
                    $result = array_merge($result, $activeData);
                    break;
                }
            }
        }
    }

    logDebug("Total records found: " . count($result));
    return $result;
}

// Функция для обработки CSV файла
function processCSVFile($file, $params) {
    logDebug("Processing CSV file: $file");

    $handle = fopen($file, "r");
    if (!$handle) {
        logDebug("Failed to open file: $file");
        return [];
    }

    // Определяем разделитель CSV файла
    $firstLine = fgets($handle);
    rewind($handle);

    $delimiter = "|"; // По умолчанию
    if (substr_count($firstLine, ",") > substr_count($firstLine, "|")) {
        $delimiter = ",";
        logDebug("Using comma delimiter");
    } else {
        logDebug("Using pipe delimiter");
    }

    // Получаем заголовки
    $headers = fgetcsv($handle, 0, $delimiter, '"', '\\');
    if (!$headers) {
        fclose($handle);
        logDebug("Failed to read headers from file: $file");
        return [];
    }

    logDebug("Headers found: " . implode(", ", array_slice($headers, 0, 10)) . (count($headers) > 10 ? "..." : ""));
    logDebug("Total headers: " . count($headers));

    // Проверяем наличие ключевых полей
    $requiredFields = ['mark', 'model', 'price'];
    $missingFields = [];
    foreach ($requiredFields as $field) {
        if (!in_array($field, $headers)) {
            $missingFields[] = $field;
        }
    }

    if (!empty($missingFields)) {
        logDebug("Missing required fields: " . implode(", ", $missingFields));
        // Попробуем найти альтернативные названия
        $alternativeNames = [
            'mark' => ['brand', 'марка', 'бренд'],
            'model' => ['модель'],
            'price' => ['цена', 'стоимость']
        ];

        foreach ($missingFields as $field) {
            if (isset($alternativeNames[$field])) {
                foreach ($alternativeNames[$field] as $alt) {
                    if (in_array($alt, $headers)) {
                        logDebug("Found alternative for $field: $alt");
                        break;
                    }
                }
            }
        }
    }

    // Фильтруем и обрабатываем данные
    $result = [];
    $count = 0;
    $processed = 0;
    
    // Определяем индексы для важных полей
    $mileageIndex = array_search('mileage', $headers);
    $kmAgeIndex = array_search('km_age', $headers); // Альтернативное поле пробега
    $kmIndex = array_search('km', $headers); // Еще одно альтернативное поле пробега
    
    logDebug("Mileage field indexes: mileage={$mileageIndex}, km_age={$kmAgeIndex}, km={$kmIndex}");
    
    while (($data = fgetcsv($handle, 0, $delimiter, '"', '\\')) !== FALSE) {
        // Пропускаем строки с неправильным количеством полей
        if (count($data) !== count($headers)) continue;
        
        $car = array_combine($headers, $data);
        
        // Применяем фильтры
        if (!empty($params['brand'])) {
            if (stripos($car['mark'] ?? '', $params['brand']) === false) {
                continue;
            }
        }
        
        if (!empty($params['model'])) {
            if (stripos($car['model'] ?? '', $params['model']) === false) {
                continue;
            }
        }
        
        // Фильтрация по году выпуска
        if (!empty($params['yearFrom']) && $params['yearFrom'] > 0) {
            $carYear = (int)($car['year'] ?? 0);
            if ($carYear < $params['yearFrom']) {
                continue;
            }
        }
        
        if (!empty($params['yearTo']) && $params['yearTo'] > 0) {
            $carYear = (int)($car['year'] ?? 0);
            if ($carYear > $params['yearTo']) {
                continue;
            }
        }
        
        // Фильтрация по цене
        if (!empty($params['priceFrom']) && $params['priceFrom'] > 0) {
            $carPrice = (int)($car['price'] ?? 0);
            if ($carPrice < $params['priceFrom']) {
                continue;
            }
        }
        
        if (!empty($params['priceTo']) && $params['priceTo'] > 0) {
            $carPrice = (int)($car['price'] ?? 0);
            if ($carPrice > $params['priceTo']) {
                continue;
            }
        }
        
        // Фильтрация по типу трансмиссии
        if (!empty($params['transmission'])) {
            $carTransmission = translateKorean($car['transmission_type'] ?? '');
            if (stripos($carTransmission, $params['transmission']) === false) {
                continue;
            }
        }
        
        // Фильтрация по типу кузова
        if (!empty($params['bodyType'])) {
            $carBodyType = translateKorean($car['body_type'] ?? '');
            if (stripos($carBodyType, $params['bodyType']) === false) {
                continue;
            }
        }
        
        // Фильтрация по типу топлива/двигателя
        if (!empty($params['fuelType'])) {
            $carFuelType = translateKorean($car['fuel_type'] ?? '');
            if (stripos($carFuelType, $params['fuelType']) === false) {
                continue;
            }
        }
        
        // Фильтрация по пробегу
        // Сначала определяем пробег авто (может быть в разных полях)
        $carMileage = 0;
        if (!empty($car['mileage'])) {
            $carMileage = (int)preg_replace('/[^0-9]/', '', $car['mileage']);
        } elseif (!empty($car['km_age'])) {
            $carMileage = (int)preg_replace('/[^0-9]/', '', $car['km_age']);
        } elseif (!empty($car['km'])) {
            $carMileage = (int)preg_replace('/[^0-9]/', '', $car['km']);
        }
        
        if (!empty($params['mileageFrom']) && $params['mileageFrom'] > 0) {
            if ($carMileage < $params['mileageFrom']) {
                continue;
            }
        }
        
        if (!empty($params['mileageTo']) && $params['mileageTo'] > 0) {
            if ($carMileage > $params['mileageTo']) {
                continue;
            }
        }
        
        // Фильтрация по типу привода
        if (!empty($params['driveType'])) {
            $carDriveType = translateKorean($car['drive_type'] ?? '');
            if (stripos($carDriveType, $params['driveType']) === false) {
                continue;
            }
        }

        // Фильтрация по категории (business, sport, suv)
        if (!empty($params['category'])) {
            $carCategory = determineCarCategory($car['mark'] ?? '', $car['model'] ?? '');
            logDebug("Car: {$car['mark']} {$car['model']} -> Category: '$carCategory', Filter: '{$params['category']}'");
            if ($carCategory !== $params['category']) {
                continue;
            }
        }
        
        // Пропускаем до offset
        if ($processed < $params['offset']) {
            $processed++;
            continue;
        }
        
        // Проверяем лимит
        if ($count >= $params['limit']) break;
        
        // Проверяем наличие поля пробега в разных вариантах и обеспечиваем его наличие
        if (!isset($car['mileage']) || empty($car['mileage'])) {
            // Проверяем альтернативные поля
            if (isset($car['km_age']) && !empty($car['km_age'])) {
                $car['mileage'] = $car['km_age'];
            } elseif (isset($car['km']) && !empty($car['km'])) {
                $car['mileage'] = $car['km'];
            }
        }
        
        // Добавляем данные для отображения на сайте
        $car['price_formatted'] = formatPrice($car['price'] ?? 0);
        $car['mileage_formatted'] = formatMileage($car['mileage'] ?? 0);
        $car['year'] = $car['year'] ?? '';
        $car['engine'] = formatEngine($car);
        
        // Переводим марку и модель
        if (!empty($car['mark'])) {
            $car['translated_mark'] = translateBrand($car['mark']);
            $car['brand'] = $car['translated_mark']; // Устанавливаем brand как переведенную марку
            $car['mark'] = $car['translated_mark'];
        }
        
        if (!empty($car['model'])) {
            // Используем переведенную марку для правильного контекста при переводе модели
            $car['translated_model'] = translateModel($car['brand'] ?? $car['mark'], $car['model']);
            $car['model'] = $car['translated_model'];
        }
        
        // Переводим значения с корейского
        if (!empty($car['transmission_type'])) {
            $car['transmission_type'] = translateKorean($car['transmission_type']);
        }
        if (!empty($car['body_type'])) {
            $car['body_type'] = translateKorean($car['body_type']);
        }
        if (!empty($car['fuel_type'])) {
            $car['fuel_type'] = translateKorean($car['fuel_type']);
        }
        if (!empty($car['drive_type'])) {
            $car['drive_type'] = translateKorean($car['drive_type']);
        }
        if (!empty($car['options'])) {
            $car['options'] = translateKorean($car['options']);
        }
        
        $car['transmission'] = $car['transmission_type'] ?? '';
        $car['is_active'] = basename($file) === 'encar_active_' . $params['date'] . '.csv';
        
        // Добавляем локальный путь к изображению авто
        $car['local_image'] = getCarImageUrl($car);
        
        // Добавляем в результат
        $result[] = $car;
        $count++;
        $processed++;
    }

    fclose($handle);
    logDebug("Processed $processed rows, returned $count cars");
    return $result;
}

// Форматирование цены для отображения
function formatPrice($price) {
    if (empty($price)) return "Цена не указана";
    
    $price = (int)$price;
    if ($price < 1000) return "$" . $price;
    
    return "$" . number_format($price, 0, '.', ' ');
}

// Форматирование пробега
function formatMileage($mileage) {
    // Проверяем нулевое или пустое значение
    if (empty($mileage) || $mileage === '0' || $mileage === 0) {
        return "Без пробега";
    }
    
    // Сохраняем исходное значение
    $originalValue = $mileage;
    
    // Предобработка строковых значений
    if (is_string($mileage)) {
        // Если строка уже содержит "км", это уже отформатированное значение
        if (strpos($mileage, 'км') !== false) {
            return $mileage;
        }
        
        // Удаляем нечисловые символы
        $mileage = preg_replace('/[^0-9]/', '', $mileage);
    }
    
    // После очистки проверяем пустое значение
    if (empty($mileage) || intval($mileage) === 0) {
        // Если исходное значение не пустое, возвращаем его
        if (!empty($originalValue) && $originalValue !== '0') {
            // При необходимости добавляем "км"
            if (strpos($originalValue, 'км') === false) {
                return $originalValue . ' км';
            }
            return $originalValue;
        }
        return "Без пробега";
    }
    
    // Преобразуем в число
    $mileage = intval($mileage);
    
    // Если значение слишком большое (>1 млн), возможно это неправильные данные
    // Преобразуем в более реалистичное значение
    if ($mileage > 1000000) {
        $mileage = round($mileage / 1000);
    }
    
    // Форматируем с разделителями разрядов
    return number_format($mileage, 0, '.', ' ') . " км";
}

// Форматирование информации о двигателе
function formatEngine($car) {
    $engine = [];
    
    // Объем двигателя
    if (!empty($car['displacement'])) {
        $displacement = (float)$car['displacement'];
        if ($displacement > 0) {
            // Деление на 1000, если значение больше 100 (некоторые данные приходят в кубических сантиметрах)
            if ($displacement > 100) {
                $displacement = $displacement / 1000;
            }
            $engine[] = number_format($displacement, 1, '.', '') . " л";
        }
    }
    
    // Мощность двигателя
    if (!empty($car['horsepower'])) {
        $horsepower = (int)$car['horsepower'];
        if ($horsepower > 0) {
            $engine[] = $horsepower . " л.с.";
        }
    }
    
    // Тип топлива
    if (!empty($car['fuel_type'])) {
        $fuelType = translateKorean($car['fuel_type']);
        $engine[] = $fuelType;
    }
    
    // Если нет данных о двигателе, вернем базовую информацию
    if (empty($engine)) {
        if (!empty($car['engine_type'])) {
            return translateKorean($car['engine_type']);
        }
        return "Нет данных";
    }
    
    return implode(' • ', $engine);
}

// Проверка наличия локальных изображений автомобиля
function getCarImageUrl($car) {
    if (empty($car['id'])) {
        return '';
    }
    
    $imagesDir = '/Img/cars/'; // Путь относительно корня сайта
    $imageFile = 'car_' . $car['id'] . '.jpg';
    $thumbFile = 'car_' . $car['id'] . '_thumb.jpg';
    
    // Проверяем наличие миниатюры
    $thumbPath = __DIR__ . '/..' . $imagesDir . $thumbFile;
    
    if (file_exists($thumbPath)) {
        return $imagesDir . $thumbFile;
    }
    
    // Проверяем наличие оригинала
    $imagePath = __DIR__ . '/..' . $imagesDir . $imageFile;
    
    if (file_exists($imagePath)) {
        return $imagesDir . $imageFile;
    }
    
    // Возвращаем исходный URL, если нет локальной копии
    return $car['image_url'] ?? '';
}

// Перевод значений с корейского на русский
function translateKorean($text) {
    if (empty($text)) return "";
    
    $translations = [
        // Типы коробок передач
        '자동' => 'Автомат',
        '수동' => 'Механика',
        '더블클러치' => 'Робот (DCT)',
        '무단변속기' => 'Вариатор (CVT)',
        '수동변속기' => 'Механика',
        '자동변속기' => 'Автомат',
        '세미오토' => 'Полуавтомат',
        '오토' => 'Автомат',
        '오토매틱' => 'Автомат',
        'AT' => 'Автомат',
        'MT' => 'Механика',
        'DCT' => 'Робот (DCT)',
        'CVT' => 'Вариатор (CVT)',
        
        // Типы кузова
        '세단' => 'Седан',
        '해치백' => 'Хэтчбек',
        '왜건' => 'Универсал',
        '쿠페' => 'Купе',
        'SUV' => 'Внедорожник',
        '컨버터블' => 'Кабриолет',
        '픽업트럭' => 'Пикап',
        '밴' => 'Фургон',
        
        // Типы топлива
        '가솔린' => 'Бензин',
        '디젤' => 'Дизель',
        '하이브리드' => 'Гибрид',
        '전기' => 'Электро',
        '가스' => 'Газ',
        'LPG' => 'Газ (LPG)',
        
        // Привод
        '전륜구동' => 'Передний',
        '후륜구동' => 'Задний',
        '사륜구동' => 'Полный',
        
        // Прочее
        '신차급' => 'Как новый',
        '썬루프' => 'Люк',
        '네비게이션' => 'Навигация',
        '가죽시트' => 'Кожаный салон',
        '스마트키' => 'Smart-ключ'
    ];
    
    // Если есть прямой перевод
    if (isset($translations[$text])) {
        return $translations[$text];
    }
    
    // Если нет прямого перевода, ищем вхождения корейских слов в строке
    foreach ($translations as $korean => $russian) {
        if (strpos($text, $korean) !== false) {
            $text = str_replace($korean, $russian, $text);
        }
    }
    
    return $text;
}

// Перевод марок автомобилей
function translateBrand($brand) {
    if (empty($brand)) return '';
    
    $brandTranslations = [
        '현대' => 'Hyundai',
        '기아' => 'Kia',
        '쌍용' => 'SsangYong',
        '르노삼성' => 'Renault Samsung',
        '제네시스' => 'Genesis',
        '쉐보레' => 'Chevrolet',
        '대우' => 'Daewoo',
        '쉐보레대우' => 'Chevrolet',
        '토요타' => 'Toyota',
        '닛산' => 'Nissan',
        '혼다' => 'Honda',
        'BMW' => 'BMW',
        '벤츠' => 'Mercedes-Benz',
        '아우디' => 'Audi',
        '폭스바겐' => 'Volkswagen',
        '볼보' => 'Volvo',
        '포드' => 'Ford',
        '렉서스' => 'Lexus',
        '미니' => 'MINI',
        '포르쉐' => 'Porsche',
        '랜드로버' => 'Land Rover',
        '재규어' => 'Jaguar',
        '크라이슬러' => 'Chrysler',
        '지프' => 'Jeep',
        '마세라티' => 'Maserati',
        '벤틀리' => 'Bentley',
        '페라리' => 'Ferrari',
        '람보르기니' => 'Lamborghini'
    ];
    
    // Проверка на известные модели, требующие особой маркировки
    if (strpos(strtolower($brand), 'matiz') !== false) {
        return 'Chevrolet';
    }
    
    return $brandTranslations[$brand] ?? $brand;
}

// Перевод моделей автомобилей
function translateModel($brand, $model) {
    if (empty($model)) return '';
    
    // Словари моделей по брендам
    $modelTranslations = [
        'Hyundai' => [
            '쏘나타' => 'Sonata',
            '아반떼' => 'Avante/Elantra',
            '그랜저' => 'Grandeur/Azera',
            '싼타페' => 'Santa Fe',
            '투싼' => 'Tucson',
            '팰리세이드' => 'Palisade',
            '아이오닉' => 'Ioniq'
        ],
        'Kia' => [
            'K5' => 'K5/Optima',
            'K7' => 'K7/Cadenza',
            'K9' => 'K9/Quoris',
            '스포티지' => 'Sportage',
            '쏘렌토' => 'Sorento',
            '카니발' => 'Carnival/Sedona',
            '모닝' => 'Morning/Picanto'
        ],
        'Genesis' => [
            'G70' => 'G70',
            'G80' => 'G80',
            'G90' => 'G90',
            'GV70' => 'GV70',
            'GV80' => 'GV80'
        ]
    ];
    
    $translatedBrand = translateBrand($brand);
    
    // Если есть словарь моделей для этой марки
    if (isset($modelTranslations[$translatedBrand])) {
        return $modelTranslations[$translatedBrand][$model] ?? $model;
    }
    
    // Для непереведенных моделей возвращаем оригинал
    return $model;
}

// Функция для определения категории автомобиля по марке и модели
function determineCarCategory($brand, $model) {
    // Переводим марку для правильного сравнения
    $translatedBrand = translateBrand($brand);

    // Определения категорий автомобилей
    $categories = [
        'business' => [
            'Mercedes-Benz' => ['S-Class', 'E-Class', 'Maybach S-Class', 'S', 'E', 'Maybach'],
            'BMW' => ['7 Series', '5 Series', '7', '5'],
            'Audi' => ['A8', 'A7', 'A6'],
            'Bentley' => ['Flying Spur', 'Continental GT', 'Continental', 'Flying'],
            'Rolls-Royce' => ['Phantom', 'Ghost', 'Wraith', 'Dawn', 'Spectre'],
            'Lexus' => ['LS', 'ES'],
            'Genesis' => ['G90', 'G80'],
            'Jaguar' => ['XJ', 'XF'],
            'Maserati' => ['Quattroporte', 'Ghibli'],
            'Porsche' => ['Panamera'],
            'Cadillac' => ['CT6', 'XTS']
        ],
        'sport' => [
            'Ferrari' => ['458', '488', '812', 'F8', 'Roma', 'SF90', 'Portofino', 'LaFerrari', 'F12', 'California'],
            'Lamborghini' => ['Aventador', 'Huracan', 'Gallardo', 'Murcielago'],
            'Porsche' => ['911', '718 Boxster', '718 Cayman', 'Boxster', 'Cayman', 'GT2', 'GT3'],
            'McLaren' => ['570S', '720S', 'Artura', 'GT', 'Elva', 'Senna', 'Speedtail'],
            'Aston Martin' => ['DB11', 'DB12', 'Vantage', 'DBS', 'Valkyrie', 'Valhalla'],
            'BMW' => ['M2', 'M3', 'M4', 'M5', 'M8', 'i8', 'Z4'],
            'Audi' => ['R8', 'RS7', 'RS6', 'RS5', 'RS3', 'TT RS'],
            'Mercedes-Benz' => ['AMG GT', 'C63 AMG', 'E63 AMG', 'SL', 'SLK', 'AMG'],
            'Chevrolet' => ['Corvette', 'Camaro'],
            'Ford' => ['Mustang', 'GT'],
            'Nissan' => ['GT-R', '370Z', 'GTR'],
            'Toyota' => ['GR Supra', 'GR86', 'Supra'],
            'Jaguar' => ['F-Type'],
            'Maserati' => ['MC20'],
            'Lotus' => ['Elise', 'Emira', 'Evija', 'Evora', 'Exige']
        ],
        'suv' => [
            'BMW' => ['X5', 'X6', 'X7', 'X3', 'X4', 'X1', 'X2', 'X5 M', 'X6 M', 'iX'],
            'Mercedes-Benz' => ['GLS', 'GLE', 'G-Class', 'Maybach GLS', 'GLC', 'GLA', 'GLB', 'G'],
            'Audi' => ['Q7', 'Q8', 'Q5', 'Q3', 'RS Q8', 'SQ7', 'SQ5'],
            'Range Rover' => ['Range Rover', 'Range Rover Sport', 'Range Rover Velar', 'Range Rover Evoque'],
            'Land Rover' => ['Defender', 'Discovery', 'Range Rover', 'Range Rover Sport', 'Range Rover Velar', 'Range Rover Evoque'],
            'Porsche' => ['Cayenne', 'Macan'],
            'Bentley' => ['Bentayga'],
            'Rolls-Royce' => ['Cullinan'],
            'Lamborghini' => ['Urus'],
            'Aston Martin' => ['DBX'],
            'Maserati' => ['Levante', 'Grecale'],
            'Ferrari' => ['Purosangue'],
            'Cadillac' => ['Escalade', 'XT5', 'XT6'],
            'Lexus' => ['LX', 'RX', 'GX', 'NX', 'UX'],
            'Toyota' => ['Land Cruiser', 'Highlander', 'RAV4', '4Runner'],
            'Hyundai' => ['Palisade', 'Santa Fe', 'Tucson'],
            'Kia' => ['Telluride', 'Sorento', 'Sportage'],
            'Volvo' => ['XC90', 'XC60', 'XC40'],
            'Jeep' => ['Grand Cherokee', 'Cherokee', 'Wrangler', 'Compass']
        ]
    ];

    // Проверяем каждую категорию
    foreach ($categories as $categoryName => $brands) {
        if (isset($brands[$translatedBrand])) {
            $models = $brands[$translatedBrand];
            foreach ($models as $categoryModel) {
                // Проверяем точное совпадение или вхождение
                if (stripos($model, $categoryModel) !== false || stripos($categoryModel, $model) !== false) {
                    return $categoryName;
                }
            }
        }
    }

    // Если категория не определена, возвращаем пустую строку
    return '';
}

try {
    header('Content-Type: application/json; charset=utf-8');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    header('Cache-Control: max-age=300'); // Кэшировать результаты на стороне браузера на 5 минут

    // Формируем параметры для фильтрации
    $params = [
        'date' => $date,
        'limit' => $limit,
        'offset' => $offset,
        'brand' => $brand,
        'model' => $model,
        'featuredOnly' => $featuredOnly,
        'showActive' => $showActive,
        'showRemoved' => $showRemoved,
        'yearFrom' => $yearFrom,
        'yearTo' => $yearTo,
        'priceFrom' => $priceFrom,
        'priceTo' => $priceTo,
        'transmission' => $transmission,
        'bodyType' => $bodyType,
        'fuelType' => $fuelType,
        'mileageFrom' => $mileageFrom,
        'mileageTo' => $mileageTo,
        'driveType' => $driveType,
        'category' => $category
    ];

    logDebug("Request parameters: " . json_encode($params, JSON_UNESCAPED_UNICODE));
    
    // Получаем данные из локальных CSV файлов
    $data = fetchDataFromLocalCSV($date, $params);
    
    // Сохраняем в кэш
    if ($useCache && !empty($data)) {
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0777, true);
        }
        file_put_contents($cacheFile, json_encode($data, JSON_UNESCAPED_UNICODE));
    }
    
    // Отправляем ответ
    sendJsonResponse($data);
    
} catch (Exception $e) {
    handleError($e->getMessage(), 500, $debug);
} 