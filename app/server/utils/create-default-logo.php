<?php
// Создаем изображение 200x100 пикселей
$width = 200;
$height = 100;
$image = imagecreatetruecolor($width, $height);

// Делаем фон белым
$white = imagecolorallocate($image, 255, 255, 255);
imagefill($image, 0, 0, $white);

// Добавляем серый текст "LOGO"
$grey = imagecolorallocate($image, 128, 128, 128);
$font = 5; // Встроенный шрифт
$text = "LOGO";
$text_width = imagefontwidth($font) * strlen($text);
$text_height = imagefontheight($font);
$x = ($width - $text_width) / 2;
$y = ($height - $text_height) / 2;
imagestring($image, $font, $x, $y, $text, $grey);

// Сохраняем изображение
header('Content-Type: image/png');
imagepng($image, __DIR__ . '/PNG/default-logo.png');
imagedestroy($image);

?> 