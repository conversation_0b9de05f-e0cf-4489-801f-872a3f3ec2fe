const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Настройка multer для загрузки изображений
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        // Убедимся, что директория существует
        const uploadDir = path.join(__dirname, '../public/uploads');
        if (!fs.existsSync(uploadDir)) {
            console.log('Создаем директорию uploads');
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        // Генерируем уникальное имя файла
        const uniqueName = Date.now() + '-' + Math.round(Math.random() * 1E9) + path.extname(file.originalname);
        console.log('Генерируем имя файла:', uniqueName);
        cb(null, uniqueName);
    }
});

// Добавляем фильтрацию файлов
const fileFilter = function(req, file, cb) {
    // Принимаем только изображения
    if (file.mimetype.startsWith('image/')) {
        cb(null, true);
    } else {
        cb(new Error('Только изображения могут быть загружены!'), false);
    }
};

const upload = multer({ 
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 20 * 1024 * 1024 // 20MB
    }
});

// Добавим маршрут тестирования для проверки работоспособности
router.get('/upload-test', (req, res) => {
    console.log('Получен запрос на тестирование маршрута загрузки');
    res.status(200).json({ message: 'Маршрут upload-test работает' });
});

// Явно добавим маршрут для загрузки файлов
router.post('/upload-temp', upload.array('detail_images', 10), (req, res) => {
    try {
        console.log('========== Получен запрос на загрузку изображений ==========');
        console.log('URL запроса:', req.originalUrl);
        console.log('Метод запроса:', req.method);
        console.log('Тело запроса:', typeof req.body, Object.keys(req.body).length);
        console.log('Количество файлов:', req.files ? req.files.length : 0);
        
        if (!req.files || req.files.length === 0) {
            console.error('Файлы не получены');
            return res.status(400).json({ error: 'Не удалось получить файлы' });
        }
        
        // Формируем массив с информацией о загруженных файлах
        const uploadedImages = req.files.map(file => {
            console.log('Загружен файл:', file.filename, 'размер:', file.size, 'путь:', file.path);
            return {
                path: '/uploads/' + file.filename,
                filename: file.filename
            };
        });
        
        console.log('Загружено изображений:', uploadedImages.length);
        res.status(200).json({ success: true, images: uploadedImages });
    } catch (error) {
        console.error('Ошибка при загрузке изображений:', error);
        res.status(500).json({ error: 'Ошибка при загрузке изображений: ' + error.message });
    }
});

module.exports = router; 