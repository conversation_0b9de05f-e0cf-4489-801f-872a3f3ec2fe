# КАРТА ССЫЛОК ПРОЕКТА SHMS

## HTML файлы и их ссылки

### public/index.html

**CSS файлы:**

- styles.css
- footer.css
- reviews.css
- mobile-header.css
- desktop-burger.css
- stock.css

**JavaScript файлы:**

- menu_index.js
- index.js
- reviews-loader.js
- premium-cars.js
- car-cards.js
- feedback.js

**Изображения:**

- /Img/k1_k1_k1 bbbbb.mp4 (видео)
- /Img/vertical.mp4 (видео)
- /Img/SHMS_logo_black.png (постер)
- /Img/Ferrari_logo.png
- /Img/BMW_logo.png
- /Img/Porsche_logo.png
- /PNG/SHMS_Logo.png

### public/stock.html

**CSS файлы:**

- stock.css
- footer.css
- mobile-header.css
- hero.css

**JavaScript файлы:**

- menu.js
- stock.js

**Изображения:**

- /PNG/SHMS_Logo.png

### public/order.html

**CSS файлы:**

- order.css
- footer.css
- mobile-header.css
- hero.css

**JavaScript файлы:**

- menu.js
- order-encar.js

### public/contacts.html

**CSS файлы:**

- contacts.css
- footer.css
- mobile-header.css

**JavaScript файлы:**

- menu.js

### public/car-detail.html

**CSS файлы:**

- car-detail.css
- car-detail-styles.css
- footer.css
- mobile-header.css
- specs-fix.css

**JavaScript файлы:**

- menu.js
- car-detail-carousel.js

### admin-stock/index.html

**CSS файлы:**

- styles.css
- https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css (внешний)

**JavaScript файлы:**

- admin.js
- database.js

### admin-stock/featured.html

**CSS файлы:**

- styles.css
- https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css (внешний)

**JavaScript файлы:**

- featured.js
- add-to-featured.js

## CSS файлы и их ссылки на изображения

### Найденные пути к изображениям в CSS:

- background-image: url('Img/...')
- background-image: url('../PNG/...')
- background-image: url('/Img/...')

## JavaScript файлы и их ссылки

### Найденные пути в JS:

- 'Img/placeholder-car.jpg'
- 'Img/bmw-m5.jpg'
- 'Img/ferrari.jpg'
- '/api/encar'
- 'public/uploads/'

## Изображения для перемещения

### public/Img/ → public/assets/img/cars/

- AUDI_Hero.jpg → audi-hero.jpg
- Audi_1.jpg → audi-1.jpg
- Audi_2.jpeg → audi-2.jpeg
- Audi_3.jpeg → audi-3.jpeg
- Audi_4.jpeg → audi-4.jpeg
- Audi_5.jpeg → audi-5.jpeg
- Audi_hero.png → audi-hero.png
- Audi_niz.jpeg → audi-niz.jpeg
- Porshe_2.jpeg → porsche-2.jpeg
- Porshe_3.jpeg → porsche-3.jpeg
- Porshe_4.jpeg → porsche-4.jpeg
- aston-martin.jpg
- audi-rs7.jpg
- bentley-bentayga.jpg
- bentley-continental.jpg
- bentley-flying-spur.jpg
- bmw-m4.jpg
- bmw-m5.jpg
- bmw.jpg
- ferrari.jpg
- ford.png
- gelik 1.png → gelik-1.png
- gelik.webp
- lamborghini.jpg
- maclaren.png
- maibax.png
- placeholder-car.jpg
- car-placeholder.jpg

### public/Img/ → public/assets/img/logos/

- AstonMartin_logo.png → logo-aston-martin.png
- Audi_logo.png → logo-audi.png
- BMW_logo.png → logo-bmw.png
- Ferrari_logo.png → logo-ferrari.png
- Jaguar_logo.png → logo-jaguar.png
- Lambo_logo.png → logo-lamborghini.png
- Merc_logo.png → logo-mercedes.png
- Porsche_logo.png → logo-porsche.png
- RR_logo.png → logo-rolls-royce.png
- Tesla_logo.png → logo-tesla.png

### public/Img/ → public/assets/img/icons/

- icon-engine.svg
- icon-mileage.svg
- icon-transmission.svg

### public/Img/ → public/assets/img/backgrounds/

- 100.mp4
- k1_1610.mp4
- k1_k1_k1 bbbbb.mp4
- vertical.mp4

### public/PNG/ → public/assets/img/icons/

- SHMS_Logo.png → logo-shms.png
- SHMS_Logo_Black.png → logo-shms-black.png
- car.png → icon-car.png
- default-logo.png → icon-default-logo.png
- gas_pump.png → icon-gas-pump.png
- icon_bts.png → icon-bts.png
- icon_card.png → icon-card.png
- icon_copy.png → icon-copy.png
- speed.png → icon-speed.png

## CSS файлы и их ссылки на ресурсы

### public/styles.css

**Шрифты:**

- url('/font/Inter-VariableFont_opsz,wght.ttf')
- url('/font/Inter-Italic-VariableFont_opsz,wght.ttf')

### public/order.css

**Шрифты:**

- url('/font/Inter-VariableFont_opsz,wght.ttf')
- url('/font/Inter-Italic-VariableFont_opsz,wght.ttf')

**Фоновые изображения:**

- url('Img/bmw-m5.jpg')
- url('Img/bentley-continental.jpg')
- url('Img/aston-martin.jpg')
- url('Img/audi-rs7.jpg')
- url('Img/ferrari.jpg')
- url('Img/lamborghini.jpg')

### Другие CSS файлы

- public/car-detail.css
- public/stock.css
- public/footer.css
- public/reviews.css
- public/mobile-header.css
- public/desktop-burger.css
- public/hero.css
- public/contacts.css
- public/car-detail-styles.css
- public/specs-fix.css
- admin-stock/styles.css

## JavaScript файлы и их ссылки на ресурсы

### Найденные пути в JS файлах:

- 'Img/placeholder-car.jpg'
- 'Img/bmw-m5.jpg'
- 'Img/ferrari.jpg'
- 'Img/bmw-m4.jpg'
- 'Img/audi-rs7.jpg'
- 'Img/lamborghini.jpg'
- 'Img/bentley-flying-spur.jpg'
- 'Img/bentley-bentayga.jpg'
- 'Img/bentley-continental.jpg'
- 'Img/aston-martin.jpg'
- '/api/encar'
- 'public/uploads/'
- '/uploads/'

## Файлы для удаления/архивирования

- \*.bak файлы
- public/Primer/ (если не используется)
- Неиспользуемые дублирующиеся файлы
