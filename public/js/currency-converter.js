/**
 * Библиотека для конвертации валют
 * Использует курс ЦБ РФ для воны и rates.json для доллара
 */

class CurrencyConverter {
  constructor() {
    this.rates = null;
    this.lastUpdate = null;
    this.cacheTimeout = 30 * 60 * 1000; // 30 минут
    this.fallbackRates = {
      KRW_to_RUB: 0.075,
      USD_rates: {
        usdtrub: { sell: 95, buy: 94 }
      }
    };
  }

  /**
   * Получение актуальных курсов валют
   */
  async getRates() {
    // Проверяем кэш
    if (this.rates && this.lastUpdate && 
        (Date.now() - this.lastUpdate) < this.cacheTimeout) {
      console.log('💰 Используем кэшированные курсы валют');
      return this.rates;
    }

    try {
      console.log('💰 Получаем свежие курсы валют...');
      
      const response = await fetch('/api/exchange-rates.php', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        cache: 'no-cache'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        this.rates = {
          KRW_to_RUB: parseFloat(data.KRW_to_RUB),
          USD_rates: data.USD_rates
        };
        this.lastUpdate = Date.now();
        
        console.log('✅ Курсы валют получены:', {
          source: data.source,
          KRW_to_RUB: this.rates.KRW_to_RUB,
          USD_rates: this.rates.USD_rates,
          updated: data.updated
        });
        
        return this.rates;
      } else {
        throw new Error(data.error || 'Failed to get rates');
      }
      
    } catch (error) {
      console.error('❌ Ошибка получения курсов валют:', error);
      
      // Используем fallback курсы
      if (!this.rates) {
        console.log('⚠️ Используем резервные курсы валют');
        this.rates = this.fallbackRates;
        this.lastUpdate = Date.now();
      }
      
      return this.rates;
    }
  }

  /**
   * Конвертация корейских вон в рубли
   */
  async convertKRWToRUB(krwAmount) {
    if (!krwAmount || krwAmount <= 0) return 0;
    
    const rates = await this.getRates();
    const rubAmount = krwAmount * rates.KRW_to_RUB;
    
    console.log(`💱 Конвертация: ${krwAmount} KRW = ${rubAmount.toFixed(2)} RUB (курс: ${rates.KRW_to_RUB})`);
    
    return rubAmount;
  }

  /**
   * Конвертация рублей в доллары
   */
  async convertRUBToUSD(rubAmount, useRate = 'sell') {
    if (!rubAmount || rubAmount <= 0) return 0;
    
    const rates = await this.getRates();
    
    // Выбираем лучший доступный курс доллара
    let usdRate = null;
    
    if (rates.USD_rates.usdtrub && rates.USD_rates.usdtrub[useRate]) {
      usdRate = rates.USD_rates.usdtrub[useRate];
    } else if (rates.USD_rates.usda7a5 && rates.USD_rates.usda7a5[useRate]) {
      usdRate = rates.USD_rates.usda7a5[useRate];
    } else if (rates.USD_rates.usdta7a5 && rates.USD_rates.usdta7a5[useRate]) {
      usdRate = rates.USD_rates.usdta7a5[useRate];
    } else {
      // Fallback курс
      usdRate = 95;
    }
    
    const usdAmount = rubAmount / usdRate;
    
    console.log(`💱 Конвертация: ${rubAmount.toFixed(2)} RUB = ${usdAmount.toFixed(2)} USD (курс: ${usdRate})`);
    
    return usdAmount;
  }

  /**
   * Прямая конвертация корейских вон в доллары
   */
  async convertKRWToUSD(krwAmount) {
    if (!krwAmount || krwAmount <= 0) return 0;
    
    // Сначала конвертируем в рубли
    const rubAmount = await this.convertKRWToRUB(krwAmount);
    
    // Затем в доллары
    const usdAmount = await this.convertRUBToUSD(rubAmount);
    
    return usdAmount;
  }

  /**
   * Расчет цены автомобиля по новой формуле
   */
  async calculateCarPrice(koreanWonPrice) {
    if (!koreanWonPrice || koreanWonPrice <= 0) return 0;
    
    try {
      // Применяем формулу: цена в вонах × 10,000 для получения реальной цены
      const realKrwPrice = koreanWonPrice * 10000;
      
      // Конвертируем в доллары
      const usdPrice = await this.convertKRWToUSD(realKrwPrice);
      
      console.log(`🚗 Расчет цены авто: ${koreanWonPrice} (сайт) → ${realKrwPrice} KRW → ${usdPrice.toFixed(0)} USD`);
      
      return Math.round(usdPrice);
      
    } catch (error) {
      console.error('❌ Ошибка расчета цены автомобиля:', error);
      
      // Fallback к старой формуле
      const fallbackPrice = Math.round((koreanWonPrice * 10000) / 1320);
      console.log(`⚠️ Используем fallback расчет: ${fallbackPrice} USD`);
      
      return fallbackPrice;
    }
  }

  /**
   * Форматирование цены в долларах
   */
  formatUSDPrice(price) {
    if (!price || price <= 0) return "$0";
    
    return new Intl.NumberFormat("ru-RU", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  }

  /**
   * Форматирование цены в рублях
   */
  formatRUBPrice(price) {
    if (!price || price <= 0) return "0 ₽";
    
    return new Intl.NumberFormat("ru-RU", {
      style: "currency",
      currency: "RUB",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  }

  /**
   * Получение информации о курсах для отладки
   */
  async getDebugInfo() {
    const rates = await this.getRates();
    
    return {
      rates: rates,
      lastUpdate: this.lastUpdate ? new Date(this.lastUpdate).toLocaleString() : 'never',
      cacheValid: this.lastUpdate && (Date.now() - this.lastUpdate) < this.cacheTimeout
    };
  }
}

// Создаем глобальный экземпляр конвертера
window.currencyConverter = new CurrencyConverter();

// Экспортируем для использования в других модулях
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CurrencyConverter;
}
