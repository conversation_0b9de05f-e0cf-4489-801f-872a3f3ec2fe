<?php
// Скрипт для отладки данных из CSV-файлов

header('Content-Type: text/html; charset=utf-8');
echo "<h1>Отладка данных о пробеге</h1>";

// Подключаем основной файл API
require_once 'encar-proxy.php';

// Находим последний CSV-файл
$dataDir = __DIR__ . '/encar_data/';
$csvFiles = glob($dataDir . '*.csv');

if (empty($csvFiles)) {
    echo "<p>CSV файлы не найдены в директории $dataDir</p>";
    exit;
}

// Выбираем последний файл
$latestCsv = $csvFiles[0];
echo "<p>Используем файл: " . basename($latestCsv) . "</p>";

// Функция для чтения данных из CSV
function readCsvData($file, $limit = 10) {
    $handle = fopen($file, "r");
    if (!$handle) {
        echo "<p>Не удалось открыть файл: $file</p>";
        return [];
    }
    
    // Получаем заголовки
    $headers = fgetcsv($handle, 0, "|");
    if (!$headers) {
        fclose($handle);
        echo "<p>Не удалось прочитать заголовки из файла: $file</p>";
        return [];
    }
    
    // Читаем данные
    $cars = [];
    $count = 0;
    
    while (($data = fgetcsv($handle, 0, "|")) !== FALSE && $count < $limit) {
        if (count($data) !== count($headers)) continue;
        
        $car = array_combine($headers, $data);
        $cars[] = $car;
        $count++;
    }
    
    fclose($handle);
    return ['headers' => $headers, 'cars' => $cars];
}

// Читаем данные из CSV
$data = readCsvData($latestCsv);

// Выводим информацию о полях, связанных с пробегом
echo "<h2>Поля, связанные с пробегом</h2>";
$mileageFields = [];
foreach ($data['headers'] as $header) {
    if (stripos($header, 'mileage') !== false || 
        stripos($header, 'km') !== false || 
        stripos($header, 'odometer') !== false ||
        stripos($header, 'пробег') !== false) {
        $mileageFields[] = $header;
    }
}

if (empty($mileageFields)) {
    echo "<p>Поля, связанные с пробегом, не найдены</p>";
} else {
    echo "<p>Найдены поля: " . implode(", ", $mileageFields) . "</p>";
}

// Выводим данные о пробеге для первых 10 машин
echo "<h2>Данные о пробеге для первых 10 машин</h2>";
echo "<table border='1' cellspacing='0' cellpadding='5'>";
echo "<tr><th>ID</th><th>Марка</th><th>Модель</th><th>Год</th>";
foreach ($mileageFields as $field) {
    echo "<th>$field</th>";
}
echo "<th>Оригинальное значение</th><th>Отформатированное значение</th></tr>";

foreach ($data['cars'] as $car) {
    echo "<tr>";
    echo "<td>" . ($car['id'] ?? 'Н/Д') . "</td>";
    echo "<td>" . ($car['mark'] ?? $car['brand'] ?? 'Н/Д') . "</td>";
    echo "<td>" . ($car['model'] ?? 'Н/Д') . "</td>";
    echo "<td>" . ($car['year'] ?? 'Н/Д') . "</td>";
    
    foreach ($mileageFields as $field) {
        echo "<td>" . ($car[$field] ?? 'Н/Д') . "</td>";
    }
    
    // Получаем значение, которое используется для отображения
    $mileageValue = $car['mileage'] ?? ($car['km_age'] ?? '0');
    
    echo "<td>$mileageValue</td>";
    echo "<td>" . formatMileage($mileageValue) . "</td>";
    echo "</tr>";
}
echo "</table>";

// Добавляем тест функции форматирования пробега
echo "<h2>Тест функции formatMileage</h2>";
$testValues = ['0', '1000', '10000', '100000', '', '0 км', '1000km', '10,000', '10.000', 'Н/Д'];
echo "<table border='1' cellspacing='0' cellpadding='5'>";
echo "<tr><th>Исходное значение</th><th>Результат formatMileage</th></tr>";

foreach ($testValues as $value) {
    echo "<tr>";
    echo "<td>$value</td>";
    echo "<td>" . formatMileage($value) . "</td>";
    echo "</tr>";
}
echo "</table>";

// Предлагаем исправленную версию функции formatMileage
echo "<h2>Предлагаемое исправление функции formatMileage</h2>";
echo "<pre>";
echo "function formatMileage(\$mileage) {
    // Если значение пустое или явно ноль, возвращаем \"Без пробега\"
    if (empty(\$mileage) || \$mileage === '0' || \$mileage === 0) {
        return \"Без пробега\";
    }
    
    // Преобразуем все варианты записи в числовое значение
    // Удаляем все нецифровые символы
    \$mileage = preg_replace('/[^0-9]/', '', \$mileage);
    
    // Если после преобразования получился ноль или пустая строка, возвращаем \"Без пробега\"
    if (empty(\$mileage) || intval(\$mileage) === 0) {
        return \"Без пробега\";
    }
    
    // Форматируем число с разделителями разрядов
    return number_format(intval(\$mileage), 0, '.', ' ') . \" км\";
}";
echo "</pre>";

// Тест предлагаемой функции
function improvedFormatMileage($mileage) {
    // Если значение пустое или явно ноль, возвращаем "Без пробега"
    if (empty($mileage) || $mileage === '0' || $mileage === 0) {
        return "Без пробега";
    }
    
    // Преобразуем все варианты записи в числовое значение
    // Удаляем все нецифровые символы
    $mileage = preg_replace('/[^0-9]/', '', $mileage);
    
    // Если после преобразования получился ноль или пустая строка, возвращаем "Без пробега"
    if (empty($mileage) || intval($mileage) === 0) {
        return "Без пробега";
    }
    
    // Форматируем число с разделителями разрядов
    return number_format(intval($mileage), 0, '.', ' ') . " км";
}

echo "<h2>Тест улучшенной функции formatMileage</h2>";
echo "<table border='1' cellspacing='0' cellpadding='5'>";
echo "<tr><th>Исходное значение</th><th>Результат improvedFormatMileage</th></tr>";

foreach ($testValues as $value) {
    echo "<tr>";
    echo "<td>$value</td>";
    echo "<td>" . improvedFormatMileage($value) . "</td>";
    echo "</tr>";
}
echo "</table>";

// Проверяем значения из реальных данных
echo "<h2>Сравнение оригинальной и улучшенной функции на реальных данных</h2>";
echo "<table border='1' cellspacing='0' cellpadding='5'>";
echo "<tr><th>ID</th><th>Марка</th><th>Модель</th><th>Значение пробега</th><th>Оригинальная функция</th><th>Улучшенная функция</th></tr>";

foreach ($data['cars'] as $car) {
    $mileageValue = $car['mileage'] ?? ($car['km_age'] ?? '0');
    
    echo "<tr>";
    echo "<td>" . ($car['id'] ?? 'Н/Д') . "</td>";
    echo "<td>" . ($car['mark'] ?? $car['brand'] ?? 'Н/Д') . "</td>";
    echo "<td>" . ($car['model'] ?? 'Н/Д') . "</td>";
    echo "<td>$mileageValue</td>";
    echo "<td>" . formatMileage($mileageValue) . "</td>";
    echo "<td>" . improvedFormatMileage($mileageValue) . "</td>";
    echo "</tr>";
}
echo "</table>";
?> 