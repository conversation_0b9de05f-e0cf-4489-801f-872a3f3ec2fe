<?php
// Скрипт для создания изображения-заглушки

// Указываем путь для сохранения файлов
$placeholder_path = 'Img/placeholder-car.jpg';
$logo_path = 'PNG/default-logo.png';

// Проверяем и создаем директории, если они не существуют
if (!is_dir('Img')) {
    mkdir('Img', 0755, true);
}
if (!is_dir('PNG')) {
    mkdir('PNG', 0755, true);
}

// Функция для создания простого изображения
function createSimpleImage($width, $height, $color, $text, $path) {
    // Создаем изображение
    $image = imagecreatetruecolor($width, $height);
    
    // Создаем цвета
    $bg_color = imagecolorallocate($image, 
        hexdec(substr($color, 1, 2)),
        hexdec(substr($color, 3, 2)),
        hexdec(substr($color, 5, 2))
    );
    $text_color = imagecolorallocate($image, 255, 255, 255);
    
    // Заполняем фон
    imagefill($image, 0, 0, $bg_color);
    
    // Добавляем текст
    $font_size = 5;
    $text_width = imagefontwidth($font_size) * strlen($text);
    $text_height = imagefontheight($font_size);
    
    // Центрируем текст
    $center_x = ($width / 2) - ($text_width / 2);
    $center_y = ($height / 2) - ($text_height / 2);
    
    imagestring($image, $font_size, $center_x, $center_y, $text, $text_color);
    
    // Сохраняем изображение
    if (pathinfo($path, PATHINFO_EXTENSION) == 'jpg' || pathinfo($path, PATHINFO_EXTENSION) == 'jpeg') {
        imagejpeg($image, $path, 90);
    } else {
        imagepng($image, $path, 9);
    }
    
    // Освобождаем память
    imagedestroy($image);
    
    return file_exists($path);
}

// Создаем изображение-заглушку для автомобиля
$placeholder_created = createSimpleImage(600, 400, '#505050', 'Placeholder Image', $placeholder_path);

// Создаем лого-заглушку
$logo_created = createSimpleImage(100, 100, '#333333', 'Logo', $logo_path);

// Выводим результат
header('Content-Type: text/html; charset=utf-8');
echo '<h1>Создание файлов-заглушек</h1>';

if ($placeholder_created) {
    echo "<p style='color:green'>✅ Файл-заглушка для автомобиля создан: {$placeholder_path}</p>";
} else {
    echo "<p style='color:red'>❌ Ошибка создания файла-заглушки для автомобиля: {$placeholder_path}</p>";
}

if ($logo_created) {
    echo "<p style='color:green'>✅ Файл-заглушка для логотипа создан: {$logo_path}</p>";
} else {
    echo "<p style='color:red'>❌ Ошибка создания файла-заглушки для логотипа: {$logo_path}</p>";
}

// Отображаем созданные изображения
echo '<h2>Предпросмотр созданных изображений:</h2>';
echo "<div style='margin-top: 20px;'>";
echo "<div style='margin-bottom: 20px;'>";
echo "<h3>Заглушка автомобиля:</h3>";
echo "<img src='{$placeholder_path}' style='max-width: 300px; border: 1px solid #ccc;'>";
echo "</div>";

echo "<div>";
echo "<h3>Заглушка логотипа:</h3>";
echo "<img src='{$logo_path}' style='max-width: 100px; border: 1px solid #ccc;'>";
echo "</div>";
echo "</div>";

// Инструкции
echo '<h2>Следующие шаги:</h2>';
echo '<ol>';
echo '<li>Откройте <a href="test.html" target="_blank">тестовую страницу</a> для проверки всех компонентов</li>';
echo '<li>Проверьте доступность API и PHP через тестовую страницу</li>';
echo '<li>Проверьте работу карточек на странице заказа: <a href="order.html" target="_blank">order.html</a></li>';
echo '</ol>';
?> 