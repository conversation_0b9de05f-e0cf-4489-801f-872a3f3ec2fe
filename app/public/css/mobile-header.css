/* Моб<PERSON>льный заголовок для всех страниц */
.mobile-header {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 64px;
  background: #fff;
  box-shadow: 0 1px 3px rgba(0,0,0,0.08);
  padding: 0;
  z-index: 100;
}

.mobile-header__logo {
  position: static;
  margin: 0 auto;
  width: auto;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-decoration: none;
}

.mobile-header__logo-img {
  height: 38px;
  width: auto;
  object-fit: contain;
}

.mobile-header-spacer {
  display: none;
  height: 64px;
  width: 100%;
}

/* Бургер меню */
.burger {
  position: absolute;
  top: 50%;
  left: 20px;
  width: 32px;
  height: 32px;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  background: none;
  border: none;
  z-index: 2000;
  cursor: pointer;
  gap: 4px;
}

.burger span {
  display: block;
  width: 22px;
  height: 2px;
  background: #000;
  border-radius: 0;
  transition: transform 0.3s cubic-bezier(.4,1.3,.6,1), opacity 0.25s;
}

/* Анимация бургера */
.burger.open span:nth-child(1) {
  transform: translateY(6px) rotate(45deg);
}
.burger.open span:nth-child(2) {
  opacity: 0;
}
.burger.open span:nth-child(3) {
  transform: translateY(-6px) rotate(-45deg);
}

/* Выпадающее меню */
.dropdown-menu {
  position: fixed;
  top: 60px;
  left: 0;
  width: 100%;
  background: #fff;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  padding: 20px;
  z-index: 1000;
  display: none;
  flex-direction: column;
  gap: 15px;
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 0.2s, transform 0.2s;
}

.dropdown-menu.active {
  display: flex;
  opacity: 1;
  transform: translateY(0);
}

.dropdown-menu a {
  color: #111;
  font-size: 1.1rem;
  text-decoration: none;
  padding: 10px 15px;
  border-radius: 5px;
  transition: background 0.2s;
}

.dropdown-menu a:hover {
  background: #f0f0f0;
}

/* Медиа-запрос для мобильного отображения */
@media (max-width: 768px) {
  .burger {
    display: flex;
  }
  
  .mobile-header {
    display: flex;
  }
  
  .mobile-header-spacer {
    display: block;
  }
} 

/* Скрываем на десктопе */
@media (min-width: 769px) {
  .mobile-header,
  .mobile-header-spacer,
  .burger,
  .dropdown-menu {
    display: none !important;
  }
} 

.burger,
.burger:focus,
.burger:active {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
} 