// Интеграция с API Encar
document.addEventListener("DOMContentLoaded", function () {
  console.log("Encar API загружен");

  // Добавляем стили для светлой минималистичной темы
  addStyles();

  // Добавляем отладочную информацию о наличии файлов
  logDebugInfo();

  // Инициализация поиска
  initSearch();

  // Показываем контейнер карточек
  const cardsContainer = document.querySelector(".order-cards");
  if (cardsContainer) {
    // Сначала показываем сообщение о загрузке
    cardsContainer.innerHTML =
      '<div class="loading">Загрузка автомобилей...</div>';

    // Устанавливаем таймаут для инициализации API
    setTimeout(() => {
      // Инициализируем API через 100мс для более быстрого отображения страницы
      initEncarIntegration();
    }, 100);
  }

  // Функция для перевода корейского текста на русский
  function translateKoreanToRussian(text) {
    if (!text) return "";

    // Словарь с наиболее распространенными корейскими словами для автомобилей
    const koreanToRussian = {
      // Двигатели
      가솔린: "Бензин",
      디젤: "Дизель",
      하이브리드: "Гибрид",
      전기: "Электро",
      가스: "Газ",

      // Трансмиссии
      자동: "Автомат",
      수동: "Механика",
      듀얼클러치: "Робот (DCT)",
      무단변속기: "Вариатор (CVT)",

      // Привод
      전륜구동: "Передний",
      후륜구동: "Задний",
      사륜구동: "Полный",

      // Типы кузова
      세단: "Седан",
      해치백: "Хэтчбек",
      왜건: "Универсал",
      쿠페: "Купе",
      SUV: "Внедорожник",
      컨버터블: "Кабриолет",

      // Цвета
      흰색: "Белый",
      검정색: "Черный",
      빨간색: "Красный",
      파란색: "Синий",
      은색: "Серебристый",
      회색: "Серый",

      // Марки
      현대: "Hyundai",
      기아: "Kia",
      쌍용: "SsangYong",
      르노삼성: "Renault Samsung",
      제네시스: "Genesis",
      쉐보레: "Chevrolet",

      // Общие слова
      신차: "Новый",
      중고: "Б/у",
      키로미터: "км",
      년식: "год выпуска",
      옵션: "опции",
      에어백: "подушки безопасности",
      네비게이션: "навигация",
      선루프: "люк",
      블루투스: "Bluetooth",
      가죽시트: "кожаные сиденья",
    };

    // Проверяем, содержит ли текст корейские символы
    if (
      /[\uAC00-\uD7AF\u1100-\u11FF\u3130-\u318F\uA960-\uA97F\uD7B0-\uD7FF]/.test(
        text
      )
    ) {
      // Пытаемся заменить корейские слова на русские
      let translated = text;

      Object.keys(koreanToRussian).forEach((korean) => {
        // Создаем регулярное выражение для поиска корейского слова в тексте
        const regex = new RegExp(korean, "g");
        translated = translated.replace(regex, koreanToRussian[korean]);
      });

      return translated;
    }

    return text;
  }

  // Замена глобального объекта window для доступа к функции перевода из любого места
  window.translateKoreanText = translateKoreanToRussian;

  // Функция добавления стилей для светлой минималистичной темы
  function addStyles() {
    const style = document.createElement("style");
    style.textContent = `
      .order-cards {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        margin: 20px auto;
      }

      .order-card {
        background: #ffffff;
        border-radius: 8px;
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        position: relative;
        color: #333;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        border: 1px solid #eaeaea;
      }

      .order-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      }

      .order-card-image {
        height: 200px;
        overflow: hidden;
        position: relative;
      }

      .order-card-image img {
        width: 100%;
        height: 100%;
        transition: opacity 0.25s ease, transform 0.25s ease;
      }

      .order-card:hover .order-card-image img {
        transform: scale(1.05);
      }

      .order-card-year {
        position: absolute;
        bottom: 10px;
        left: 10px;
        background: rgba(255, 255, 255, 0.85);
        color: #555;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 13px;
        font-weight: 500;
        border: 1px solid #e5e5e5;
        z-index: 5;
      }

      /* Стрелки для навигации по изображениям */
      .card-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background-color: rgba(255, 255, 255, 0.7);
        color: #333;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 18px;
        opacity: 0;
        transition: opacity 0.2s ease, background-color 0.2s ease;
        z-index: 5;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      }

      .prev-img {
        left: 8px;
      }

      .next-img {
        right: 8px;
      }

      .order-card:hover .card-nav {
        opacity: 0.8;
      }

      .card-nav:hover {
        opacity: 1 !important;
        background-color: rgba(255, 255, 255, 0.9);
      }

      .order-card-content {
        padding: 15px;
      }

      .order-card-title h3 {
        margin: 0 0 15px 0;
        font-size: 17px;
        font-weight: 600;
        color: #333;
        letter-spacing: 0.3px;
      }

      .order-card-specs {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 15px;
        border-left: 2px solid #e0e0e0;
        padding-left: 10px;
      }

      .spec-item {
        display: flex;
        justify-content: space-between;
      }

      .spec-label {
        color: #777;
        font-size: 13px;
      }

      .spec-value {
        color: #333;
        font-size: 13px;
        font-weight: 500;
      }

      .order-card-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 15px;
        border-top: 1px solid #f0f0f0;
        padding-top: 15px;
      }

      .order-card-price {
        font-size: 18px;
        font-weight: 700;
        color: #444;
        letter-spacing: 0.3px;
      }

      .order-card-buttons {
        display: flex;
        gap: 8px;
      }

      .order-card-details-btn {
        background: #f5f5f5;
        color: #555;
        border: 1px solid #e0e0e0;
        padding: 7px 14px;
        border-radius: 20px;
        cursor: pointer;
        font-size: 13px;
        transition: all 0.3s ease;
        font-weight: 500;
      }

      .order-card-details-btn:hover {
        background: #ebebeb;
        color: #333;
      }

      .order-card-contact-btn {
        background: #e8f4ff;
        color: #2980b9;
        border: 1px solid #bce0fd;
        padding: 7px 14px;
        border-radius: 20px;
        cursor: pointer;
        font-size: 13px;
        transition: all 0.3s ease;
        font-weight: 500;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
      }

      .order-card-contact-btn:hover {
        background: #d1e9ff;
        color: #1c6ca2;
        box-shadow: 0 2px 5px rgba(41, 128, 185, 0.1);
      }

      .order-card-details {
        background: #f9f9f9;
        padding: 12px;
        margin-top: 15px;
        border-radius: 6px;
        font-size: 13px;
        line-height: 1.5;
        color: #555;
      }

      .order-card-details div {
        margin-bottom: 8px;
      }

      .order-card-details b {
        color: #333;
      }

      .loading, .no-results, .error {
        padding: 30px 20px;
        text-align: center;
        background: #f9f9f9;
        border-radius: 8px;
        margin: 20px 0;
        color: #666;
        grid-column: 1 / -1;
      }

      .error {
        border-left: 3px solid #e74c3c;
      }

      .no-results {
        border-left: 3px solid #e0e0e0;
      }

      /* Адаптивность для мобильных устройств */
      @media (max-width: 768px) {
        .order-cards {
          grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        }

        .card-nav {
          opacity: 0.8; /* Всегда показываем на мобильных */
          width: 28px;
          height: 28px;
          font-size: 16px;
        }
      }
    `;
    document.head.appendChild(style);
  }

  // Функция для логирования отладочной информации
  async function logDebugInfo() {
    console.log("🔍 Проверка наличия необходимых файлов...");

    // Проверка наличия прокси-скрипта
    try {
      const proxyResponse = await fetch("/api/encar", { method: "HEAD" });
      console.log(
        `📋 Статус прокси-скрипта: ${proxyResponse.status} ${
          proxyResponse.ok ? "✅" : "❌"
        }`
      );
    } catch (e) {
      console.error("❌ Ошибка при проверке прокси-скрипта:", e);
    }

    // Проверка наличия локальных изображений
    const imagesToCheck = [
      "Img/placeholder-car.jpg",
      "Img/bmw-m5.jpg",
      "Img/ferrari.jpg",
    ];

    for (const img of imagesToCheck) {
      try {
        const imgResponse = await fetch(img, { method: "HEAD" });
        console.log(
          `🖼️ Изображение ${img}: ${imgResponse.status} ${
            imgResponse.ok ? "✅" : "❌"
          }`
        );
      } catch (e) {
        console.error(`❌ Ошибка при проверке изображения ${img}:`, e);
      }
    }

    // Проверка наличия мок-данных
    try {
      const mockResponse = await fetch("/api/mock-data", { method: "HEAD" });
      console.log(
        `📊 Статус мок-данных: ${mockResponse.status} ${
          mockResponse.ok ? "✅" : "❌"
        }`
      );
    } catch (e) {
      console.error("❌ Ошибка при проверке мок-данных:", e);
    }

    console.log("🔍 Проверка завершена");
  }

  // Инициализация функционала поиска
  function initSearch() {
    const searchInput = document.querySelector(".search-input");
    const searchButton = document.querySelector("#search-btn");
    if (!searchInput) return;

    // Функция выполнения поиска
    function executeSearch() {
      const searchText = searchInput.value.trim();
      if (searchText.length > 0) {
        console.log("🔍 Поиск автомобиля:", searchText);
        // Показываем индикатор загрузки
        const cardsContainer = document.querySelector(".order-cards");
        if (cardsContainer) {
          cardsContainer.innerHTML =
            '<div class="loading">Поиск автомобилей по запросу "' +
            searchText +
            '"...</div>';
        }
        // Запускаем поиск
        searchCars(searchText);
      }
    }

    // Улучшенная обработка поиска
    searchInput.addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        executeSearch();
      }
    });

    // Обработчик клика на кнопку поиска
    if (searchButton) {
      searchButton.addEventListener("click", executeSearch);
    }

    // Улучшенная обработка очистки поиска для возврата к исходному меню
    let typingTimer;
    const doneTypingInterval = 800; // Таймер в мс после последнего ввода

    searchInput.addEventListener("input", function (e) {
      clearTimeout(typingTimer);

      if (searchInput.value.trim() === "") {
        // Если поле полностью очищено, сразу возвращаем исходные карточки
        const cardsContainer = document.querySelector(".order-cards");
        if (cardsContainer) {
          console.log("Поле поиска очищено, возвращаем исходные карточки");
          // Показываем индикатор загрузки
          cardsContainer.innerHTML =
            '<div class="loading">Загрузка автомобилей...</div>';
          // Возвращаем исходные карточки
          initEncarIntegration();
        }
      } else {
        // Если пользователь набирает текст, ждем, когда он закончит
        typingTimer = setTimeout(function () {
          // Ничего не делаем, просто ждем Enter
        }, doneTypingInterval);
      }
    });
  }

  // Улучшенная функция поиска автомобилей
  async function searchCars(searchText) {
    const cardsContainer = document.querySelector(".order-cards");
    if (!cardsContainer) return;

    console.log("🔍 Поиск автомобилей по запросу:", searchText);

    // Показываем индикатор загрузки с текстом запроса
    cardsContainer.innerHTML =
      '<div class="loading">Поиск автомобилей по запросу "' +
      searchText +
      '"...</div>';

    // Устанавливаем таймаут для поиска
    const searchTimeoutMs = 60000; // 60 секунд (увеличено для обработки больших файлов)
    let searchTimedOut = false;

    // Таймер для отмены поиска, если он слишком долгий
    const timeoutId = setTimeout(() => {
      searchTimedOut = true;
      console.warn(
        "⏱️ Поиск превысил " +
          searchTimeoutMs / 1000 +
          " сек, выполняем локальный поиск"
      );
      cardsContainer.innerHTML =
        '<div class="loading">Поиск занимает больше времени, чем ожидалось. Используем локальные данные...</div>';
      performLocalSearch(searchText);
    }, searchTimeoutMs);

    try {
      // Нормализуем поисковой запрос
      searchText = searchText.toLowerCase().trim();
      const searchTerms = searchText.split(/\s+/);

      // Добавляем тест доступности прокси
      console.log("🔍 Проверка доступности прокси-сервера...");
      try {
        const proxyCheckResponse = await fetch("/api/encar?check=1");
        if (!proxyCheckResponse.ok) {
          console.error("❌ Прокси-сервер недоступен или вернул ошибку");
          console.error("❌ Статус ответа прокси:", proxyCheckResponse.status);
          throw new Error("Прокси-сервер недоступен");
        }

        const proxyCheckText = await proxyCheckResponse.text();
        console.log("✅ Ответ от проверки прокси:", proxyCheckText);
      } catch (proxyError) {
        console.error("❌ Ошибка при проверке прокси:", proxyError);

        // Добавим проверку, существует ли файл
        console.log("🔍 Проверка наличия файла прокси...");
        const fileCheckResponse = await fetch("/api/encar", {
          method: "HEAD",
        }).catch((e) => {
          console.error("❌ Файл прокси не обнаружен или недоступен:", e);
          return { ok: false, status: 404 };
        });

        if (!fileCheckResponse.ok) {
          console.error(
            "❌ Файл encar-proxy-improved.php не найден или недоступен. Статус:",
            fileCheckResponse.status
          );
          console.log(
            "⚠️ Убедитесь, что файл encar-proxy-improved.php находится в корневой директории сайта"
          );
        }

        // Продолжаем выполнение для использования локального поиска
      }

      // Ищем известные бренды в поисковом запросе
      const knownBrands = [
        "bmw",
        "audi",
        "mercedes",
        "мерседес",
        "бенц",
        "benz",
        "ferrari",
        "феррари",
        "lamborghini",
        "ламборгини",
        "bentley",
        "бентли",
        "rolls",
        "royce",
        "роллс",
        "ройс",
        "porsche",
        "порше",
        "aston",
        "астон",
        "martin",
        "мартин",
      ];

      let searchBrand = null;
      let searchModel = "";

      // Проверяем, есть ли известный бренд в поисковом запросе
      for (const term of searchTerms) {
        if (knownBrands.includes(term.toLowerCase())) {
          searchBrand = term;
          break;
        }
      }

      // Объединяем бренды из нескольких слов
      if (!searchBrand) {
        if (
          searchText.includes("rolls royce") ||
          searchText.includes("роллс ройс")
        ) {
          searchBrand = "Rolls-Royce";
        } else if (
          searchText.includes("aston martin") ||
          searchText.includes("астон мартин")
        ) {
          searchBrand = "Aston Martin";
        } else if (
          searchText.includes("mercedes benz") ||
          searchText.includes("мерседес бенц")
        ) {
          searchBrand = "Mercedes-Benz";
        }
      }

      // Если нашли бренд, извлекаем модель
      if (searchBrand) {
        // Удаляем бренд из поискового запроса
        searchModel = searchText.replace(searchBrand.toLowerCase(), "").trim();
        console.log("🔍 Найден бренд:", searchBrand, "модель:", searchModel);
      }

      // Пробуем получить данные через API
      console.log("🔍 Запрос к API Encar...");
      console.log("⚠️ Явно указываем fileType = active_offer");
      const apiData = await fetchEncarData(
        "active_offer",
        searchBrand,
        searchModel
      );

      if (apiData && apiData.length > 0) {
        console.log(
          "✅ Получены данные от API:",
          apiData.length,
          "автомобилей"
        );
        renderCarCards(apiData, cardsContainer);
      } else {
        console.log("⚠️ API не вернул данные, используем локальный поиск");
        performLocalSearch(searchText);
      }
    } catch (error) {
      console.error("❌ Ошибка при поиске:", error);
      performLocalSearch(searchText);
    } finally {
      clearTimeout(timeoutId);
    }
  }

  // Функция фильтрации автомобилей по поисковому запросу
  function filterCarsBySearch(cars, searchQuery) {
    if (!searchQuery) return cars;

    // Нормализуем поисковый запрос
    searchQuery = searchQuery.toLowerCase().trim();
    const searchTerms = searchQuery.split(/\s+/);

    // Фильтруем автомобили
    return cars.filter((car) => {
      // Создаем строку для поиска из всех полей автомобиля
      const searchString = [
        car.brand,
        car.model,
        car.year,
        car.mileage,
        car.price,
      ]
        .join(" ")
        .toLowerCase();

      // Проверяем, содержатся ли все слова из поискового запроса
      return searchTerms.every((term) => searchString.includes(term));
    });
  }

  // Функция локального поиска
  async function performLocalSearch(searchQuery) {
    console.log("🔍 Выполняем локальный поиск...");

    try {
      // Пробуем получить мок-данные
      const mockData = await fetchMockData();

      if (mockData && mockData.length > 0) {
        console.log("✅ Получены мок-данные:", mockData.length, "автомобилей");

        // Фильтруем данные по поисковому запросу
        const filteredCars = filterCarsBySearch(mockData, searchQuery);

        if (filteredCars.length > 0) {
          console.log("✅ Найдено", filteredCars.length, "автомобилей");
          renderCarCards(filteredCars, document.querySelector(".order-cards"));
        } else {
          console.log("⚠️ По запросу ничего не найдено");
          document.querySelector(".order-cards").innerHTML =
            '<div class="no-results">По вашему запросу ничего не найдено</div>';
        }
      } else {
        // Если мок-данные недоступны, используем резервные данные
        console.log("⚠️ Мок-данные недоступны, используем резервные данные");
        const fallbackData = getFallbackMockData();
        const filteredCars = filterCarsBySearch(fallbackData, searchQuery);

        if (filteredCars.length > 0) {
          console.log(
            "✅ Найдено",
            filteredCars.length,
            "автомобилей в резервных данных"
          );
          renderCarCards(filteredCars, document.querySelector(".order-cards"));
        } else {
          console.log("⚠️ В резервных данных ничего не найдено");
          document.querySelector(".order-cards").innerHTML =
            '<div class="no-results">По вашему запросу ничего не найдено</div>';
        }
      }
    } catch (error) {
      console.error("❌ Ошибка при локальном поиске:", error);
      document.querySelector(".order-cards").innerHTML =
        '<div class="error">Произошла ошибка при поиске. Пожалуйста, попробуйте позже.</div>';
    }
  }

  // Функция получения данных от API Encar
  async function fetchEncarData(
    fileType = "active_offer",
    brand = "",
    model = ""
  ) {
    console.log("🔍 Запрос к API Encar...");
    console.log("⚠️ Используемый fileType:", fileType);

    try {
      // Убедимся, что fileType всегда 'active_offer'
      fileType = "active_offer";

      // Формируем URL для запроса - используем правильный endpoint
      const url = new URL("/api/encar-proxy.php", window.location.href);
      url.searchParams.set("fileType", fileType);
      url.searchParams.set("date", new Date().toISOString().split("T")[0]);
      // Добавляем разумный лимит по умолчанию для производительности
      url.searchParams.set("limit", "20");

      if (brand) {
        url.searchParams.set("brand", brand);
      }
      if (model) {
        url.searchParams.set("model", model);
      }

      // Добавляем параметр для прямого запроса к API
      url.searchParams.set("direct", "1");

      console.log("🔍 URL запроса:", url.toString());

      // Выполняем запрос с таймаутом
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 секунд таймаут

      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log("✅ Получены данные от API:", data);

      return data;
    } catch (error) {
      console.error("❌ Ошибка при получении данных от API:", error);
      return null;
    }
  }

  // Функция получения мок-данных
  async function fetchMockData() {
    console.log("🔍 Запрос мок-данных...");

    try {
      const response = await fetch("/api/mock-data");

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log("✅ Получены мок-данные:", data);

      return data;
    } catch (error) {
      console.error("❌ Ошибка при получении мок-данных:", error);
      return null;
    }
  }

  // Функция получения резервных мок-данных
  function getFallbackMockData() {
    console.log("🔍 Используем резервные мок-данные...");

    return [
      {
        brand: "BMW",
        model: "M5",
        year: "2023",
        mileage: "0",
        price: "120000",
        image: "Img/bmw-m5.jpg",
        logo: "PNG/bmw-logo.png",
      },
      {
        brand: "Audi",
        model: "RS7",
        year: "2023",
        mileage: "1000",
        price: "110000",
        image: "Img/audi-rs7.jpg",
        logo: "PNG/audi-logo.png",
      },
    ];
  }

  // Функция обработки URL изображения
  function processImageUrl(imageUrl) {
    if (!imageUrl) return "Img/placeholder-car.jpg";

    // Проверяем, является ли URL валидным
    if (!isValidUrl(imageUrl)) {
      console.warn("⚠️ Некорректный URL изображения:", imageUrl);
      return "Img/placeholder-car.jpg";
    }

    try {
      // Пробуем обработать внешний URL
      return processExternalUrl(imageUrl);
    } catch (error) {
      console.error("❌ Ошибка при обработке URL изображения:", error);
      return "Img/placeholder-car.jpg";
    }
  }

  // Функция проверки валидности URL
  function isValidUrl(urlString) {
    try {
      new URL(urlString);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Функция обработки внешнего URL
  function processExternalUrl(url) {
    // Извлекаем модель из URL
    const model = extractModelFromUrl(url);

    if (model) {
      // Пробуем найти локальное изображение по модели
      const localImage = getLocalImageByModel(model);
      if (localImage) {
        return localImage;
      }
    }

    // Если не нашли локальное изображение, возвращаем заглушку
    return "Img/placeholder-car.jpg";
  }

  // Функция извлечения модели из URL
  function extractModelFromUrl(url) {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split("/");

      // Ищем часть URL, которая может быть моделью
      for (const part of pathParts) {
        if (part.match(/^[a-zA-Z0-9-]+$/)) {
          return part.toLowerCase();
        }
      }
    } catch (e) {
      console.error("❌ Ошибка при извлечении модели из URL:", e);
    }

    return null;
  }

  // Функция получения локального изображения по модели
  function getLocalImageByModel(model) {
    // Список известных моделей и их изображений
    const modelImages = {
      m5: "Img/bmw-m5.jpg",
      rs7: "Img/audi-rs7.jpg",
      ferrari: "Img/ferrari.jpg",
      lamborghini: "Img/lamborghini.jpg",
    };

    return modelImages[model] || null;
  }

  // Функция форматирования цены
  function formatPrice(price) {
    if (!price) return "$0";

    // Преобразуем цену в число
    const numericPrice = parseInt(price);

    if (isNaN(numericPrice)) {
      console.warn("⚠️ Некорректная цена:", price);
      return "$0";
    }

    // Форматируем цену с разделителями
    return "$" + numericPrice.toLocaleString();
  }

  // Функция тестирования URL изображения
  async function testImageUrl(url) {
    try {
      const response = await fetch(url, { method: "HEAD" });
      return response.ok;
    } catch (e) {
      return false;
    }
  }

  // Функция резервного копирования существующих карточек
  function backupExistingCards(container) {
    if (!container) return;

    // Сохраняем HTML контейнера
    const backupHtml = container.innerHTML;

    // Создаем скрытый элемент для хранения резервной копии
    let backupElement = document.getElementById("cards-backup");
    if (!backupElement) {
      backupElement = document.createElement("div");
      backupElement.id = "cards-backup";
      backupElement.style.display = "none";
      document.body.appendChild(backupElement);
    }

    // Сохраняем резервную копию
    backupElement.innerHTML = backupHtml;

    console.log("✅ Создана резервная копия карточек");
  }

  // Функция отрисовки карточек автомобилей
  function renderCarCards(cars, container) {
    if (!container) return;

    backupExistingCards(container);
    container.innerHTML = "";

    if (!cars || cars.length === 0) {
      container.innerHTML =
        '<div class="no-results">Автомобили не найдены</div>';
      return;
    }

    cars.forEach((car) => {
      const card = document.createElement("div");
      card.className = "order-card";

      // Получаем первое изображение или заглушку
      let imageUrl = "/assets/img/cars/default-car.jpg";
      let imagesArray = [];

      if (car.images) {
        try {
          // Handle double-encoded JSON string
          const imagesStr = car.images.replace(/^"|"$/g, ""); // Remove outer quotes
          imagesArray = JSON.parse(imagesStr);
          if (Array.isArray(imagesArray) && imagesArray.length > 0) {
            // Remove extra quotes from each URL
            imageUrl = imagesArray[0].replace(/^"|"$/g, "");
          }
        } catch (e) {
          console.error("Error parsing images:", e);
        }
      } else if (car.image_url) {
        imageUrl = car.image_url;
        imagesArray = [imageUrl];
      } else if (car.image) {
        imageUrl = car.image;
        imagesArray = [imageUrl];
      }

      // Определяем, есть ли несколько изображений для показа стрелок
      const hasMultipleImages = imagesArray.length > 1;

      // Переводим текстовые данные автомобиля
      const brand = window.translateKoreanText(car.mark || car.brand || "");
      const model = window.translateKoreanText(car.model || "");
      const engine = window.translateKoreanText(
        car.engine_type || car.engine || "Не указан"
      );
      const transmission = window.translateKoreanText(
        car.transmission_type || "Не указан"
      );
      const color = window.translateKoreanText(car.color || "Не указан");
      const description = window.translateKoreanText(car.description || "—");
      const address = window.translateKoreanText(car.address || "—");
      const seller = window.translateKoreanText(car.seller || "—");

      // Создаем URL для формы обратной связи
      const feedbackUrl = `feedback-mockup.html?type=order&brand=${encodeURIComponent(
        brand
      )}&model=${encodeURIComponent(model)}&year=${encodeURIComponent(
        car.year || ""
      )}`;

      // Создаем стрелки навигации, если есть несколько изображений
      const navigationArrows = hasMultipleImages
        ? `
        <div class="card-nav prev-img" data-car-id="${
          car.id || ""
        }" onclick="event.stopPropagation(); navigateCardImage('${
            car.id || ""
          }', -1)">❮</div>
        <div class="card-nav next-img" data-car-id="${
          car.id || ""
        }" onclick="event.stopPropagation(); navigateCardImage('${
            car.id || ""
          }', 1)">❯</div>
      `
        : "";

      // Формируем HTML карточки в светлом минималистичном стиле
      card.innerHTML = `
        <div class="order-card-image">
          <img src="${imageUrl}" alt="${brand} ${model}" data-car-id="${
        car.id || ""
      }" data-img-index="0" data-images='${JSON.stringify(imagesArray)}'>
          ${navigationArrows}
          <div class="order-card-year">${car.year || ""}</div>
        </div>
        <div class="order-card-content">
          <div class="order-card-title">
            <h3>${brand} ${model}</h3>
          </div>
          <div class="order-card-specs">
            <div class="spec-item">
              <span class="spec-label">Двигатель:</span>
              <span class="spec-value">${engine}</span>
            </div>
            <div class="spec-item">
              <span class="spec-label">КПП:</span>
              <span class="spec-value">${transmission}</span>
            </div>
            <div class="spec-item">
              <span class="spec-label">Пробег:</span>
              <span class="spec-value">${
                car.km_age || car.mileage || "—"
              } км</span>
            </div>
          </div>
          <div class="order-card-footer">
            <div class="order-card-price">${formatPrice(car.price)}</div>
            <div class="order-card-buttons">
              <button class="order-card-details-btn">Подробнее</button>
              <a href="${feedbackUrl}" class="order-card-contact-btn">Связаться</a>
            </div>
          </div>
          <div class="order-card-details" style="display:none; margin-top:0.7rem;">
            <div><b>Цвет:</b> ${color}</div>
            <div><b>Адрес:</b> ${address}</div>
            <div><b>Продавец:</b> ${seller}</div>
            <div><b>Описание:</b> ${description}</div>
            ${
              car.diagnosis
                ? '<div><b>Диагностика:</b><br><pre style="white-space:pre-wrap;">' +
                  window.translateKoreanText(
                    JSON.stringify(car.diagnosis, null, 2)
                  ) +
                  "</pre></div>"
                : ""
            }
            ${
              car.accidents
                ? '<div><b>Аварии:</b><br><pre style="white-space:pre-wrap;">' +
                  window.translateKoreanText(
                    JSON.stringify(car.accidents, null, 2)
                  ) +
                  "</pre></div>"
                : ""
            }
          </div>
        </div>
      `;

      // Добавляем обработчик для кнопки "Подробнее"
      const btn = card.querySelector(".order-card-details-btn");
      const details = card.querySelector(".order-card-details");
      if (btn && details) {
        btn.addEventListener("click", () => {
          details.style.display =
            details.style.display === "none" ? "block" : "none";
          btn.textContent =
            details.style.display === "block" ? "Скрыть" : "Подробнее";
        });
      }

      container.appendChild(card);
    });

    console.log("✅ Отрисовано", cars.length, "карточек");
  }

  // Функция для навигации по изображениям в карточках
  function navigateCardImage(carId, direction) {
    // Находим изображение в карточке
    const img = document.querySelector(`img[data-car-id="${carId}"]`);
    if (!img) return;

    // Получаем текущий индекс и массив изображений
    let currentIndex = parseInt(img.getAttribute("data-img-index") || "0");
    let images = [];

    try {
      images = JSON.parse(img.getAttribute("data-images") || "[]");
    } catch (e) {
      console.error("Ошибка при получении массива изображений:", e);
      return;
    }

    if (!Array.isArray(images) || images.length <= 1) return;

    // Вычисляем новый индекс с круговой навигацией
    let newIndex = currentIndex + direction;
    if (newIndex < 0) newIndex = images.length - 1;
    if (newIndex >= images.length) newIndex = 0;

    // Меняем изображение с анимацией
    img.style.opacity = "0";
    img.style.transform = "scale(0.95)";

    setTimeout(() => {
      img.src = images[newIndex];
      img.setAttribute("data-img-index", newIndex);

      setTimeout(() => {
        img.style.opacity = "1";
        img.style.transform = "scale(1)";
      }, 50);
    }, 200);
  }

  // Функция получения изображения бренда
  function getBrandImage(brand) {
    if (!brand) return "Img/placeholder-car.jpg";

    // Приводим бренд к нижнему регистру для сравнения
    brand = brand.toLowerCase();

    // Список известных брендов и их изображений
    const brandImages = {
      bmw: "Img/bmw-m5.jpg",
      audi: "Img/audi-rs7.jpg",
      ferrari: "Img/ferrari.jpg",
      lamborghini: "Img/lamborghini.jpg",
      bentley: "Img/bentley-flying-spur.jpg",
      "aston martin": "Img/aston-martin.jpg",
    };

    return brandImages[brand] || "Img/placeholder-car.jpg";
  }

  // Функция получения логотипа бренда
  function getLogo(brand) {
    if (!brand) return "PNG/default-logo.png";

    // Приводим бренд к нижнему регистру для сравнения
    brand = brand.toLowerCase();

    // Список известных брендов и их логотипов
    const brandLogos = {
      bmw: "PNG/bmw-logo.png",
      audi: "PNG/audi-logo.png",
      ferrari: "PNG/ferrari-logo.png",
      lamborghini: "PNG/lamborghini-logo.png",
      bentley: "PNG/bentley-logo.png",
      "aston martin": "PNG/aston-martin-logo.png",
    };

    return brandLogos[brand] || "PNG/default-logo.png";
  }

  // Функция инициализации интеграции с Encar
  async function initEncarIntegration() {
    console.log("🔍 Инициализация интеграции с Encar...");

    try {
      // Пробуем получить данные от API
      console.log(
        "⚠️ Явно указываем fileType = active_offer для инициализации"
      );
      const apiData = await fetchEncarData("active_offer");

      if (apiData && apiData.length > 0) {
        console.log(
          "✅ Получены данные от API:",
          apiData.length,
          "автомобилей"
        );
        renderCarCards(apiData, document.querySelector(".order-cards"));
      } else {
        console.log("⚠️ API не вернул данные, используем мок-данные");

        // Пробуем получить мок-данные
        const mockData = await fetchMockData();

        if (mockData && mockData.length > 0) {
          console.log(
            "✅ Получены мок-данные:",
            mockData.length,
            "автомобилей"
          );
          renderCarCards(mockData, document.querySelector(".order-cards"));
        } else {
          console.log("⚠️ Мок-данные недоступны, используем резервные данные");

          // Используем резервные данные
          const fallbackData = getFallbackMockData();
          renderCarCards(fallbackData, document.querySelector(".order-cards"));
        }
      }
    } catch (error) {
      console.error("❌ Ошибка при инициализации:", error);

      // Пробуем загрузить резервные карточки
      loadBackupCards();
    }
  }

  // Функция загрузки резервных карточек
  function loadBackupCards() {
    console.log("🔍 Загрузка резервных карточек...");

    const backupElement = document.getElementById("cards-backup");
    const cardsContainer = document.querySelector(".order-cards");

    if (backupElement && cardsContainer) {
      cardsContainer.innerHTML = backupElement.innerHTML;
      console.log("✅ Резервные карточки загружены");
    } else {
      console.log("⚠️ Резервные карточки не найдены");
      cardsContainer.innerHTML =
        '<div class="error">Не удалось загрузить данные. Пожалуйста, обновите страницу.</div>';
    }
  }
});
