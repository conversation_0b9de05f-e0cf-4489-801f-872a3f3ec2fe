<?php
/**
 * Обработчик форм обратной связи для SHMS Авто
 * Адаптирован для работы с почтой Рег.ру
 */

// Настройки почты (можно вынести в отдельный конфиг файл)
$mail_config = [
    'smtp_host' => 'smtp.yandex.ru',
    'smtp_port' => 465,
    'smtp_user' => '<EMAIL>', // Замените на ваш email
    'smtp_pass' => 'SHMSAutogroup777!',   // Замените на ваш пароль
    'from_email' => '<EMAIL>',
    'from_name' => 'SHMS Авто',
    'to_email' => '<EMAIL>'   // Замените на вашу почту для получения заявок
];

// Настройки подключения к базе (если используется)
$db_config = [
    'host' => 'localhost',
    'user' => 'your_db_user',
    'pass' => 'your_db_pass',
    'name' => 'your_db_name'
];

// Получение данных из формы
$name    = $_POST['name']    ?? '';
$region  = $_POST['region']  ?? '';
$carType = $_POST['carType'] ?? '';
$model   = $_POST['model']   ?? '';
$phone   = $_POST['phone']   ?? '';
$email   = $_POST['email']   ?? '';
$contact = $_POST['contact'] ?? '';
$message = $_POST['message'] ?? '';
$promo   = $_POST['promo']   ?? '';
$consent = isset($_POST['consent']) ? 'Да' : 'Нет';
$formType = $_POST['formType'] ?? 'Обратная связь';

// Валидация данных
if (empty($name) && empty($contact) && empty($message) && empty($phone) && empty($email)) {
    http_response_code(400);
    echo json_encode(['error' => 'Необходимо заполнить хотя бы одно поле']);
    exit;
}

// Сохранение в базу данных (опционально)
try {
    if (!empty($db_config['host'])) {
        $mysqli = new mysqli($db_config['host'], $db_config['user'], $db_config['pass'], $db_config['name']);
        $mysqli->set_charset('utf8');

        if ($promo !== '') {
            $stmt = $mysqli->prepare("INSERT INTO promocodes (code, created_at) VALUES (?, NOW())");
            $stmt->bind_param("s", $promo);
            $stmt->execute();
            $stmt->close();
        }

        // Сохранение заявки в БД (если нужно)
        $stmt = $mysqli->prepare("INSERT INTO contact_forms (name, region, car_type, model, phone, email, contact_method, message, promo, consent, form_type, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");
        $stmt->bind_param("sssssssssss", $name, $region, $carType, $model, $phone, $email, $contact, $message, $promo, $consent, $formType);
        $stmt->execute();
        $stmt->close();

        $mysqli->close();
    }
} catch (Exception $e) {
    error_log("Ошибка сохранения в БД: " . $e->getMessage());
}

// Формирование сообщения
$mail_message = "Новая заявка с сайта SHMS Авто\n\n";
$mail_message .= "Тип формы: $formType\n";
$mail_message .= "Имя: $name\n";
$mail_message .= "Регион: $region\n";
$mail_message .= "Тип авто: $carType\n";
$mail_message .= "Модель: $model\n";
$mail_message .= "Телефон: $phone\n";
$mail_message .= "Email: $email\n";
$mail_message .= "Способ связи: $contact\n";
$mail_message .= "Сообщение: $message\n";
$mail_message .= "Промокод: $promo\n";
$mail_message .= "Согласие на обработку: $consent\n";
$mail_message .= "Дата: " . date('d.m.Y H:i:s') . "\n";

// Отправка через обычную функцию mail() (работает на большинстве хостингов)
$to = $mail_config['to_email'];
$subject = "Новая заявка с сайта SHMS Авто - $formType";
$headers = "From: {$mail_config['from_name']} <{$mail_config['from_email']}>\r\n";
$headers .= "Reply-To: {$mail_config['from_email']}\r\n";
$headers .= "Content-type: text/plain; charset=utf-8\r\n";
$headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";

$mail_sent = mail($to, $subject, $mail_message, $headers);

if ($mail_sent) {
    echo json_encode([
        'success' => true,
        'message' => 'Спасибо! Ваше сообщение отправлено. Мы свяжемся с вами в ближайшее время.'
    ]);
} else {
    http_response_code(500);
    echo json_encode([
        'error' => 'Ошибка отправки сообщения. Попробуйте позже или свяжитесь с нами по телефону.'
    ]);
}
?>